package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagUserDeleteReqDTO implements Serializable {

    private static final long serialVersionUID = 5949200086628155963L;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 操作员编号
     */
    private List<String> operatorNos;

    /**
     * 手机号(批量导入时传)
     */
    private List<String> mobiles;

}
