package com.lifekh.data.warehouse.api.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class TagClassificationQueryReqDTO implements Serializable {
    private static final long serialVersionUID = -3582757431691190582L;
    /**
     * 标签分类名称
     */
    private String tagClassificationName;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 是否全部业务线
     */
    private String roleNo;

    /**
     * 语言
     */
    private String language;
}
