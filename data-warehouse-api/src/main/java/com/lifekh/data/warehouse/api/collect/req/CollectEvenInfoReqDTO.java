package com.lifekh.data.warehouse.api.collect.req;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@Builder
public class CollectEvenInfoReqDTO implements Serializable {
    private static final long serialVersionUID = -6928714689024501358L;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * spm信息
     */
    private CollectSpmReqDTO spm;

    /**
     * 事件标识
     * 登陆：@login
     * 打开APP：@openApp
     * 页面访问：@viewPage
     * 点击：@click
     */
    private String event;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 业务信息
     */
    private Map<String, Object> ext;

    /**
     * sessionId
     */
    private String sessionId;

}
