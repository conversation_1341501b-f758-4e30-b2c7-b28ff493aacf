package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RuleTypeEnum implements BaseEnum<RuleTypeEnum, String> {

    LANGUAGE_TAG("language", "语言"),
    ZONE_TAG("zone", "地区"),
    BETA_TAG("beta", "内测"),
    RULE_TAG("rule", "规则"),
    CUSTOMIZE("customize", "自定义"),
    GENERAL_USER("general user", "普通用户"),
    INTERNAL_USER("internal user", "内部用户"),
    ORDER_SUCCESS("order success", "下单成功(次数)"),
    ORDER_AMOUNT("order amount", "订单金额(元)"),
    FIRST_ORDER_BIZ("first_order_biz", "用户首单业务线"),
    USER_ONLINE("user_online", "打开APP登录成功"),
    USER_OFFLINE("user_offline", "没有打开过APP"),

    ORDER_AVERAGE_PRICE("order_average_price", "客单价"),

    ONLINE_PAYMENT_COMPLETE_ORDER("online_payment_complete_order", "在线支付完单数量"),
    CASH_PAYMENT_COMPLETE_ORDER("cash_payment_complete_order", "货到付款完成订单"),
    SUBMIT_ORDER("submit_order", "下单数"),
    CANCEL_ORDER("cancel_order", "取消订单数"),

    HAVA_VOUCHER_COUPON("hava_voucher_coupon", "拥有现金券数量"),
    HAVA_SHIPPING_COUPON("hava_shipping_coupon", "拥有运费券数量"),
    USE_PROMO_CODE("use_promo_code", "使用优惠码"),
    ;

    private String code;

    private String message;

    public static RuleTypeEnum getByCode(String code) {
        for (RuleTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
