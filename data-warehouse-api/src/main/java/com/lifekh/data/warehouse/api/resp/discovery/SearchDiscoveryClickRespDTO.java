package com.lifekh.data.warehouse.api.resp.discovery;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SearchDiscoveryClickRespDTO implements Serializable {
    private static final long serialVersionUID = -2141216146541948435L;
    /**
     * 内容编号
     */
    private String contentNo;

    /**
     * 内容所属业务线
     */
    private String contentBusinessLine;

    /**
     * 内容类别, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private String contentCategory;

    /**
     * 内容类别集合, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private List<String> contentCategoryList;

    /**
     * 内容类型, 枚举：com.chaos.discovery.review.api.enums.ContentTypeEnum
     */
    private String contentType;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 用户
     */
    private String loginName;
    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 内容语言
     */
    private String contentLanguage;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 内容创建时间
     */
    private Date contentCreateTime;
}
