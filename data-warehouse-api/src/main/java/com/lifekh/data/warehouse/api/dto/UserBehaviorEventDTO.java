package com.lifekh.data.warehouse.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBehaviorEventDTO implements Serializable {

    private static final long serialVersionUID = -6500490040406851660L;

    /**
     * 用户编号
     */
    private String operatorNo;

    /**
     * 登陆号
     */
    private String loginName;

    /**
     * 事件
     */
    private String event;

    /**
     * 语言
     */
    private String language;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * app版本号
     */
    private String appVersion;

    /**
     * 应用版本号
     */
    private String appNo;

    /**
     * 用户属性
     */
    private String appId;

    /**
     * ip
     */
    private String ip;
}
