package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class EventTypeQueryReqDTO extends PageReqDTO implements Serializable {

    /**
     * 事件组
     */
    @NotBlank(message = "事件组不能为空")
    private String eventGroup;

    /**
     * 事件编号
     */
    @NotBlank(message = "事件编号不能为空")
    private String eventNo;

    /**
     * 事件名
     */
    @NotBlank(message = "事件名不能为空")
    private String eventName;

    /**
     * 业务线
     */
    @NotBlank(message = "业务线不能为空")
    private String bussinessLine;

    /**
     * 备注
     */
    private String remark;

    /**
     * 事件存储表
     */
    @NotBlank(message = "事件存储表不能为空")
    private String table;

    /**
     * 创建人
     */
    private String createBy;
}
