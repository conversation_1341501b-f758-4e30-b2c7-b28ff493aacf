package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.dto.req.RuleDataQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagNoRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserAttributesQueryRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRuleRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.RuleTagInfoDetailRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface RuleTagInfoFacade {

    /**
     * 新增规则标签
     * @param reqDTO
     * @throws PendingException
     */
    void add(RuleTagInfoAddReqDTO reqDTO) throws PendingException;

    /**
     * 新增规则标签V2
     * @param reqDTO
     * @throws PendingException
     */
    TagNoRespDTO addV2(RuleTagInfoAddReqV2DTO reqDTO) throws PendingException;

    /**
     * 编辑规则标签
     * @param reqDTO
     * @throws PendingException
     */
    void edit(RuleTagInfoEditReqDTO reqDTO) throws PendingException;

    /**
     * 编辑规则标签
     * @param reqDTO
     * @throws PendingException
     */
    void editV2(RuleTagInfoEditReqV2DTO reqDTO) throws PendingException;

    /**
     * 规则标签列表
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<RuleTagInfoDetailRespDTO> list(RuleTagInfoListReqDTO reqDTO) throws PendingException;

    /**
     * 详情
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    RuleTagInfoDetailRespDTO detail(RuleTagInfoDetailReqDTO reqDTO) throws PendingException;

    /**
     * 启用/关闭标签
     * @param reqDTO
     * @throws PendingException
     */
    void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException;
    /**
     * 查询用户属性
     *  @param ruleDataQueryReqDTO
     * @return
     * @throws PendingException
     */
    List<UserAttributesQueryRespDTO> queryUserAttributes(RuleDataQueryReqDTO ruleDataQueryReqDTO);
    /**
     * 查询用户行为
     * @return
     * @throws PendingException
     */
    List<UserBehaviorRuleRespDTO> queryUserBehaviorRule();

}
