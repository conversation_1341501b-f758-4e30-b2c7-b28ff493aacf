package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.dto.req.QueryInactiveUserReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UserBehaviorSearchReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.InactiveUserRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface UserBehaviorFacade {

    /**
     * 查询不活跃用户
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<InactiveUserRespDTO> queryInactiveUser(QueryInactiveUserReqDTO reqDTO) throws PendingException;

    /**
     * 用户行为数据查询
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    List<UserBehaviorRespDTO> queryUserBehavior(UserBehaviorSearchReqDTO reqDTO) throws PendingException;
}
