package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagUserAddReqDTO implements Serializable {

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 标签编号
     */
    private List<String> tagNo;
}
