package com.lifekh.data.warehouse.api.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class TagUserCountReqDTO implements Serializable {
    private static final long serialVersionUID = 1633417954200598788L;

    /**
     * 标签编号
     */
    private List<String> tagNos;

    /**
     * 标签逻辑：and-且、or-或
     */
    private String tagLogic;

}
