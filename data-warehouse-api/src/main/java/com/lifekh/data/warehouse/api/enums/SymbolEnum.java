package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SymbolEnum implements BaseEnum<SymbolEnum, String> {

    MORE_THAN("more than", "大于"),
    LESS_THAN("less than", "小于"),
    EQUAL("equal", "等于"),
    INTERVAL("interval", "区间"),
    MORE_OR_EQUAL("more or equal", "大于等于"),
    LESS_OR_EQUAL("less or equal", "小于等于"),
    ;

    private String code;

    private String message;
}
