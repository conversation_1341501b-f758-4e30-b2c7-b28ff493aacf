package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.dto.req.TagClassificationQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagClassificationRespDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagClassificationFacade {

    /**
     *
     *获取标签分类选项
     * @return
     * @throws PendingException
     */
    List<TagClassificationRespDTO> queryTagClassification(TagClassificationQueryReqDTO tagClassificationQueryReqDTO) throws PendingException;

}
