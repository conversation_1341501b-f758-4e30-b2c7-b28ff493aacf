package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.req.discovery.AdsViewLogSaveReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.AdsViewLogSearchReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.AdsViewLogRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

public interface AdsLogFacade {

    /**
     * 保存广告浏览记录
     *
     * @param reqDTO
     * @throws PendingException
     */
    void saveViewLog(AdsViewLogSaveReqDTO reqDTO) throws PendingException;

    /**
     * 分页查询广告浏览记录
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<AdsViewLogRespDTO> searchViewLog(AdsViewLogSearchReqDTO reqDTO) throws PendingException;
}
