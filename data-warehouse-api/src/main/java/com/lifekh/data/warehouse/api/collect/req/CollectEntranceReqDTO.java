package com.lifekh.data.warehouse.api.collect.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
public class CollectEntranceReqDTO implements Serializable {

    /**
     * 入口名称
     */
    private String entranceName;

    /**
     * 入口类型
     */
    private String entranceType;

    /**
     * 入口id
     */
    private String entranceId;
}
