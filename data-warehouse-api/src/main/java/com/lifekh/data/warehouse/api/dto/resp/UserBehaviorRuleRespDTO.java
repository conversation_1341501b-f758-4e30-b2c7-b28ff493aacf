package com.lifekh.data.warehouse.api.dto.resp;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/26 17:20
 * @Version 1.0
 **/
@Data
public class UserBehaviorRuleRespDTO implements Serializable {
    private static final long serialVersionUID = -3417248690678547445L;
    /**
     * 规则编号behavior
     */
    private String ruleNo;
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 通用规则
     */
    private List<RuleDTO> generalRules;

    /**
     * 特殊规则
     */
    private List<RuleDTO> specialRules;
}
