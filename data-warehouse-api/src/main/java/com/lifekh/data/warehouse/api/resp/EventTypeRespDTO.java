package com.lifekh.data.warehouse.api.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EventTypeRespDTO implements Serializable {

    private String id;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 事件组
     */
    private String eventGroup;

    /**
     * 事件编号
     */
    private String eventNo;

    /**
     * 事件名
     */
    private String eventName;

    /**
     * 业务线
     */
    private String bussinessLine;

    /**
     * 状态：10-采集，11-停止
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 事件存储表
     */
    private String table;
}
