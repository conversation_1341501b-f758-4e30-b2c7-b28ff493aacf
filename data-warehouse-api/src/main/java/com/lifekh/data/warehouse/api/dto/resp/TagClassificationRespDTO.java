package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagClassificationRespDTO implements Serializable {
    private static final long serialVersionUID = -7716228634979799304L;
    /**
     * 一级分类编号
     */
    private String firstTagClassificationNo;

    /**
     * 一级分类名称-中文
     */
    private String firstTagClassificationName;

    /**
     * 一级分类名称-英文
     */
    private String firstTagClassificationNameEn;

    /**
     * 一级分类名称-柬文
     */
    private String firstTagClassificationNameKm;

    /**
     * 二级标签数量
     */
    private Integer size;

    /**
     * 是否允许编辑分类，10-允许，11-不允许
     */
    private Integer isAllowEdit;

    /**
     * 二级分类标签
     */
    private List<TagSeconfClassificationRespDTO> secondClassificationTags;
}
