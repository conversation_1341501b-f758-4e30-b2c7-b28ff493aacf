package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ActiveUserStaticReqDTO extends PageReqDTO implements Serializable {
    private static final long serialVersionUID = -2246307585399299207L;
    /**
     * 开始时间
     */
    private Date  startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 统计类型：10-按天，11-按周，12-按月
     */
    private Integer staticType;
}
