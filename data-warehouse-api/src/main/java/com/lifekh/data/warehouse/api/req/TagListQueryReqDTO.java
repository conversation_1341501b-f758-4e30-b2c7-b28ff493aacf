package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class TagListQueryReqDTO extends PageReqDTO implements Serializable {

    private static final long serialVersionUID = 2276392070059979630L;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 语言
     */
    @NotBlank
    private String language;
}
