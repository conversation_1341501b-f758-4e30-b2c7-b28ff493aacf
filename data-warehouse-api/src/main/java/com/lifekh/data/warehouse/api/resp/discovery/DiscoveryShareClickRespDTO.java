package com.lifekh.data.warehouse.api.resp.discovery;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class DiscoveryShareClickRespDTO implements Serializable {
    private static final long serialVersionUID = 3631430303512251790L;

    /**
     * 内容编号
     */
    private String contentNo;

    /**
     * 内容所属业务线
     */
    private String contentBusinessLine;

    /**
     * 内容类别集合, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private List<String> contentCategoryList;

    /**
     * 内容类型, 枚举：com.chaos.discovery.review.api.enums.ContentTypeEnum
     */
    private String contentType;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 内容语言
     */
    private String contentLanguage;

    /**
     * 好物名称
     */
    private String goodsName;

    /**
     * 好物种类  com.chaos.discovery.review.api.enums.GoodsTypeEnum
     */
    private String goodsType;

    /**
     * 好物链接
     */
    private String goodsLink;

    /**
     * 分享人
     */
    private String loginName;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 分享时间
     */
    private Date recordTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 内容创建时间
     */
    private Date contentCreateTime;
}
