package com.lifekh.data.warehouse.api.resp.discovery;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class DiscoveryBehaviorCountRespDTO implements Serializable {
    private static final long serialVersionUID = 5558234994755426206L;

    /**
     * 语言
     */
    private String language;

    /**
     * 栏目编号
     */
    private String categoryNo;

    /**
     * 浏览数
     */
    private Integer viewCount;

    /**
     * 分享数
     */
    private Integer shareCount;
}
