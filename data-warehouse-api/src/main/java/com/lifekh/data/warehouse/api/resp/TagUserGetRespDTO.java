package com.lifekh.data.warehouse.api.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class TagUserGetRespDTO implements Serializable {
    private static final long serialVersionUID = -8679252485401334218L;

    /**
     * mongo数据id
     */
    private String id;

    /**
     * 操作员编号
     */
    private String operatorNo;

}
