package com.lifekh.data.warehouse.api.resp.discovery;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AdsViewLogRespDTO implements Serializable {
    private static final long serialVersionUID = -3106164718652149862L;

    /**
     * 广告编号
     */
    private String adsNo;

    /**
     * 广告主体
     */
    private String adsSubject;

    /**
     * 广告类型
     */
    private String adsType;

    /**
     * 广告名称
     */
    private String adsName;

    /**
     * 广告标题
     */
    private String adsTitle;

    /**
     * 应用场景
     */
    private String useScene;

    /**
     * 语言
     */
    private String language;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 点击时间
     */
    private Date recordTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备ID
     */
    private String deviceId;

}
