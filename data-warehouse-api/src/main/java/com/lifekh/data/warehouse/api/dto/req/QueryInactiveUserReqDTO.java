package com.lifekh.data.warehouse.api.dto.req;

import com.lifekh.data.warehouse.api.req.PageReqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QueryInactiveUserReqDTO extends PageReqDTO implements Serializable {
    private static final long serialVersionUID = 4956525373078759116L;

    /**
     * 最后活跃时间
     */
    private Date lastOnlineTime;

    /**
     * 注册开始时间
     */
    private Date registerStartTime;

    /**
     * 注册结束时间
     */
    private Date registerEndTime;
}
