package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.ReturnCode;

public enum WarehouseErrorCodeEnum implements ReturnCode {
    W1001("W1001","新增二级标签分类失败，名称已存在"),
    W1002("W1002","标签编辑失败，名称已存在"),
    W1003("W1003","新增失败，用户属性不允许新增二级分类"),

    W2001("W2001","中文标签名称已存在"),
    W2002("W2002","英文标签名称已存在"),
    W2003("W2003","柬文标签名称已存在"),
    W2004("W2004","中文二级分类已存在"),
    W2005("W2005","英文二级分类已存在"),
    W2006("W2006","柬文二级分类已存在"),
    W2007("W2007","系统标签不可编辑"),
    ;

    WarehouseErrorCodeEnum(String code, String message){
        this.code = code;
        this.message = message;
    }
    private String code;
    private String message;

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
