package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.req.ClassificationTagReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoFirstClassificationRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoRespDTO;
import com.lifekh.data.warehouse.api.req.TagInfoHasUserReqDTO;
import com.lifekh.data.warehouse.api.req.TagListQueryReqDTO;
import com.lifekh.data.warehouse.api.resp.TagInfoHasUserRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagInfoFacade {

    PageInfoDTO<TagInfoDTO> list(TagListQueryReqDTO reqDTO) throws PendingException;

    PageInfoDTO<TagInfoHasUserRespDTO> listForHasUser(TagInfoHasUserReqDTO reqDTO) throws PendingException;

    /**
     * 新增基础标签
     * @param reqDTO
     * @throws PendingException
     */
    void add(TagAddReqDTO reqDTO) throws PendingException;

    /**
     * 编辑基础标签
     * @param reqDTO
     * @throws PendingException
     */
    void edit(TagEditReqDTO reqDTO) throws PendingException;

    /**
     * 启用关闭标签
     * @throws PendingException
     */
    void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException;
    /**
     * 根据标签编号批量查询标签
     *
     * @param tagNos
     * @return
     * @throws PendingException
     */
    List<TagInfoRespDTO> queryTagByTagNos(List<String> tagNos) throws PendingException;

    /**
     * 根据标签名查询分类下的标签内容(分群-标签选择器)
     * @param reqDTO
     */
    List<TagInfoFirstClassificationRespDTO> queryClassificationTagByTagName(ClassificationTagReqDTO reqDTO);
}
