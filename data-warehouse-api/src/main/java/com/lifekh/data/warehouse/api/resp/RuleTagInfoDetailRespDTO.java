package com.lifekh.data.warehouse.api.resp;

import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class RuleTagInfoDetailRespDTO implements Serializable {

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 一级分类编号
     */
    private String firstClassificationNo;

    /**
     * 一级分类名称
     */
    private String firstClassification;

    /**
     * 二级分类编号
     */
    private String secondaryClassificationNo;

    /**
     * 二级分类名称
     */
    private String secondaryClassification;

    /**
     * 二级分类名称-英文
     */
    private String secondaryClassificationEn;

    /**
     * 二级分类名称-柬文
     */
    private String secondaryClassificationKm;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 标签规则编号
     */
    private List<RuleTagRuleReqV2DTO> rule;

    /**
     * 标签分类，枚举：TagCatalogueEnum
     */
    private String tagCatalogue;

    /**
     * 更新方式，枚举：TagExecTypeEnum
     */
    private String execType;

    /**
     * 生效状态，枚举：TagExecStatusEnum
     */
    private String execStatus;

    /**
     * 执行时间
     */
    private Date execTime;

    /**
     * 可见范围，枚举：TagScopeEnum
     */
    private String tagScope;

    /**
     * 操作时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 操作人
     */
    private String updateBy;
}
