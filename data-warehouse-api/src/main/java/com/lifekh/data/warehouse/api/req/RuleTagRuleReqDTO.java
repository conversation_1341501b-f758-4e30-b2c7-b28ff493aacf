package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
@Data
public class RuleTagRuleReqDTO implements Serializable {

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 业务线：枚举BusinessLineEnum
     */
    private String businessLine;

    /**
     * 标签规则
     */
    private String ruleType;

    /**
     * 标签值
     */
    private String ruleValue;

    /**
     * 天数
     */
    private Long days;

    /**
     * 最小值
     */
    private Long min;

    /**
     * 最大值
     */
    private Long max;

    /**
     * 符号
     */
    private String symbol;

    /**
     * 行为
     */
    private String behavior;
}
