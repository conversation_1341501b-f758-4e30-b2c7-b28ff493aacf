package com.lifekh.data.warehouse.api.collect.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CollectBuriedPointReqDTO implements Serializable {

    private static final long serialVersionUID = -358755568046145036L;

    /**
     * 登录手机号
     */
    private String loginName;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 语言
     */
    private String language;

    /**
     * 用户属性
     */
    private String appId;

    /**
     * 应用编号
     */
    private String appNo;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 版本号
     */
    private String appVersion;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * IP
     */
    @JsonIgnore
    private String ip;

    /**
     * 事件信息
     */
    private List<CollectEvenInfoReqDTO> evenInfos;

}
