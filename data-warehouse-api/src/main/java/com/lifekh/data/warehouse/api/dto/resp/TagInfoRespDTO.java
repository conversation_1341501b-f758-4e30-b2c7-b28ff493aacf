package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class TagInfoRespDTO implements Serializable {
    private static final long serialVersionUID = -93441534607844363L;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签所属业务线 枚举 TagClassifyEnum
     */
    private String tagClassify;
}
