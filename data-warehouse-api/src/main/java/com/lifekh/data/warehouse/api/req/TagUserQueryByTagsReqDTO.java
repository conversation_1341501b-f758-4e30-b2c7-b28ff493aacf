package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagUserQueryByTagsReqDTO extends PageReqDTO implements Serializable {

    private static final long serialVersionUID = -8291432435353497649L;

    /**
     * 标签编号列表
     */
    private List<String> tagNos;

    /**
     * 标签逻辑
     */
    private String tagLogic;
}
