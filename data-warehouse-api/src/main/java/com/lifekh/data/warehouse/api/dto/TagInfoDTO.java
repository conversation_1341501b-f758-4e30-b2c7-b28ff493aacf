package com.lifekh.data.warehouse.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Data
public class    TagInfoDTO implements Serializable {
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagCatalogueEnum
     */
    private String tagCatalogue;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 规则
     */
    private List<TagRuleDTO> rule;
}
