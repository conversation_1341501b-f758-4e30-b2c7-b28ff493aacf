package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.req.discovery.DiscoveryBehaviorCountReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryGoodsClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryShareClickReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryBehaviorCountRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryShareClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryGoodsClickRespDTO;
import com.outstanding.framework.core.PageInfoDTO;

public interface DiscoveryContentFacade {
    /**
     * 浏览数查询
     * @throws
     */
    PageInfoDTO<SearchDiscoveryClickRespDTO> searchDiscoveryClickDetail(SearchDiscoveryClickReqDTO searchDiscoveryClickReqDTO);

    /**
     * 好物点击数查询接口
     * @throws
     */
    PageInfoDTO<SearchDiscoveryGoodsClickRespDTO> searchDiscoveryGoodsClick(SearchDiscoveryGoodsClickReqDTO searchDiscoveryGoodsClickReqDTO);

    /**
     * 分享数查询接口
     * @throws
     */
    PageInfoDTO<DiscoveryShareClickRespDTO> searchDiscoveryShareClick(SearchDiscoveryShareClickReqDTO reqDTO);

    /**
     * 统计发现频道行为数量
     *
     * @param reqDTO
     * @return
     */
    DiscoveryBehaviorCountRespDTO countDiscoveryBehavior(DiscoveryBehaviorCountReqDTO reqDTO);
}
