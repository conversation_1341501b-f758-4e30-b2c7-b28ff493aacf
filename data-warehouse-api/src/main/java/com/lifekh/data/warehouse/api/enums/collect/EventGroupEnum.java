package com.lifekh.data.warehouse.api.enums.collect;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventGroupEnum implements BaseEnum<EventGroupEnum, String> {

    LOGIN("login", "登陆"),
    VIEW_PAGE("viewPage", "页面访问"),
    ORDER("order", "下单"),
    CLICK("click", "点击"),
    OTHER("other", "其他")
    ;

    private String code;

    private String message;

    public static EventGroupEnum getByCode(String code) {
        for (EventGroupEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
