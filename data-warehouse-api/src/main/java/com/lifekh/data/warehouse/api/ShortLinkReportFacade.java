package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.req.ShortLinkReportQueryReqDTO;
import com.lifekh.data.warehouse.api.resp.ShortLinkReportRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

public interface ShortLinkReportFacade {

    /**
     * 查询短链报表
     */
    PageInfoDTO<ShortLinkReportRespDTO> queryReport(ShortLinkReportQueryReqDTO reqDTO) throws PendingException;

}
