package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TagUserRespDTO implements Serializable {
    private static final long serialVersionUID = 7782519542999276763L;

    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 标签编号
     */
    private List<String> tagNo;
}
