package com.lifekh.data.warehouse.api.enums.collect;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum  EventPageBusinessLineEnum implements BaseEnum<EventPageBusinessLineEnum, String> {
    SUPERAPP("SuperApp", "超级APP"),
    YUMNOW("YumNow", "外卖"),
    TINHNOW("TinhNow", "电商"),
    GROUP_BUY("GroupBuy", "团购"),
    PAYNOW("BillPayment", "支付"),
    PHONETOPUP("PhoneTopUp", "话费充值"),
    OTA("OTA", "机票"),
    GAME_CHANNEL("GameChannel", "游戏频道"),
    HOTEL_CHANNEL("HotelChannel", "酒店频道"),
    MEMBER_CENTRE("MemberCentre", "会员");

    private String code;
    private String message;

    public static EventPageBusinessLineEnum getByCode(String code) {
        for (EventPageBusinessLineEnum e : EventPageBusinessLineEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
