package com.lifekh.data.warehouse.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@Data
public class TagUserDTO implements Serializable {
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 关联标签表
     */
    private List<TagInfoDTO> tagInfo;

    private List<String> tagNo;
}
