package com.lifekh.data.warehouse.api.req;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RuleTagRuleReqV2DTO implements Serializable {

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则时效性
     */
    private Boolean timeliness;

    /**
     * 规则类型（属性、行为）枚举ClassificationEnum
     */
    private String classification;

    /**
     * 标签规则
     */
    private String ruleType;

    /**
     * 规则的值
     */
    private String ruleValue;

    /**
     * 通用规则：业务线
     */
    private List<RuleDTO> generalRules;

    /**
     * 特殊规则
     */
    private List<RuleDTO> specialRules;
}
