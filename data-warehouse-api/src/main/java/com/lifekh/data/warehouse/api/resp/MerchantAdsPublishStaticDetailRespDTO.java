package com.lifekh.data.warehouse.api.resp;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

@Data
public class MerchantAdsPublishStaticDetailRespDTO implements Serializable {
    private static final long serialVersionUID = 7488631560652410246L;
    @Id
    private String id;

    /**
     * 英文商户名
     */
    private String merchantNameEn;

    /**
     * 柬文商户名
     */
    private String merchantNameKm;

    /**
     * 商户名
     */
    private String merchantNameZh;

    /**
     * 投放编号
     */
    private String nodePublishNo;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 实际结束时间
     */
    private Date realEndTime;

    /**
     * 页面访问数
     */
    private Integer pageViewNum = 0;

    /**
     * 页面访问用户数
     */
    private Integer pageViewUserNum = 0;

    /**
     * 广告总点击次数
     */
    private Integer  adsTotalClickNum = 0;

    /**
     * 广告总点击用户数
     */
    private Integer  adsTotalClickUserNum = 0;

    /**
     * 点击次数占比
     */
    private Double clickNumRate = 0.0;
    /**
     * 页面访问用户数占比
     */
    private Double viewUserNumRate = 0.0;
}
