package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagUserGetReqDTO implements Serializable {
    private static final long serialVersionUID = 9179383179337527871L;

    /**
     * 标签群编号
     */
    private List<String> groupNos;

    /**
     * 标签编号
     */
    private List<String> tagNos;

    /**
     * 前一条记录的id，为空时相当于查第一页
     */
    private String prevId;

    /**
     * 查询数量
     */
    private Integer limit;
}
