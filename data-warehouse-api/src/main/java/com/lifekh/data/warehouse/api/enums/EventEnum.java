package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum EventEnum implements BaseEnum<EventEnum, String> {

    /**
     *刷新登陆令牌
     */
    REFRESH_TOKEN("REFRESH_TOKEN", "刷新登陆令牌"),
    /**
     *登陆
     */
    LOGIN("LOGIN", "登陆"),
    /**
     *LOGOUT
     */
    LOGOUT("LOGOUT", "登出"),
    /**
     *下单
     */
    CREATE_ORDER("CREATE_ORDER", "下单"),
    /**
     *外卖门店列表查询
     */
    YUMNOW_CATEGORY("YUMNOW_CATEGORY", "外卖门店列表查询"),
    /**
     *外卖门店详情
     */
    YUMNOW_STORE_DETAIL("YUMNOW_STORE_DETAIL", "外卖门店详情"),
    /**
     *电商商品列表查询
     */
    TINHNOW_CATEGORY("TINHNOW_CATEGORY", "电商商品列表查询"),
    /**
     *电商商品详情
     */
    TINHNOW_PRODUCT_DETAIL("TINHNOW_PRODUCT_DETAIL", "电商商品详情"),
    /**
     *其它
     */
    OTHER("OTHER", ""),
    ;

    private String code;

    private String message;


    public static String getMessageByCode(String code) {
        for (EventEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.message;
            }
        }
        return OTHER.message;
    }
}
