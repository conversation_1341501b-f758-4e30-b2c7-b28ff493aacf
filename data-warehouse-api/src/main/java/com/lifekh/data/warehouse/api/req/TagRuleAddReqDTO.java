package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class TagRuleAddReqDTO implements Serializable {

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则策略：RuleTypeEnum
     */
    private String RuleType;

    /**
     * 规则的值
     */
    private String ruleValue;

    /**
     * 业务线
     */
    private String businessLine;
}
