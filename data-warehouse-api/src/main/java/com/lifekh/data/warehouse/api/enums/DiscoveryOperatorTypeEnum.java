package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscoveryOperatorTypeEnum implements BaseEnum<DiscoveryOperatorTypeEnum, String> {
    GOODS_CLICK("discovry_goods_click", "好物点击"),
    ADD_SHOP_CART("add_shopcart", "加购好物"),
    ORDER_SUBMIT("order_submit", "好物下单"),
    ;
    private String code;

    private String message;
}
