package com.lifekh.data.warehouse.api.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateUserTagMQReqDTO implements Serializable {
    private static final long serialVersionUID = -3161679338313558677L;

    /**
     * 更新操作
     */
    private List<UpdateUserTagDTO> list;
}
