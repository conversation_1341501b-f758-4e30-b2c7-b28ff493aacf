package com.lifekh.data.warehouse.api;


import com.lifekh.data.warehouse.api.req.ActiveUserStaticReqDTO;
import com.lifekh.data.warehouse.api.req.ReportStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserReportStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserStickyRespDTO;
import com.lifekh.data.warehouse.api.resp.NewUserReportStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.UserRemainStaticRespDTO;

import java.util.List;

public interface UserDataStaticFacade {
    List<NewUserReportStaticRespDTO> getNewUserReportStatic(ReportStaticReqDTO reportStaticReqDTO);
    List<ActiveUserReportStaticRespDTO> getActiveUserReportStatic(ActiveUserStaticReqDTO reportStaticReqDTO);
    List<ActiveUserStickyRespDTO> getActiveUserStickyStatic(ReportStaticReqDTO reportStaticReqDTO);
    List<UserRemainStaticRespDTO> getUserRemainStatic(ReportStaticReqDTO reportStaticReqDTO);
}
