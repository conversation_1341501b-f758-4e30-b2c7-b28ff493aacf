package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UpdateUserOperatorInfoReqDTO implements Serializable {

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 头像
     */
    private String headGroup;
    /**
     * 头像
     */
    private String headFileId;

    /**
     * 头像地址
     */
    private String headURL;


    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 昵称
     */
    private String nickname;
    /**
     * 邮件
     */
    private String email;

    /**AddressFacade
     * 手机
     */
    private String mobile;

    /**
     * 联系人 json格式：[{"areaCode":"86","phone":"13682242967","name":"z3","tag":"home","address":"xxx","email":"<EMAIL>"}]
     */
    private String contacts;

    /**
     * app语言
     */
    private String language;
}