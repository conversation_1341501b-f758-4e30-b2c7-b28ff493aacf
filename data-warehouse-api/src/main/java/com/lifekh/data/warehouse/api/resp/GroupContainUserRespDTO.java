package com.lifekh.data.warehouse.api.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GroupContainUserRespDTO implements Serializable {
    private static final long serialVersionUID = 704662880208900140L;
    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户标签
     */
    private List<String> userTagNos;

    /**
     * 操作员是否在群中比较结果
     */
    private Boolean checkResult;
}
