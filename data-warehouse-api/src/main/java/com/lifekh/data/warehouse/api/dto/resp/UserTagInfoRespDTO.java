package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class UserTagInfoRespDTO implements Serializable {
    private static final long serialVersionUID = 5786206441821252423L;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户标签更新时间
     */
    private Date updateTime;

    /**
     * 标签编号
     */
    private List<String> tagNo;

    /**
     * 标签信息
     */
    private List<SimpleTagInfoRespDTO> tagInfos;

}
