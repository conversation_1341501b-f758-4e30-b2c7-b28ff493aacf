package com.lifekh.data.warehouse.api.constants;

public interface Topic {

    /**
     * 更新用户标签TOPIC
     */
    String DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC = "DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC";

    /**用户行为信息采集*/
    String USER_BEHAVIOR_INFO_TOPIC = "USER_BEHAVIOR_INFO_TOPIC";

    /**修改操作员信息topic**/
    String USER_MODIFY_OPERATOR_INFO_TOPIC = "USER_MODIFY_OPERATOR_INFO_TOPIC";

    /**
     * 埋点采集
     */
    String DATA_WAREHOUSE_COLLECTION_MQ_TOPIC = "DATA_WAREHOUSE_COLLECTION_MQ_TOPIC";

    /**
     * 标准埋点采集
     */
    String DATA_WAREHOUSE_COLLECTION_STANDARD_MQ_TOPIC = "DATA_WAREHOUSE_COLLECTION_STANDARD_MQ_TOPIC";

    /**
     * 其它埋点采集
     */
    String DATA_WAREHOUSE_COLLECTION_OTHER_MQ_TOPIC = "DATA_WAREHOUSE_COLLECTION_OTHER_MQ_TOPIC";
}
