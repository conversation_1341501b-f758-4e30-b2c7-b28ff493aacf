package com.lifekh.data.warehouse.api.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GreenBlueTagRespDTO implements Serializable {
    private static final long serialVersionUID = 7782519542999276763L;

    /**
     * 标签编号
     */
    private List<String> tagNos;
}
