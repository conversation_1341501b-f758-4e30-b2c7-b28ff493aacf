package com.lifekh.data.warehouse.api.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/31 14:28
 * @Version 1.0
 **/
@Data
public class ClassificationTagReqDTO  implements Serializable {
    private static final long serialVersionUID = 3834022989986545766L;
    /**
     * 标签分类业务线：公共：public，外卖：YumNow，电商：TinhNow
     */
    private String tagClassify;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 语言
     */
    private String language;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 是否全部业务线
     */
    private String roleNo;

    /**
     * 登录用户
     */
    private String loginName;
}
