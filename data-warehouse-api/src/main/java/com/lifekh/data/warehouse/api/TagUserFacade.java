package com.lifekh.data.warehouse.api;

import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.AddZoneTagReqDTO;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UserTagInfoGetReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserTagInfoRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.GroupContainUserRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserGetRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserInfoRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagUserFacade {

    PageInfoDTO<TagUserDTO> list(TagUserListReqDTO reqDTO) throws PendingException;

    /**
     * 根据操作员编号查询下用户标签
     *
     * @return
     * @throws PendingException
     */
    TagUserRespDTO queryByOperatorNo(TagUserQueryReqDTO reqDTO) throws PendingException;

    /**
     * 导入用户标签
     * @param reqDTO
     * @throws PendingException
     */
    void tagUserImport(TagUserImportReqDTO reqDTO) throws PendingException;

    /**
     * 删除用户标签
     * @param reqDTO
     * @throws PendingException
     */
    void tagUserDelete(TagUserDeleteReqDTO reqDTO) throws PendingException;

    /**
     * 根据标签查询用户总数(默认且逻辑)
     *
     * @param tagNos
     * @return
     * @throws PendingException
     */
    Long queryUserTotalByTag(List<String> tagNos) throws PendingException;

    /**
     * 查询标签用户数量
     */
    Long countTagUser(TagUserCountReqDTO reqDTO) throws PendingException;

    /**
     * 根据标签编号查询用户信息
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfo(TagUserQueryByTagsReqDTO reqDTO) throws PendingException;

    /**
     * 根据分群查询用户信息
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfoByTagGroup(UserTagGroupsReqDTO reqDTO) throws PendingException;

    /**
     * 查询传入操作员是否存在于传入的用户群中
     *
     * @param operatorNoAndGroupNoReqDTO
     * @return
     * @throws PendingException
     */
    GroupContainUserRespDTO checkUserOfGroup(OperatorNoAndGroupNoReqDTO operatorNoAndGroupNoReqDTO);

    /**
     * 新增地区标签
     *
     * @throws PendingException
     */
    void addZoneTag(AddZoneTagReqDTO reqDTO) throws PendingException;

    /**
     * 根据标签查询用户
     */
    List<TagUserGetRespDTO> queryUserByTag(TagUserGetReqDTO reqDTO) throws PendingException;

    /**
     * 获取用户标签信息
     */
    UserTagInfoRespDTO getUserTagInfo(UserTagInfoGetReqDTO reqDTO) throws PendingException;
}
