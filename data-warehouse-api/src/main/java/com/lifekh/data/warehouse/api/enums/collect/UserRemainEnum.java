package com.lifekh.data.warehouse.api.enums.collect;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserRemainEnum implements BaseEnum<UserRemainEnum, String> {
    ONE("oneDayRemainNum", "一日留存",-2),
    TWO("twoDayRemainNum", "二日留存",-3),
    THREE("threeDayRemainNum", "三日留存",-4),
    FOUR("fourDayRemainNum","四日留存",-5),
    FIVE("fiveDayRemainNum","五日留存",-6),
    SIX("sixDayRemainNum","六日留存",-7),
    SEVEN("sevenDayRemainNum","七日留存",-8),
    FIFTEEN("fifteenDayRemainNum","十五日留存",-16),
    THIRTY("thirtyDayRemainNum","三十日留存",-31)
    ;

    private String code;

    private String message;

    private Integer beforeDay;

    public static UserRemainEnum getByCode(String code) {
        for (UserRemainEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
