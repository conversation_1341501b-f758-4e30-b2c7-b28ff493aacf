package com.lifekh.data.warehouse.api.resp;

import com.lifekh.data.warehouse.api.req.PageReqDTO;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class TagDetailQueryRespDTO extends PageReqDTO {

    private static final long serialVersionUID = 2276392070059979630L;

    private Date createTime;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 标签规则
     */
    private List<RuleTagRuleReqDTO> rule;
}
