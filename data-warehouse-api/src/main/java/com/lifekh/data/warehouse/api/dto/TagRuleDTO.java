package com.lifekh.data.warehouse.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagRuleDTO implements Serializable {

    private String id;

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型：RuleTypeEnum
     */
    private String ruleType;

    /**
     * 规则的值
     */
    private String ruleValue;

    /**
     * 关联标签表
     */
    private List<TagInfoDTO> tagInfo;
}
