package com.lifekh.data.warehouse.api.collect.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/28 16:30
 * @Version 1.0
 **/
@Data
public class OtherDataCollectionReqDTO implements Serializable {
    private static final long serialVersionUID = 3812144201368591733L;
    /**
     * 事件分组：
     * 登录：login
     * 页面访问：viewPage
     * 下单：order
     */
    @JsonIgnore
    private String eventGroup;
    /**
     * 事件标识
     * 登陆：@login
     * 打开APP：@openApp
     * 页面访问：@viewPage
     * 点击：@click
     */
    private String event;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 事件名称
     * 登陆，打开APP，页面访问，下单
     */
    @JsonIgnore
    private String eventName;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 登陆号
     */
    private String loginName;

    /**
     * 语言
     */
    private String language;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * spm信息
     */
    private CollectSpmReqDTO spm;

    /**
     * 业务信息
     */
    private Map<String, Object> ext;

    /**
     * IP
     */
    @JsonIgnore
    private String ip;

    /**
     * 用户属性
     */
    private String appId;

    /**
     * 应用编号
     */
    private String appNo;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 版本号
     */
    private String appVersion;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型
     */
    private String deviceType;
}
