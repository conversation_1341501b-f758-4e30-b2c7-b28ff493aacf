package com.lifekh.data.warehouse.api.enums.collect;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CollectionEventEnum implements BaseEnum<CollectionEventEnum, String> {

    LOGIN("@login", "登陆"),
    OPEN_APP("@openApp", "打开APP"),
    VIEW_PAGE("@viewPage", "页面访问"),
    ORDER("@order","下单"),
    SESSION_START("@sessionStart","打开APP"),
    SESSION_END("@sessionEnd","关闭APP")
    ;

    private String code;

    private String message;

    public static CollectionEventEnum getByCode(String code) {
        for (CollectionEventEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
