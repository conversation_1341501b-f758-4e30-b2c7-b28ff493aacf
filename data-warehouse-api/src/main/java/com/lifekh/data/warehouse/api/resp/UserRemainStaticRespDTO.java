package com.lifekh.data.warehouse.api.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserRemainStaticRespDTO implements Serializable {
    private static final long serialVersionUID = 7500431776041746792L;
    /**
     * 日期
     */
    private String date;


    /**
     * 总人数
     */
    private Integer newUserNum;

    /**
     * 一日留存
     */
    private Integer oneDayRemainNum;

    /**
     * 一日留存率
     */
    private Double oneDayRemainRate;


    /**
     * 二日留存
     */
    private Integer twoDayRemainNum;

    /**
     * 二日留存率
     */
    private Double twoDayRemainRate;

    /**
     * 三日留存
     */
    private Integer threeDayRemainNum;

    /**
     * 三日留存率
     */
    private Double threeDayRemainRate;

    /**
     * 四日留存
     */
    private Integer fourDayRemainNum;

    /**
     * 四日留存率
     */
    private Double fourDayRemainRate;

    /**
     * 五日留存
     */
    private Integer fiveDayRemainNum;

    /**
     * 五日留存率
     */
    private Double fiveDayRemainRate;

    /**
     * 六日留存
     */
    private Integer sixDayRemainNum;

    /**
     * 六日留存率
     */
    private Double sixDayRemainRate;

    /**
     * 七日留存
     */
    private Integer sevenDayRemainNum;

    /**
     * 七日留存率
     */
    private Double sevenDayRemainRate;

    /**
     * 15日留存
     */
    private Integer fifteenDayRemainNum;

    /**
     * 15日留存率
     */
    private Double fifteenDayRemainRate;

    /**
     * 30日留存
     */
    private Integer thirtyDayRemainNum;

    /**
     * 30日留存率
     */
    private Double thirtyDayRemainRate;
}
