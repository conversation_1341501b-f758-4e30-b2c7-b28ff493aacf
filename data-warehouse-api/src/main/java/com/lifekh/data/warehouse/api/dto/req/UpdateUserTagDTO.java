package com.lifekh.data.warehouse.api.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateUserTagDTO implements Serializable {
    private static final long serialVersionUID = 4732509218541552271L;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 新标签编号
     */
    private String tagNo;

    /**
     * 操作封装
     */
    private String optType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 昵称
     */
    private String nickName;
}
