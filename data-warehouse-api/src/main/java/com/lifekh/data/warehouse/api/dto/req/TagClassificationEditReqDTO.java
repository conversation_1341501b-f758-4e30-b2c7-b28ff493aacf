package com.lifekh.data.warehouse.api.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TagClassificationEditReqDTO implements Serializable {
    private static final long serialVersionUID = -8699616268307335357L;
    /**
     * 一级分类编号
     */
    @NotNull
    private String firstTagClassificationNo;
    /**
     * 二级分类编号,编辑时传，新增时不传
     */
    private String secondTagClassificationNo;

    /**
     * 二级分类名称
     */
    private String secondaryClassificationName;
}
