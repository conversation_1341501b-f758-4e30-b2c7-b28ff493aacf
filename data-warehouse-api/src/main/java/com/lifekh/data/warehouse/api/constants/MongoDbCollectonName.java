package com.lifekh.data.warehouse.api.constants;

public interface MongoDbCollectonName {
    /**
     * 用户行为：登录，打开APP事件
     */
    String COLLECT_BEHAVIOR = "collect_behavior_v4";

    /**
     * 数据埋点-浏览页面
     */
    String COLLECT_BURIED_POINT_VIEW_PAGE = "collect_buried_point_viewPage_v5";

    /**
     * 数据埋点-点击
     */
    String COLLECT_BURIED_POINT_CLICK = "collect_buried_point_click_v4";

    /**
     * 数据埋点-其他
     */
    String COLLECT_BURIED_POINT_OTHER = "collect_buried_point_other_v4";

    /**
     * 埋点功能开关，用于临时表统计功能开关
     */
    String COLLECT_FUNCTION_SWITCH_CONFIG = "collect_function_switch_config";

    /**
     * 新增用户统计表
     */
    String NEW_USER_STATIC = "new_user_static";

    /**
     * 活跃用户统计表-日
     */
    String ACTIVE_USER_STATIC_DAY = "active_user_static_day";

    /**
     * 活跃用户统计表-周
     */
    String ACTIVE_USER_STATIC_WEEK = "active_user_static_week";

    /**
     * 活跃用户统计表-月
     */
    String ACTIVE_USER_STATIC_MONTH = "active_user_static_month";

    String SUP_MOBILE_TOKEN = "SUP_MOBILE_TOKEN";

    /**
     * 用户信息表
     */
    String USER_OPERATOR_INFO = "USER_OPERATOR_INFO";

    /**
     * 用户登录号表
     */
    String USER_OPERATOR_LOGIN_INFO = "USER_OPERATOR_LOGIN_INFO";

    /**
     * 用户留存统计表
     */
    String USER_REMAIN_STATIC = "user_remain_static";

    /**
     * 广告投放日统计表
     */
    String MERCHANT_ADS_PUBLISH_DAY = "merchant_ads_publish_day";

    /**
     * 发现页内容浏览(点击)表
     */
    String DISCOVERY_DETAIL_CLICK = "discovry_details_click";

    /**
     * 好物点击(点击，加购，下单)表
     */
    String GOODS_EVENT = "discovry_goods_click";

    /**
     * 发现页分享点击表
     */
    String DISCOVERY_SHARE_CLICK = "discovery_share_click";

    /**
     * 点击事件日统计
     */
    String REPORT_CLICK_DAY = "report_click_day";

    /**
     * 首页各节点转化率
     */
    String REPORT_CLICK_HOMEPAGE_NODE_RATE_DAY = "report_click_homepage_node_rate";

    /**
     * 点击事件日统计(语言)
     */
    String REPORT_CLICK_LANGUAGE_DAY = "report_click_language_day";

    /**
     * 点击事件周统计
     */
    String REPORT_CLICK_WEEK = "report_click_week";

    /**
     * 点击事件月统计
     */
    String REPORT_CLICK_MONTH = "report_click_month";

    /**
     * 点击事件日统计
     */
    String REPORT_CLICK_TWO_LAYER_DAY = "report_click_two_layer_day";

    /**
     * 点击事件周统计
     */
    String REPORT_CLICK_TWO_LAYER_WEEK = "report_click_two_layer_week";

    /**
     * 点击事件月统计
     */
    String REPORT_CLICK_TWO_LAYER_MONTH = "report_click_two_layer_month";

    /**
     * 邀请拉新明细
     */
    String INVITE_REGISTER_RECORD = "invite_register_record";

    /**
     * 邀请注册事件日统计
     */
    String REPORT_INVITE_REGISTER_DAY = "report_invite_register_day";

    /**
     * 邀请注册事件日统计
     */
    String REPORT_INVITE_REGISTER_SUMMERY = "report_invite_register_summary";

    /**
     * 邀请注册但未打开APP的用户记录
     */
    String INVITE_REGISTER_BUT_NOT_OPEN_APP = "invite_register_but_not_open_app";

    /**
     * 设备池
     */
    String DEVICE_POOL = "device_pool";

    /**
     * 邀请注册但未打开APP的用户记录,带活动编号
     */
    String REPORT_INVITE_REGISTER_ACTIVITY_DAY = "report_invite_register_activity_day";


    /**
     * 邀请注册事件日统计,带活动编号
     */
    String REPORT_INVITE_REGISTER_ACTIVITY_SUMMERY = "report_invite_register_activity_summary";

    /**
     * 数据看板-实时拉新报表
     */
    String REPORT_INVITE_NEW_USER = "report_invite_new_user";

    /**
     * 数据看板-邀请有礼报表
     */
    String REPORT_ACTIVITY_INVITE = "report_activity_invite";

    /**
     * 每日渠道维度相关数据统计
     */
    String REPORT_CHANNEL_DAY = "report_channel_day";

    /**
     * 指标统计
     */
    String REPORT_TARGET = "report_target";

    /**
     * 用户标签表
     */
    String USER_LABEL = "USER_LABEL";

    /**
     * 订单表
     */
    String AGGREGATE_ORDER = "AGGREGATE_ORDER";

    /**
     * 每日地区维度相关数据统计
     */
    String REPORT_LOCATION_DAY = "report_location_day";

    /**
     * 每日语言维度相关数据统计
     */
    String REPORT_LANGUAGE_DAY = "report_language_day";
    /**
     * 业务线日活报表
     */
    String REPORT_BUSINESS_ACTIVE_USER = "report_business_active_user";
    /**
     * 业务线各语言日活报表
     */
    String REPORT_BUSINESS_LANGUAGE_ACTIVE_USER = "report_business_language_active_user";

    /**
     * 业务线活跃埋点表
     */
    String COLLECT_BURIED_POINT_BUSINESS_ACTIVE = "collect_buried_point_business_active_v2";

    /**
     * 发现页事件表
     */
    String DISCOVRY_EVENT_RECORD = "discovry_event_record";

    /**
     * 设备记录临时表
     */
    String DEVICE_EVENT_RECORD = "device_event_record";

    /**
     * 每日设备新增数统计
     */
    String DEVICE_NEW_STATIC_DAY = "device_new_static_day";

    /**
     * 事件类型
     */
    String EVENT_TYPE = "event_type";

    /**
     * 运营活动埋点表
     */
    String COLLECT_BURIED_POINT_ACTIVITY = "collect_buried_point_activity";

    /**
     * 用户行为信息
     */
    String DWD_USER_BEHAVIOR_INFO = "dwd_user_behavior_info";

    /**
     * 聚合订单dwd
     */
    String DWD_AGGREGATE_ORDER = "dwd_aggregate_order";

    /**
     * 外卖门店页浏览
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_STORE_DETAIL_PV = "collect_buried_point_takeaway_store_detail_pv";

    /**
     * 开屏广告统计
     */
    String OPEN_SCREEN_STATIC = "open_screen_static";

    /**
     * 弹窗统计
     */
    String POP_ADS_STATIC = "pop_ads_static";

    /**
     * banner点击数统计
     */
    String BANNER_CLICK_STATIC = "banner_click_static";

    /**
     * 订单事件埋点表
     */
    String COLLECT_BURIED_POINT_ORDER = "collect_buried_point_order";

    String COLLECT_BURIED_POINT_ORDER_NEW = "collect_buried_point_takeaway_order_submit";

    /**
     * 发现页事件埋点表
     */
    String COLLECT_BURIED_POINT_DISCOVRY = "collect_buried_point_discovry";

    /**
     * 广告浏览记录
     */
    String ADS_VIEW_LOG = "ads_view_log";

    /**
     * 埋点汇总
     */
    String COLLECT_BURIED_POINT_ALL = "collect_buried_point_all";

    /**
     * 首页点击报表
     */
    String REPORT_HOME_PAGE_CLICK = "report_home_page_click";

    /**
     * 聚合订单报表
     */
    String REPORT_AGGREGATE_ORDER = "report_aggregate_order";

    /**
     * 聚合订单报表--按天
     */
    String REPORT_AGGREGATE_ORDER_LANGUAGE_OF_DAY = "report_aggregate_order_language_of_day";

    /**
     * 新用户下单报表
     */
    String REPORT_AGGREGATE_ORDER_LANGUAGE_NEW_USER = "report_aggregate_order_language_new_user";

    /**
     * 运营周报
     */
    String REPORT_OPERATIONAL_WEEKLY_REPORT = "report_operational_weekly_report";

    /**
     * 事件页面
     */
    String EVENT_PAGE = "event_page";

    /**
     * 首页流量报表
     */
    String REPORT_HOME_PAGE_VIEW = "report_home_page_view";

    /**
     * 首页实时在线流量报表
     */
    String REPORT_HOME_PAGE_ONLINE_USER= "report_home_page_online_user";

    /**
     * 页面浏览报表
     */
    String REPORT_PAGE_VIEW = "report_page_view";

    /**
     * 短信业务统计
     */
    String REPORT_SMS_BIZ_COUNT = "report_sms_biz_count";

    /**
     * 短信验证率
     */
    String REPORT_SMS_VERIFY_RATE = "report_sms_verify_rate";

    /**
     * 首页搜索埋点记录
     */
    String COLLECT_BURIED_POINT_SEARCH = "collect_buried_point_search";

    /**
     * WOWNOW首页埋点临时表
     */
    String COLLECT_BURIED_POINT_HOME_PAGE_TEMP = "collect_buried_point_home_page_temp";

    /**
     * 外卖首页埋点临时表
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP = "collect_buried_point_takeaway_home_page_temp";

    /**
     * 首页专题搜索报表
     */
    String REPORT_THEMATIC_SEARCH = "report_thematic_search";

    /**
     * 首页热词搜索报表
     */
    String REPORT_HOTWORD_SEARCH = "report_hotword_search";

    /**
     * 短信发发送渠道验证率报表
     */
    String REPORT_SMS_CHANNEL_RATE = "report_sms_channel_rate";

    /**
     * 首页流量转化报表
     */
    String REPORT_HOME_FLOW_CONVERSION = "report_home_flow_conversion";

    /**
     * 浏览埋点临时表
     */
    String VIEW_EVENT_RECORD = "view_event_record";

    /**
     * 业务线浏览报表
     */
    String REPORT_BUSINESS_PAGE_VIEW = "report_business_page_view";

    /**
     * 新设备转化率报表
     */
    String REPORT_NEW_DEVICE_CONVERSION_RATE = "report_new_device_conversion_rate";

    /**
     * 操作员登录方式
     */
    String REPORT_OPERATOR_LOGIN_METHOD = "report_operator_login_method";

    /**
     * 操作员注册方式
     */
    String REPORT_OPERATOR_REGISTER_METHOD = "report_operator_register_method";

    /**
     * 悬浮窗统计报表
     */
    String REPORT_FLOAT_WINDOW = "report_float_window";

    /**
     * 发现频道报表
     */
    String REPORT_DISCOVERY = "report_discovery";

    /**
     * 首页下单
     */
    String REPORT_HOME_PAGE_ORDER = "report_home_page_order";

    /**
     * 短链报表
     */
    String REPORT_SHORT_LINK = "report_short_link";

    /**
     * 首页推荐报表
     */
    String REPORT_HOME_PAGE_RECOMMEND = "report_home_page_recommend";

    /**
     * 首页Banner报表
     */
    String REPORT_HOME_PAGE_BANNER = "report_home_page_banner";

    /**
     * 外卖门店订单转化率报表
     */
    String REPORT_YUMNOW_STORE_ORDER_CONVERSION_RATE = "report_yumnow_store_order_conversion_rate";

    /**
     * 外卖首页订单转化率报表
     */
    String REPORT_TAKEAWAY_FLOW_CONVERSION = "report_takeaway_flow_conversion";

    /**
     * 外卖首页订单转化率报表
     */
    String REPORT_TAKEAWAY_KING_KONG_FLOW_CONVERSION = "report_takeaway_king_kong_flow_conversion";

    /**
     * 外卖首页Banner订单转化率报表
     */
    String REPORT_TAKEAWAY_BANNER_FLOW_CONVERSION = "report_takeaway_banner_flow_conversion";

    /**
     * 外卖首页瀑布流订单转化率报表
     */
    String REPORT_TAKEAWAY_WATERFALL_FLOW_CONVERSION = "report_takeaway_waterfall_flow_conversion";

    /**
     * 外卖首页EOT订单转化率报表
     */
    String REPORT_TAKEAWAY_EOT_FLOW_CONVERSION = "report_takeaway_eot_flow_conversion";

    /**
     * 外卖首页广告区订单转化率报表
     */
    String REPORT_TAKEAWAY_AD_FLOW_CONVERSION = "report_takeaway_ad_flow_conversion";

    /**
     * 外卖首页主题区订单转化率报表
     */
    String REPORT_TAKEAWAY_THEME_FLOW_CONVERSION = "report_takeaway_theme_flow_conversion";

    /**
     * 外卖首页PV
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_PV = "collect_buried_point_takeaway_home_page_pv";

    /**
     * 外卖首页专题页PV
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_TOPIC_PAGE_PV = "collect_buried_point_takeaway_topic_page_pv";

    /**
     * 外卖首页门店分类列表PV
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_STORE_LIST_PV = "collect_buried_point_takeaway_store_list_pv";

    /**
     * 外卖支付页PV
     */
    String COLLECT_BURIED_POINT_TAKEAWAY_PAYMENT_PV = "collect_buried_point_takeaway_payment_pv";


    String COLLECT_BURIED_POINT_TAKEAWAY_OTHER_V2 = "collect_buried_point_takeaway_other_v2";


    String REPORT_TAKEAWAY_HOME_EXPOSURE_STATISTICS = "report_takeaway_home_exposure_statistics";

    String COLLECT_BURIED_POINT_TAKEAWAY_VIEW_V2 = "collect_buried_point_takeaway_view_v2";
}
