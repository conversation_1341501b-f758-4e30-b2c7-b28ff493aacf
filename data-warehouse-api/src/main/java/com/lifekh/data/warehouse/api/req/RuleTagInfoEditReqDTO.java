package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class RuleTagInfoEditReqDTO implements Serializable {

    /**
     * 标签编号
     */
    private String tagNo;
    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 标签规则编号
     */
    private List<RuleTagRuleReqDTO> rule;
}
