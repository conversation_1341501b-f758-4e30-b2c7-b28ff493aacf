package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class SimpleTagInfoRespDTO implements Serializable {
    private static final long serialVersionUID = 2547697028967043396L;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagCatalogueEnum
     */
    private String tagCatalogue;

}
