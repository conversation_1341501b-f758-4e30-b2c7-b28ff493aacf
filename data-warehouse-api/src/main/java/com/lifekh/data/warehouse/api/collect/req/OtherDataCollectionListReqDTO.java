package com.lifekh.data.warehouse.api.collect.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/28 16:30
 * @Version 1.0
 **/
@Data
public class OtherDataCollectionListReqDTO implements Serializable {
    private static final long serialVersionUID = 6643340159883814001L;
    /**
     * 登陆号
     */
    private String loginName;

    private List<StandardDataCollectionReqDTO> otherCollectionDatas;
}
