package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ShortLinkReportQueryReqDTO extends PageReqDTO implements Serializable {
    private static final long serialVersionUID = 6440139074769501620L;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 短链id
     */
    private String shortId;
}
