package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class RuleTagInfoEditReqV2DTO implements Serializable {

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 业务所属 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 一级分类名称
     */
    private String firstClassification;

    /**
     * 一级分类id
     */
    private String firstClassificationNo;

    /**
     * 二级分类名称英文-中文
     */
    private String secondaryClassification;

    /**
     * 二级分类名称-英文
     */
    private String secondaryClassificationEn;

    /**
     * 二级分类名称-柬文
     */
    private String secondaryClassificationKm;

    /**
     * 二级分类id
     */
    private String secondaryClassificationNo;

    /**
     * 标签状态 枚举 TagStatusEnum：默认传open
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 时效性（true：实时, false:定时）默认传false
     */
    private Boolean timeliness;

    /**
     * 标签规则编号
     */
    private List<RuleTagRuleReqV2DTO> rule;

    /**
     * 编辑人
     */
    private String updateBy;

    /**
     * 可见范围，枚举：TagScopeEnum
     */
    private String tagScope;
}
