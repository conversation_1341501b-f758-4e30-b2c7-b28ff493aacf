package com.lifekh.data.warehouse.api.dto.resp;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/26 17:20
 * @Version 1.0
 **/
@Data
public class UserAttributesDetailRespDTO implements Serializable {
    private static final long serialVersionUID = -4540088244596017290L;
    /**
     * 规则编号
     */
    private String ruleNo;
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 特殊规则
     */
    private List<RuleDTO> specialRules;
}
