package com.lifekh.data.warehouse.api.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum OptTypeEnum implements BaseEnum<OptTypeEnum, String> {
    /**
     * 新增
     */
    ADD("ADD", "新增"),
    /**
     * 删除
     */
    DEL("DEL", "删除");

    private String code;

    private String message;

    public static OptTypeEnum getByCode(String code) {
        for (OptTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static List<OptTypeEnum> getByCodeList(List<String> codeList) {
        if (codeList == null || codeList.size() <= 0) {
            return new ArrayList<>();
        }
        List<OptTypeEnum> list = new ArrayList<>();
        codeList.forEach(c -> list.add(getByCode(c)));
        return list;
    }

    public static List<String> getCodeListByEnum(OptTypeEnum... type) {
        if (type == null) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        for (OptTypeEnum aType : type) {
            list.add(aType.code);
        }
        return list;
    }
}
