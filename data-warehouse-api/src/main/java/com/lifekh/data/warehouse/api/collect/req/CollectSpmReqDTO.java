package com.lifekh.data.warehouse.api.collect.req;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollectSpmReqDTO  implements Serializable {
    private static final long serialVersionUID = 8255374147334966103L;
    /**
     * 应用编号
     */
    @JsonIgnore
    private String appNo;
    /**
     * 子页
     */
    private String childPage;

    /**
     * 上级页面
     */
    private String parentPage;

    /**
     * 当前页面
     */
    private String currentPage;

    /**
     * 当前区域
     */
    private String currentArea;

    /**
     * 节点
     */
    private String node;

    /**
     * 停留时间
     */
    private Long stayTime;
}
