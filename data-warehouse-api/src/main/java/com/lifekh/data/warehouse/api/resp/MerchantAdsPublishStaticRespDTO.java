package com.lifekh.data.warehouse.api.resp;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

@Data
public class MerchantAdsPublishStaticRespDTO implements Serializable {
    private static final long serialVersionUID = -1860876477164693985L;
    @Id
    private String id;
    /**
     * 统计数据日期
     */
    private Date staticDate;

    /**
     * 投放编号
     */
    private String nodePublishNo;

    /**
     * 广告日点击次数
     */
    private Integer  adsTodayClickNum;

    /**
     * 广告日点击用户数
     */
    private Integer  adsTodayClickUserNum;
}
