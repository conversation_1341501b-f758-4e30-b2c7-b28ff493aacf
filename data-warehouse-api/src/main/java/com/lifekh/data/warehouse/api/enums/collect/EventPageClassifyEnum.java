package com.lifekh.data.warehouse.api.enums.collect;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventPageClassifyEnum implements BaseEnum<EventPageClassifyEnum, String> {
    HOME("home", "首页"),
    DISCOVERY("discovery", "发现"),
    ORDER("order", "订单"),
    MESSAGE("message", "消息"),
    MINE("mine", "我的"),
    OTHER("other", "其他");

    private String code;
    private String message;

    public static EventPageClassifyEnum getByCode(String code) {
        for (EventPageClassifyEnum e : EventPageClassifyEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
