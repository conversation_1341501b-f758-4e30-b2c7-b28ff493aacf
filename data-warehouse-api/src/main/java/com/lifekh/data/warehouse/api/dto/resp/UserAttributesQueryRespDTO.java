package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/26 17:20
 * @Version 1.0
 **/
@Data
public class UserAttributesQueryRespDTO implements Serializable {
    private static final long serialVersionUID = -2157355046149162241L;
    /**
     * 规则类型名称
     */
    private String ruleTypeName;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则详情列表
     */
    private List<UserAttributesDetailRespDTO> ruleDetailList;
}
