package com.lifekh.data.warehouse.api.req.discovery;

import com.lifekh.data.warehouse.api.req.PageReqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AdsViewLogSearchReqDTO extends PageReqDTO implements Serializable {
    private static final long serialVersionUID = 3243688552370773165L;

    /**
     * 广告编号
     */
    private String adsNo;

    /**
     * 广告主体
     */
    private String adsSubject;

    /**
     * 广告类型
     */
    private String adsType;

    /**
     * 广告名称
     */
    private String adsName;

    /**
     * 广告标题
     */
    private String adsTitle;

    /**
     * 应用场景
     */
    private String useScene;

    /**
     * 语言
     */
    private String language;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 点击开始时间
     */
    private Date startTime;

    /**
     * 点击结束时间
     */
    private Date endTime;

    /**
     * 操作员编号
     */
    private String operatorNo;
}
