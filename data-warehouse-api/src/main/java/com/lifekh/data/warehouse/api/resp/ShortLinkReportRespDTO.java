package com.lifekh.data.warehouse.api.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class ShortLinkReportRespDTO implements Serializable {
    private static final long serialVersionUID = -3949930822033331939L;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 短链id
     */
    private String shortId;

    /**
     * 短链浏览数
     */
    private Long pv;

    /**
     * 打开APP次数
     */
    private Long openAppCount;

    /**
     * 首次打开APP次数
     */
    private Long firstOpenAppCount;

    /**
     * 注册用户数
     */
    private Long registerCount;

    /**
     * 用户下单数
     */
    private Long orderCount;

    /**
     * 打开APP转化率
     */
    private Double openAppRate;

    /**
     * 注册转化率
     */
    private Double registerRate;

    /**
     * 下单转化率
     */
    private Double orderRate;

}
