package com.lifekh.data.warehouse.api.req.discovery;

import lombok.Data;

import java.io.Serializable;

@Data
public class DiscoveryBehaviorCountReqDTO implements Serializable {
    private static final long serialVersionUID = -7434647913222712582L;

    /**
     * 语言
     */
    private String language;

    /**
     * 统计天数
     */
    private Integer days;

    /**
     * 栏目编号
     */
    private String categoryNo;
}
