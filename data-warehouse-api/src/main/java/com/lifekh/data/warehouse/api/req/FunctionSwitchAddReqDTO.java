package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class FunctionSwitchAddReqDTO implements Serializable {
    private static final long serialVersionUID = 6393007289666242287L;


    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 功能名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 备注
     */
    private String remark;
}
