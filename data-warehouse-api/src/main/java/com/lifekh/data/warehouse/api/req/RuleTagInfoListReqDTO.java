package com.lifekh.data.warehouse.api.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class RuleTagInfoListReqDTO extends PageReqDTO implements Serializable {

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagCatalogueEnum
     */
    private String tagCatalogue;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 可见范围，枚举：TagScopeEnum
     */
    private String tagScope;

    /**
     * 是否生效，枚举：TagExecStatusEnum
     */
    private String execStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 操作人
     */
    private String updateBy;

    /**
     * 当前登录人
     */
    private String loginName;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 角色编号
     */
    private String roleNo;
}
