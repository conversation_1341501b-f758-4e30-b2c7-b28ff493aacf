package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagInfoFirstClassificationRespDTO implements Serializable {
    private static final long serialVersionUID = -8312506429422618038L;

    /**
     * 一级分类编号
     */
    private String firstTagClassificationNo;
    /**
     * 一级分类名称
     */
    private String firstTagClassificationName;
    /**
     * 二级下标签数量
     */
    private Integer size;

    /**
     * 二级标签分类
     */
    private List<TagInfoSeconfClassificationRespDTO> tagInfoSeconfClassifications;
}
