package com.lifekh.data.warehouse.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntranceType {

    BANNER("banner", "首页BANNER"),
    EOT("eot", "按时吃饭"),
    WATERFALL("waterfall", "瀑布流"),
    KKD("kkd", "金刚区"),
    SEARCH("search", "搜索"),
    ORDER_LIST("order_list", "订单列表"),
    AD("ad", "广告"),
    PRODUCT_THEME("product_theme", "商品专题"),
    STORE_THEME("store_theme", "门店专题"),
    BRAND_THEME("brand_theme", "品牌专题"),
    ;

    private String code;

    private String message;
}
