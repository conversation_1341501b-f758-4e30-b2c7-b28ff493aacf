package com.lifekh.data.warehouse.api.dto.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class TagInfoClassificationRespDTO implements Serializable {
    private static final long serialVersionUID = -6914087135727080728L;
    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签分类业务线：公共：public，外卖：YumNow，电商：TinhNow
     */
    private String tagClassify;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagCatalogueEnum
     */
    private String tagCatalogue;
}
