package com.lifekh.data.warehouse.api.req.discovery;

import com.lifekh.data.warehouse.api.req.PageReqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SearchDiscoveryGoodsClickReqDTO extends PageReqDTO implements Serializable {
    private static final long serialVersionUID = -5897322765468057439L;
    /**
     * 内容编号
     */
    private String contentNo;

    /**
     * 内容所属业务线
     */
    private String contentBusinessLine;

    /**
     * 内容类别, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private String contentCategory;

    /**
     * 内容类型, 枚举：com.chaos.discovery.review.api.enums.ContentTypeEnum
     */
    private String contentType;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 用户
     */
    private String loginName;

    /**
     * 内容语言
     */
    private String contentLanguage;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 开始时间
     */
    private Date endTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 事件类型：discovry_goods_click（好物点击）、add_shopcart（好物加购）、order_submit（好物下单）
     */
    private String eventType;

    /**
     * 内容创建开始时间
     */
    private Date contentCreateStartTime;

    /**
     * 内容创建结束时间
     */
    private Date contentCreateEndTime;
}
