package com.lifekh.data.warehouse.config.route;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DataBaseContextHolder {

    private static final ThreadLocal<DataSourceType> contextHolder = new ThreadLocal<>();

    /**
     * 向当前线程里添加数据源类型
     * @param type
     */
    public static void setDataSourceType(DataSourceType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        log.debug("获取当前数据源的类型为：{}",type);
        contextHolder.set(type);
    }

    /**
     * 获取数据源类型
     * @return
     */
    public static DataSourceType getDataSourceType() {

        DataSourceType type = contextHolder.get();
        if (type == null) {
            // 如果当前数据源类型为空，取默认数据源
            type = DataSourceType.LIFEKH_MP_CUSTOMER;
        }
       log.debug("获取当前数据源的类型为：{}",type);
        return type;
    }

    /**
     * 清空数据源类型
     */
    public static void clearDataSourceType() {
        contextHolder.remove();
    }

}
