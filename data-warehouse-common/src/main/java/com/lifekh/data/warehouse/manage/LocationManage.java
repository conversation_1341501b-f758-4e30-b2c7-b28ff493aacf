package com.lifekh.data.warehouse.manage;

import cn.hutool.json.JSONUtil;
import com.lifekh.data.warehouse.utils.GFGUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.w3c.dom.*;

import javax.annotation.PostConstruct;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 本地地区经纬度围栏
 */
@Component
@Slf4j
public class LocationManage {

    private Map<String, List<List<GFGUtil.Point>>> geoMap;

    /**
     * 默认开启本地经纬度匹配
     */
    @Value("${long.lat.match.open:true}")
    private boolean isLocationOpen;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    /**
     * 单位分钟，默认48小时
     */
    @Value("${zone.cache.time:2880}")
    private Integer zoneCacheTime;

    @PostConstruct
    public void loadConfig() {
        try {
            geoMap = loadPoint();
            log.info("总共加载地区经纬度围栏个数为:{}", geoMap.size());
        } catch (Exception e) {
            log.error("地区经纬度围栏加载失败", e);
        }
    }

    private Map<String, List<List<GFGUtil.Point>>> loadPoint() throws Exception {
        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("zone.xml");

        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        Document doc = dBuilder.parse(inputFile);

        NodeList childNodes = doc.getChildNodes();
        Element folder = (Element) childNodes.item(0);
        NodeList placemarks = folder.getElementsByTagName("Placemark");


        int length = placemarks.getLength();
        Map<String, List<List<GFGUtil.Point>>> geoMap = new HashMap<>();
        for (int i = 0; i < length; i++) {

            Element curPlacemark = (Element) placemarks.item(i);
            NodeList simpleData = curPlacemark.getElementsByTagName("SimpleData");
            List<String> names = new ArrayList<>();
            List<String> attrNames = Arrays.asList("ADM2_EN", "ADM1_EN", "ADM0_EN");
            for (int k = 0; k < simpleData.getLength(); k++) {
                Node item = simpleData.item(k);
                NamedNodeMap attributes = item.getAttributes();
                String name = attributes.getNamedItem("name").getNodeValue();
                if (attrNames.contains(name)) {
                    names.add(item.getTextContent());
                }
            }

            NodeList coordinates = curPlacemark.getElementsByTagName("coordinates");

            int length1 = coordinates.getLength();
            List<List<GFGUtil.Point>> polygons = new ArrayList<>();
            for (int j = 0; j < length1; j++) {

                Node coordinate = coordinates.item(j);
                String textContent = coordinate.getTextContent();
                List<GFGUtil.Point> polygon = Stream.of(textContent.split(" "))
                        .map(x -> {
                            String trim = x.trim();
                            if (trim.length() == 0) {
                                return null;
                            }
                            String[] values = x.split(",");
                            return new GFGUtil.Point(Double.valueOf(values[1]), Double.valueOf(values[0]));
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                polygons.add(polygon);
            }

            geoMap.put(String.join(",", names), polygons);
        }
        return geoMap;
    }

    /**
     * 构造redis缓存key
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @return String
     */
    private String getKey(BigDecimal latitude, BigDecimal longitude) {
        return "data_warehouse:zone:" + latitude.toString() + "_" + longitude.toString();
    }

    /**
     * 根据经纬度查询地区名称
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return LocationDTO
     */
    public LocationDTO getNameByPosition(String longitude, String latitude) {
        //默认开启本地经纬度匹配
        if (!isLocationOpen) {
            return null;
        }
        //判断经纬度是否为空
        if (StringUtils.isBlank(latitude) || StringUtils.isBlank(longitude)) {
            return null;
        }

        //先查询缓存,缓存没有再进行匹配
        String key = getKey(new BigDecimal(latitude), new BigDecimal(longitude));
        LocationDTO locationDTO = getByCache(key);
        if (Objects.nonNull(locationDTO)) {
            log.debug("命中缓存, key:{}, LocationDTO:{}", key, locationDTO);
            return locationDTO;
        }

        GFGUtil.Point point = new GFGUtil.Point(new BigDecimal(latitude).doubleValue(), new BigDecimal(longitude).doubleValue());
        if (Objects.nonNull(geoMap) && geoMap.size() > 0) {
            String nameStr = geoMap.entrySet()
                    .stream()
                    .filter(entry -> {
                        return entry.getValue().stream()
                                .anyMatch(points -> GFGUtil.isInside(points.toArray(new GFGUtil.Point[0]), point));
                    })
                    .findFirst()
                    .map(Map.Entry::getKey).orElse("");

            //拆分国家省市区 nameArr[2] 国家, nameArr[1] 省/市, nameArr[0] 区
            if (StringUtils.isNotBlank(nameStr)) {
                log.debug("命中本地经纬度匹配, key:{}, LocationDTO:{}", key, nameStr);
                String[] nameArr = nameStr.split(",");
                LocationDTO location = new LocationDTO(nameArr[2], nameArr[1], nameArr[0]);
                //更新缓存
                putCahe(key, location);
                return location;
            }
        }
        return null;
    }

    /**
     * 查询缓存
     * @param key
     * @return
     */
    private LocationDTO getByCache(String key) {
        String value = null;
        try {
            value = redisTemplate.opsForValue().get(key);

            if(StringUtils.isNotBlank(value)) {
                //log.info("经纬度查询缓命中存key:{}", key);
                return JSONUtil.toBean(value, LocationDTO.class);
            }
        } catch (Exception e) {
            log.warn("经纬度查询缓存失败, key:{}, value:{}", key, value, e);
        }
        return null;
    }

    /**
     * 添加缓存
     *
     * @param key
     * @param location
     */
    private void putCahe(String key, LocationDTO location) {
        try {
            redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(location), zoneCacheTime, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("缓存添加失败, key:{}, value:{}", key, location, e);
        }
    }
}
