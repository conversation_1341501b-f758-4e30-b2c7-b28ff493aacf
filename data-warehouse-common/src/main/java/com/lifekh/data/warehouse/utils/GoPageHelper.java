package com.lifekh.data.warehouse.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class GoPageHelper {

    public static <T> PageInfoDTO<T> createPageInfoDTO(List<T> list) {
        PageInfoDTO<T> pageInfoDTO = new PageInfoDTO<>();
        BeanCopierHelper.copyProperties(new PageInfo<>(list), pageInfoDTO);
        return pageInfoDTO;
    }

    public static <R, U> PageInfoDTO<R> createPageInfoDTO(Page<U> page, Function<U, R> map) {
        List<R> result = page.stream().map(map).collect(Collectors.toList());
        PageInfoDTO<R> resultPage = new PageInfoDTO<>();
        resultPage.setPageSize(page.getPageSize());
        resultPage.setPageNum(page.getPageNum());
        resultPage.setTotal(page.getTotal());
        resultPage.setList(result);
        return resultPage;
    }
}
