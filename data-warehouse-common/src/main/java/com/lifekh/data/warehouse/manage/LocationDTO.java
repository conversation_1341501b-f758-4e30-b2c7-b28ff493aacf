package com.lifekh.data.warehouse.manage;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LocationDTO {

    private String latitude;

    private String longitude;

    /**
     * 国家
     */
    String countryName;

    /**
     * 省/市
     */
    String provinceName;

    /**
     * 区
     */
    String distinctName;

    public LocationDTO(){}

    public LocationDTO(String countryName, String provinceName, String distinctName) {
        this.countryName = countryName;
        this.provinceName = provinceName;
        this.distinctName = distinctName;
    }
}
