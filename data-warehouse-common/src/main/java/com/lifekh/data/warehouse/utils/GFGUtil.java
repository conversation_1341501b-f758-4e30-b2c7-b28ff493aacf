package com.lifekh.data.warehouse.utils;

public class GFGUtil {
    static int INF = 10000;

    public GFGUtil() {
    }

    static boolean onSegment(Point p, Point q, Point r) {
        return q.lat <= Math.max(p.lat, r.lat) && q.lat >= Math.min(p.lat, r.lat) && q.lon <= Math.max(p.lon, r.lon) && q.lon >= Math.min(p.lon, r.lon);
    }

    static int orientation(Point p, Point q, Point r) {
        double val = (q.lon - p.lon) * (r.lat - q.lat) - (q.lat - p.lat) * (r.lon - q.lon);
        if (val == 0.0D) {
            return 0;
        } else {
            return val > 0.0D ? 1 : 2;
        }
    }

    static boolean doIntersect(Point p1, Point q1, Point p2, Point q2) {
        int o1 = orientation(p1, q1, p2);
        int o2 = orientation(p1, q1, q2);
        int o3 = orientation(p2, q2, p1);
        int o4 = orientation(p2, q2, q1);
        if (o1 != o2 && o3 != o4) {
            return true;
        } else if (o1 == 0 && onSegment(p1, p2, q1)) {
            return true;
        } else if (o2 == 0 && onSegment(p1, q2, q1)) {
            return true;
        } else if (o3 == 0 && onSegment(p2, p1, q2)) {
            return true;
        } else {
            return o4 == 0 && onSegment(p2, q1, q2);
        }
    }

    public static boolean isInside(Point[] polygon, Point p) {
        int n = polygon.length;
        if (n < 3) {
            return false;
        } else {
            Point extreme = new Point((double) INF, p.lon);
            int count = 0;
            int i = 0;

            int next;
            do {
                next = (i + 1) % n;
                if (doIntersect(polygon[i], polygon[next], p, extreme)) {
                    if (orientation(polygon[i], p, polygon[next]) == 0) {
                        return onSegment(polygon[i], p, polygon[next]);
                    }

                    ++count;
                }

                i = next;
            } while (next != 0);

            return count % 2 == 1;
        }
    }


    public static class Point {
        double lat;
        double lon;

        public Point(double lat, double lon) {
            this.lat = lat;
            this.lon = lon;
        }

        public double getLat() {
            return lat;
        }

        public double getLon() {
            return lon;
        }

        @Override
        public String toString() {
            return String.format("%s,%s", lat, lon);
        }
    }
}

