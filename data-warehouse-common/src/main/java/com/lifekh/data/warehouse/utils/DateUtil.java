package com.lifekh.data.warehouse.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 日期
 */
//@Slf4j
public class DateUtil {

    /***
     * yyyy必须小写
     */
    private static final String YEAR_MONTH_PATTERN = "yyyyMM";

    private static final String YEAR_MONTH_DAY_PATTERN = "yyyyMMdd";


    private static final String DATE_PATTERN="yyyy-MM-dd";

    private static final String DATE_PATTERN_2="yyyy-MM-dd HH:mm:ss";

    private static final String DATE_PATTERN_3="dd/MM/yyyy";

    private static final String DAY_START_PATTERN=" 00:00:00";

    private static final String DAY_START_END_PATTERN=" 23:59:59";

    private DateUtil() {
    }

    /**
     * 根据日期获取当前年和月份，例如：202105
     *
     * @param date 日期
     * @return Long
     */
    public static Long getYearAndMonthLong(Date date) {
        return Long.valueOf(DateFormatUtils.format(date, YEAR_MONTH_PATTERN));
    }

    /**
     * 根据日期获取当前年和月份，例如：202105
     *
     * @param date 日期
     * @return Long
     */
    public static Long getYearAndMonthAndDayLong(Date date) {
        return Long.valueOf(DateFormatUtils.format(date, YEAR_MONTH_DAY_PATTERN));
    }

    /**
     *
     * 当前加/减天的日期
     *
     * @param date   当前时间
     * @param field  Calendar.year 、mouth等等
     * @param amount 时间数值
     * @return
     */
    public static Date dateCalculation(Date date, int field, int amount) {
        //获取时间加一年或加一月或加一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.add(field, amount);
        return cal.getTime();
    }

    /**
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static String getDayStr(Date date, int field, int amount) {
        Date date1 = dateCalculation(date, field, amount);
        try {
            return DateFormatUtils.format(date1, DATE_PATTERN);
        } catch (Exception e) {
//            log.info("日期转换异常", e);
        }
        return "";
    }
    /**
     *将传入日期转换为字符串
     * @param date
     * @return
     */
    public static String dataToString(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        return sdf.format(date);
    }


    /**
     *增加指定天数后返回
     * @param date
     * @return
     */
    public static String dateAddToString(Date date,Integer day){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);
        dateAddOneDay(date,day);
        return sdf.format(date);
    }

    /**
     *指定日期—+1天
     * @param date
     * @return
     */
    public static Date dateAddOneDay(Date date,Integer day){
        Calendar   calendar   =   new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(calendar.DATE,day);
        return calendar.getTime();
    }

    // 获得当前日期与本周一相差的天数
    public static  int getMondayPlus() {
        Calendar cd = Calendar.getInstance();
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1) {
            return 6;
        } else {
            return dayOfWeek - 2;
        }
    }

    /**
     * 获取今日开始时间
     * @return
     */
    public static Date getTodayStartDate(){
        Calendar currentDate = new GregorianCalendar();
        currentDate.set(Calendar.HOUR_OF_DAY, 0);
        currentDate.set(Calendar.MINUTE, 0);
        currentDate.set(Calendar.SECOND, 0);
        return currentDate.getTime();
    }

    /**
     * 获取上个月开始时间
     * @return
     */
    public static Date getLastMonthStartTime(){
        Long currentTime = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTimeInMillis(currentTime);
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取上个月结束时间
     * @return
     */
    public static Date getLastMonthEndTime() {
        Long currentTime = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTimeInMillis(currentTime);
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));// 获取当前月最后一天
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }


    /**
     * 获取今天几号
     * @return
     */
    public static int getTodayNum(){
        Calendar c = Calendar.getInstance();
        return c.get(Calendar.DATE);
    }


    /**
     * 获取时间段内的自然周
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    public static List<List<String>> getNatureWeeks(String dateStart, String dateEnd) {
        List<List<String>> natureWeekList = new ArrayList<>();
        try {
            DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);
            Date startDateNum = dateFormat.parse(dateStart);
            Date endDateNum = dateFormat.parse(dateEnd);
            if (startDateNum.getTime() >= endDateNum.getTime()) {
                return natureWeekList;
            }
            Date startDate = dateFormat.parse(dateStart);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(startDate);
            for (; ; ) {
                int dayOfWeeek = new Integer(calendar.get(Calendar.DAY_OF_WEEK));
                //周一(周日-周一...周五-周六依次用1-7表示)
                if (dayOfWeeek == 2) {
                    calendar.add(Calendar.DATE, 6);
                    Date dateNum = calendar.getTime();
                    if (dateNum.getTime() <= endDateNum.getTime()) {
                        List<String> natureWeek = new ArrayList<>();
                        calendar.add(Calendar.DATE, -6);
                        for (int i = 0; i < 7; i++) {
                            String day = dateFormat.format(calendar.getTime());
                            natureWeek.add(day);
                            calendar.add(Calendar.DATE, 1);
                        }
                        natureWeekList.add(natureWeek);
                    } else {
                        break;
                    }
                } else {
                    calendar.add(Calendar.DATE, 1);
                }
            }
        } catch (ParseException e) {
//            log.error("dateStart:{}, dateEnd:{}",dateStart, dateEnd,e);
        }
        return natureWeekList;
    }


    /**
     *
     * 获取查询统计时间
     *
     * @param date   当前时间
     * @param field  Calendar.year 、mouth等等
     * @param amount 时间数值
     * @return
     */
    public static Date getSearchStaticTime(Date date, int field, int amount) {
        //获取时间加一年或加一月或加一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 12);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(field, amount);
        return cal.getTime();
    }

    /**
     *
     * 获取传入日期所在周的开始时间-String
     *
     * @return
     */
    public static String getFirstOfWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        return new SimpleDateFormat(DATE_PATTERN).format(cal.getTime());
    }

    /**
     *
     * 获取传入日期所在周的开始时间-Date
     *
     * @return
     */
    public static Date getFirstDateOfWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     *
     * 获取传入日期所在周的结束时间
     *
     * @return
     */
    public static String getLastOfWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        cal.add(Calendar.DAY_OF_WEEK, 6);
        return new SimpleDateFormat(DATE_PATTERN).format(cal.getTime());
    }

    /**
     *
     * 获取传入日期所在周的结束时间
     *
     * @return
     */
    public static Date getLastDateOfWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        cal.add(Calendar.DAY_OF_WEEK, 6);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }


    /**
     *
     * 获取传入日期所在月的结束时间
     *
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int last = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH,last);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }


    /**
     *
     * 获取传入日期所在月的开始时间
     *
     * @return
     */
    public static Date getFirstDayDateOfMonth(final Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int first = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH,first);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     *
     * 获取2个日期之间的自然月
     *
     * @return
     */
    public static List<List<String>> getMonthBetween(Date minDate, Date maxDate){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN);//格式化为年月
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        Calendar curr = min;
        List<List<String>> CycleTimeList=new ArrayList();
        while (curr.before(max)) {
            List<String> time = new ArrayList<>();
            time.add(sdf.format(curr.getTime()));
            String monthEnd = getMonthEnd(sdf.format(curr.getTime()));
            time.add(monthEnd);
            CycleTimeList.add(time);
            curr.add(Calendar.MONTH, 1);
        }
        return CycleTimeList;
    }

    public static String getMonthEnd(String  time){
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat(DATE_PATTERN);
        Date date= null;
        try {
            date = simpleDateFormat.parse(time);
        } catch (ParseException e) {
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND,59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间
        return simpleDateFormat.format(c.getTime());
    }

    /**
     *
     * 获得传入日期与当前月1号相差的天数
     *
     * @return
     */
    public static  int getOneDatePlus(Date date) {
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);
        int month = cd.get(Calendar.DAY_OF_MONTH);
        return month;
    }
    /**
     *
     * 获取当前年月
     *
     * @return
     */

    public static String getLocalYearAndMonth(){
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        return year + "_" + month;
    }


    /**
     * 获取今日开始时间
     * @return
     */
    public static Date getYesterdayStartDate(){
        Calendar currentDate = new GregorianCalendar();
        currentDate.add(Calendar.DATE,-1);
        currentDate.set(Calendar.HOUR_OF_DAY, 0);
        currentDate.set(Calendar.MINUTE, 0);
        currentDate.set(Calendar.SECOND, 0);
        return currentDate.getTime();
    }

    /**
     * 获取当前分钟，秒钟变成0
     * @return
     */
    public static Date getCurrentMinute() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date()); // 将当前时间设置到Calendar对象中
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取当前分钟，秒钟变成0
     * @return
     */
    public static Date getCurrentHour() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date()); // 将当前时间设置到Calendar对象中
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取当前分钟，用于判断当前时间是整点，还是半点
     * @param time
     */
    public static int getMinute(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time); // 将当前时间设置到Calendar对象中
        return cal.get(Calendar.MINUTE);
    }

    /**
     * 当前时间加 interval 分钟
     * @param time
     * @param interval
     * @return
     */
    public static Date addMinute(Date time, int interval) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time); // 将当前时间设置到Calendar对象中
        cal.add(Calendar.MINUTE, interval); // 将分钟数减interval
        return cal.getTime(); // 获取减去interval分钟后的时间
    }

    /**
     * 当前时间加 interval 小时
     * @param time
     * @param interval
     * @return
     */
    public static Date addHour(Date time, int interval) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time); // 将当前时间设置到Calendar对象中
        cal.add(Calendar.HOUR, interval); // 将分钟数减interval
        return cal.getTime(); // 获取减去interval分钟后的时间
    }


    /**
     * 获取某天开始时间
     * @return
     */
    public static Date getStartTimeOfDate(Date date){
        Calendar currentDate = Calendar.getInstance();
        currentDate.setTime(date);
        currentDate.set(Calendar.HOUR_OF_DAY, 0);
        currentDate.set(Calendar.MINUTE, 0);
        currentDate.set(Calendar.SECOND, 0);
        return currentDate.getTime();
    }

    /**
     * 获取某天结束时间 23:59:59:999
     *
     * @param
     * @return
     */
    public static Date getEndTimeOfDate(Date date) {
        Calendar currentDate = Calendar.getInstance();
        currentDate.setTime(date);
        currentDate.set(Calendar.HOUR_OF_DAY, 23);
        currentDate.set(Calendar.MINUTE, 59);
        currentDate.set(Calendar.SECOND, 59);
        return currentDate.getTime();
    }

    /**
     * 获取每天的结束时间 23:59:59:999
     *
     * @param
     * @return
     */
    public static Date getYesterdayEndTime() {
        Calendar currentDate = new GregorianCalendar();
        currentDate.add(Calendar.DATE,-1);
        currentDate.set(Calendar.HOUR_OF_DAY, 23);
        currentDate.set(Calendar.MINUTE, 59);
        currentDate.set(Calendar.SECOND, 59);
        return currentDate.getTime();
    }

    public static long calBetweenDays(Date startTime, Date endTime) {
        if(startTime == null || endTime == null) {
            return 0;
        }

        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        if(endTime.getTime() > startTime.getTime()) {
            startCal.setTime(startTime);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);

            endCal.setTime(endTime);
            endCal.set(Calendar.HOUR_OF_DAY, 0);
            endCal.set(Calendar.MINUTE, 0);
            endCal.set(Calendar.SECOND, 0);
        } else {
            startCal.setTime(endTime);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);

            endCal.setTime(startTime);
            endCal.set(Calendar.HOUR_OF_DAY, 0);
            endCal.set(Calendar.MINUTE, 0);
            endCal.set(Calendar.SECOND, 0);
        }

        return (endCal.getTimeInMillis() - startCal.getTimeInMillis()) / (60 * 60 * 24 * 1000);
    }

    /**
     * 判断是否在同一个月
     *
     * @return false:不在同一个月内，true在同一个月内
     */
    public static boolean isSameMonth(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);
        return calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR)
                && calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH);
    }

    public static Date getDataTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 12);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

}
