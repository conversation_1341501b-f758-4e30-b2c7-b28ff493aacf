package com.lifekh.data.warehouse.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class FastJsonUtil {
    /**
     * JSON字符串转换成对象
     */
    public static <T> T jsonToObject(String jsonStr, Class<T> obj) {
        try {
            if(StringUtils.isBlank(jsonStr)) {
                return null;
            }
            return JSONObject.parseObject(jsonStr, obj);
        } catch (Exception e) {
            log.warn("将JSON串{}转换成 指定对象失败:{}" + jsonStr, e);
        }
        return null;    }

    /**
     * 对象转JSON
     */
    public static <T> String objectToJson(T obj) {
        try {
            return JSONObject.toJSONString(obj);
        } catch (Exception e) {
            log.warn("将指定对象转成JSON串{}失败:{}" + obj.toString(), e);
        }
        return "";
    }

    /**
     * json转换成对象列表List<T>
     */
    public static <T> List<T> jsonToList(String json, Class<T> obj) {
        try {
            return JSONArray.parseArray(json, obj);
        } catch (Exception e) {
            log.info("将JSON串{}转成对象列表失败:{}" + json, e);
        }
        return new ArrayList<>();
    }
}
