package com.lifekh.data.warehouse.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.config.route.DynamicDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@MapperScan(basePackages = {"com.lifekh.data.warehouse.oracle.dao", "com.lifekh.data.warehouse.mysql.dao"}, sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfig {

    /**
     * 设置默认数据库
     */
    @Primary
    @Bean(name = "customerDataSource")
    @ConfigurationProperties(prefix = "db.customer.jdbc")
    public DataSource getDataSource() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }

    @Bean(name = "shopDataSource")
    @ConfigurationProperties(prefix = "db.shop.jdbc")
    public DataSource getDataSource0() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }

    @Bean(name = "appconfigDataSource")
    @ConfigurationProperties(prefix = "db.appconfig.jdbc")
    public DataSource getAppConfigDataSource() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }

    @Bean(name = "ordsDataSource")
    @ConfigurationProperties(prefix = "db.ords.jdbc")
    public DataSource getOrdsDataSource() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }

    /**
     * 创建数据源
     *
     * @param customerDataSource
     * @param shopDataSource
     * @return
     */
    @Bean
    public DynamicDataSource dynamicDataSource(@Qualifier("customerDataSource") DataSource customerDataSource,
                                               @Qualifier("shopDataSource") DataSource shopDataSource,
                                               @Qualifier("appconfigDataSource") DataSource appconfigDataSource,
                                               @Qualifier("ordsDataSource") DataSource ordsDataSource) {
        Map<Object, Object> targetDataSource = new HashMap<>();
        targetDataSource.put(DataSourceType.LIFEKH_MP_CUSTOMER, customerDataSource);
        targetDataSource.put(DataSourceType.LIFEKH_MP_SHOP, shopDataSource);
        targetDataSource.put(DataSourceType.LIFEKH_MP_APPCONFIG, appconfigDataSource);
        targetDataSource.put(DataSourceType.LIFEKH_MP_ORDS, ordsDataSource);

        DynamicDataSource dataSource = new DynamicDataSource();
        dataSource.setTargetDataSources(targetDataSource);
        return dataSource;
    }

    /**
     * 根据数据源创建SqlSessionFactory
     *
     * @return
     */
    @Bean
    public SqlSessionFactory sqlSessionFactory(DynamicDataSource dynamicDataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        // 指定数据源
        bean.setDataSource(dynamicDataSource);
        return bean.getObject();
    }

    /**
     * 配置事务管理器
     *
     * @return
     */
    @Bean
    public DataSourceTransactionManager transactionManager(DynamicDataSource dynamicDataSource) {
        return new DataSourceTransactionManager(dynamicDataSource);
    }
}
