#yapi tag
api.tag=@io.swagger.annotations.ApiOperation#tags
#find module from comment tag
module=#module
# read api name from tag `api.name`, @api.name
api.name=#api.name
#导出文件后缀名为Controller或Facade或Impl文件的api
mdoc.class.filter=groovy:it.name().endsWith("Controller") || it.name().endsWith("Facade") || it.name().endsWith("Impl")
#添加对必填字段注解的支持
field.required=@io.swagger.annotations.ApiModelProperty#required
#添加对默认值注解的支持, @default
field.default.value=#default
#添加对忽略class/method的支持, @ignore
ignore=#ignore
#忽略API参数, 添加对swagger @ApiParam支持
param.ignore=@io.swagger.annotations.ApiParam#hidden
# ignore field
json.rule.field.ignore=#fieldIgnore