<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>user-data-warehouse</artifactId>
        <groupId>com.lifekh</groupId>
        <version>2.0.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>data-warehouse-collection</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-elastic-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>app-config-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>chaos-usercenter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>shop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kh-super.takeaway</groupId>
            <artifactId>product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kh-super.takeaway</groupId>
            <artifactId>merchant-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>framework-container-springmvc</artifactId>
            <groupId>com.outstanding</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>