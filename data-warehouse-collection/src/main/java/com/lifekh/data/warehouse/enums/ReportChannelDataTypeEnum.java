package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportChannelDataTypeEnum implements BaseEnum<ReportChannelDataTypeEnum, String> {

    NEW_USER_COUNT("new_user_count", "渠道新增用户数"),
    ORDER_USER_COUNT("order_user_count", "渠道下单用户数"),
    INVITE_REGISTER_ACTIVITY_COUNT("invite_register_activity_count", "渠道邀请次数（邀请有礼）");

    private String code;

    private String message;
}
