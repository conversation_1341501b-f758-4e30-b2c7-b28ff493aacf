package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportDiscoveryDataTypeEnum implements BaseEnum<ReportDiscoveryDataTypeEnum, String> {

    VIEW_COUNT("view_count", "浏览数"),
    GOODS_CLICK_COUNT("goods_click_count", "好物点击数"),
    ;

    private String code;

    private String message;
}
