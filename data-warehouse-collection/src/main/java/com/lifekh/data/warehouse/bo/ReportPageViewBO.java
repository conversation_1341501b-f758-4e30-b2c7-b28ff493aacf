package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_PAGE_VIEW)
@Data
@Accessors(chain = true)
public class ReportPageViewBO implements Serializable {
    private static final long serialVersionUID = 6568189974553803343L;

    public static final String LANGUAGE = "language";
    public static final String DEVICE_TYPE = "deviceType";
    public static final String PV = "pv";
    public static final String UV = "uv";
    public static final String DV = "dv";

    private Date dataTime;
    private String pageName;
    private String language;
    private String deviceType;
    private Integer pv;
    private Integer uv;
    private Integer dv;
    private Date createTime;
}
