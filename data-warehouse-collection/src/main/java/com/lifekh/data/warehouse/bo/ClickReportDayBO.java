package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Document(collection = MongoDbCollectonName.REPORT_CLICK_DAY)
@Data
public class ClickReportDayBO extends ReportBasicBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_CLICK_DAY;

    private static final long serialVersionUID = -2599035028656451031L;


}
