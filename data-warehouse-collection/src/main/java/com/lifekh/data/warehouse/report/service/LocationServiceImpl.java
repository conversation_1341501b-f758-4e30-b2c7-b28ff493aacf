package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.oracle.bo.OracleZoneBO;
import com.lifekh.data.warehouse.oracle.dao.OracleZoneDAO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
public class LocationServiceImpl implements LocationService {

    @Autowired
    private LocationManage locationManage;

    @Autowired
    private OracleZoneDAO oracleZoneDAO;

    @Override
    public LocationDTO getLocationByCoordinate(String longitude, String latitude) throws PendingException {
        LocationDTO locationResponse = new LocationDTO();
        //根据经纬度找出对应的省份名称
        LocationDTO locationDTO = locationManage.getNameByPosition(longitude, latitude);
        if (Objects.nonNull(locationDTO)) {
            OracleZoneBO province = oracleZoneDAO.queryZone(locationDTO.getProvinceName().toLowerCase(), 11);
            if(province != null) {
                locationResponse.setProvinceName(province.getMsgEn());
                OracleZoneBO district = oracleZoneDAO.queryZoneByParent(locationDTO.getDistinctName().toLowerCase(), province.getCode());
                locationResponse.setDistinctName(Optional.ofNullable(district).orElse(new OracleZoneBO()).getMsgEn());
            }
        }
        locationResponse.setLatitude(latitude);
        locationResponse.setLongitude(longitude);
        return locationResponse;
    }
}
