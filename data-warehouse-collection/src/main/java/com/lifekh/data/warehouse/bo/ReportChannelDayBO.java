package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_CHANNEL_DAY)
@Data
@Accessors(chain = true)
public class ReportChannelDayBO implements Serializable {
    private static final long serialVersionUID = 8107859918954474996L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 数据类型 {@link com.lifekh.data.warehouse.enums.ReportChannelDataTypeEnum}
     */
    private String dataType;

    /**
     * 数据类型名称
     */
    private String dataTypeName;

    /**
     * 渠道
     */
    private String channel;
    public final static String CHANNEL = "channel";


    /**
     * 数量统计
     */
    private Long count;
    public final static String COUNT = "count";

    private Date createTime;

    private Date updateTime;

}
