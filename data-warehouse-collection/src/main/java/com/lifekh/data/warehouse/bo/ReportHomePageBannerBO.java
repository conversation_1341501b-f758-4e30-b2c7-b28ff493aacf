package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Document(collection = MongoDbCollectonName.REPORT_HOME_PAGE_RECOMMEND)
@Data
public class ReportHomePageBannerBO extends ReportBasicBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_HOME_PAGE_RECOMMEND;

    private static final long serialVersionUID = -2599035028656451031L;

    /**
     * 业务线
     */
    private String businessLine;
    public static final String BUSINESS_LINE = "businessLine";


}
