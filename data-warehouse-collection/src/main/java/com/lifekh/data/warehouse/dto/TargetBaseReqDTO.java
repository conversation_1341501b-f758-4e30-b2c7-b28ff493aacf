package com.lifekh.data.warehouse.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class TargetBaseReqDTO {

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
