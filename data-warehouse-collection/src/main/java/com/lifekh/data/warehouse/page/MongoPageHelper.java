package com.lifekh.data.warehouse.page;

import com.lifekh.data.warehouse.constant.ClickReportConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MongoPageHelper<T> {

    @Autowired
    private MongoTemplate mongoTemplate;

    public PageRespDTO<T> query(List<AggregationOperation> aggregationList,
                                PageReqDTO reqDTO,
                                String collection,
                                Boolean allowDiskUse,
                                Long totalResults,
                                Class<T> clazz) {
        PageRespDTO<T> pageRespDTO = new PageRespDTO<T>();

        List<AggregationOperation> queryList = new ArrayList<>(aggregationList);

        //查询总条数
        long total = 0L;

        if (totalResults == null) {
            CountOperation countOperation = Aggregation.count().as(ClickReportConstant.TOTAL);
            queryList.add(countOperation);
            Aggregation aggregationTotal = Aggregation.newAggregation(queryList);
            if (Boolean.TRUE.equals(allowDiskUse)) {
                aggregationTotal.withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            }
            AggregationResults<PageTotalDTO> aggregateTotalResults = mongoTemplate.aggregate(aggregationTotal, collection, PageTotalDTO.class);
            List<PageTotalDTO> totalList = aggregateTotalResults.getMappedResults();
            if (CollectionUtils.isNotEmpty(totalList)) {
                total = totalList.get(0).getTotal();
                if (total < 1) {
                    return pageRespDTO;
                }
                pageRespDTO.setTotal(total);
            }
            queryList.remove(countOperation);
        } else {
            total = totalResults;
        }

        //根据分页查询记录
        queryList.add(Aggregation.skip((reqDTO.getPageNum()) * reqDTO.getPageSize()));
        queryList.add(Aggregation.limit(reqDTO.getPageSize()));

        Aggregation aggregation = Aggregation.newAggregation(queryList);
        if (Boolean.TRUE.equals(allowDiskUse)) {
            aggregation.withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        }
        AggregationResults<T> aggregationResults = mongoTemplate.aggregate(aggregation, collection, clazz);

        pageRespDTO.setList(aggregationResults.getMappedResults());
        pageRespDTO.setTotal(total);
        pageRespDTO.setPages((total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        pageRespDTO.setSize(CollectionUtils.isEmpty(aggregationResults.getMappedResults()) ? 0 : aggregationResults.getMappedResults().size());
        pageRespDTO.setPageNum((int) reqDTO.getPageNum());
        return pageRespDTO;
    }

    public Long queryTotal(List<AggregationOperation> aggregationList,
                           String collection,
                           Boolean allowDiskUse) {
        List<AggregationOperation> queryList = new ArrayList<>(aggregationList);

        //查询总条数
        queryList.add(Aggregation.count().as(ClickReportConstant.TOTAL));
        Aggregation aggregationTotal = Aggregation.newAggregation(queryList);
        if (Boolean.TRUE.equals(allowDiskUse)) {
            aggregationTotal.withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        }

        try {
            AggregationResults<PageTotalDTO> aggregateTotalResults = mongoTemplate.aggregate(aggregationTotal, collection, PageTotalDTO.class);
            List<PageTotalDTO> totalList = aggregateTotalResults.getMappedResults();
            if (CollectionUtils.isNotEmpty(totalList)) {
                return totalList.get(0).getTotal();
            }
        } catch (Exception e) {
            try {
                TimeUnit.SECONDS.sleep(1);
                AggregationResults<PageTotalDTO> aggregateTotalResults = mongoTemplate.aggregate(aggregationTotal, collection, PageTotalDTO.class);
                List<PageTotalDTO> totalList = aggregateTotalResults.getMappedResults();
                if (CollectionUtils.isNotEmpty(totalList)) {
                    return totalList.get(0).getTotal();
                }
            } catch (Exception e1) {
                log.error("查询异常 aggregationList:{}", aggregationList, e);
            }
        }
        return 0L;
    }

    public Long queryTotal(Query query, String collection) {
        return mongoTemplate.count(query, collection);
    }
}
