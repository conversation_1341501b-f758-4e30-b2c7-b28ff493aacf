package com.lifekh.data.warehouse.manage;

import com.lifekh.data.warehouse.api.req.FunSwitchDisableReqDTO;
import com.lifekh.data.warehouse.api.req.FunSwitchEnableReqDTO;
import com.lifekh.data.warehouse.api.req.FunctionSwitchAddReqDTO;
import com.lifekh.data.warehouse.bo.collection.EventTypeBO;
import com.lifekh.data.warehouse.bo.collection.FunctionSwitchBO;
import com.outstanding.framework.core.BeanCopierHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
public class FunctionSwitchManager {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final Map<String, Object> switchMap = new HashMap<>();

    @PostConstruct
    public void init() {
        try {
            List<FunctionSwitchBO> list = mongoTemplate.find(new Query(new Criteria().and(FunctionSwitchBO.ENABLE).is(true)), FunctionSwitchBO.class);
            for (FunctionSwitchBO bo : list) {
                if (StringUtils.isNotBlank(bo.getName())) {
                    switchMap.put(bo.getName(), Optional.ofNullable(bo.getEnable()).orElse(Boolean.FALSE));
                }
            }
        } catch (Exception e) {
            log.warn("功能开关配置加载异常", e);
        }
    }

    public boolean getSwitchBool(String name) {
        try {
            if(StringUtils.isBlank(name)) {
                return false;
            }

            Boolean flag = (Boolean) switchMap.get(name);
            return flag != null && flag;
        } catch (Exception e) {
            log.warn("获取功能配置异常,name:{}", name, e);
        }
        return false;
    }


    public void add(FunctionSwitchAddReqDTO reqDTO) {
        FunctionSwitchBO bo = mongoTemplate.findOne(new Query(new Criteria().and(FunctionSwitchBO.NAME).is(reqDTO.getName())), FunctionSwitchBO.class);
        if (bo != null) {
            log.info("功能已存在，无法添加,name:{}", reqDTO.getName());
            return;
        }

        FunctionSwitchBO functionSwitchBO = new FunctionSwitchBO();
        BeanCopierHelper.copyProperties(reqDTO, functionSwitchBO);
        functionSwitchBO.setCreateTime(new Date());
        functionSwitchBO.setUpdateTime(new Date());
        functionSwitchBO.setUpdateBy(reqDTO.getUpdateBy());
        functionSwitchBO.setEnable(true);
        mongoTemplate.save(functionSwitchBO);
    }

    public void enable(FunSwitchEnableReqDTO reqDTO) {
        FunctionSwitchBO bo = mongoTemplate.findOne(new Query(new Criteria().and(FunctionSwitchBO.NAME).is(reqDTO.getName())), FunctionSwitchBO.class);
        if (bo == null) {
            log.info("功能不存在，无法修改,name:{}", reqDTO.getName());
            return;
        }

        switchMap.put(reqDTO.getName(), true);

        Query query = new Query();
        query.addCriteria(Criteria.where(FunctionSwitchBO.NAME).is(reqDTO.getName()));
        Update update = new Update();
        update.set(FunctionSwitchBO.ENABLE, true);
        update.set(FunctionSwitchBO.UPDATE_TIME, new Date());
        update.set(FunctionSwitchBO.UPDATE_BY, reqDTO.getUpdateBy());
        mongoTemplate.updateFirst(query, update, FunctionSwitchBO.class);
    }

    public void disable(FunSwitchDisableReqDTO reqDTO) {
        FunctionSwitchBO bo = mongoTemplate.findOne(new Query(new Criteria().and(FunctionSwitchBO.NAME).is(reqDTO.getName())), FunctionSwitchBO.class);
        if (bo == null) {
            log.info("功能不存在，无法修改,name:{}", reqDTO.getName());
            return;
        }

        switchMap.put(reqDTO.getName(), false);

        Query query = new Query();
        query.addCriteria(Criteria.where(FunctionSwitchBO.NAME).is(reqDTO.getName()));
        Update update = new Update();
        update.set(FunctionSwitchBO.ENABLE, false);
        update.set(FunctionSwitchBO.UPDATE_TIME, new Date());
        update.set(FunctionSwitchBO.UPDATE_BY, reqDTO.getUpdateBy());
        mongoTemplate.updateFirst(query, update, FunctionSwitchBO.class);
    }
}
