package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_ACTIVITY_INVITE)
@Data
@Accessors(chain = true)
public class ReportActivityInviteBO implements Serializable {
    private static final long serialVersionUID = 1127509781367754222L;

    public static final String CLICK_SHARE_COUNT = "clickShareCount";
    public static final String REGISTER_PAGE_COUNT = "registerPageCount";
    public static final String REGISTER_COUNT = "registerCount";

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 进入邀请页面数
     */
    private Long invitePageCount;

    /**
     * 点击分享按钮数
     */
    private Long clickShareCount;

    /**
     * 进入注册页面数
     */
    private Long registerPageCount;

    /**
     * 点击领取优惠数
     */
    private Long clickGetCount;

    /**
     * 进入下载页面数
     */
    private Long downloadPageCount;

    /**
     * 注册用户数
     */
    private Long registerCount;

    private Date createTime;

    private Date updateTime;

    private Integer version;

}
