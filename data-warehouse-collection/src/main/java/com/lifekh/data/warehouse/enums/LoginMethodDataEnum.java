package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;

public enum LoginMethodDataEnum implements BaseEnum<LoginMethodDataEnum,String> {
    /**
     * 登录方式
     */
    PWD("密码登录"),
    REG("注册登录"),
    THR("第三方登录"),
    LOGOUT("登出"),
    MODIFY_PWD("密码修改"),
    RESET_PWD("重置密码"),
    SMS_LOGIN("短信登录"),
    FACEBOOK("Facebook授权登录"),
    APPLEID("AppleID授权登录"),
    WECHAT("微信公众号授权登录"),
    APP_WECHAT("APP微信授权登录"),
    OTHER("其它");
    private final String message;

    LoginMethodDataEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return name();
    }

    @Override
    public String getMessage() {
        return message;
    }

    public static String getMessageByCode(String code) {
        if(null==code || "".equals(code) || code.length() < 1) {
            return "";
        }

        switch (code) {
            case "THR":
            case "WECHAT": return WECHAT.getMessage();
            case "LOGOUT":
            case "MODIFY_PWD":
            case "RESET_PWD": return LOGOUT.getMessage();
            case "REG" :
            case "SMS_LOGIN" : return SMS_LOGIN.getMessage();
            case "PWD" : return PWD.getMessage();
            case "FACEBOOK" : return FACEBOOK.getMessage();
            case "APP_WECHAT" : return APP_WECHAT.getMessage();
            case "APPLEID" : return APPLEID.getMessage();
            default: return OTHER.getMessage();
        }
    }
}