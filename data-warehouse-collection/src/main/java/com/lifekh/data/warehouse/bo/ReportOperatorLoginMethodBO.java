package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_OPERATOR_LOGIN_METHOD)
@Data
public class ReportOperatorLoginMethodBO implements Serializable {

    private static final long serialVersionUID = 8107859918954474996L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    public static final String DATA_TIME = "dataTime";

    /**
     * 数量统计
     */
    private Integer count;

    public final static String COUNT = "count";

    /**
     * 总数统计
     */
    private Integer total;

    public final static String TOTAL = "total";

    /**
     * appid
     */
    private String appId;

    public final static String APP_ID = "appId";

    /**
     * 登录方式
     */
    private String loginMethod;

    public final static String LOGIN_METHOD = "loginMethod";

    private Date createTime;

    private Date updateTime;
}
