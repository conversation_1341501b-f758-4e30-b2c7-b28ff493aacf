package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.date.DateUtil;
import com.chaos.common.enums.BusinessLineEnum;
import com.chaos.common.enums.LanguageEnum;
import com.chaos.shop.api.enums.AggregateOrderFinalStateEnum;
import com.github.pagehelper.PageHelper;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.EventPageBusinessLineEnum;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.OrderConstant;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.enums.ReportLanguageDataTypeEnum;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.oracle.bo.OracleAggregateOrderBO;
import com.lifekh.data.warehouse.oracle.bo.OracleNewUserLangugeBO;
import com.lifekh.data.warehouse.oracle.bo.OracleUserAddressBO;
import com.lifekh.data.warehouse.oracle.bo.OracleUserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.oracle.dao.OracleAggregateOrderDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserAddressDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.Decimal128;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DwdAggregateOrderServiceImpl implements DwdAggregateOrderService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private LocationService locationService;

    @Autowired
    private TargetReportService targetReportService;

    @Autowired
    private OracleUserAddressDAO oracleUserAddressDAO;
    @Autowired
    private OracleAggregateOrderDAO oracleAggregateOrderDAO;
    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    /**
     * 用户端Android，用户端iOS
     */
    private static List<Integer> APP_NO_LIST = Arrays.asList(10,11);

    @Override
    public void syncDwdAggregateOrder(Date startTime, Date endTime) {
        int pageSize = 1000;
        for (int pageNum = 1; ; pageNum++) {
            log.info("开始同步聚合订单, pageNum：{}", pageNum);
            //查询聚合订单表
            PageHelper.startPage(pageNum, pageSize);
            List<OracleAggregateOrderBO> aggOrders = oracleAggregateOrderDAO.queryByOrderTime(startTime, endTime);
            if (CollectionUtils.isEmpty(aggOrders)) {
                break;
            }

            for (OracleAggregateOrderBO aggOrder : aggOrders) {
                this.saveOrder(aggOrder);
            }
        }
    }

    private void saveOrder(OracleAggregateOrderBO aggOrder) {
        try {
            if (OrderConstant.EXCLUDE_MERCHANT_NOS.contains(aggOrder.getMerchantNo())) {
                return;
            }

            String operatorNo = aggOrder.getUserId();
            //查询dwd订单
            DwdAggregateOrderDO dwdOrder = mongoTemplate.findOne(Query.query(Criteria.where(DwdAggregateOrderDO.AGGREGATE_ORDER_NO).is(aggOrder.getAggregateOrderNo())),
                    DwdAggregateOrderDO.class, MongoDbCollectonName.DWD_AGGREGATE_ORDER);
            if (dwdOrder == null) {
                dwdOrder = new DwdAggregateOrderDO()
                        .setInsertTime(new Date())
                        .setCreateTime(DateUtil.beginOfHour(DateUtils.setHours(aggOrder.getOrderTime(), 12)))
                        .setOrderTime(aggOrder.getOrderTime())
                        .setMonth(Long.parseLong(DateUtil.format(aggOrder.getOrderTime(), "yyyyMM")))
                        .setDay(Long.parseLong(DateUtil.format(aggOrder.getOrderTime(), "yyyyMMdd")))
                        .setSearchTime(DateUtil.beginOfHour(DateUtils.setHours(aggOrder.getOrderTime(), 12)))
                        .setAggregateOrderNo(aggOrder.getAggregateOrderNo())
                        .setFirstMerchantNo(aggOrder.getFirstMerchantNo())
                        .setMerchantNo(aggOrder.getMerchantNo())
                        .setStoreNo(aggOrder.getStoreId())
                        .setStoreName(aggOrder.getStoreName())
                        .setBusinessLine(aggOrder.getBusinessLine())
                        .setOperatorNo(operatorNo)
                        .setDeviceId(aggOrder.getDeviceId());

                //查询操作员信息
                OracleUserOperatorLoginInfoBO userInfo = oracleUserOperatorLoginInfoDAO.queryUserByOperatorNo(operatorNo);
                if (userInfo != null) {
                    dwdOrder.setMobile(userInfo.getLoginName())
                            .setRegisterTime(userInfo.getRegisterTime())
                            .setLanguage(userInfo.getLanguage());

                    //查询用户在线经纬度
                    if (userInfo.getOnlineLatitude() != null && userInfo.getOnlineLongitude() != null) {
                        LocationDTO locationDTO = locationService.getLocationByCoordinate(userInfo.getOnlineLongitude().toString(), userInfo.getOnlineLatitude().toString());
                        dwdOrder.setLatitude(userInfo.getOnlineLatitude())
                                .setLongitude(userInfo.getOnlineLongitude())
                                .setProvince(locationDTO.getProvinceName())
                                .setDistrict(locationDTO.getDistinctName());
                    }
                }

                //查询鉴权表
                Date onlineTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -2));
                OracleUserOperatorLoginInfoBO loginInfo = oracleUserOperatorLoginInfoDAO.queryUserByOperatorNoAndTime(onlineTime, operatorNo);
                if (loginInfo != null) {
                    dwdOrder.setAppNo(Optional.ofNullable(loginInfo.getAppNo()).orElse(0))
                            .setAppVersion(APP_NO_LIST.contains(loginInfo.getAppNo()) ? loginInfo.getAppVersion() : null);
                }

                //查询地址信息
                if (StringUtils.isNotBlank(aggOrder.getAddressNo())) {
                    OracleUserAddressBO address = oracleUserAddressDAO.queryByAdddressNo(aggOrder.getAddressNo());
                    if (address != null) {
                        dwdOrder.setAddressNo(address.getAddressNo())
                                .setAddressLatitude(address.getLatitude() != null ? address.getLatitude().floatValue() : null)
                                .setAddressLongitude(address.getLongitude() != null ? address.getLongitude().floatValue() : null);
                        if (address.getLongitude() != null && address.getLatitude() != null) {
                            LocationDTO locationDTO = locationService.getLocationByCoordinate(address.getLongitude().toString(), address.getLatitude().toString());
                            dwdOrder.setAddressProvince(locationDTO.getProvinceName())
                                    .setAddressDistrict(locationDTO.getDistinctName());
                        }
                    }
                }
            }

            //默认英文
            if (StringUtils.isBlank(dwdOrder.getLanguage())) {
                dwdOrder.setLanguage(LanguageEnum.EN_US.getCode());
            }

            dwdOrder.setUpdateTime(new Date())
                    .setPayOrderNo(aggOrder.getPayOrderNo())
                    .setPayType(aggOrder.getPayType())
                    .setPayChannel(aggOrder.getPayChannel())
                    .setOrderType(aggOrder.getOrderType())
                    .setAggregateOrderFinalState(aggOrder.getAggregateOrderFinalState())
                    .setAggregateOrderState(aggOrder.getAggregateOrderState())
                    .setActualPayAmount(aggOrder.getActualPayAmount() != null ? Decimal128.parse(aggOrder.getActualPayAmount().toString()) : null)
                    .setTotalPayableAmount(aggOrder.getTotalPayableAmount() != null ? Decimal128.parse(aggOrder.getTotalPayableAmount().toString()) : null);

            mongoTemplate.save(dwdOrder, MongoDbCollectonName.DWD_AGGREGATE_ORDER);
        } catch (Exception e) {
            log.error("dwd聚合订单保存出现异常, aggregateOrderNo:{}", aggOrder.getAggregateOrderNo(), e);
        }
    }

    @Override
    public void updateDwdAggregateOrder(Date startTime, Date endTime) {
        int pageSize = 1000;
        for (int pageNum = 1; ; pageNum++) {
            log.info("开始更新聚合订单, pageNum：{}", pageNum);
            //查询聚合订单表
            PageHelper.startPage(pageNum, pageSize);
            List<OracleAggregateOrderBO> aggOrders = oracleAggregateOrderDAO.queryByUpdateTimeAndExcludeCreate(startTime, endTime);
            if (CollectionUtils.isEmpty(aggOrders)) {
                break;
            }

            for (OracleAggregateOrderBO aggOrder : aggOrders) {
                try {
                    //查询dwd订单
                    DwdAggregateOrderDO dwdOrder = mongoTemplate.findOne(Query.query(Criteria.where(DwdAggregateOrderDO.AGGREGATE_ORDER_NO).is(aggOrder.getAggregateOrderNo())),
                            DwdAggregateOrderDO.class, MongoDbCollectonName.DWD_AGGREGATE_ORDER);
                    if (dwdOrder != null) {
                        dwdOrder.setUpdateTime(new Date())
                                .setAggregateOrderFinalState(aggOrder.getAggregateOrderFinalState())
                                .setAggregateOrderState(aggOrder.getAggregateOrderState());
                        mongoTemplate.save(dwdOrder, MongoDbCollectonName.DWD_AGGREGATE_ORDER);
                    }
                } catch (Exception e) {
                    log.error("聚合订单dwd更新出现异常, aggregateOrderNo:{}", aggOrder.getAggregateOrderNo(), e);
                }
            }
        }
    }

    @Override
    public void saveDwdAggregateOrder(String aggregateOrderNo) {
        OracleAggregateOrderBO aggOrder = oracleAggregateOrderDAO.queryByAggregateOrderNo(aggregateOrderNo);
        if (aggOrder != null) {
            this.saveOrder(aggOrder);
        }
    }

    @Override
    public void staticOrderReport() {
        Date yesterday = DateUtil.yesterday();
        Date startTime = DateUtil.beginOfDay(yesterday);
        Date endTime = DateUtil.endOfDay(yesterday);
        Date dataTime = DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12));

        try {//统计用户下单数
        Criteria criteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).ne(99)
                .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS);

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(DwdAggregateOrderDO.BUSINESS_LINE, DwdAggregateOrderDO.LANGUAGE, DwdAggregateOrderDO.AGGREGATE_ORDER_FINAL_STATE, DwdAggregateOrderDO.PROVINCE)
                        .first(DwdAggregateOrderDO.BUSINESS_LINE).as(ReportAggregateOrderBO.BUSINESS_LINE)
                        .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderBO.LANGUAGE)
                        .first(DwdAggregateOrderDO.AGGREGATE_ORDER_FINAL_STATE).as(ReportAggregateOrderBO.AGGREGATE_ORDER_FINAL_STATE)
                        .first(DwdAggregateOrderDO.PROVINCE).as(ReportAggregateOrderBO.PROVINCE)
                        .sum(DwdAggregateOrderDO.ACTUAL_PAY_AMOUNT).as(ReportAggregateOrderBO.ACTUAL_AMOUNT)
                        .sum(DwdAggregateOrderDO.TOTAL_PAYABLE_AMOUNT).as(ReportAggregateOrderBO.TOTAL_AMOUNT)
                        .count().as(ReportAggregateOrderBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportAggregateOrderBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderBO.class).getMappedResults();

        reports.forEach(r -> {
            r.setDataTime(dataTime).setCreateTime(new Date());
            mongoTemplate.insert(r, MongoDbCollectonName.REPORT_AGGREGATE_ORDER);
        });

        log.info("统计用户下单数成功,:{}", reports);
        } catch (Exception e) {
            log.error("统计用户下单数异常", e);
        }

        try {//按天统计下单数--按业务线和语言
            Criteria criteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                    .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).ne(99)
                    .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS);

            Aggregation aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                    .count().as(ReportAggregateOrderNewUserBO.COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportAggregateOrderNewUserBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

            //统计下单用户数
            Aggregation aggregationUserAgg = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO, DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.OPERATOR_NO).as(DwdAggregateOrderDO.OPERATOR_NO)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE),
                            Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                    .count().as(ReportAggregateOrderNewUserBO.USER_COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportAggregateOrderNewUserBO> newUserOrders = mongoTemplate.aggregate(aggregationUserAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

            reports.forEach(r -> {
                r.setDataTime(dataTime).setCreateTime(new Date());
                r.setUserCount(getUserCount(r, newUserOrders));
                mongoTemplate.insert(r, MongoDbCollectonName.REPORT_AGGREGATE_ORDER_LANGUAGE_OF_DAY);
            });

            log.info("统计活跃用户下单数成功,:{}", reports);
        } catch (Exception e) {
            log.error("统计活跃用户下单数异常", e);
        }

        try {
            //统计新注册用户下单数
            Criteria criteria = Criteria.where(DwdAggregateOrderDO.REGISTER_TIME).gte(startTime).lte(endTime)
                    .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).ne(99)
                    .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS);

            Aggregation aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                    .count().as(ReportAggregateOrderNewUserBO.COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportAggregateOrderNewUserBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

            //统计新注册用户下单用户数
            Aggregation aggregationUserAgg = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO, DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.OPERATOR_NO).as(DwdAggregateOrderDO.OPERATOR_NO)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE),
                            Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                    .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                    .count().as(ReportAggregateOrderNewUserBO.USER_COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportAggregateOrderNewUserBO> newUserOrders = mongoTemplate.aggregate(aggregationUserAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

            reports.forEach(r -> {
                r.setDataTime(dataTime).setCreateTime(new Date());
                r.setUserCount(getUserCount(r, newUserOrders));
                mongoTemplate.insert(r, MongoDbCollectonName.REPORT_AGGREGATE_ORDER_LANGUAGE_NEW_USER);
            });

            log.info("统计新注册用户下单数和用户数,:{}", reports);
        } catch (Exception e) {
            log.error("统计新注册用户下单数和用户数异常", e);
        }

        try {
            //统计上一周下单用户数数据
            //当前时间如果是周一才统计前七天的数据
            if(Calendar.MONDAY == Calendar.getInstance().get(Calendar.DAY_OF_WEEK)) {
                Date lastWeekStartTime = DateUtil.beginOfDay(com.lifekh.data.warehouse.utils.DateUtil.dateAddOneDay(new Date(), -7));
                Date lastWeekEndTime = DateUtil.endOfDay(yesterday);

                //统计用户下单数
                Criteria criteriaLastWeek = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(lastWeekStartTime).lte(lastWeekEndTime)
                        .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).ne(99)
                        .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS);
                //统计上周活跃用户下单用户数 userOrderCount
                Aggregation aggregationUserAgg = Aggregation.newAggregation(
                                Aggregation.match(criteriaLastWeek),
                                Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO, DwdAggregateOrderDO.LANGUAGE)
                                        .first(DwdAggregateOrderDO.OPERATOR_NO).as(DwdAggregateOrderDO.OPERATOR_NO)
                                        .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE),
                                Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                        .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                        .count().as(ReportAggregateOrderNewUserBO.USER_COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportAggregateOrderNewUserBO> activeUserOrdersLastWeek = mongoTemplate.aggregate(aggregationUserAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

                //统计上周新注册用户且下单的用户数  newUserOrderCount
                Criteria criteriaLastWeekRegister = Criteria.where(DwdAggregateOrderDO.REGISTER_TIME).gte(lastWeekStartTime).lte(lastWeekEndTime)
                        .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).ne(99)
                        .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS);
                Aggregation aggregationLastWeekRegister = Aggregation.newAggregation(
                                Aggregation.match(criteriaLastWeekRegister),
                                Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO, DwdAggregateOrderDO.LANGUAGE)
                                        .first(DwdAggregateOrderDO.OPERATOR_NO).as(DwdAggregateOrderDO.OPERATOR_NO)
                                        .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE),
                                Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                        .first(DwdAggregateOrderDO.LANGUAGE).as(ReportAggregateOrderNewUserBO.LANGUAGE)
                                        .count().as(ReportAggregateOrderNewUserBO.USER_COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportAggregateOrderNewUserBO> reportsLastWeekRegister = mongoTemplate.aggregate(aggregationLastWeekRegister, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderNewUserBO.class).getMappedResults();

                //统计上周新注册用户数  newUserCount
                List<OracleNewUserLangugeBO>  registerUserLanguageReports = oracleUserOperatorLoginInfoDAO.queryNewUserLanguageByTime(lastWeekStartTime, lastWeekEndTime);

                //统计上周活跃用户数  userActiveCount
                Query queryActiveLastWeek = Query.query(Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(lastWeekStartTime).lte(lastWeekEndTime)
                        .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_WEEK.getCode()));
                List<ReportLanguageDayBO> userActiveCountReport = mongoTemplate.find(queryActiveLastWeek, ReportLanguageDayBO.class);

                //上周活跃用户数（7天累计）  userActiveSummaryCount
                Map<String, Integer> activeUserCountMap = new HashMap<>();
                Criteria activeCri = Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(lastWeekStartTime).lte(lastWeekEndTime)
                        .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT.getCode());
                List<ReportLanguageDayBO> activeUserCounts = mongoTemplate.find(Query.query(activeCri), ReportLanguageDayBO.class, MongoDbCollectonName.REPORT_LANGUAGE_DAY);
                activeUserCounts.forEach(countBo -> {
                    Integer count = activeUserCountMap.get(countBo.getLanguage());
                    if(count == null) {
                        activeUserCountMap.put(countBo.getLanguage(), countBo.getCount().intValue());
                    } else {
                        activeUserCountMap.put(countBo.getLanguage(), countBo.getCount().intValue() + count);
                    }
                });

                //上周下单人数（7天累计）   userOrderSummaryCount
                Map<String, Integer> userOrderSummaryCountMap = new HashMap<>();
                Criteria userOrderSummaryCountCri = Criteria.where(ReportAggregateOrderNewUserBO.DATA_TIME).gte(lastWeekStartTime).lte(lastWeekEndTime);
                List<ReportAggregateOrderNewUserBO> userOrderSummaryCounts = mongoTemplate.find(Query.query(userOrderSummaryCountCri), ReportAggregateOrderNewUserBO.class, MongoDbCollectonName.REPORT_AGGREGATE_ORDER_LANGUAGE_OF_DAY);
                userOrderSummaryCounts.forEach(countBo -> {
                    Integer count = userOrderSummaryCountMap.get(countBo.getLanguage());
                    if(count == null) {
                        userOrderSummaryCountMap.put(countBo.getLanguage(), countBo.getUserCount());
                    } else {
                        userOrderSummaryCountMap.put(countBo.getLanguage(), countBo.getUserCount() + count);
                    }
                });

                for(LanguageEnum languageEnum : LanguageEnum.values()) {
                    ReportOperationalWeeklyBO weeklyBO = new ReportOperationalWeeklyBO();
                    weeklyBO.setDataTime(dataTime).setCreateTime(new Date());
                    weeklyBO.setLanguage(languageEnum.getCode());
                    weeklyBO.setUserActiveSummaryCount(Optional.ofNullable(activeUserCountMap.get(languageEnum.getCode())).orElse(0));
                    weeklyBO.setUserOrderSummaryCount(Optional.ofNullable(userOrderSummaryCountMap.get(languageEnum.getCode())).orElse(0));
                    weeklyBO.setUserActiveCount(getUserActiveCount(languageEnum.getCode(), userActiveCountReport));
                    weeklyBO.setUserOrderCount(getUserOrderCount(languageEnum.getCode(), activeUserOrdersLastWeek));
                    weeklyBO.setNewUserCount(getNewUserCount(languageEnum.getCode(), registerUserLanguageReports));
                    weeklyBO.setNewUserOrderCount(getNewUserOrderCount(languageEnum.getCode(), reportsLastWeekRegister));

                    if(weeklyBO.getUserActiveSummaryCount() != null && weeklyBO.getUserActiveSummaryCount() > 0) {
                        weeklyBO.setOrderSummaryRate((weeklyBO.getUserOrderSummaryCount() + 0.0) / weeklyBO.getUserActiveSummaryCount());
                    }
                    if(weeklyBO.getUserActiveCount() != null && weeklyBO.getUserActiveCount() > 0) {
                        weeklyBO.setUserOrderRate((weeklyBO.getUserOrderCount() + 0.0) / weeklyBO.getUserActiveCount());
                    }
                    if(weeklyBO.getNewUserCount() != null && weeklyBO.getNewUserCount() > 0) {
                        weeklyBO.setNewUserOrderRate((weeklyBO.getNewUserOrderCount() + 0.0) / weeklyBO.getNewUserCount());
                    }
                    mongoTemplate.insert(weeklyBO, MongoDbCollectonName.REPORT_OPERATIONAL_WEEKLY_REPORT);
                }
                log.info("统计上一周下单用户数数据完成");
            }
        } catch (Exception e) {
            log.error("统计上一周下单用户数数据异常", e);
        }
    }

    public static void main(String[] args) {
        ReportOperationalWeeklyBO weeklyBO = new ReportOperationalWeeklyBO();
        weeklyBO.setNewUserCount(0);
        weeklyBO.setNewUserOrderCount(0);
        if(weeklyBO.getNewUserCount() != null && weeklyBO.getNewUserOrderCount() > 0) {
            System.out.println(weeklyBO.getNewUserOrderCount() + 0.0 / weeklyBO.getNewUserCount());
        }
    }

    private Integer getUserActiveCount(String language, List<ReportLanguageDayBO> list) {
        Integer count = 0;
        for(ReportLanguageDayBO bo : list) {
            if(language.equals(bo.getLanguage())) {
                count = bo.getCount().intValue();
                break;
            }
        }
        return count;
    }

    private Integer getUserOrderCount(String language, List<ReportAggregateOrderNewUserBO> list) {
        Integer count = 0;
        for(ReportAggregateOrderNewUserBO bo : list) {
            if(language.equals(bo.getLanguage())) {
                count = bo.getUserCount();
                break;
            }
        }
        return count;
    }

    private Integer getNewUserOrderCount(String language, List<ReportAggregateOrderNewUserBO> list) {
        Integer count = 0;
        for(ReportAggregateOrderNewUserBO bo : list) {
            if(language.equals(bo.getLanguage())) {
                count = bo.getUserCount();
                break;
            }
        }
        return count;
    }

    /**
     * 查找对应语言的新注册用户数
     *
     * @param language
     * @param list
     * @return
     */
    private Integer getNewUserCount(String language, List<OracleNewUserLangugeBO> list) {
        Integer count = 0;
        for(OracleNewUserLangugeBO bo : list) {
            if(language.equals(bo.getLanguage())) {
                count = bo.getNum();
                break;
            }
        }
        return count;
    }


    /**
     * 匹配相同对象，获取下单的用户数量
     * @param bo
     * @param newUserOrders
     * @return
     */
    private Integer getUserCount(ReportAggregateOrderNewUserBO bo, List<ReportAggregateOrderNewUserBO> newUserOrders) {
        Integer newUserCount = 0;
        for(ReportAggregateOrderNewUserBO newUserBO : newUserOrders) {
            if(StringUtils.compare(bo.getBusinessLine(), newUserBO.getBusinessLine()) == 0) {
                if(StringUtils.compare(bo.getLanguage(), newUserBO.getLanguage()) == 0) {
                    newUserCount = newUserBO.getUserCount();
                    break;
                }
            }
        }
        return newUserCount;
    }

    @Override
    public void staticHomePageOrderReport() {
        ReportDateDTO dateDTO = ReportDateDTO.yesterday();
        //统计首页下单数
        Criteria orderList = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                .and(ClickReportConstant.EVENT).is(ClickReportConstant.ORDER_SUBMIT_EVEN);
        Aggregation homeAgg = Aggregation.newAggregation(
                        Aggregation.match(orderList),
                        Aggregation.group(ClickReportConstant.LANGUAGE, ClickReportConstant.BUSINESS_LINE, ClickReportConstant.BUSINESS_NAME)
                                .first(ClickReportConstant.LANGUAGE).as(ReportHomePageOrderBO.LANGUAGE)
                                .first(ClickReportConstant.BUSINESS_LINE).as(ReportHomePageOrderBO.BUSINESS_LINE)
                                .first(ClickReportConstant.BUSINESS_NAME).as(ReportHomePageOrderBO.BUSINESS_NAME)
                                .count().as(ReportHomePageOrderBO.HOME_PAGE_ORDER_COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportHomePageOrderBO> homePageOrder = mongoTemplate.aggregate(homeAgg, MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, ReportHomePageOrderBO.class).getMappedResults();

        //统计下单总数-按语言分
        Criteria dwdLanguageOrderCriteria = Criteria.where(DwdAggregateOrderDO.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                .and(DwdAggregateOrderDO.BUSINESS_LINE).in(BusinessLineEnum.YUMNOW.getCode(), BusinessLineEnum.TINHNOW.getCode());
        Aggregation dwdLanguageOrderAgg = Aggregation.newAggregation(
                        Aggregation.match(dwdLanguageOrderCriteria),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE, DwdAggregateOrderDO.BUSINESS_LINE)
                                .first(ReportHomePageOrderBO.LANGUAGE).as(ReportHomePageOrderBO.LANGUAGE)
                                .first(ReportHomePageOrderBO.BUSINESS_LINE).as(ReportHomePageOrderBO.BUSINESS_LINE)
                                .count().as(ReportAggregateOrderBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportAggregateOrderBO> dwdLanguageOrderCount = mongoTemplate.aggregate(dwdLanguageOrderAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderBO.class).getMappedResults();

        Map<String, Integer> dwdLanguageOrderCountMap = new HashMap<>();
        for (ReportAggregateOrderBO bo : dwdLanguageOrderCount) {
            dwdLanguageOrderCountMap.put(bo.getBusinessLine() + bo.getLanguage(), bo.getCount());
        }

        //统计下单总数-不按语言分
        Criteria dwdOrderCriteria = Criteria.where(DwdAggregateOrderDO.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                .and(DwdAggregateOrderDO.BUSINESS_LINE).in(BusinessLineEnum.YUMNOW.getCode(), BusinessLineEnum.TINHNOW.getCode());
        Aggregation dwdOrderAgg = Aggregation.newAggregation(
                        Aggregation.match(dwdOrderCriteria),
                        Aggregation.group(DwdAggregateOrderDO.BUSINESS_LINE)
                                .first(ReportHomePageOrderBO.BUSINESS_LINE).as(ReportHomePageOrderBO.BUSINESS_LINE)
                                .count().as(ReportAggregateOrderBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportAggregateOrderBO> dwdOrderCount = mongoTemplate.aggregate(dwdOrderAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportAggregateOrderBO.class).getMappedResults();

        Map<String, Integer> dwdOrderCountMap = new HashMap<>();
        for (ReportAggregateOrderBO bo : dwdOrderCount) {
            dwdOrderCountMap.put(bo.getBusinessLine(), bo.getCount());
        }

        //查询首页PV/UV数据
        Criteria criteria = Criteria.where(ReportHomePageViewBO.DATA_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                .and(ReportHomePageViewBO.BUSINESS_LINE).is(EventPageBusinessLineEnum.SUPERAPP.getCode())
                .and(ReportHomePageViewBO.BUSINESS_NAME).is("WOWNOW首页");
        List<ReportHomePageViewBO> homePageViews = mongoTemplate.find(Query.query(criteria), ReportHomePageViewBO.class, MongoDbCollectonName.REPORT_HOME_PAGE_VIEW);
        Map<String, ReportHomePageViewBO> homePageViewMap = new HashMap<>();
        for(ReportHomePageViewBO bo : homePageViews) {
            homePageViewMap.put(bo.getLanguage(), bo);
        }

        for(ReportHomePageOrderBO homeOrder : homePageOrder) {
            homeOrder.setDataTime(dateDTO.getDataTime());
            homeOrder.setTotalOrderCount(dwdOrderCountMap.get(homeOrder.getBusinessLine()));
            homeOrder.setTotalLanguageOrderCount(dwdLanguageOrderCountMap.get(homeOrder.getBusinessLine() + homeOrder.getLanguage()));

            //查询完成的订单数
            List<String> orderNoList = mongoTemplate.findDistinct(new Query().addCriteria(
                            Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                                    .and(ClickReportConstant.EVENT).is(ClickReportConstant.ORDER_SUBMIT_EVEN)
                                    .and(ClickReportConstant.LANGUAGE).is(homeOrder.getLanguage())
                                    .and(ClickReportConstant.BUSINESS_NAME).is(homeOrder.getBusinessName())),
                    ClickReportConstant.EXT_ORDERNO,
                    MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, String.class);

            Criteria dwdFinishOrderCriteria = Criteria.where(DwdAggregateOrderDO.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                    .and(DwdAggregateOrderDO.BUSINESS_LINE).is(homeOrder.getBusinessLine())
                    .and(DwdAggregateOrderDO.AGGREGATE_ORDER_FINAL_STATE).is(AggregateOrderFinalStateEnum.COMPLETE.getCode())
                    .and(DwdAggregateOrderDO.AGGREGATE_ORDER_NO).in(orderNoList);
            long dwdFinishOrderCount = mongoTemplate.count(new Query().addCriteria(dwdFinishOrderCriteria), MongoDbCollectonName.DWD_AGGREGATE_ORDER);
            homeOrder.setCompleteOrderCount(dwdFinishOrderCount);


            Query query = new Query();
            query.addCriteria(Criteria.where(ReportHomePageOrderBO.DATA_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                    .and(ReportHomePageOrderBO.BUSINESS_NAME).is(homeOrder.getBusinessName())
                    .and(ReportHomePageOrderBO.BUSINESS_LINE).is(homeOrder.getBusinessLine())
                    .and(ReportHomePageOrderBO.LANGUAGE).is(homeOrder.getLanguage()));

            Update update = new Update();
            update.set(ReportHomePageOrderBO.CREATE_TIME, new Date());
            update.set(ReportHomePageOrderBO.DATA_TIME, homeOrder.getDataTime());
            update.set(ReportHomePageOrderBO.LANGUAGE, homeOrder.getLanguage());
            update.set(ReportHomePageOrderBO.BUSINESS_LINE, homeOrder.getBusinessLine());
            update.set(ReportHomePageOrderBO.BUSINESS_NAME, homeOrder.getBusinessName());
            update.set(ReportHomePageOrderBO.TOTAL_LANGUAGE_ORDER_COUNT,homeOrder.getTotalLanguageOrderCount());
            update.set(ReportHomePageOrderBO.TOTAL_ORDER_COUNT, homeOrder.getTotalOrderCount());
            update.set(ReportHomePageOrderBO.HOME_PAGE_ORDER_COUNT, homeOrder.getHomePageOrderCount());
            update.set(ReportHomePageOrderBO.COMPLETEL_ORDER_COUNT, homeOrder.getCompleteOrderCount());
            update.set(ReportHomePageOrderBO.HOME_PAGE_DV, Optional.ofNullable(homePageViewMap.get(homeOrder.getLanguage())).orElse(new ReportHomePageViewBO()).getDv());
            update.set(ReportHomePageOrderBO.HOME_PAGE_UV, Optional.ofNullable(homePageViewMap.get(homeOrder.getLanguage())).orElse(new ReportHomePageViewBO()).getUv());
            update.set(ReportHomePageOrderBO.HOME_PAGE_PV, Optional.ofNullable(homePageViewMap.get(homeOrder.getLanguage())).orElse(new ReportHomePageViewBO()).getPv());
            mongoTemplate.upsert(query, update, MongoDbCollectonName.REPORT_HOME_PAGE_ORDER);
        }
    }

    @Override
    public void staticYumNowStoreOrderRateReport(ReportDateDTO dateDTO) {
        Date startTime = dateDTO.getStartTime();
        Date endTime = dateDTO.getEndTime();

        //日活用户数 （用户维度）
        Query queryActiveQuery = Query.query(Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(startTime).lte(endTime)
                .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT.getCode()));
        List<ReportLanguageDayBO> userActiveCountReport = mongoTemplate.find(queryActiveQuery, ReportLanguageDayBO.class);

        //转换成map
        Map<String, Long> userActiveMapuserActiveMap;
        if(CollectionUtils.isNotEmpty(userActiveCountReport)) {
            userActiveMapuserActiveMap = userActiveCountReport.stream().collect(Collectors.toMap(ReportLanguageDayBO::getLanguage,ReportLanguageDayBO::getCount));
        } else {
            userActiveMapuserActiveMap = new HashMap<>();
        }

        //门店页浏览用户数（用户维度）
        Criteria storeUserCriteria = Criteria.where(ReportYumNowStoreOrderConversionRateBO.CREATE_TIME).gte(startTime).lte(endTime);
        Aggregation storeUserAggregation = Aggregation.newAggregation(
                        Aggregation.match(storeUserCriteria),
                        Aggregation.group(ClickReportConstant.LANGUAGE, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                                .first(ClickReportConstant.LANGUAGE).as(ReportYumNowStoreOrderConversionRateBO.LANGUAGE)
                                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO),
                        Aggregation.group(ReportYumNowStoreOrderConversionRateBO.LANGUAGE)
                                .first(ReportYumNowStoreOrderConversionRateBO.LANGUAGE).as(ReportYumNowStoreOrderConversionRateBO.LANGUAGE)
                                .count().as(ReportYumNowStoreOrderConversionRateBO.STORE_VIEW_UV))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportYumNowStoreOrderConversionRateBO> storeUserList = mongoTemplate.aggregate(storeUserAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_STORE_DETAIL_PV, ReportYumNowStoreOrderConversionRateBO.class).getMappedResults();
        //转换成map
        Map<String, Long> storeUserMap;
        if(CollectionUtils.isNotEmpty(storeUserList)) {
            storeUserMap = storeUserList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getStoreViewUv));
        } else {
            storeUserMap = new HashMap<>();
        }

        //下单用户数（用户维度）
        Criteria orderUserCountCriteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                .and(DwdAggregateOrderDO.BUSINESS_LINE).is(BusinessLineEnum.YUMNOW.getCode())
                .and(DwdAggregateOrderDO.FIRST_MERCHANT_NO).ne("M11250139");
        Aggregation orderUserCountCriteriaAggregation = Aggregation.newAggregation(
                        Aggregation.match(orderUserCountCriteria),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE, DwdAggregateOrderDO.OPERATOR_NO)
                                .first(DwdAggregateOrderDO.LANGUAGE).as(DwdAggregateOrderDO.LANGUAGE)
                                .first(DwdAggregateOrderDO.OPERATOR_NO).as(DwdAggregateOrderDO.OPERATOR_NO),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                .first(DwdAggregateOrderDO.LANGUAGE).as(ReportYumNowStoreOrderConversionRateBO.LANGUAGE)
                                .count().as(ReportYumNowStoreOrderConversionRateBO.ORDER_USER_COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportYumNowStoreOrderConversionRateBO> orderUserCountList = mongoTemplate.aggregate(orderUserCountCriteriaAggregation,
                MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportYumNowStoreOrderConversionRateBO.class).getMappedResults();
        //转换成map
        Map<String, Long> orderUserCountMap;
        if(CollectionUtils.isNotEmpty(orderUserCountList)) {
            orderUserCountMap = orderUserCountList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getOrderUserCount));
        } else {
            orderUserCountMap = new HashMap<>();
        }

        //外卖订单支付数 （订单维度）
        Criteria orderPaidCountCriteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                .and(DwdAggregateOrderDO.BUSINESS_LINE).is(BusinessLineEnum.YUMNOW.getCode())
                .and(DwdAggregateOrderDO.AGGREGATE_ORDER_STATE).in(11,14)
                .and(DwdAggregateOrderDO.FIRST_MERCHANT_NO).ne("M11250139");
        Aggregation orderPaidCountCriteriaAggregation = Aggregation.newAggregation(
                        Aggregation.match(orderPaidCountCriteria),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                .first(DwdAggregateOrderDO.LANGUAGE).as(DwdAggregateOrderDO.LANGUAGE)
                                .count().as(ReportYumNowStoreOrderConversionRateBO.ORDER_PAID_COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportYumNowStoreOrderConversionRateBO> orderPaidCountList = mongoTemplate.aggregate(orderPaidCountCriteriaAggregation,
                MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportYumNowStoreOrderConversionRateBO.class).getMappedResults();
        //转换成map
        Map<String, Long> orderPaidCountMap;
        if(CollectionUtils.isNotEmpty(orderPaidCountList)) {
            orderPaidCountMap = orderPaidCountList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getOrderPaidCount));
        } else {
            orderPaidCountMap = new HashMap<>();
        }

        //外卖总订单数 （订单维度）
        Criteria orderTotalCountCriteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                .and(DwdAggregateOrderDO.BUSINESS_LINE).is(BusinessLineEnum.YUMNOW.getCode())
                .and(DwdAggregateOrderDO.FIRST_MERCHANT_NO).ne("M11250139");
        Aggregation orderTotalCountCriteriaAggregation = Aggregation.newAggregation(
                        Aggregation.match(orderTotalCountCriteria),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                .first(DwdAggregateOrderDO.LANGUAGE).as(DwdAggregateOrderDO.LANGUAGE)
                                .count().as(ReportYumNowStoreOrderConversionRateBO.ORDER_TOTAL_COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportYumNowStoreOrderConversionRateBO> orderTotalCountList = mongoTemplate.aggregate(orderTotalCountCriteriaAggregation,
                MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportYumNowStoreOrderConversionRateBO.class).getMappedResults();
        //转换成map
        Map<String, Long> orderTotalCountMap;
        if(CollectionUtils.isNotEmpty(orderTotalCountList)) {
            orderTotalCountMap = orderTotalCountList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getOrderTotalCount));
        } else {
            orderTotalCountMap = new HashMap<>();
        }

        //订单均价（订单维度）
        Criteria averagePriceCriteria = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(startTime).lte(endTime)
                .and(DwdAggregateOrderDO.BUSINESS_LINE).is(BusinessLineEnum.YUMNOW.getCode())
                .and(DwdAggregateOrderDO.FIRST_MERCHANT_NO).ne("M11250139");
        Aggregation averagePriceCriteriaAggregation = Aggregation.newAggregation(
                        Aggregation.match(averagePriceCriteria),
                        Aggregation.group(DwdAggregateOrderDO.LANGUAGE)
                                .first(DwdAggregateOrderDO.LANGUAGE).as(DwdAggregateOrderDO.LANGUAGE)
                                .avg(DwdAggregateOrderDO.ACTUAL_PAY_AMOUNT).as(ReportYumNowStoreOrderConversionRateBO.ACTUAL_PAY_AVERAGE_PRICE)
                                .avg(DwdAggregateOrderDO.TOTAL_PAYABLE_AMOUNT).as(ReportYumNowStoreOrderConversionRateBO.TOTAL_PAYABLE_AVERAGE_PRICE))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportYumNowStoreOrderConversionRateBO> averagePriceList = mongoTemplate.aggregate(averagePriceCriteriaAggregation,
                MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportYumNowStoreOrderConversionRateBO.class).getMappedResults();


        //转换成map
        Map<String, Double> actualPayAveragePriceMap, totalPayableAverageMap;
        if(CollectionUtils.isNotEmpty(averagePriceList)) {
            actualPayAveragePriceMap = averagePriceList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getActualPayAveragePrice));
            totalPayableAverageMap = averagePriceList.stream().collect(Collectors.toMap(ReportYumNowStoreOrderConversionRateBO::getLanguage,ReportYumNowStoreOrderConversionRateBO::getTotalPayableAveragePrice));

        } else {
            actualPayAveragePriceMap = new HashMap<>();
            totalPayableAverageMap = new HashMap<>();
        }

        ReportYumNowStoreOrderConversionRateBO bo = new ReportYumNowStoreOrderConversionRateBO();

        for(LanguageEnum language : LanguageEnum.values()) {
            bo.setLanguage(language.getCode());
            bo.setCreateTime(new Date());
            bo.setDataTime(dateDTO.getDataTime());
            //日活用户数 （用户维度）
            bo.setDau(Optional.ofNullable(userActiveMapuserActiveMap.get(language.getCode())).orElse(0L));

            //门店页浏览用户数（用户维度）
            bo.setStoreViewUv(Optional.ofNullable(storeUserMap.get(language.getCode())).orElse(0L));

            //门店进入率 = 门店页浏览用户数 / 日活用户数 （用户维度）
            bo.setStoreViewRate(getRate(bo.getStoreViewUv(), bo.getDau()));

            //下单用户数（用户维度）
            bo.setOrderUserCount(Optional.ofNullable(orderUserCountMap.get(language.getCode())).orElse(0L));

            //门店页下单转化率（用户维度）= 下单用户数（用户维度）/ 门店页浏览用户数
            bo.setOrderStoreUserRate(getRate(bo.getOrderUserCount(), bo.getStoreViewUv()));

            //外卖订单支付数 （订单维度）
            bo.setOrderPaidCount(Optional.ofNullable(orderPaidCountMap.get(language.getCode())).orElse(0L));

            //外卖总订单数 （订单维度）
            bo.setOrderTotalCount(Optional.ofNullable(orderTotalCountMap.get(language.getCode())).orElse(0L));

            //外卖订单支付率 = 外卖订单支付数 / 外卖总订单数 （订单维度）
            bo.setOrderPaidRate(getRate(bo.getOrderPaidCount(), bo.getOrderTotalCount()));

            //订单均价（订单维度）实付金额
            bo.setActualPayAveragePrice(getDoubleFormat(Optional.ofNullable(actualPayAveragePriceMap.get(language.getCode())).orElse(0D)));

            //订单均价（订单维度）应付金额
            bo.setTotalPayableAveragePrice(getDoubleFormat(Optional.ofNullable(totalPayableAverageMap.get(language.getCode())).orElse(0D)));
            log.info("统计外卖订单报表数据为:{}", bo);

            Query yumNowOrdersQuery = new Query();
            yumNowOrdersQuery.addCriteria(Criteria.where(ReportYumNowStoreOrderConversionRateBO.DATA_TIME).gte(startTime).lt(endTime)
                    .and(ReportYumNowStoreOrderConversionRateBO.LANGUAGE).is(language));

            //入库
            mongoTemplate.upsert(yumNowOrdersQuery, getYumNowReport(bo), MongoDbCollectonName.REPORT_YUMNOW_STORE_ORDER_CONVERSION_RATE);
        }
    }

    public Update getYumNowReport(ReportYumNowStoreOrderConversionRateBO bo) {
        Update update = new Update();
        update.set(ReportYumNowStoreOrderConversionRateBO.CREATE_TIME, new Date());
        update.set(ReportYumNowStoreOrderConversionRateBO.DATA_TIME, bo.getDataTime());
        update.set(ReportYumNowStoreOrderConversionRateBO.LANGUAGE, bo.getLanguage());
        update.set(ReportYumNowStoreOrderConversionRateBO.STORE_VIEW_UV, bo.getStoreViewUv());
        update.set(ReportYumNowStoreOrderConversionRateBO.DAU, bo.getDau());
        update.set(ReportYumNowStoreOrderConversionRateBO.STORE_VIEW_RATE, bo.getStoreViewRate());
        update.set(ReportYumNowStoreOrderConversionRateBO.ORDER_USER_COUNT, bo.getOrderUserCount());
        update.set(ReportYumNowStoreOrderConversionRateBO.ORDER_STORE_USER_RATE, bo.getOrderStoreUserRate());
        update.set(ReportYumNowStoreOrderConversionRateBO.ORDER_PAID_COUNT, bo.getOrderPaidCount());
        update.set(ReportYumNowStoreOrderConversionRateBO.ORDER_TOTAL_COUNT, bo.getOrderTotalCount());
        update.set(ReportYumNowStoreOrderConversionRateBO.ORDER_PAID_RATE, bo.getOrderPaidRate());
        update.set(ReportYumNowStoreOrderConversionRateBO.ACTUAL_PAY_AVERAGE_PRICE, bo.getActualPayAveragePrice());
        update.set(ReportYumNowStoreOrderConversionRateBO.TOTAL_PAYABLE_AVERAGE_PRICE, bo.getTotalPayableAveragePrice());
        return update;
    }

    /**
     * 返回结果: firstValue/secondValue
     */
    public double getRate(Long firstValue, Long secondValue) {
        try {
            DecimalFormat df = new DecimalFormat("#.####");
            return Double.parseDouble(df.format((double) firstValue /secondValue));
        } catch (Exception e) {
            log.error("异常", e);
        }
        return 0D;
    }

    public double getDoubleFormat(double value) {
        try {
            DecimalFormat df = new DecimalFormat("#.##");
            return Double.parseDouble(df.format(value));
        } catch (Exception e) {
            log.error("异常", e);
        }
        return 0D;
    }

}
