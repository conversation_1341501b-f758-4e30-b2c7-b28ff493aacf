package com.lifekh.data.warehouse.report;

import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;

@Slf4j
public abstract class AbstractReportBasic implements ReportBasic {

    public static String[] WOWNOW_HOME_PAGE_ARR = ClickReportConstant.WOWNOW_HOME_PAGE_LIST;

    @Override
    public void statistics(Integer item, Integer total) {
        //计算昨天日期
        int dayOffset = 1;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);
        log.debug("计算昨天日期 startTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime);
        //统计昨天数据
        try {
            this.statisticsByDay(new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday));
            log.info("标准点击事件日数据统计完成, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime);
        } catch (Exception e) {
            log.warn("统计昨天标准点击事件异常, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime, e);
        }

        //计算当周日期
        Date weekStartTime = DateUtil.getFirstDateOfWeek(yesterday);
        Date weekEndTime = DateUtil.getLastDateOfWeek(yesterday);
        log.debug("计算当周日期 weekStartTime:{}, weekEndTime:{}", weekStartTime, weekEndTime);
        //统计当周数据
        try {
            this.statisticsByWeek(new ReportBasicReqDTO(weekStartTime, weekEndTime, yesterday));
            log.info("标准点击事件周数据统计完成, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime);
        } catch (Exception e) {
            log.warn("计算当周标准点击事件异常, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime, e);
        }

        //计算当月日期
        Date monthStartTime = DateUtil.getFirstDayDateOfMonth(yesterday);
        Date monthEndTime = DateUtil.getLastDayOfMonth(yesterday);
        log.debug("计算当月日期 monthStartTime:{}, monthEndTime:{}", monthStartTime, monthEndTime);
        //统计当月数据
        try {
            this.statisticsByMonth(new ReportBasicReqDTO(monthStartTime, monthEndTime, yesterday));
            log.info("标准点击事件月数据统计完成, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime);
        } catch (Exception e) {
            log.warn("计算当月标准点击事件异常, beginTime:{}, endTime:{}", yesterdayStartTime, yesterdayEndTime, e);
        }
    }
}
