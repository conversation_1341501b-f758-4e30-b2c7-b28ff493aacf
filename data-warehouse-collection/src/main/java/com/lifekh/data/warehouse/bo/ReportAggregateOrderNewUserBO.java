package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.bson.types.Decimal128;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_AGGREGATE_ORDER_LANGUAGE_NEW_USER)
@Data
@Accessors(chain = true)
public class ReportAggregateOrderNewUserBO implements Serializable {
    private static final long serialVersionUID = -7202233901113337740L;

    public static final String DATA_TIME = "dataTime";
    public static final String BUSINESS_LINE = "businessLine";
    public static final String LANGUAGE = "language";
    public static final String PROVINCE = "province";
    public static final String COUNT = "count";
    public static final String USER_COUNT = "userCount";
    public static final String ACTUAL_AMOUNT = "actualAmount";
    public static final String TOTAL_AMOUNT = "totalAmount";
    public static final String AGGREGATE_ORDER_FINAL_STATE = "aggregateOrderFinalState";

    private Date dataTime;
    private String businessLine;
    private String language;
    private String province;
    private Integer count;
    private Integer userCount;
    private Decimal128 actualAmount;
    private Decimal128 totalAmount;
    private Integer aggregateOrderFinalState;
    private Date createTime;
}
