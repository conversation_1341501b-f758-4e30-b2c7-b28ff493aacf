package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportLocationDataTypeEnum implements BaseEnum<ReportLocationDataTypeEnum, String> {

    ACTIVE_USER_COUNT("active_user_count", "活跃用户数"),
    NEW_USER_COUNT("new_user_count", "新增用户数"),
    ;

    private String code;

    private String message;
}
