package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.collection.CollectBaseBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = MongoDbCollectonName.INVITE_REGISTER_RECORD)
public class InviteRegisterRecordBO extends CollectBaseBO implements Serializable {
    private static final long serialVersionUID = 8487394164950881001L;
    @Id
    private String id;
    /**
     * 业务名称
     */
    private String businessName;
    /**
     * 应用编号
     */
    private String appNo;

    /**
     * 子页
     */
    private String childPage;

    /**
     * 上级页面
     */
    private String parentPage;

    /**
     * 当前页面
     */
    private String currentPage;

    /**
     * 当前区域
     */
    private String currentArea;
    /**
     * 当前节点
     */
    private String node;

    /**
     * 当前区域所处页面位置---广告点击事件时传
     */
    private Integer currentAreaLocation;

    /**
     * 当前节点所处卡片位置---广告点击事件时传
     */
    private Integer currentNodeLocation;

    /**
     * 停留时间
     */
    private Long stayTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 计数
     */
    private Integer count;

    /**
     * 业务名称全称
     */
    private String fullBusinessName;

    /**
     * 活动编号
     */
    private String activityNo;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 跳转路径
     */
    private String from;
}
