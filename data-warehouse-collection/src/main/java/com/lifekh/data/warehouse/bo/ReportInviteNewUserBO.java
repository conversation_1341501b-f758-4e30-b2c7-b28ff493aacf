package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_INVITE_NEW_USER)
@Data
@Accessors(chain = true)
public class ReportInviteNewUserBO implements Serializable {
    private static final long serialVersionUID = 7055774984564271538L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 进入注册页面数
     */
    private Long registerPageCount;

    /**
     * 点击立即使用次数
     */
    private Long clickGouseCount;

    /**
     * 进入下载页面数
     */
    private Long downloadPageCount;

    /**
     * 点击下载按钮次数
     */
    private Long clickDownloadCount;

    /**
     * 注册成功用户数
     */
    private Long registerCount;

    private Date createTime;

    private Date updateTime;

    private Integer version;

}
