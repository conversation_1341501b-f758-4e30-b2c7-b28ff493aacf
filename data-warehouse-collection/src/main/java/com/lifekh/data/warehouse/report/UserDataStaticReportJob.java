package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.LanguageReportStrategy;
import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.lifekh.data.warehouse.report.service.UserReportStaticService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;


/**
 * 每天4点统计新增用户，活跃用户，留存数据
 * <AUTHOR>
 * @Date 2021/11/5 10:13
 * @Version 1.0
 **/
@Slf4j
@ElasticJobConf(name = "user_report_static", cron = "30 0 0 * * ?", description = "统计新增用户，活跃用户，留存数据",shardingTotalCount = 1)
public class UserDataStaticReportJob extends AbstractSimpleJob {

    @Autowired
    private UserReportStaticService userReportStaticService;

    @Autowired
    private TargetReportService targetReportService;

    @Autowired
    private LanguageReportStrategy languageReportStrategy;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        //统计新增用户
        try {
            userReportStaticService.newUserReportStatic();
            log.info("统计新增用户结束");
        } catch (Exception e){
            log.error("统计新增用户异常", e);
        }

        //统计活跃用户
        try {
            log.info("统计活跃用户开始");
            userReportStaticService.activeUserReportStatic();

            // 语言每日数据统计
            log.info("语言每日数据统计开始");
            languageReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
            log.info("统计活跃用户结束");
        } catch (Exception e){
            log.error("统计活跃用户异常", e);
        }

        try {
            log.info("统计1, 7, 30活跃用户开始");
            targetReportService.staticActiveUserCount(Arrays.asList(1, 7, 30));

            //统计粘性值
            log.info("统计粘性值开始");
            targetReportService.staticActiveUserStickiness();
        } catch (Exception e) {
            log.error("统计1, 7, 30天的活跃用户异常", e);
        }

        try {
            //统计流失用户数
            log.info("统计流失用户数开始");
            targetReportService.statisticsLossUser();
            log.info("统计流失用户数结束");
        } catch (Exception e) {
            log.error("统计流失用户数", e);
        }

        //统计用户留存
        try {
            log.info("统计留存用户开始");
            userReportStaticService.userRemainReportStatic();
            log.info("统计留存用户结束");
        } catch (Exception e){
            log.error("统计用户留存异常", e);
        }

        try {
            //统计1, 7, 30天的留存用户
            log.info("统计1, 7, 30天的留存用户开始");
            targetReportService.staticRemainUserCount(Arrays.asList(1, 7, 30));
            log.info("统计1, 7, 30天的留存用户结束");
        } catch (Exception e) {
            log.error("统计1, 7, 30天的留存用户异常", e);
        }

        try {
            //统计前一天登录方式
            log.info("统计前一天登录方式开始");
            userReportStaticService.userLoginMethodReport();
            log.info("统计前一天登录方式结束");
        } catch (Exception e) {
            log.error("统计前一天登录方式异常", e);
        }

        try {
            //统计前一天注册方式
            log.info("统计前一天注册方式开始");
            userReportStaticService.userRegisterMethodReport();
            log.info("统计前一天注册方式结束");
        } catch (Exception e) {
            log.error("统计前一天注册方式异常", e);
        }
    }
}
