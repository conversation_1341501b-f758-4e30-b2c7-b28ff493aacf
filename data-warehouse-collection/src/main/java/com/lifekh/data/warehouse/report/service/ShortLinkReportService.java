package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.api.req.ShortLinkReportQueryReqDTO;
import com.lifekh.data.warehouse.api.resp.ShortLinkReportRespDTO;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

public interface ShortLinkReportService {

    /**
     * 统计短链报表
     */
    void statisticsShortLinkReport(ReportDateDTO dateDTO);

    /**
     * 查询短链报表
     */
    PageInfoDTO<ShortLinkReportRespDTO> queryReport(ShortLinkReportQueryReqDTO reqDTO) throws PendingException;
}
