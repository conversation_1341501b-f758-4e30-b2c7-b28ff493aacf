package com.lifekh.data.warehouse.report;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.report.service.DwdAggregateOrderService;
import com.lifekh.data.warehouse.report.service.PageViewReportService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * dwd聚合订单
 * 在dwd_user_behavior_info_job之后执行
 */
@Slf4j
@ElasticJobConf(name = "dwd_aggregate_order_job", cron = "0 20 5 * * ?", description = "统计聚合订单数据", shardingTotalCount = 1)
public class DwdAggregateOrderJob extends AbstractSimpleJob {

    @Autowired
    private DwdAggregateOrderService dwdAggregateOrderService;
    @Autowired
    private PageViewReportService pageViewReportService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始同步dwd聚合订单数据");
        try {
            //传时间范围参数
            if (StringUtils.isNotBlank(shardingContext.getJobParameter())) {
                String dateFmt = "yyyyMMdd";
                String dateArr[] = shardingContext.getJobParameter().split("-");
                Date startDate = DateUtil.parse(dateArr[0], dateFmt);
                Date endDate = DateUtil.parse(dateArr[1], dateFmt);
                //按天查询
                while (DateUtil.compare(startDate, endDate, dateFmt) <= 0) {
                    Date startTime = DateUtil.beginOfDay(startDate);
                    Date endTime = DateUtil.endOfDay(startDate);
                    dwdAggregateOrderService.syncDwdAggregateOrder(startTime, endTime);

                    //天数加1
                    startDate = DateUtil.offsetDay(startDate, 1);
                }
                //结束job
                return;
            } else {
                Date yesterday = DateUtil.yesterday();
                Date startTime = DateUtil.beginOfDay(yesterday);
                Date endTime = DateUtil.endOfDay(yesterday);
                dwdAggregateOrderService.syncDwdAggregateOrder(startTime, endTime);
                dwdAggregateOrderService.updateDwdAggregateOrder(startTime, endTime);
            }
        } catch (Exception e) {
            log.error("同步dwd聚合订单数据出现异常", e);
        }
        log.info("结束同步dwd聚合订单数据");

        try {
            dwdAggregateOrderService.staticOrderReport();
        } catch (Exception e) {
            log.error("统计聚合订单报表出现异常", e);
        }
        log.info("统计聚合订单报表结束");

        try {
            log.info("开始首页流量转化统计");
            pageViewReportService.homeFlowConversionStatistics();
            log.info("结束首页流量转化统计");
        } catch (Exception e) {
            log.error("首页流量转化统计出现异常", e);
        }

        try {
            log.info("开始新设备首页流量转化统计");
            pageViewReportService.newDeviceConversionRate();
            log.info("结束新设备首页流量转化统计");
        } catch (Exception e) {
            log.error("新设备首页流量转化统计出现异常", e);
        }

        try {
            //从PageViewReportJob迁移过来
            log.info("开始删除临时表");
            pageViewReportService.deleteViewEventRecord();
            log.info("结束删除临时表");
        } catch (Exception e) {
            log.error("清除浏览事件临时表", e);
        }

        try {
            //统计首页下单
            log.info("开始统计首页下单");
            dwdAggregateOrderService.staticHomePageOrderReport();
            log.info("结束统计首页下单");
        } catch (Exception e) {
            log.error("统计首页下单报表", e);
        }

        try {
            //统计外卖订单转化率
            log.info("开始统计外卖订单转化率");
            dwdAggregateOrderService.staticYumNowStoreOrderRateReport(ReportDateDTO.yesterday());
            log.info("结束统计外卖订单转化率");
        } catch (Exception e) {
            log.error("统计外卖订单转化率异常", e);
        }
        log.info("聚合订单DWD表处理结束");
    }

}
