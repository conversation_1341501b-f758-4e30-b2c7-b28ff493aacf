package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOME_FLOW_CONVERSION)
@Data
@Accessors(chain = true)
public class ReportHomeFlowConversionBO implements Serializable {
    private static final long serialVersionUID = 6192353663647048926L;

    public static final String LANGUAGE = "language";
    public static final String ORDER_USER_COUNT = "orderUserCount";
    public static final String COMPLETE_ORDER_USER_COUNT = "completeOrderUserCount";
    public static final String BUSINESS_LINE = "businessLine";


    private Date dataTime;
    private String language;
    private String businessLine;
    private String businessName;
    private Integer homePv;
    private Integer homeUv;
    private Integer activeUserCount;
    private Integer businessPv;
    private Integer businessUv;
    private Double businessPvRate;
    private Double businessUvRate;
    private Integer orderUserCount;
    private Double orderUserCountRate;
    private Integer completeOrderUserCount;
    private Double completeOrderUserCountRate;
    private Date createTime;
}
