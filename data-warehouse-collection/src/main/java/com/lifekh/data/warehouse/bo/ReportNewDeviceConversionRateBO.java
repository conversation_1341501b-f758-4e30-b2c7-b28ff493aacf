package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_NEW_DEVICE_CONVERSION_RATE)
@Data
public class ReportNewDeviceConversionRateBO implements Serializable  {

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 业务线
     */
    private String businessLine;
    public static String BUSINESS_LINE = "businessLine";



    /**
     * homePageDv
     */
    private Integer homePagedv;

    public static String HOME_PAGE_DV = "homePageDv";

    /**
     * homePageBizDv
     */
    private Integer homePageBizdv;

    public static String HOME_PAGE_BIZ_DV = "homePageBizDv";


    /**
     * homePageBizOrderDv
     */
    private Integer homePageBizOrderDv;

    public static String HOME_PAGE_BIZ_ORDER_DV = "homePageBizOrderDv";

    /**
     * homePageBizOrderFinishDv
     */
    private Integer homePageBizOrderFinishDv;

    public static String HOME_PAGE_BIZ_ORDER_FINISH_DV = "homePageBizOrderFinishDv";

    /**
     * 语言
     */
    private String language;

    public static String LANGUAGE = "language";
}
