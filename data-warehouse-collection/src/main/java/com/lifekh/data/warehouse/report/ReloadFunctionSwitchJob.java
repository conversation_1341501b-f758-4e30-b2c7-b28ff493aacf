package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.manage.FunctionSwitchManager;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ElasticJobConf(name = "reload_function_switch_config", cron = "3/33 * * * * ?", description = "埋点功能开关配置加载定时任务", shardingTotalCount = 2)
public class ReloadFunctionSwitchJob extends AbstractSimpleJob {

    @Autowired
    private FunctionSwitchManager functionSwitchManager;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        functionSwitchManager.init();
    }
}
