package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_TAKEAWAY_KING_KONG_FLOW_CONVERSION)
@Data
@Accessors(chain = true)
public class ReportTakeawayKingKongFlowConversionBO extends ReportTakeawayFlowConversionBO implements Serializable {
    private static final long serialVersionUID = 6192353663647048926L;

    private String areaName;

    public String getGroupKey() {
        return getAreaName() + "_" + getLanguage() + "_" + getDeviceType() + "_" + getAppVersion() + "_" + getProvinceName();
    }
}
