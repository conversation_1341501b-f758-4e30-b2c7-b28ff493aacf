package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.enums.ReportLocationDataTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_LOCATION_DAY)
@Data
@Accessors(chain = true)
public class ReportLocationDayBO implements Serializable {
    private static final long serialVersionUID = 8107859918954474996L;

    @Id
    private String id;

    private String operatorNo;
    public final static String OPERATOR_NO = "operatorNo";

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 数据类型 {@link ReportLocationDataTypeEnum}
     */
    private String dataType;

    /**
     * 数据类型名称
     */
    private String dataTypeName;

    /**
     * 地区
     */
    private String location;
    public final static String LOCATION = "location";


    /**
     * 数量统计
     */
    private Long count;
    public final static String COUNT = "count";

    private Date createTime;

    private Date updateTime;

}
