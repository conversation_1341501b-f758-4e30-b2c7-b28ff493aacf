package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_SMS_CHANNEL_RATE)
@Data
@Accessors(chain = true)
public class ReportSmsChannelRateBO implements Serializable {
    private static final long serialVersionUID = -2049795582598557512L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 发送渠道
     */
    private String sendChannel;

    /**
     * 已验证成功数量
     */
    private Integer usedCount;
    public final static String USEDCOUNT = "usedCount";

    /**
     * 未验证成功数量
     */
    private Integer notUsedCount;
    public final static String NOTUSEDCOUNT = "notUsedCount";

    /**
     * 验证通过率
     */
    private Double rate;
    public final static String RATE = "rate";

    /**
     * 总数
     */
    private Integer count;
    public final static String COUNT = "count";

    /**
     * 运营商名称
     */
    private String carrierName;
    public final static String CARRIER_NAME = "carrierName";

    private Date createTime;

    private Date updateTime;
}
