package com.lifekh.data.warehouse.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InviteRegisterButNotOpenAppBO implements Serializable{
    private static final long serialVersionUID = 3502779802543172008L;

    /**
     * 登录号
     */
    private String loginName;

    public static String LOGIN_NAME = "loginName";

    /**
     * 状态
     */
    private String status;

    public static String STATUS = "status";

    /**
     * 渠道
     */
    private String channel;

    public static String CHANNEL = "channel";

    /**
     * 语言
     */
    public String language;

    public static String  LANGUAGE = "language";

    /**
     * 注册时间
     */
    private Date registerTime;

    public static String REGISTER_TIME = "registerTime";


    private Date createTime;

    public static String CREATE_TIME = "createTime";

    private Date updateTime;

    public static String UPDATE_TIME = "updateTime";

    private String source;

    public static String SOURCE = "source";

    private String from;

    public static String FROM = "from";

    /**
     * 活动编号
     */
    private String activityNo;

    public static String ACTIVITY_NO = "activityNo";

    /**
     * 实际注册时间
     */
    private Date actualRegisterTime;

    public static String ACTUAL_REGISTER_TIME = "actualRegisterTime";

}
