package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.ClickHomePageNodeRateReportDayBO;
import com.lifekh.data.warehouse.bo.ClickReportDayBO;
import com.lifekh.data.warehouse.bo.ClickReportWeekBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class HomePageClickReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
        try {
            PageRespDTO<ClickReportDayBO> pageRespDTO = mongoPageHelper.query(
                    getHomePageAggregationCriteria(reportBasicReqDTO), new PageReqDTO(0, 10), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY, true, null, ClickReportDayBO.class);

            if (pageRespDTO == null || pageRespDTO.getTotal() < 1) {
                return null;
            }
            Long homePagePv = 0L, homePageUv = 0L, homePageDv = 0L;
            List<ClickReportDayBO> homeList = pageRespDTO.getList();

            for(ClickReportDayBO homePageBo : homeList) {
                try {
                    homePagePv = homePageBo.getPv();
                    homePageUv = homePageBo.getUv();
                    homePageDv = homePageBo.getDv();

                    long pageNum = 0, pageSize = 500;
                    PageRespDTO<ClickReportDayBO> nodeList = mongoPageHelper.query(
                            getHomePageNodeAggregationCriteria(reportBasicReqDTO, homePageBo.getLanguage()), new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY, true, null, ClickReportDayBO.class);

                    if (nodeList == null || nodeList.getSize() < 1) {
                        return null;
                    }

                    long pages = nodeList.getPages();
                    for (int i = 0; i < pages; i++) {
                        //统计
                        if (i > 0) {
                            nodeList = mongoPageHelper.query(
                                    getHomePageNodeAggregationCriteria(reportBasicReqDTO, homePageBo.getLanguage()), new PageReqDTO(i, pageSize), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY, true, pageRespDTO.getTotal(), ClickReportDayBO.class);
                            if (nodeList == null || nodeList.getSize() < 1) {
                                return null;
                            }
                        }

                        for (ClickReportDayBO bo : nodeList.getList()) {
                            Query query = new Query();
                            query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                                    .gte(reportBasicReqDTO.getBeginTime())
                                    .lt(reportBasicReqDTO.getEndTime())
                                    .and(ClickReportDayBO.PAGE_NAME).is(bo.getPageName())
                                    .and(ClickReportDayBO.CARD_NAME).is(bo.getCardName())
                                    .and(ClickReportDayBO.NODE_NAME).is(bo.getNodeName())
                                    .and(ClickReportDayBO.LANGUAGE).is(bo.getLanguage()));
                            bo.setDataTime(reportBasicReqDTO.getDataTime());
                            //更新总表
                            mongoTemplate.upsert(query, getUpdateBo(bo, homePagePv, homePageUv, homePageDv), ClickHomePageNodeRateReportDayBO.COLLECTION_NAME);
                        }
                    }
                    log.info("统计首页点击转化率事件,时间:{}", reportBasicReqDTO);
                } catch (Exception e) {
                    log.error("首页点击转化率异常,homePageBo:{}", homePageBo, e);
                }
            }
        } catch (Exception e) {
            log.error("统计首页点击事件转化率异常", e);
        }
        return null;
    }

    private static Update getUpdateBo(ClickReportDayBO clickReportDayB0, Long homePagePv, Long homePageUv, Long homePageDv) {
        Update update = new Update();
        update.set(ClickHomePageNodeRateReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickHomePageNodeRateReportDayBO.CREATE_TIME, new Date());
        update.set(ClickHomePageNodeRateReportDayBO.PAGE_NAME, clickReportDayB0.getPageName());
        update.set(ClickHomePageNodeRateReportDayBO.CARD_NAME, clickReportDayB0.getCardName());
        update.set(ClickHomePageNodeRateReportDayBO.NODE_NAME, clickReportDayB0.getNodeName());
        update.set(ClickHomePageNodeRateReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ClickHomePageNodeRateReportDayBO.UV, clickReportDayB0.getUv());
        update.set(ClickHomePageNodeRateReportDayBO.PV, clickReportDayB0.getPv());
        update.set(ClickHomePageNodeRateReportDayBO.DV, clickReportDayB0.getDv());
        update.set(ClickHomePageNodeRateReportDayBO.HOME_PAGE_PV, homePagePv);
        update.set(ClickHomePageNodeRateReportDayBO.HOME_PAGE_UV, homePageUv);
        update.set(ClickHomePageNodeRateReportDayBO.HOME_PAGE_DV, homePageDv);
        update.set(ClickHomePageNodeRateReportDayBO.LANGUAGE, clickReportDayB0.getLanguage());
        return update;
    }

    private List<AggregationOperation> getHomePageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.NODE_NAME, ClickReportDayBO.LANGUAGE)
                .first(ClickReportDayBO.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        return querList;
    }

    private List<AggregationOperation> getHomePageNodeAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportDayBO.PAGE_NAME).in(Arrays.asList(WOWNOW_HOME_PAGE_ARR));
        criteria.and(ClickReportDayBO.LANGUAGE).is(language);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME, ClickReportDayBO.NODE_NAME, ClickReportDayBO.LANGUAGE)
                .first(ClickReportDayBO.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
        return null;
    }
}
