package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.dto.ReportDateDTO;

import java.util.Date;

public interface DwdAggregateOrderService {

    void syncDwdAggregateOrder(Date startTime, Date endTime);

    void updateDwdAggregateOrder(Date startTime, Date endTime);

    void saveDwdAggregateOrder(String aggregateOrderNo);

    void staticOrderReport();

    void staticHomePageOrderReport();

    void staticYumNowStoreOrderRateReport(ReportDateDTO dateDTO);
}
