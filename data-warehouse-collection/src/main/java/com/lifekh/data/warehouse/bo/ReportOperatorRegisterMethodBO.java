package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_OPERATOR_REGISTER_METHOD)
@Data
public class ReportOperatorRegisterMethodBO implements Serializable {

    private static final long serialVersionUID = 8107859918954474996L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    public static final String DATA_TIME = "dataTime";

    /**
     * 数量统计
     */
    private Integer count;

    public final static String COUNT = "count";

    /**
     * 总数统计
     */
    private Integer total;

    public final static String TOTAL = "total";

    /**
     * 注册方式
     */
    private String registerMethod;

    public final static String REGISTER_METHOD = "registerMethod";

    private Date createTime;

    private Date updateTime;
}
