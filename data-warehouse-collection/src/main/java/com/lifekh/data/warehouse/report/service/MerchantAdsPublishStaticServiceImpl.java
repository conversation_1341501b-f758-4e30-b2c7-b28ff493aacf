package com.lifekh.data.warehouse.report.service;

import com.chaos.app.config.api.cmsFacade.CmsMerchantAdsPublishFacade;
import com.chaos.app.config.api.enums.CmsAdsPublishStateEnum;
import com.chaos.app.config.api.req.cms.MerchantNodePlanNoReqDTO;
import com.chaos.app.config.api.resp.cms.MerchantNodePlanRespDTO;
import com.chaos.app.config.api.resp.cms.MerchantPublishPlanDetailRespDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.CmsMerchantAdsPublishReqDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticDetailRespDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticRespDTO;
import com.lifekh.data.warehouse.bo.MerchantAdsPublishStaticBO;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/1/10 14:47
 * @Version 1.0
 **/
@Service
@Slf4j
public class MerchantAdsPublishStaticServiceImpl implements MerchantAdsPublishStaticService{

    @Autowired
    CmsMerchantAdsPublishFacade cmsMerchantAdsPublishFacade;

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String iosHomePageLabel = "SANewHomeViewController";

    private final String androidHomePageLabel = "HomeChangeFragment";


    @Override
    public void staticMerchantAdsPublish() {
        Date startDate = DateUtil.getYesterdayStartDate();
        Date endDate = DateUtil.getYesterdayEndTime();
        List<MerchantNodePlanRespDTO> merchantNodePlanRespDTOS = cmsMerchantAdsPublishFacade.searchNeedStaticAdsPublish(startDate,endDate);
        for (MerchantNodePlanRespDTO merchantNodePlanRespDTO:merchantNodePlanRespDTOS){
            MerchantAdsPublishStaticBO merchantAdsPublishStaticBO = new MerchantAdsPublishStaticBO();
            merchantAdsPublishStaticBO.setPlanStartTime(merchantNodePlanRespDTO.getPlanStartTime());
            merchantAdsPublishStaticBO.setPlanEndTime(merchantNodePlanRespDTO.getPlanEndTime());
            merchantAdsPublishStaticBO.setRealEndTime(merchantNodePlanRespDTO.getRealEndTime());
            merchantAdsPublishStaticBO.setMerchantNameZh(merchantNodePlanRespDTO.getMerchantNameZh());
            merchantAdsPublishStaticBO.setMerchantNameEn(merchantNodePlanRespDTO.getMerchantNameEn());
            merchantAdsPublishStaticBO.setMerchantNameKm(merchantNodePlanRespDTO.getMerchantNameKm());
            merchantAdsPublishStaticBO.setNodePublishNo(merchantNodePlanRespDTO.getNodePublishNo());
            merchantAdsPublishStaticBO.setCreateDate(new Date());
            merchantAdsPublishStaticBO.setStaticDate(DateUtil.getSearchStaticTime(new Date(), Calendar.DATE,-1));
            //统计昨天投放中广告点击次数
            int adsTodayClickNum = countClickNum(startDate,endDate,merchantNodePlanRespDTO.getNodePublishNo());
            merchantAdsPublishStaticBO.setAdsTodayClickNum(adsTodayClickNum);
            //统计昨天广告点击人数
            int adsTodayClickUserNum = countUserClickNum(startDate,endDate,merchantNodePlanRespDTO.getNodePublishNo());
            merchantAdsPublishStaticBO.setAdsTodayClickUserNum(adsTodayClickUserNum);
            //统计广告当前总点击次数
            int adsTotalClickNum = countClickNum(merchantNodePlanRespDTO.getPlanStartTime(),endDate,merchantNodePlanRespDTO.getNodePublishNo());
            merchantAdsPublishStaticBO.setAdsTotalClickNum(adsTotalClickNum);
            //统计广告总点击人数
            int adsTotalClickUserNum = countUserClickNum(merchantNodePlanRespDTO.getPlanStartTime(),endDate,merchantNodePlanRespDTO.getNodePublishNo());
            merchantAdsPublishStaticBO.setAdsTotalClickUserNum(adsTotalClickUserNum);
            if (CmsAdsPublishStateEnum.PUBLISHING.getCode().equals(merchantNodePlanRespDTO.getPublishState())){//投放中的广告统计
                //统计投放开始以来页面总访问数
                int pageViewNum = countPageViewNum(merchantNodePlanRespDTO.getPlanStartTime(),endDate);
                merchantAdsPublishStaticBO.setPageViewNum(pageViewNum);
                //统计投放开始以来页面总访问用户数
                int pageViewUserNum = countPageViewUserNum(merchantNodePlanRespDTO.getPlanStartTime(),endDate);
                merchantAdsPublishStaticBO.setPageViewUserNum(pageViewUserNum);
            }else {
                //统计投放开始以来页面总访问数
                int pageViewNum = countPageViewNum(merchantNodePlanRespDTO.getPlanStartTime(),merchantNodePlanRespDTO.getRealEndTime());
                merchantAdsPublishStaticBO.setPageViewNum(pageViewNum);
                //统计投放开始以来页面总访问用户数
                int pageViewUserNum = countPageViewUserNum(merchantNodePlanRespDTO.getPlanStartTime(),merchantNodePlanRespDTO.getRealEndTime());
                merchantAdsPublishStaticBO.setPageViewUserNum(pageViewUserNum);
            }
            mongoTemplate.insert(merchantAdsPublishStaticBO,MongoDbCollectonName.MERCHANT_ADS_PUBLISH_DAY);
        }
    }

    @Override
    public PageInfoDTO<MerchantAdsPublishStaticRespDTO> seachMerchantNodePlanStaticList(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("nodePublishNo").is(cmsMerchantAdsPublishReqDTO.getNodePublishNo());
        Query query = new Query(criteria);

        long total = mongoTemplate.count(query,MongoDbCollectonName.MERCHANT_ADS_PUBLISH_DAY);

        query.with(Sort.by(Sort.Direction.DESC, "staticDate"));
        query.skip((cmsMerchantAdsPublishReqDTO.getPageNum() - 1) * cmsMerchantAdsPublishReqDTO.getPageSize());
        query.limit(cmsMerchantAdsPublishReqDTO.getPageSize());
        List<MerchantAdsPublishStaticRespDTO> merchantAdsPublishStaticRespDTOS =  mongoTemplate.find(query,MerchantAdsPublishStaticRespDTO.class,MongoDbCollectonName.MERCHANT_ADS_PUBLISH_DAY);

        PageInfoDTO<MerchantAdsPublishStaticRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(cmsMerchantAdsPublishReqDTO.getPageSize());
        target.setPageNum(cmsMerchantAdsPublishReqDTO.getPageNum());
        target.setSize(merchantAdsPublishStaticRespDTOS.size());
        target.setTotal(total);
        target.setList(merchantAdsPublishStaticRespDTOS);
        return target;
    }

    @Override
    public MerchantAdsPublishStaticDetailRespDTO seachMerchantNodePlanStaticDetail(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("nodePublishNo").is(cmsMerchantAdsPublishReqDTO.getNodePublishNo());
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "staticDate"));
        MerchantAdsPublishStaticDetailRespDTO merchantAdsPublishStaticRespDTOS = null;
        merchantAdsPublishStaticRespDTOS =  mongoTemplate.findOne(query,MerchantAdsPublishStaticDetailRespDTO.class,MongoDbCollectonName.MERCHANT_ADS_PUBLISH_DAY);
        if (Objects.isNull(merchantAdsPublishStaticRespDTOS)){
            merchantAdsPublishStaticRespDTOS = new MerchantAdsPublishStaticDetailRespDTO();
            MerchantNodePlanNoReqDTO merchantNodePlanNoReqDTO = new MerchantNodePlanNoReqDTO();
            merchantNodePlanNoReqDTO.setNodePublishNo(cmsMerchantAdsPublishReqDTO.getNodePublishNo());
            MerchantPublishPlanDetailRespDTO merchantPublishPlanDetailRespDTO = cmsMerchantAdsPublishFacade.searchMerchantPublishPlanDetail(merchantNodePlanNoReqDTO);
            merchantAdsPublishStaticRespDTOS.setMerchantNameZh(merchantPublishPlanDetailRespDTO.getMerchantNameZh());
            merchantAdsPublishStaticRespDTOS.setMerchantNameEn(merchantPublishPlanDetailRespDTO.getMerchantNameEn());
            merchantAdsPublishStaticRespDTOS.setMerchantNameKm(merchantPublishPlanDetailRespDTO.getMerchantNameEn());
            merchantAdsPublishStaticRespDTOS.setNodePublishNo(merchantPublishPlanDetailRespDTO.getNodePublishNo());
            merchantAdsPublishStaticRespDTOS.setPlanStartTime(merchantPublishPlanDetailRespDTO.getPlanStartTime());
            merchantAdsPublishStaticRespDTOS.setRealEndTime(merchantPublishPlanDetailRespDTO.getRealEndTime());
        }
        BigDecimal multyplyNum = BigDecimal.valueOf(100.0);
        if (merchantAdsPublishStaticRespDTOS.getAdsTotalClickNum() != 0 && merchantAdsPublishStaticRespDTOS.getPageViewNum() != 0) {
            BigDecimal a = new BigDecimal(merchantAdsPublishStaticRespDTOS.getAdsTotalClickNum());
            BigDecimal b = new BigDecimal(merchantAdsPublishStaticRespDTOS.getPageViewNum());
            Double clickNumRate = a.divide(b,4,BigDecimal.ROUND_HALF_UP).multiply(multyplyNum).doubleValue();
            merchantAdsPublishStaticRespDTOS.setClickNumRate(clickNumRate);
        }else {
            merchantAdsPublishStaticRespDTOS.setClickNumRate(0.0);
        }
        if (merchantAdsPublishStaticRespDTOS.getAdsTotalClickUserNum() != 0 && merchantAdsPublishStaticRespDTOS.getPageViewUserNum() != 0) {
            BigDecimal c = new BigDecimal(merchantAdsPublishStaticRespDTOS.getAdsTotalClickUserNum());
            BigDecimal d = new BigDecimal(merchantAdsPublishStaticRespDTOS.getPageViewUserNum());
            Double viewUserNumRate = c.divide(d,4,BigDecimal.ROUND_HALF_UP).multiply(multyplyNum).doubleValue();
            merchantAdsPublishStaticRespDTOS.setViewUserNumRate(viewUserNumRate);
        }else {
            merchantAdsPublishStaticRespDTOS.setViewUserNumRate(0.0);
        }
        return merchantAdsPublishStaticRespDTOS;
    }

    //统计点击次数
    public int countClickNum(Date startDate,Date endDate,String nodePublishNo){
        Criteria criteria = new Criteria();
        criteria.and("createTime").gte(startDate)
                .lt(endDate);
        criteria.and("ext.nodePublishNo").is(nodePublishNo);
        Query query = new Query(criteria);
        Long newUserNum = mongoTemplate.count(query, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK);
        return newUserNum.intValue();
    }

    //统计点击用户数
    public int countUserClickNum(Date startDate,Date endDate,String nodePublishNo){
        Criteria criteria = new Criteria();
        criteria.and("userInfoBo.operatorNo").ne(null);
        criteria.and("createTime").gte(startDate)
                .lt(endDate);
        criteria.and("ext.nodePublishNo").is(nodePublishNo);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userInfoBo.operatorNo"),
                Aggregation.count().as("adsTodayClickUserNum")
        );
        AggregationResults<MerchantAdsPublishStaticBO> aggregationResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, MerchantAdsPublishStaticBO.class);
        List<MerchantAdsPublishStaticBO> num = aggregationResults.getMappedResults();
        if (Objects.nonNull(num) && num.size() > 0) {
            return num.get(0).getAdsTodayClickUserNum();
        }else {
            return 0;
        }
    }

    //统计页面访问量
    public int countPageViewNum(Date startDate,Date endDate){
        Criteria criteria = new Criteria();
        criteria.and("createTime").gte(startDate)
                .lt(endDate);
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("currentPage").is(iosHomePageLabel));
        criteriaList.add(Criteria.where("currentPage").is(androidHomePageLabel));
        criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        Query query = new Query(criteria);
        Long vistNum = mongoTemplate.count(query, MongoDbCollectonName.COLLECT_BURIED_POINT_VIEW_PAGE);
        return vistNum.intValue();
    }

    //统计访问页面用户数
    public int countPageViewUserNum(Date startDate,Date endDate){
        Criteria criteria = new Criteria();
        criteria.and("userInfoBo.operatorNo").ne(null);
        criteria.and("createTime").gte(startDate)
                .lt(endDate);
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("currentPage").is(iosHomePageLabel));
        criteriaList.add(Criteria.where("currentPage").is(androidHomePageLabel));
        criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userInfoBo.operatorNo"),
                Aggregation.count().as("pageViewUserNum")
        );
        AggregationResults<MerchantAdsPublishStaticBO> aggregationResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_VIEW_PAGE, MerchantAdsPublishStaticBO.class);
        List<MerchantAdsPublishStaticBO> num = aggregationResults.getMappedResults();
        if (Objects.nonNull(num) && num.size() > 0) {
            return num.get(0).getPageViewUserNum();
        }else {
            return 0;
        }
    }
}
