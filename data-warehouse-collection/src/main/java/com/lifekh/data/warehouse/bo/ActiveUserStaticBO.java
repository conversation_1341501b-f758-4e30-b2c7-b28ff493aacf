package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
public class ActiveUserStaticBO {
    @Id
    private String id;

    /**
     * 数据日期
     */
    private String staticDate;

    /**
     * 日活数
     */
    private Long activeUserNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 查询统计时间，用于BOSS查询统计
     */
    private Date searchStaticTime;

    /**
     * 当天所在月的日活数
     */
    private Long localMonthActiveNum;

    /**
     * 当天所在周的日活数
     */
    private Long localWeekActiveNum;
}
