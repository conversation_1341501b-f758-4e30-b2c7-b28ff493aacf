package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Document(collection = MongoDbCollectonName.REPORT_CLICK_HOMEPAGE_NODE_RATE_DAY)
@Data
public class ClickHomePageNodeRateReportDayBO extends ReportBasicBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_CLICK_HOMEPAGE_NODE_RATE_DAY;

    private static final long serialVersionUID = -2599035028656451031L;

    private Long homePagePv;
    public static String HOME_PAGE_PV = "homePagePv";


    private Long homePageUv;
    public static String HOME_PAGE_UV = "homePageUv";


    private Long homePageDv;
    public static String HOME_PAGE_DV = "homePageDv";

}
