package com.lifekh.data.warehouse.report.service;

import com.khsuper.takeaway.merchant.api.dto.theme.ThemeActivitiesReqDTO;
import com.khsuper.takeaway.merchant.api.dto.theme.ThemeActivitiesRespDTO;
import com.khsuper.takeaway.merchant.api.facade.ThemeFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.EntranceType;
import com.lifekh.data.warehouse.bo.ReportTakeawayThemeFlowConversionBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PageInfoDTO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TakeawayThemeFlowConversionReportStrategy extends AbstractTakeawayFlowConversionReportStrategy<ReportTakeawayThemeFlowConversionBO> implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ThemeFacade themeFacade;


    @Override
    String getMongoDbCollectionName() {
        return MongoDbCollectonName.REPORT_TAKEAWAY_THEME_FLOW_CONVERSION;
    }

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ReportTakeawayThemeFlowConversionBO> statExposureCount(Date startTime, Date endTime) {
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and("eventBo.event").is("browseTheme")
                .and("ext.type").is("THEME");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.plateId",
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("pv")
                        .first("ext.plateId").as("themeNo")
                        .first(ClickReportConstant.CREATE_TIME).as("dataTime")
                        .first(ClickReportConstant.LANGUAGE).as("language")
                        .first(ClickReportConstant.PROVINCE_NAME_EN).as("provinceName")
                        .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as("deviceType")
                        .first(ClickReportConstant.DEVICEINFOBO_APP_VERSION).as("appVersion"),
                Aggregation.sort(Sort.Direction.DESC, "pv"),
                Aggregation.limit(50)
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportTakeawayThemeFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_OTHER_V2, ReportTakeawayThemeFlowConversionBO.class).getMappedResults();
//        List<ReportTakeawayThemeFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation,"collect_buried_point_takeaway_other", ReportTakeawayThemeFlowConversionBO.class).getMappedResults();
        if (!mappedResults.isEmpty()) {
            List<ThemeActivitiesRespDTO> themeList = loadTheme(startTime, endTime);
            if (!themeList.isEmpty()) {
                Map<String, ThemeActivitiesRespDTO> themeMap = themeList.stream().collect(Collectors.toMap(ThemeActivitiesRespDTO::getThemeNo, Function.identity()));
                for (ReportTakeawayThemeFlowConversionBO mappedResult : mappedResults) {
                    ThemeActivitiesRespDTO theme = themeMap.get(mappedResult.getThemeNo());
                    if(theme != null) {
                        mappedResult.setBusinessName(theme.getName());
                        mappedResult.setThemeType(theme.getType().getMessage());
                    } else {
                        mappedResult.setBusinessName("unknown");
                        mappedResult.setThemeType("unknown");
                    }
                    mappedResult.setLanguage(StringUtil.isEmpty(mappedResult.getLanguage()) ? "unknown" : mappedResult.getLanguage() );
                    mappedResult.setProvinceName(StringUtil.isEmpty(mappedResult.getProvinceName()) ? "unknown" : mappedResult.getProvinceName() );
                }
            }
        }
        return mappedResults;
    }

    /**
     * 获取主题列表
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private List<ThemeActivitiesRespDTO> loadTheme(Date startTime, Date endTime) {
        ThemeActivitiesReqDTO themeActivitiesReqDTO = new ThemeActivitiesReqDTO();
        themeActivitiesReqDTO.setFrom(DateUtil.addHour(startTime, -24));
        themeActivitiesReqDTO.setTo(DateUtil.addHour(endTime, 24));
        themeActivitiesReqDTO.setPageNum(1);
        themeActivitiesReqDTO.setPageSize(200);
        List<ThemeActivitiesRespDTO> themeList = Lists.newArrayList();
        PageInfoDTO<ThemeActivitiesRespDTO> themePage = themeFacade.list(themeActivitiesReqDTO);
        themeList.addAll(themePage.getList());
        while (themePage.isHasNextPage()) {
            themeActivitiesReqDTO.setPageNum(themePage.getNextPage());
            themePage = themeFacade.list(themeActivitiesReqDTO);
            themeList.addAll(themePage.getList());
        }
        return themeList;
    }


    /**
     * 统计点击数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    @Override
    public void statClickCount(Date startTime, Date endTime, List<ReportTakeawayThemeFlowConversionBO> conversionBOList) {

    }

    @Override
    public Aggregation getEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayThemeFlowConversionBO> conversionBOList) {
        List<String> themeNoList = conversionBOList.stream().map(ReportTakeawayThemeFlowConversionBO::getThemeNo).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).in(EntranceType.PRODUCT_THEME.getCode(), EntranceType.STORE_THEME.getCode(), EntranceType.BRAND_THEME.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(themeNoList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")

        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayThemeFlowConversionBO> conversionBOList) {
        List<String> themeNoList = conversionBOList.stream().map(ReportTakeawayThemeFlowConversionBO::getThemeNo).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).in(EntranceType.PRODUCT_THEME.getCode(), EntranceType.STORE_THEME.getCode(), EntranceType.BRAND_THEME.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(themeNoList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("count")
                        .push("ext.orderNo").as("orderList"),
                Aggregation.project("count",  "orderList")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
