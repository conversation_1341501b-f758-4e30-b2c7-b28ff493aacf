package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

@Data
public class UserRemainStaticBO implements Serializable {
    private static final long serialVersionUID = 542565290056437606L;
    @Id
    private String id;
    /**
     * 日期
     */
    private String staticDate;


    /**
     * 总人数
     */
    private Integer newUserNum;

    /**
     * 一日留存
     */
    private Integer oneDayRemainNum;


    /**
     * 二日留存
     */
    private Integer twoDayRemainNum;


    /**
     * 三日留存
     */
    private Integer threeDayRemainNum;


    /**
     * 四日留存
     */
    private Integer fourDayRemainNum;


    /**
     * 五日留存
     */
    private Integer fiveDayRemainNum;


    /**
     * 六日留存
     */
    private Integer sixDayRemainNum;


    /**
     * 七日留存
     */
    private Integer sevenDayRemainNum;


    /**
     * 15日留存
     */
    private Integer fifteenDayRemainNum;


    /**
     * 30日留存
     */
    private Integer thirtyDayRemainNum;
}
