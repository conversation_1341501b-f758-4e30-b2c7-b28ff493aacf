package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.OPEN_SCREEN_STATIC)
@Data
@Accessors(chain = true)
public class OpenScreenStaticBO implements Serializable {
    private static final long serialVersionUID = 5881334271475068249L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    private String adNo;
    private String adName;
    private String imageUrl;
    private String language;
    private Long count;

    private Date createTime;
}
