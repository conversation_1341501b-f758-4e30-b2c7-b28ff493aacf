package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOTWORD_SEARCH)
@Data
public class HotWordSearchReportDayBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_HOTWORD_SEARCH;

    private static final long serialVersionUID = -2599035028656451031L;

    public static String ID = "_id";

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 热搜词
     */
    private String keyWord;
    public static String KEYWORD = "keyWord";

    /**
     * 业务线
     */
    private String businessLine;
    public static String BUSINESSLINE = "businessLine";

    /**
     * 数据日期
     */
    private Date dataTime;

    public static String DATA_TIME = "dataTime";


    /**
     * PV
     */
    private Long pv;

    public static String PV = "pv";

    /**
     * 语言
     */
    private String language;

    public static String LANGUAGE = "language";
}
