package com.lifekh.data.warehouse.report.service;

import com.khsuper.product.api.ProductFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.EntranceType;
import com.lifekh.data.warehouse.bo.ReportTakeawayBannerFlowConversionBO;
import com.lifekh.data.warehouse.bo.ReportTakeawayEotConversionBO;
import com.lifekh.data.warehouse.bo.ReportTakeawayHomeExposureStatBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.CommonCountDTO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TakeawayEotFlowConversionReportStrategy extends AbstractTakeawayFlowConversionReportStrategy<ReportTakeawayEotConversionBO>  implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ProductFacade productFacade;


    @Override
    String getMongoDbCollectionName() {
        return MongoDbCollectonName.REPORT_TAKEAWAY_EOT_FLOW_CONVERSION;
    }

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ReportTakeawayEotConversionBO> statExposureCount(Date startTime, Date endTime) {
        //collect_buried_point_takeaway_view_v2 event=takeawayProductExposure ext.type=eatOnTimeProduct
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(startTime).lte(endTime)
                .and(ClickReportConstant.EVENT).is("takeawayProductExposure")
                .and("ext.type").is("eatOnTimeProduct");

        Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group("ext.productId",
                                        ClickReportConstant.LANGUAGE,
                                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                        ClickReportConstant.PROVINCE_NAME_EN).count().as("pv")
                                .first("ext.productId").as("productId")
                                .first(ClickReportConstant.CREATE_TIME).as("dataTime")
                                .first(ClickReportConstant.LANGUAGE).as("language")
                                .first(ClickReportConstant.PROVINCE_NAME_EN).as("provinceName")
                                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as("deviceType")
                                .first(ClickReportConstant.DEVICEINFOBO_APP_VERSION).as("appVersion"),
                        Aggregation.sort(Sort.Direction.DESC, "pv"),
                        Aggregation.limit(200)
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportTakeawayEotConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_VIEW_V2, ReportTakeawayEotConversionBO.class).getMappedResults();
//        List<ReportTakeawayEotConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_view", ReportTakeawayEotConversionBO.class).getMappedResults();
        if (!mappedResults.isEmpty()) {
            Set<Long> productSet = mappedResults.stream().map(p -> Long.parseLong(p.getProductId())).collect(Collectors.toSet());
            if (!productSet.isEmpty()) {
                Map<String, String> productMap = productFacade.listByIdSet(productSet).stream().collect(Collectors.toMap(p -> p.getId().toString(),
                        p -> Optional.ofNullable(p.getNameZh())
                        .orElse(Optional.ofNullable(p.getNameKm())
                                .orElse(Optional.ofNullable(p.getName())
                                        .orElse(p.getCode())))));
                for (ReportTakeawayEotConversionBO mappedResult : mappedResults) {
                    mappedResult.setProductName(productMap.get(mappedResult.getProductId()));
                    mappedResult.setLanguage(StringUtil.isEmpty(mappedResult.getLanguage()) ? "unknown" : mappedResult.getLanguage() );
                    mappedResult.setProvinceName(StringUtil.isEmpty(mappedResult.getProvinceName()) ? "unknown" : mappedResult.getProvinceName() );
                }
            }
        }
        return mappedResults;
    }

    @Override
    public void statClickCount(Date startTime, Date endTime, List<ReportTakeawayEotConversionBO> conversionBOList) {
        //do nothing
    }

    @Override
    public Aggregation getEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayEotConversionBO> conversionBOList) {
        List<String> productIdList = conversionBOList.stream().map(ReportTakeawayEotConversionBO::getProductId).collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.EOT.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(productIdList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayEotConversionBO> conversionBOList) {
        List<String> productIdList = conversionBOList.stream().map(ReportTakeawayEotConversionBO::getProductId).collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.EOT.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(productIdList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("count")
                        .push("ext.orderNo").as("orderList"),
                Aggregation.project("count",  "orderList")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }



    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
