package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

@Data
public class MerchantAdsPublishStaticBO implements Serializable {
    private static final long serialVersionUID = 2995678624630273602L;
    @Id
    private String id;

    /**
     * 统计数据日期
     */
    private Date staticDate;

    /**
     * 投放编号
     */
    private String nodePublishNo;

    /**
     * 广告日点击次数
     */
    private Integer  adsTodayClickNum;

    /**
     * 广告日点击用户数
     */
    private Integer  adsTodayClickUserNum;

    /**
     * 广告总点击次数
     */
    private Integer  adsTotalClickNum;

    /**
     * 广告总点击用户数
     */
    private Integer  adsTotalClickUserNum;

    /**
     * 页面访问数
     */
    private Integer pageViewNum;

    /**
     * 页面访问用户数
     */
    private Integer pageViewUserNum;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 计划开始时间
     */
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    private Date planEndTime;
    /**
     * 实际结束时间
     */
    private Date realEndTime;

    /**
     * 英文商户名
     */
    private String merchantNameEn;

    /**
     * 柬文商户名
     */
    private String merchantNameKm;

    /**
     * 商户名
     */
    private String merchantNameZh;
}
