package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_CLICK_WEEK)
@Data
public class ClickReportWeekBO extends ReportBasicBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_CLICK_WEEK;

    private static final long serialVersionUID = -2599035028656451031L;

    /**
     * 每周一日期
     */
    private Date firstOfWeek;
}
