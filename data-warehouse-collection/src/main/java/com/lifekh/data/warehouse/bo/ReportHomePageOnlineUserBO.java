package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOME_PAGE_ONLINE_USER)
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ReportHomePageOnlineUserBO {
    private Date createTime;

    /**
     * 时区+7
     */
    private Date dataTime;

    /**
     * WOWNOW首页整点uv
     */
    private Integer wownowHourUv;
    public static String WOWNOW_HOUR_UV = "wownowHourUv";

    /**
     * WOWNOW首页整点dv
     */
    private Integer wownowHourDv;
    public static String WOWNOW_HOUR_DV = "wownowHourDv";

    /**
     * 外卖首页整点uv
     */
    private Integer takeawayHourUv;
    public static String TAKEAWAY_HOUR_UV = "takeawayHourUv";

    /**
     * 外卖首页整点dv
     */
    private Integer takeawayHourDv;
    public static String TAKEAWAY_HOUR_DV = "takeawayHourDv";
}
