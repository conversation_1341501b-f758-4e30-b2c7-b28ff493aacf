package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.util.NumberUtil;
import com.chaos.common.enums.BusinessLineEnum;
import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.EventPageBusinessLineEnum;
import com.lifekh.data.warehouse.api.enums.collect.EventPageClassifyEnum;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.collection.EventPageBO;
import com.lifekh.data.warehouse.bo.collection.ReportPVBO;
import com.lifekh.data.warehouse.bo.collection.ReportPVByDayBO;
import com.lifekh.data.warehouse.bo.device.DevicePoolBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.OrderConstant;
import com.lifekh.data.warehouse.dto.DeviceDTO;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.enums.ReportLanguageDataTypeEnum;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service("pageViewReportService")
@Slf4j
public class PageViewReportServiceImpl implements PageViewReportService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    @Override
    public void countThePVofTheCurrentPage() {
        //1.查询当前页面昨天的spm数据并且分组，按数量从小到大排序
        Criteria criteria = new Criteria();
        criteria.and("currentPage").ne(null);
        criteria.and("createTime").gte(com.lifekh.data.warehouse.utils.DateUtil.dateCalculation(new Date(), Calendar.DATE, -1))
                .lt(com.lifekh.data.warehouse.utils.DateUtil.dateCalculation(new Date(), Calendar.DATE, 0));

//        criteria.and("parentPage").is(null);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("parentPage", "currentPage")
                        .first("parentPage").as("parentPage")
                        .first("currentPage").as("currentPage")
                        .count().as("num"),
                Aggregation.sort(Sort.Direction.ASC, "num")
        );
        AggregationResults<PageViewGroupByBO> aggregationResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_VIEW_PAGE, PageViewGroupByBO.class);

        //2.根据数量进行分片统计
        List<PageViewGroupByBO> list = aggregationResults.getMappedResults();

        //3.入库
        for (PageViewGroupByBO currentPage : list) {
            //查询总表
            Query query = new Query();
            query.addCriteria(Criteria.where("parentPage").is(currentPage.getParentPage()).and("currentPage").is(currentPage.getCurrentPage()));
            ReportPVBO reportPVBO = mongoTemplate.findOne(query, ReportPVBO.class);

            if (Objects.isNull(reportPVBO)) {
                reportPVBO = new ReportPVBO();
                reportPVBO.setCreateTime(new Date());
                reportPVBO.setParentPage(currentPage.getParentPage());
                reportPVBO.setCurrentPage(currentPage.getCurrentPage());
                reportPVBO.setViewNum(0L);
                reportPVBO.setUserNum(0L);
                reportPVBO.setDeviceNum(0L);
                reportPVBO.setAlias("");
            }

            //计算当前页面的访问数
            reportPVBO.setViewNum(reportPVBO.getViewNum() + currentPage.getNum());

            //更新总表
            mongoTemplate.save(reportPVBO);

            //按天插入表
            ReportPVByDayBO reportPVByDayBO = new ReportPVByDayBO();
            reportPVByDayBO.setCreateTime(new Date());
            reportPVByDayBO.setUpdateTime(new Date());
            reportPVByDayBO.setDay(com.lifekh.data.warehouse.utils.DateUtil.getDayStr(new Date(), Calendar.DATE, -1));
            reportPVByDayBO.setViewNum(currentPage.getNum());
            reportPVByDayBO.setReportPVId(reportPVBO.getId());
            mongoTemplate.save(reportPVByDayBO);
            log.info("已完成页面PV统计, 页面标识:{}", currentPage.getCurrentPage());
        }
    }

    @Override
    public void pageViewStatistics() {
        ReportDateDTO dateDTO = ReportDateDTO.yesterday();

        //遍历页面
        for (EventPageClassifyEnum classify : EventPageClassifyEnum.values()) {
            if (classify == EventPageClassifyEnum.OTHER) {
                continue;
            }
            String pageName = classify.getMessage();
            log.info("开始查询页面浏览量，pageName: {}", pageName);
            try {
                List<String> pageCodes = mongoTemplate.find(Query.query(Criteria.where(EventPageBO.PAGE_CLASSIFY).is(classify.getCode())), EventPageBO.class)
                        .stream().map(EventPageBO::getPageCode).collect(toList());
                if (pageCodes.isEmpty()) {
                    log.info("查询对应页面名称为空");
                    continue;
                }
                //pv
                Criteria pvCri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                        .and(ClickReportConstant.CURRENT_PAGE).in(pageCodes);
                Aggregation pvAgg = Aggregation.newAggregation(
                        Aggregation.match(pvCri),
                        Aggregation.group(ClickReportConstant.LANGUAGE, ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE)
                                .first(ClickReportConstant.LANGUAGE).as(ReportPageViewBO.LANGUAGE)
                                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as(ReportPageViewBO.DEVICE_TYPE)
                                .count().as(ReportHomePageViewBO.PV))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportPageViewBO> pvDatas = mongoTemplate.aggregate(pvAgg, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportPageViewBO.class).getMappedResults();
                pvDatas.forEach(d -> {
                    if (StringUtils.isNotBlank(d.getLanguage())) {
                        //查询每个语言下的uv、dv
                        Criteria cri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                                .and(ClickReportConstant.CURRENT_PAGE).in(pageCodes)
                                .and(ClickReportConstant.LANGUAGE).is(d.getLanguage())
                                .and(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).is(d.getDeviceType());

                        d.setPageName(pageName)
                                .setUv(countUV(cri))
                                .setDv(countDV(cri))
                                .setDataTime(dateDTO.getDataTime())
                                .setCreateTime(new Date());
                        mongoTemplate.insert(d);
                    }
                });
            } catch (Exception e) {
                log.error("页面浏览量查询出现异常，pageName: {}", pageName, e);
            }
        }
    }

    @Override
    public void homePageStatistics() {
        log.info("开始统计首页浏览报表");
        try {
            this.statisticsHomeViewReport(true);
        } catch (Exception e) {
            log.error("统计首页浏览报表出现异常", e);
        }

        log.info("开始统计业务线浏览报表");
        try {
            this.statisticsHomeViewReport(false);
        } catch (Exception e) {
            log.error("统计业务线浏览报表出现异常", e);
        }
    }

    private void statisticsHomeViewReport(boolean hasHomePageParam) {
        ReportDateDTO dateDTO = ReportDateDTO.yesterday();

        //首页页面
        List<String> homePages = mongoTemplate.find(Query.query(Criteria.where(EventPageBO.PAGE_CLASSIFY).is(EventPageClassifyEnum.HOME.getCode())), EventPageBO.class)
                .stream().map(EventPageBO::getPageCode).collect(toList());
        if (homePages.isEmpty()) {
            log.info("查询首页页面名称为空，无法统计");
            return;
        }

        String collectionName = hasHomePageParam ? MongoDbCollectonName.REPORT_HOME_PAGE_VIEW : MongoDbCollectonName.REPORT_BUSINESS_PAGE_VIEW;

        //首页pv
        Map<String, ReportHomePageViewBO> homeViewMap = new HashMap<>();
        Criteria homeCri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime());
        if (hasHomePageParam) {
            homeCri.and(ClickReportConstant.CURRENT_PAGE).in(homePages);
        }
        Aggregation homeAgg = Aggregation.newAggregation(
                Aggregation.match(homeCri),
                Aggregation.group(ClickReportConstant.LANGUAGE)
                        .first(ClickReportConstant.LANGUAGE).as(ReportHomePageViewBO.LANGUAGE)
                        .count().as(ReportHomePageViewBO.PV))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportHomePageViewBO> homeViews = mongoTemplate.aggregate(homeAgg, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportHomePageViewBO.class).getMappedResults();
        homeViews.forEach(v -> {
            if (StringUtils.isNotBlank(v.getLanguage())) {
                Criteria cri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                        .and(ClickReportConstant.LANGUAGE).is(v.getLanguage());
                if (hasHomePageParam) {
                    cri.and(ClickReportConstant.CURRENT_PAGE).in(homePages);
                }

                v.setBusinessLine(EventPageBusinessLineEnum.SUPERAPP.getCode())
                        .setBusinessName("WOWNOW首页")
                        .setUv(countUV(cri))
                        .setDv(countDV(cri))
                        .setPvRate(1D)
                        .setUvRate(1D)
                        .setDvRate(1D)
                        .setDataTime(dateDTO.getDataTime())
                        .setCreateTime(new Date());
                mongoTemplate.insert(v, collectionName);

                homeViewMap.put(v.getLanguage(), v);
            }
        });

        //首页进入各业务线流量 pv
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime());
        if (hasHomePageParam) {
            criteria.and(ClickReportConstant.PARENT_PAGE).in(homePages);
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.LANGUAGE, ClickReportConstant.BUSINESS_LINE)
                        .first(ClickReportConstant.LANGUAGE).as(ReportHomePageViewBO.LANGUAGE)
                        .first(ClickReportConstant.BUSINESS_LINE).as(ReportHomePageViewBO.BUSINESS_LINE)
                        .count().as(ReportHomePageViewBO.PV))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportHomePageViewBO> views = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportHomePageViewBO.class).getMappedResults();
        views.forEach(v -> {
            if (StringUtils.isNotBlank(v.getLanguage())) {
                EventPageBusinessLineEnum businessLine = EventPageBusinessLineEnum.getByCode(v.getBusinessLine());
                if (businessLine != null && businessLine != EventPageBusinessLineEnum.SUPERAPP) {
                    //获取首页对应语言的流量，与首页计算占比
                    int homePv = 0, homeUv = 0, homeDv = 0;
                    ReportHomePageViewBO homeView = homeViewMap.get(v.getLanguage());
                    if (homeView != null) {
                        homePv = homeView.getPv();
                        homeUv = homeView.getUv();
                        homeDv = homeView.getDv();
                    }

                    //uv、dv
                    Criteria cri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                            .and(ClickReportConstant.LANGUAGE).is(v.getLanguage())
                            .and(ClickReportConstant.BUSINESS_LINE).is(v.getBusinessLine());
                    if (hasHomePageParam) {
                        cri.and(ClickReportConstant.PARENT_PAGE).in(homePages);
                    }
                    v.setBusinessName(businessLine.getMessage())
                            .setUv(countUV(cri))
                            .setDv(countDV(cri))
                            .setPvRate(homePv > 0 ? NumberUtil.div(Double.valueOf(v.getPv()), Double.valueOf(homePv), 4) : 0)
                            .setUvRate(homeUv > 0 ? NumberUtil.div(Double.valueOf(v.getUv()), Double.valueOf(homeUv), 4) : 0)
                            .setDvRate(homeDv > 0 ? NumberUtil.div(Double.valueOf(v.getDv()), Double.valueOf(homeDv), 4) : 0)
                            .setDataTime(dateDTO.getDataTime())
                            .setCreateTime(new Date());
                    mongoTemplate.insert(v, collectionName);
                }
            }
        });

        //添加游戏频道点击统计
        this.addGameClick(dateDTO, homeViewMap, collectionName);
    }

    private void addGameClick(ReportDateDTO dateDTO, Map<String, ReportHomePageViewBO> homeViewMap, String collectionName) {
        //查询游戏频道首页点击数
        Criteria criteria = Criteria.where(ReportHomePageClickBO.BUSINESS_NAME).is("游戏")
                .and(ReportHomePageClickBO.DATA_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime());
        List<ReportHomePageClickBO> clickList = mongoTemplate.find(Query.query(criteria), ReportHomePageClickBO.class);
        clickList.forEach(c -> {
            int homePv = 0, homeUv = 0, homeDv = 0;
            ReportHomePageViewBO homeView = homeViewMap.get(c.getLanguage());
            if (homeView != null) {
                homePv = homeView.getPv();
                homeUv = homeView.getUv();
                homeDv = homeView.getDv();
            }

            ReportHomePageViewBO pageView = new ReportHomePageViewBO()
                    .setLanguage(c.getLanguage())
                    .setBusinessLine(EventPageBusinessLineEnum.GAME_CHANNEL.getCode())
                    .setBusinessName(EventPageBusinessLineEnum.GAME_CHANNEL.getMessage())
                    .setPv(c.getPv())
                    .setUv(c.getUv())
                    .setDv(c.getDv())
                    .setPvRate(homePv > 0 ? NumberUtil.div(Double.valueOf(c.getPv()), Double.valueOf(homePv), 4) : 0)
                    .setUvRate(homeUv > 0 ? NumberUtil.div(Double.valueOf(c.getUv()), Double.valueOf(homeUv), 4) : 0)
                    .setDvRate(homeDv > 0 ? NumberUtil.div(Double.valueOf(c.getDv()), Double.valueOf(homeDv), 4) : 0)
                    .setDataTime(dateDTO.getDataTime())
                    .setCreateTime(new Date());
            mongoTemplate.insert(pageView, collectionName);
        });
    }

    @Override
    public void homeFlowConversionStatistics() {
        ReportDateDTO dateDTO = ReportDateDTO.yesterday();
        //查询首页和各业务线流量统计
        List<ReportHomePageViewBO> businessViews = new ArrayList<>();
        Map<String, ReportHomePageViewBO> homeViewMap = new HashMap<>();
        Criteria criteria = Criteria.where(ReportHomePageViewBO.DATA_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime());
        List<ReportHomePageViewBO> viewList = mongoTemplate.find(Query.query(criteria), ReportHomePageViewBO.class, MongoDbCollectonName.REPORT_BUSINESS_PAGE_VIEW);
        viewList.forEach(view -> {
            if (EventPageBusinessLineEnum.SUPERAPP.getCode().equals(view.getBusinessLine()))
                homeViewMap.put(view.getLanguage(), view);
            else
                businessViews.add(view);
        });

        //查询活跃用户统计
        Map<String, ReportLanguageDayBO> activeUserCountMap = new HashMap<>();
        Criteria activeCri = Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT.getCode());
        List<ReportLanguageDayBO> activeUserCounts = mongoTemplate.find(Query.query(activeCri), ReportLanguageDayBO.class, MongoDbCollectonName.REPORT_LANGUAGE_DAY);
        activeUserCounts.forEach(ac -> {
            activeUserCountMap.put(ac.getLanguage(), ac);
        });

        //统计各业务线流量转化
        businessViews.forEach(view -> {
            EventPageBusinessLineEnum businessLine = EventPageBusinessLineEnum.getByCode(view.getBusinessLine());
            if (businessLine != null) {
                ReportHomePageViewBO homeView = homeViewMap.get(view.getLanguage());
                ReportLanguageDayBO activeUser = activeUserCountMap.get(view.getLanguage());
                int homePv = homeView == null ? 0 : homeView.getPv();
                int homeUv = homeView == null ? 0 : homeView.getUv();
                int activeUserCount = activeUser == null ? 0 : activeUser.getCount().intValue();

                //查询下单用户数
                Criteria orderCri = Criteria.where(DwdAggregateOrderDO.ORDER_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                        .and(DwdAggregateOrderDO.MERCHANT_NO).nin(OrderConstant.EXCLUDE_MERCHANT_NOS)
                        .and(DwdAggregateOrderDO.BUSINESS_LINE).is(view.getBusinessLine())
                        .and(DwdAggregateOrderDO.LANGUAGE).is(view.getLanguage());

                Aggregation orderUserAgg = Aggregation.newAggregation(
                        Aggregation.match(orderCri),
                        Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO),
                        Aggregation.count().as(ReportHomeFlowConversionBO.ORDER_USER_COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportHomeFlowConversionBO> orderUserResults = mongoTemplate.aggregate(orderUserAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportHomeFlowConversionBO.class).getMappedResults();
                int orderUserCount = 0;
                if (!orderUserResults.isEmpty()) {
                    orderUserCount = orderUserResults.get(0).getOrderUserCount() == null ? 0 : orderUserResults.get(0).getOrderUserCount();
                }

                //查询已完成订单用户数
                orderCri.and(DwdAggregateOrderDO.AGGREGATE_ORDER_FINAL_STATE).is(11);
                Aggregation completeOrderUserAgg = Aggregation.newAggregation(
                        Aggregation.match(orderCri),
                        Aggregation.group(DwdAggregateOrderDO.OPERATOR_NO),
                        Aggregation.count().as(ReportHomeFlowConversionBO.COMPLETE_ORDER_USER_COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportHomeFlowConversionBO> completeOrderUserResults = mongoTemplate.aggregate(completeOrderUserAgg, MongoDbCollectonName.DWD_AGGREGATE_ORDER, ReportHomeFlowConversionBO.class).getMappedResults();
                int completeOrderUserCount = 0;
                if (!completeOrderUserResults.isEmpty()) {
                    completeOrderUserCount = completeOrderUserResults.get(0).getCompleteOrderUserCount() == null ? 0 : completeOrderUserResults.get(0).getCompleteOrderUserCount();
                }

                ReportHomeFlowConversionBO conversion = new ReportHomeFlowConversionBO()
                        .setDataTime(dateDTO.getDataTime())
                        .setLanguage(view.getLanguage())
                        .setBusinessLine(businessLine.getCode())
                        .setBusinessName(businessLine.getMessage())
                        .setHomePv(homePv)
                        .setHomeUv(homeUv)
                        .setActiveUserCount(activeUserCount)
                        .setBusinessPv(view.getPv())
                        .setBusinessUv(view.getUv())
                        .setOrderUserCount(orderUserCount)
                        .setCompleteOrderUserCount(completeOrderUserCount)
                        .setBusinessPvRate(homePv > 0 ? NumberUtil.div(Double.valueOf(view.getPv()), Double.valueOf(homePv), 4) : 0)
                        .setBusinessUvRate(activeUserCount > 0 ? NumberUtil.div(Double.valueOf(view.getUv()), Double.valueOf(activeUserCount), 4) : 0)
                        .setOrderUserCountRate(activeUserCount > 0 ? NumberUtil.div(Double.valueOf(orderUserCount), Double.valueOf(activeUserCount), 4) : 0)
                        .setCompleteOrderUserCountRate(activeUserCount > 0 ? NumberUtil.div(Double.valueOf(completeOrderUserCount), Double.valueOf(activeUserCount), 4) : 0)
                        .setCreateTime(new Date());
                mongoTemplate.insert(conversion);
            }
        });
    }

    /**
     * uv数
     *
     * @param criteria
     * @return
     */
    private int countUV(Criteria criteria) {
        int uv = 0;
        try {
            Aggregation uvAgg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group(ClickReportConstant.USERINFOBO_OPERATOR_NO),
                    Aggregation.count().as(ReportPageViewBO.UV))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportPageViewBO> uvDatas = mongoTemplate.aggregate(uvAgg, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportPageViewBO.class).getMappedResults();
            uv = uvDatas.isEmpty() ? 0 : uvDatas.get(0).getUv();
        } catch (Exception e) {
            log.error("查询浏览uv数出现异常", e);
        }
        return uv;
    }

    /**
     * dv数
     *
     * @param criteria
     * @return
     */
    private int countDV(Criteria criteria) {
        int dv = 0;
        try {
            Aggregation dvAgg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID),
                    Aggregation.count().as(ReportPageViewBO.DV))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportPageViewBO> dvDatas = mongoTemplate.aggregate(dvAgg, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportPageViewBO.class).getMappedResults();
            dv = dvDatas.isEmpty() ? 0 : dvDatas.get(0).getDv();
        } catch (Exception e) {
            log.error("查询浏览dv数出现异常", e);
        }
        return dv;
    }

    @Override
    public void saveViewEventRecord(CollectSpmBO collectSpm) {
        try {
            collectSpm.setId(null);
            mongoTemplate.insert(collectSpm, MongoDbCollectonName.VIEW_EVENT_RECORD);
        } catch (Exception e) {
            log.error("保存浏览埋点到临时表出现异常", e);
        }
    }

    @Override
    public void deleteViewEventRecord() {
        log.info("开始删除浏览埋点临时表数据");
        try {
            ReportDateDTO dateDTO = ReportDateDTO.yesterday();
            mongoTemplate.remove(Query.query(Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lt(dateDTO.getEndTime())), MongoDbCollectonName.VIEW_EVENT_RECORD);
        } catch (Exception e) {
            log.error("删除浏览埋点临时表数据出现异常", e);
        }
        log.info("结束删除浏览埋点临时表数据");
    }

    @Override
    public void newDeviceConversionRate() {
        log.info("开始新设备首页转化率");
        try {
            Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);
            Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
            Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);

            //查询新设备数的数量
            Aggregation aggregation = Aggregation.newAggregation(
                            Aggregation.match(Criteria.where("recordTime")
                                    .gte(yesterdayStartTime)
                                    .lt(yesterdayEndTime)),
                            Aggregation.group("language")
                                    .first("language").as(ReportHomePageViewBO.LANGUAGE)
                                    .count().as(ReportHomePageViewBO.DV))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportHomePageViewBO> newDeviceCount = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DEVICE_POOL, ReportHomePageViewBO.class).getMappedResults();
            log.info("新设备数量为：{}", newDeviceCount.size());
            Map<String, Integer> newDevicePv = newDeviceCount.stream().collect(Collectors.toMap(ReportHomePageViewBO::getLanguage,ReportHomePageViewBO::getDv));

            //查询首页名称
            List<String> homePages = mongoTemplate.find(Query.query(Criteria.where(EventPageBO.PAGE_CLASSIFY).is(EventPageClassifyEnum.HOME.getCode())), EventPageBO.class)
                    .stream().map(EventPageBO::getPageCode).collect(toList());
            log.info("首页名称为：{}", homePages);

            LanguageEnum[] languages = LanguageEnum.values();
            for (int i = 0; i < languages.length; i++) {
                LanguageEnum languageEnum = languages[i];
                Criteria deviceListQuery = new Criteria();

                //查询各语言的设备列表
                deviceListQuery.and("recordTime").gte(yesterdayStartTime).lte(yesterdayEndTime)
                        .and(DevicePoolBO.LANGUAGE).is(languageEnum.getCode());
                List<String> deviceList = mongoTemplate.find(new Query().addCriteria(deviceListQuery),
                                DeviceDTO.class, MongoDbCollectonName.DEVICE_POOL)
                        .stream().map(DeviceDTO::getDeviceId).collect(toList());
                log.info("语言:{},设备数为：{}", languageEnum, deviceList.size());
                if (CollectionUtils.isEmpty(deviceList)) {
                    continue;
                }

                //查询各语言的新设备从首页进入频道的数量
                Aggregation bizAggregation = Aggregation.newAggregation(
                                Aggregation.match(Criteria.where("createTime")
                                        .gte(yesterdayStartTime)
                                        .lt(yesterdayEndTime)
                                        .and("deviceInfoBo.deviceId").in(deviceList)
                                        .and("userInfoBo.language").is(languageEnum.getCode())
                                        .and("parentPage").in(homePages)
                                        .and("businessLine").in(BusinessLineEnum.YUMNOW.getCode(), BusinessLineEnum.TINHNOW.getCode())),
                                Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID, ClickReportConstant.BUSINESS_LINE)
                                        .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as("deviceId")
                                        .first(ClickReportConstant.BUSINESS_LINE).as(ReportHomePageViewBO.BUSINESS_LINE)         ,
                                Aggregation.group(ReportHomePageViewBO.BUSINESS_LINE)
                                        .first(ReportHomePageViewBO.BUSINESS_LINE).as(ReportHomePageViewBO.BUSINESS_LINE)
                                        .count().as(ReportHomePageViewBO.DV))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportHomePageViewBO> bizCount = mongoTemplate.aggregate(bizAggregation, MongoDbCollectonName.VIEW_EVENT_RECORD, ReportHomePageViewBO.class).getMappedResults();

                log.info("业务设备数为：{}", bizCount);
                for(ReportHomePageViewBO bo: bizCount) {
                    //新设备从首页下单的设备数
                    Aggregation orderAggregation = Aggregation.newAggregation(
                                    Aggregation.match(Criteria.where("createTime")
                                            .gte(yesterdayStartTime)
                                            .lt(yesterdayEndTime)
                                            .and("deviceInfoBo.deviceId").in(deviceList)
                                            .and("userInfoBo.language").is(languageEnum.getCode())
                                            .and("businessLine").is(bo.getBusinessLine())
                                            .and("eventBo.event").is("order_submit")
                                            .and("businessName").regex("^((?!_other).)*$")),
                                    Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID, ClickReportConstant.BUSINESS_LINE)
                                            .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(DeviceDTO.DEVICE_ID)
                                            .first(ClickReportConstant.BUSINESS_LINE).as(DeviceDTO.BUSINESS_LINE)         ,
                                    Aggregation.group(DeviceDTO.BUSINESS_LINE)
                                            .first(DeviceDTO.BUSINESS_LINE).as(DeviceDTO.BUSINESS_LINE)
                                            .count().as(DeviceDTO.DV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<DeviceDTO> orderCount = mongoTemplate.aggregate(orderAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, DeviceDTO.class).getMappedResults();
                    log.info("语言:{},业务：{}, 下单设备数为：{}", languageEnum, bo.getBusinessLine(), orderCount);
                    if(CollectionUtils.isEmpty(orderCount)) {
                        log.info("下单设备数为空，重新遍历下一个语言");
                        continue;
                    }


                    //新设备从首页进入频道下单且完成
                    List<String> orderDevices = mongoTemplate.findDistinct(new Query().addCriteria(Criteria.where("createTime")
                            .gte(yesterdayStartTime)
                            .lt(yesterdayEndTime)
                            .and("deviceInfoBo.deviceId").in(deviceList)
                            .and("userInfoBo.language").is(languageEnum.getCode())
                            .and("businessLine").is(bo.getBusinessLine())
                            .and("eventBo.event").is("order_submit")
                            .and("businessName").regex("^((?!_other).)*$")),
                            ClickReportConstant.DEVICEINFOBO_DEVICE_ID,
                            MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, String.class);


                    Aggregation orderFinishAggregation = Aggregation.newAggregation(
                                    Aggregation.match(Criteria.where("ORDER_TIME")
                                            .gte(yesterdayStartTime)
                                            .lt(yesterdayEndTime)
                                            .and("DEVICE_ID").in(orderDevices)
                                            .and("AGGREGATE_ORDER_FINAL_STATE").is(11)
                                            .and("BUSINESS_LINE").is(bo.getBusinessLine())),
                                    Aggregation.group("DEVICE_ID", "BUSINESS_LINE")
                                            .first("DEVICE_ID").as("DEVICE_ID")
                                            .first("BUSINESS_LINE").as(DeviceDTO.BUSINESS_LINE),
                                    Aggregation.group(DeviceDTO.BUSINESS_LINE)
                                            .first(DeviceDTO.BUSINESS_LINE).as(DeviceDTO.BUSINESS_LINE)
                                            .count().as(DeviceDTO.DV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<DeviceDTO> orderFinishDevices = mongoTemplate.aggregate(orderFinishAggregation, MongoDbCollectonName.AGGREGATE_ORDER, DeviceDTO.class).getMappedResults();
                    log.info("语言:{},业务：{}, 完成订单设备数为：{}", languageEnum, bo.getBusinessLine(), orderFinishDevices);

                    ReportNewDeviceConversionRateBO conversionRateBO = new ReportNewDeviceConversionRateBO();
                    conversionRateBO.setCreateTime(new Date());
                    conversionRateBO.setBusinessLine(bo.getBusinessLine());
                    conversionRateBO.setHomePageBizdv(bo.getDv());
                    if(CollectionUtils.isEmpty(orderCount)) {
                        conversionRateBO.setHomePageBizOrderDv(0);
                    } else {
                        conversionRateBO.setHomePageBizOrderDv(Optional.ofNullable(orderCount.get(0)).orElse(new DeviceDTO()).getDv());
                    }
                    if(CollectionUtils.isEmpty(orderFinishDevices)) {
                        conversionRateBO.setHomePageBizOrderFinishDv(0);
                    } else {
                        conversionRateBO.setHomePageBizOrderFinishDv(Optional.ofNullable(orderFinishDevices.get(0)).orElse(new DeviceDTO()).getDv());
                    }                    conversionRateBO.setLanguage(languageEnum.getCode());
                    conversionRateBO.setHomePagedv(newDevicePv.get(languageEnum.getCode()));
                    conversionRateBO.setDataTime(yesterday);
                    try {
                        log.info("设备转化为：{}", conversionRateBO);
                        mongoTemplate.save(conversionRateBO);
                    } catch (Exception e) {
                        log.error("新设备首页转化率异常,conversionRateBO:{}", conversionRateBO, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("新设备首页转化率异常", e);
        }
        log.info("结束新设备首页转化率");
    }
}
