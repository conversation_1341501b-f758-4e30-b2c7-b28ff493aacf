package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY)
@Data
public class InviteRegisterReportDayBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY;

    private static final long serialVersionUID = -2599035028656451031L;

    public static String ID = "_id";

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 更新日期
     */
    private Date updateTime;

    public static String UPDATE_TIME = "updateTime";

    /**
     * 数据日期
     */
    private Date dataTime;

    public static String DATA_TIME = "dataTime";

    /**
     * 事件
     */
    public String even;

    public static String EVEN = "even";

    /**
     * 业务名称
     */
    public String businessName;

    public static String BUSINESS_NAME = "businessName";

    /**
     * 语言
     */
    public String language;

    public static String  LANGUAGE = "language";

    /**
     * 渠道
     */
    public String channel;

    public static String CHANNEL = "channel";

    /**
     * 数量
     */
    private Long count;

    public static String COUNT = "count";

    /**
     * 数量
     */
    private Long uv;

    public static String UV = "uv";

    private String from;

    public static String FROM = "from";

    public String activityNo;

    public static String ACTIVITYNO = "activityNo";

}
