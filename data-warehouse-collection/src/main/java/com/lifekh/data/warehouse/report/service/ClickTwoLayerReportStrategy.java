package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.collection.CollectionUtil;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class ClickTwoLayerReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //WOWNOW首页
        dayBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_TOTAL, WOWNOW_HOME_PAGE_ARR);

        //我的页面
        dayBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_MINE, ClickReportConstant.WOWNOW_MINE_PAGE_LIST);
        return null;
    }

    private void dayBasicStatistics(ReportBasicReqDTO reportBasicReqDTO, String topPageName, String[] pageNames) {
        Query queryHomePage = new Query();
        queryHomePage.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportDayBO.PAGE_NAME).is(topPageName));
        ClickReportDayBO homePageBo = mongoTemplate.findOne(queryHomePage, ClickReportDayBO.class);
        if (homePageBo == null) {
            homePageBo = new ClickReportDayBO();
        }

        //查询卡-节点级日数据 pv uv dv
        AggregationResults<ClickReportDayBO> pagePageAndCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageDayCardAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportDayBO.COLLECTION_NAME, ClickReportDayBO.class);
        List<ClickReportDayBO> cardPageBoList = pagePageAndCardResults.getMappedResults();

        //遍历入库
        if (CollectionUtil.isNotEmpty(cardPageBoList)) {
            for (ClickReportDayBO dayBo : cardPageBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportDayBO.FIRST_NAME).is(topPageName + "@" + dayBo.getPageName())
                        .and(ClickTwoLayerReportDayBO.SECOND_NAME).is(dayBo.getCardName()));

                ClickTwoLayerReportDayBO dayTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportDayBO.class);
                if (Objects.isNull(dayTwoLayerBo)) {
                    ClickTwoLayerReportDayBO bo = new ClickTwoLayerReportDayBO();
                    bo.setCreateTime(new Date());
                    bo.setUpdateTime(new Date());
                    bo.setFirstName(topPageName + "@" + dayBo.getPageName());
                    bo.setSecondName(dayBo.getCardName());
                    bo.setFullName(topPageName+ "@" + dayBo.getPageName() + "@" + dayBo.getCardName());
                    bo.setDataTime(reportBasicReqDTO.getDataTime());
                    bo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                    bo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                    bo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                    bo.setSuv(dayBo.getUv());
                    bo.setSpv(dayBo.getPv());
                    bo.setSdv(dayBo.getDv());
                    mongoTemplate.save(bo);
                } else {
                    dayTwoLayerBo.setUpdateTime(new Date());
                    dayTwoLayerBo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setSuv(dayBo.getUv());
                    dayTwoLayerBo.setSpv(dayBo.getPv());
                    dayTwoLayerBo.setSdv(dayBo.getDv());
                    dayTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                    mongoTemplate.save(dayTwoLayerBo);
                }
            }
        }

        //查询卡-节点级日数据 pv uv dv
        AggregationResults<ClickReportDayBO> pageCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageDayCardAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportDayBO.COLLECTION_NAME, ClickReportDayBO.class);

        //查询首页下的各个数据
        List<ClickReportDayBO> pageCardList = pageCardResults.getMappedResults();
        for (ClickReportDayBO cardBo : pageCardList) {
            //查询节点下的各个数据
            Query queryNode = new Query();
            queryNode.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportDayBO.PAGE_NAME).is(cardBo.getPageName())
                    .and(ClickReportDayBO.CARD_NAME).is(cardBo.getCardName()));
            List<ClickReportDayBO> nodeBoList = mongoTemplate.find(queryNode, ClickReportDayBO.class);

            for (ClickReportDayBO nodeBo : nodeBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportDayBO.FIRST_NAME).is(nodeBo.getCardName())
                        .and(ClickTwoLayerReportDayBO.SECOND_NAME).is(nodeBo.getNodeName()));

                ClickTwoLayerReportDayBO dayTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportDayBO.class);
                if (Objects.isNull(dayTwoLayerBo)) {
                    ClickTwoLayerReportDayBO bo = new ClickTwoLayerReportDayBO();
                    bo.setCreateTime(new Date());
                    bo.setUpdateTime(new Date());
                    bo.setFirstName(nodeBo.getCardName());
                    bo.setSecondName(nodeBo.getNodeName());
                    bo.setFullName(nodeBo.getCardName() + "@" + nodeBo.getNodeName());
                    bo.setDataTime(reportBasicReqDTO.getDataTime());
                    bo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                    bo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                    bo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                    bo.setSuv(nodeBo.getUv());
                    bo.setSpv(nodeBo.getPv());
                    bo.setSdv(nodeBo.getDv());
                    mongoTemplate.save(bo);
                } else {
                    dayTwoLayerBo.setUpdateTime(new Date());
                    dayTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                    dayTwoLayerBo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                    dayTwoLayerBo.setSuv(nodeBo.getUv());
                    dayTwoLayerBo.setSpv(nodeBo.getPv());
                    dayTwoLayerBo.setSdv(nodeBo.getDv());
                    mongoTemplate.save(dayTwoLayerBo);
                }
            }
        }
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageDayCardAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportDayBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageCardWeekAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportWeekBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportWeekBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportWeekBO.PAGE_NAME, ClickReportWeekBO.CARD_NAME)
                .first(ClickReportWeekBO.PAGE_NAME).as(ClickReportWeekBO.PAGE_NAME)
                .first(ClickReportWeekBO.CARD_NAME).as(ClickReportWeekBO.CARD_NAME)
                .first(ClickReportWeekBO.NODE_NAME).as(ClickReportWeekBO.NODE_NAME)
                .sum(ClickReportWeekBO.PV).as(ClickReportWeekBO.PV)
                .sum(ClickReportWeekBO.DV).as(ClickReportWeekBO.DV)
                .sum(ClickReportWeekBO.UV).as(ClickReportWeekBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportWeekBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageCardMonthAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportMonthBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportMonthBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportMonthBO.PAGE_NAME, ClickReportMonthBO.CARD_NAME)
                .first(ClickReportMonthBO.PAGE_NAME).as(ClickReportMonthBO.PAGE_NAME)
                .first(ClickReportMonthBO.CARD_NAME).as(ClickReportMonthBO.CARD_NAME)
                .first(ClickReportMonthBO.NODE_NAME).as(ClickReportMonthBO.NODE_NAME)
                .sum(ClickReportMonthBO.PV).as(ClickReportMonthBO.PV)
                .sum(ClickReportMonthBO.DV).as(ClickReportMonthBO.DV)
                .sum(ClickReportMonthBO.UV).as(ClickReportMonthBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportMonthBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageWeekCardAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportWeekBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportWeekBO.PAGE_NAME, ClickReportWeekBO.CARD_NAME)
                .first(ClickReportWeekBO.PAGE_NAME).as(ClickReportWeekBO.PAGE_NAME)
                .first(ClickReportWeekBO.CARD_NAME).as(ClickReportWeekBO.CARD_NAME)
                .first(ClickReportWeekBO.NODE_NAME).as(ClickReportWeekBO.NODE_NAME)
                .sum(ClickReportWeekBO.PV).as(ClickReportWeekBO.PV)
                .sum(ClickReportWeekBO.DV).as(ClickReportWeekBO.DV)
                .sum(ClickReportWeekBO.UV).as(ClickReportWeekBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportWeekBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageMonthCardAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportMonthBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportMonthBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportMonthBO.PAGE_NAME, ClickReportMonthBO.CARD_NAME)
                .first(ClickReportMonthBO.PAGE_NAME).as(ClickReportMonthBO.PAGE_NAME)
                .first(ClickReportMonthBO.CARD_NAME).as(ClickReportMonthBO.CARD_NAME)
                .first(ClickReportMonthBO.NODE_NAME).as(ClickReportMonthBO.NODE_NAME)
                .sum(ClickReportMonthBO.PV).as(ClickReportMonthBO.PV)
                .sum(ClickReportMonthBO.DV).as(ClickReportMonthBO.DV)
                .sum(ClickReportMonthBO.UV).as(ClickReportMonthBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        //WOWNOW首页
        weekBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_TOTAL, WOWNOW_HOME_PAGE_ARR);

        //我的页面
        weekBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_MINE, ClickReportConstant.WOWNOW_MINE_PAGE_LIST);
        return null;
    }

    private void weekBasicStatistics(ReportBasicReqDTO reportBasicReqDTO, String topPageName, String[] pageNames) {
        //查询页-卡级日数据 pv uv dv
        Query queryHomePage = new Query();
        queryHomePage.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportWeekBO.PAGE_NAME).is(topPageName));
        ClickReportWeekBO homePageBo = mongoTemplate.findOne(queryHomePage, ClickReportWeekBO.class);
        if (homePageBo == null) {
            homePageBo = new ClickReportWeekBO();
        }

        //构造分组条件
        AggregationResults<ClickReportWeekBO> pagePageAndCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageCardWeekAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportWeekBO.COLLECTION_NAME, ClickReportWeekBO.class);
        List<ClickReportWeekBO> cardPageBoList = pagePageAndCardResults.getMappedResults();

        //遍历入库
        if (CollectionUtil.isNotEmpty(cardPageBoList)) {
            for (ClickReportWeekBO weekBo : cardPageBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportWeekBO.FIRST_NAME).is(topPageName + "@" + weekBo.getPageName())
                        .and(ClickTwoLayerReportWeekBO.SECOND_NAME).is(weekBo.getCardName()));

                ClickTwoLayerReportWeekBO weekTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportWeekBO.class);

                try {
                    if (Objects.isNull(weekTwoLayerBo)) {
                        ClickTwoLayerReportWeekBO bo = new ClickTwoLayerReportWeekBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setFirstName(topPageName + "@" + weekBo.getPageName());
                        bo.setSecondName(weekBo.getCardName());
                        bo.setFullName(topPageName + "@" + weekBo.getPageName() + "@" + weekBo.getCardName());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                        bo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                        bo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                        bo.setSuv(weekBo.getUv());
                        bo.setSpv(weekBo.getPv());
                        bo.setSdv(weekBo.getDv());
                        bo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        weekTwoLayerBo.setUpdateTime(new Date());
                        weekTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                        weekTwoLayerBo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setSuv(weekBo.getUv());
                        weekTwoLayerBo.setSpv(weekBo.getPv());
                        weekTwoLayerBo.setSdv(weekBo.getDv());
                        weekTwoLayerBo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(weekTwoLayerBo);
                    }
                } catch (Exception e) {
                    log.warn("统计周数据异常, reportBasicReqDTO:{}, topPageName:{}", reportBasicReqDTO, topPageName, e);
                }
            }
        }

        //查询卡-节点级日数据 pv uv dv
        AggregationResults<ClickReportWeekBO> pageCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageWeekCardAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportWeekBO.COLLECTION_NAME, ClickReportWeekBO.class);

        //查询首页下的各个数据
        List<ClickReportWeekBO> pageCardList = pageCardResults.getMappedResults();
        for (ClickReportWeekBO cardBo : pageCardList) {
            //查询节点下的各个数据
            Query queryNode = new Query();
            queryNode.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportWeekBO.PAGE_NAME).is(cardBo.getPageName())
                    .and(ClickReportWeekBO.CARD_NAME).is(cardBo.getCardName()));
            List<ClickReportWeekBO> nodeBoList = mongoTemplate.find(queryNode, ClickReportWeekBO.class);
            for (ClickReportWeekBO nodeBo : nodeBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportWeekBO.FIRST_NAME).is(nodeBo.getCardName())
                        .and(ClickTwoLayerReportWeekBO.SECOND_NAME).is(nodeBo.getNodeName()));

                ClickTwoLayerReportWeekBO weekTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportWeekBO.class);
                try {
                    if (Objects.isNull(weekTwoLayerBo)) {
                        ClickTwoLayerReportWeekBO bo = new ClickTwoLayerReportWeekBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setFirstName(nodeBo.getCardName());
                        bo.setSecondName(nodeBo.getNodeName());
                        bo.setFullName(nodeBo.getCardName() + "@" + nodeBo.getNodeName());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                        bo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                        bo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                        bo.setSuv(nodeBo.getUv());
                        bo.setSpv(nodeBo.getPv());
                        bo.setSdv(nodeBo.getDv());
                        bo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        weekTwoLayerBo.setUpdateTime(new Date());
                        weekTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                        weekTwoLayerBo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                        weekTwoLayerBo.setSuv(nodeBo.getUv());
                        weekTwoLayerBo.setSpv(nodeBo.getPv());
                        weekTwoLayerBo.setSdv(nodeBo.getDv());
                        weekTwoLayerBo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(weekTwoLayerBo);
                    }
                } catch (Exception e) {
                    log.warn("统计周数据异常sub, reportBasicReqDTO:{}, topPageName:{}", reportBasicReqDTO, topPageName, e);
                }
            }
        }
    }


    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        //WOWNWOW首页
        monthBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_TOTAL, WOWNOW_HOME_PAGE_ARR);

        //我的页面
        monthBasicStatistics(reportBasicReqDTO, ClickReportConstant.WOWNOW_CLICK_MINE, ClickReportConstant.WOWNOW_MINE_PAGE_LIST);
        return null;
    }

    private void monthBasicStatistics(ReportBasicReqDTO reportBasicReqDTO, String topPageName, String[] pageNames) {
        //查询页-卡级日数据 pv uv dv
        Query queryHomePage = new Query();
        queryHomePage.addCriteria(Criteria.where(ClickReportMonthBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportMonthBO.PAGE_NAME).is(topPageName));
        ClickReportMonthBO homePageBo = mongoTemplate.findOne(queryHomePage, ClickReportMonthBO.class);
        if (homePageBo == null) {
            homePageBo = new ClickReportMonthBO();
        }

        //构造分组条件
        AggregationResults<ClickReportMonthBO> pagePageAndCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageCardMonthAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportMonthBO.COLLECTION_NAME, ClickReportMonthBO.class);
        List<ClickReportMonthBO> cardPageBoList = pagePageAndCardResults.getMappedResults();

        //遍历入库
        if (CollectionUtil.isNotEmpty(cardPageBoList)) {
            for (ClickReportMonthBO monthBo : cardPageBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportMonthBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportMonthBO.FIRST_NAME).is(topPageName + "@" + monthBo.getPageName())
                        .and(ClickTwoLayerReportMonthBO.SECOND_NAME).is(monthBo.getCardName()));

                ClickTwoLayerReportMonthBO dayTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportMonthBO.class);
                try {
                    if (Objects.isNull(dayTwoLayerBo)) {
                        ClickTwoLayerReportMonthBO bo = new ClickTwoLayerReportMonthBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setFirstName(topPageName + "@" + monthBo.getPageName());
                        bo.setSecondName(monthBo.getCardName());
                        bo.setFullName(topPageName + "@" + monthBo.getPageName()+ "@" + monthBo.getCardName());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                        bo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                        bo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                        bo.setSuv(monthBo.getUv());
                        bo.setSpv(monthBo.getPv());
                        bo.setSdv(monthBo.getDv());
                        bo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        dayTwoLayerBo.setUpdateTime(new Date());
                        dayTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                        dayTwoLayerBo.setFuv(Optional.ofNullable(homePageBo.getUv()).orElseGet(() -> 0L));
                        dayTwoLayerBo.setFpv(Optional.ofNullable(homePageBo.getPv()).orElseGet(() -> 0L));
                        dayTwoLayerBo.setFdv(Optional.ofNullable(homePageBo.getDv()).orElseGet(() -> 0L));
                        dayTwoLayerBo.setSuv(monthBo.getUv());
                        dayTwoLayerBo.setSpv(monthBo.getPv());
                        dayTwoLayerBo.setSdv(monthBo.getDv());
                        dayTwoLayerBo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(dayTwoLayerBo);
                    }
                } catch (Exception e) {
                    log.warn("统计月点击数据异常, reportBasicReqDTO:{}, pageNames:{}", reportBasicReqDTO, pageNames, e);
                }
            }
        }

        //查询卡-节点级日数据 pv uv dv
        AggregationResults<ClickReportMonthBO> pageCardResults =
                mongoTemplate.aggregate(
                        Aggregation.newAggregation(
                                getHomePageMonthCardAggregationCriteria(reportBasicReqDTO, pageNames)),
                        ClickReportMonthBO.COLLECTION_NAME, ClickReportMonthBO.class);

        //查询首页下的各个数据
        List<ClickReportMonthBO> pageCardList = pageCardResults.getMappedResults();
        for (ClickReportMonthBO cardBo : pageCardList) {
            //查询节点下的各个数据
            Query queryNode = new Query();
            queryNode.addCriteria(Criteria.where(ClickReportMonthBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportMonthBO.PAGE_NAME).is(cardBo.getPageName())
                    .and(ClickReportMonthBO.CARD_NAME).is(cardBo.getCardName()));
            List<ClickReportMonthBO> nodeBoList = mongoTemplate.find(queryNode, ClickReportMonthBO.class);

            for (ClickReportMonthBO nodeBo : nodeBoList) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickTwoLayerReportWeekBO.FIRST_NAME).is(nodeBo.getCardName())
                        .and(ClickTwoLayerReportWeekBO.SECOND_NAME).is(nodeBo.getNodeName()));

                ClickTwoLayerReportMonthBO monthTwoLayerBo = mongoTemplate.findOne(query, ClickTwoLayerReportMonthBO.class);
                try {
                    if (Objects.isNull(monthTwoLayerBo)) {
                        ClickTwoLayerReportMonthBO bo = new ClickTwoLayerReportMonthBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setFirstName(nodeBo.getCardName());
                        bo.setSecondName(nodeBo.getNodeName());
                        bo.setFullName(nodeBo.getCardName() + "@" + nodeBo.getNodeName());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                        bo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                        bo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                        bo.setSuv(nodeBo.getUv());
                        bo.setSpv(nodeBo.getPv());
                        bo.setSdv(nodeBo.getDv());
                        bo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        monthTwoLayerBo.setUpdateTime(new Date());
                        monthTwoLayerBo.setDataTime(reportBasicReqDTO.getDataTime());
                        monthTwoLayerBo.setFuv(Optional.ofNullable(cardBo.getUv()).orElseGet(() -> 0L));
                        monthTwoLayerBo.setFpv(Optional.ofNullable(cardBo.getPv()).orElseGet(() -> 0L));
                        monthTwoLayerBo.setFdv(Optional.ofNullable(cardBo.getDv()).orElseGet(() -> 0L));
                        monthTwoLayerBo.setSuv(nodeBo.getUv());
                        monthTwoLayerBo.setSpv(nodeBo.getPv());
                        monthTwoLayerBo.setSdv(nodeBo.getDv());
                        monthTwoLayerBo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(monthTwoLayerBo);
                    }
                } catch (Exception e) {
                    log.warn("统计月点击数据异常sub, reportBasicReqDTO:{}, pageNames:{}", reportBasicReqDTO, pageNames, e);
                }
            }
        }
    }
}
