package com.lifekh.data.warehouse.report;

import cn.hutool.core.collection.CollectionUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.DwdUserBehaviorInfoDO;
import com.lifekh.data.warehouse.bo.InviteRegisterReportDayBO;
import com.lifekh.data.warehouse.bo.ReportTargetBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.TargetBaseReqDTO;
import com.lifekh.data.warehouse.enums.ReportTargetEnum;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

@Slf4j
@ElasticJobConf(name = "reback_and_silent_user_report_job", cron = "0 0 7 * * ?", description = "统计回流用户和沉默用户任务")
public class RebackAndSilentUserReportJob extends AbstractSimpleJob  {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    @Autowired
    private TargetReportService targetReportService;

    @Value("${reback.users.days:8}")
    private int rebackUsersDays;

    @Value("${silent.users.days.lte:7}")
    private int silentUsersDaysLte;

    @Value("${silent.users.days.gte:30}")
    private int silentUsersDaysGte;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        long page = 0, pageSize = 1000;
        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(new Criteria().and(DwdUserBehaviorInfoDO.ONLINE_TIME).gte(DateUtil.getYesterdayStartDate())
                .lt(DateUtil.getYesterdayEndTime())));
        querList.add(Aggregation.sort(Sort.Direction.DESC, DwdUserBehaviorInfoDO.ONLINE_TIME));

        double rebackUsers = 0;
        Long total = null;
        try {
            do {
                PageRespDTO<DwdUserBehaviorInfoDO> pageRespDTO = mongoPageHelper.query(
                        querList, new PageReqDTO(page++, pageSize), MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO, true, total, DwdUserBehaviorInfoDO.class);

                log.info("回流和沉默用户数据统计,当前页数:{}, 当前页数量:{}", (page-1), pageRespDTO.getList().size());
                if(CollectionUtil.isEmpty(pageRespDTO.getList()) || pageRespDTO.getPages() == (page-1)){
                    break;
                }

                if(total == null) {
                    total = pageRespDTO.getTotal();
                }

                for(DwdUserBehaviorInfoDO bo : pageRespDTO.getList()) {
                    //统计回流用户数据 onlineTime - lastOnlineTime >= 8
                    long days = DateUtil.calBetweenDays(bo.getLastOnlineTime(), bo.getOnlineTime());
                    if(days >= rebackUsersDays ) {
                        rebackUsers++;
                    }

                }
            } while (true);
        } catch (Exception e) {
            log.error("统计回流用户异常", e);
        }

        //回流用户入库
        targetReportService.statisticsRebackUsers(rebackUsers);

        //统计沉默用户数据  onlineTime > 30day && onlineTime < 7day
        Criteria criteria = new Criteria();
        criteria.and(DwdUserBehaviorInfoDO.ONLINE_TIME).gt(dateAddOneDay(new Date(), -silentUsersDaysGte))
                .lt(dateAddOneDay(new Date(), -silentUsersDaysLte));
        long silentCount = mongoTemplate.count(new Query(criteria), MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
        targetReportService.statisticsSilentUsers(silentCount);
    }

    public static Date dateAddOneDay(Date date,Integer day){
        Calendar   calendar   =   new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(calendar.DATE,day);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }
}
