package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_SMS_BIZ_COUNT)
@Data
@Accessors(chain = true)
public class ReportSmsBizCountBO implements Serializable {
    private static final long serialVersionUID = -2049795582598557512L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 数量
     */
    private Integer count;
    public final static String COUNT = "count";

    private Date createTime;

    private Date updateTime;
}
