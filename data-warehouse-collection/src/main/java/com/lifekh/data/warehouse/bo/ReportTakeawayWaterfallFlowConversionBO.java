package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Document(collection = MongoDbCollectonName.REPORT_TAKEAWAY_WATERFALL_FLOW_CONVERSION)
@Data
@Accessors(chain = true)
public class ReportTakeawayWaterfallFlowConversionBO extends ReportTakeawayFlowConversionBO implements Serializable {
    private static final long serialVersionUID = 6192353663647048926L;

    private String storeId;

    private String storeName;

    public String getGroupKey() {
        return getStoreId() + "_" + getLanguage() + "_" + getDeviceType() + "_" + getAppVersion() + "_" + getProvinceName();
    }
}
