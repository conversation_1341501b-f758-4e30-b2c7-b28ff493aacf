package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_SMS_VERIFY_RATE)
@Data
@Accessors(chain = true)
public class ReportSmsVerifyRateBO implements Serializable {
    private static final long serialVersionUID = -2049795582598557512L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 短信成功数
     */
    private Integer smsSuccessCount;

    /**
     * 短信失败数
     */
    private Integer smsFailCount;

    /**
     * 短信发送总数
     */
    private Integer smsTotalCount;

    /**
     * 短信成功数
     */
    private Integer voipSuccessCount;

    /**
     * 短信失败数
     */
    private Integer voipFailCount;

    /**
     * 语音发送总数
     */
    private Integer voipTotalCount;

    private Date createTime;

    private Date updateTime;
}
