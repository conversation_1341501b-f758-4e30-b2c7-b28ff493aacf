package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.PageViewGroupByBO;
import com.lifekh.data.warehouse.bo.ReportHomePageOnlineUserBO;
import com.lifekh.data.warehouse.bo.collection.CollectHomePageViewBO;
import com.lifekh.data.warehouse.bo.collection.ReportPVBO;
import com.lifekh.data.warehouse.bo.collection.ReportPVByDayBO;
import com.lifekh.data.warehouse.constant.FunctionSwitchConstant;
import com.lifekh.data.warehouse.manage.FunctionSwitchManager;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.mongodb.client.result.DeleteResult;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service("userViewReportService")
@Slf4j
public class UserViewReportServiceImpl implements UserViewReportService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private FunctionSwitchManager functionSwitchManager;

    @Override
    public void countTheUVofTheCurrentPage() {
        //1.查询当前页面昨天的spm数据并且分组，按数量从小到大排序
        Criteria criteria = new Criteria();
        criteria.and("currentPage").ne(null).and("userInfoBo.operatorNo").ne(null);
        criteria.and("createTime").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1))
                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));

//        criteria.and("parentPage").is(null);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("parentPage","currentPage","userInfoBo.operatorNo")
                        .first("parentPage").as("parentPage")
                        .first("currentPage").as("currentPage")
                        .count().as("num"),
                Aggregation.group("parentPage","currentPage")
                        .first("parentPage").as("parentPage")
                        .first("currentPage").as("currentPage")
                        .count().as("num"),
                Aggregation.sort(Sort.Direction.ASC, "num")
        );
        AggregationResults<PageViewGroupByBO> aggregationResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_VIEW_PAGE,PageViewGroupByBO.class);

        //2.根据数量进行分片统计
        List<PageViewGroupByBO> list = aggregationResults.getMappedResults();

        //3.入库
        for(PageViewGroupByBO currentPage : list) {
            //查询总表
            Query query = new Query();
            query.addCriteria(Criteria.where("parentPage").is(currentPage.getParentPage()).and("currentPage").is(currentPage.getCurrentPage()));
            ReportPVBO reportPVBO = mongoTemplate.findOne(query,ReportPVBO.class);

            if(Objects.isNull(reportPVBO)) {
                reportPVBO = new ReportPVBO();
                reportPVBO.setCreateTime(new Date());
                reportPVBO.setParentPage(currentPage.getParentPage());
                reportPVBO.setCurrentPage(currentPage.getCurrentPage());
                reportPVBO.setViewNum(0L);
                reportPVBO.setUserNum(0L);
                reportPVBO.setDeviceNum(0L);
                reportPVBO.setAlias("");
            }

            //计算当前页面的访问数
            reportPVBO.setUserNum(reportPVBO.getUserNum() + currentPage.getNum());

            //更新总表
            mongoTemplate.save(reportPVBO);

            //按天插入表
            //查询天表
            Query queryDay = new Query();
            queryDay.addCriteria(Criteria.where("reportPVId").is(reportPVBO.getId()).and("day").is(DateUtil.getDayStr(new Date(), Calendar.DATE,-1)));
            ReportPVByDayBO reportPVByDayBO1 = mongoTemplate.findOne(queryDay,ReportPVByDayBO.class);
            if (Objects.isNull(reportPVByDayBO1)) {
                ReportPVByDayBO reportPVByDayBO= new ReportPVByDayBO();
                reportPVByDayBO.setCreateTime(new Date());
                reportPVByDayBO.setUpdateTime(new Date());
                reportPVByDayBO.setDay(DateUtil.getDayStr(new Date(), Calendar.DATE,-1));
                reportPVByDayBO.setUserNum(currentPage.getNum());
                reportPVByDayBO.setReportPVId(reportPVBO.getId());
                mongoTemplate.save(reportPVByDayBO);
            }else {
                reportPVByDayBO1.setUpdateTime(new Date());
                reportPVByDayBO1.setUserNum(currentPage.getNum());
                mongoTemplate.save(reportPVByDayBO1);
            }
            log.info("已完成页面UV统计, 页面标识:{}", currentPage.getCurrentPage());
        }
    }

    @Override
    public void currentOnlineUser() throws PendingException {
        Date endTime = DateUtil.getCurrentHour();
        Date hourStartTime = DateUtil.addMinute(endTime, -60);
        Date dataTime = DateUtil.addHour(endTime, 7);

        log.info("开始统计WOWNOW首页流量");
        ReportHomePageOnlineUserBO bo = new ReportHomePageOnlineUserBO(endTime, dataTime, 0, 0, 0, 0);
        try {
            //DV
            AggregationResults<ReportHomePageOnlineUserBO> wownowDv =
                    mongoTemplate.aggregate(getAggregation(hourStartTime, endTime,
                                    ReportHomePageOnlineUserBO.WOWNOW_HOUR_DV, CollectHomePageViewBO.DEVICE_ID),
                            MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP, ReportHomePageOnlineUserBO.class);
            if(!wownowDv.getMappedResults().isEmpty()) {
                bo.setWownowHourDv(wownowDv.getMappedResults().get(0).getWownowHourDv());
            }

            //UV
            AggregationResults<ReportHomePageOnlineUserBO> wownowUv =
                    mongoTemplate.aggregate(getAggregation(hourStartTime, endTime,
                                    ReportHomePageOnlineUserBO.WOWNOW_HOUR_UV, CollectHomePageViewBO.OPERATOR_NO),
                            MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP, ReportHomePageOnlineUserBO.class);
            if(!wownowUv.getMappedResults().isEmpty()) {
                bo.setWownowHourUv(wownowUv.getMappedResults().get(0).getWownowHourUv());
            }
        } catch (Exception e) {
            log.error("WOWNOW首页流量统计异常", e);
        }

        log.info("结束统计WOWNOW首页流量");

        log.info("开始统计外卖首页流量");
        try {
            //DV
            AggregationResults<ReportHomePageOnlineUserBO> takeawayDv =
                    mongoTemplate.aggregate(getAggregation(hourStartTime, endTime,
                                    ReportHomePageOnlineUserBO.TAKEAWAY_HOUR_DV, CollectHomePageViewBO.DEVICE_ID),
                            MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP, ReportHomePageOnlineUserBO.class);
            if(!takeawayDv.getMappedResults().isEmpty()) {
                bo.setTakeawayHourDv(takeawayDv.getMappedResults().get(0).getTakeawayHourDv());
            }

            //UV
            AggregationResults<ReportHomePageOnlineUserBO> takeawayUv =
                    mongoTemplate.aggregate(getAggregation(hourStartTime, endTime,
                                    ReportHomePageOnlineUserBO.TAKEAWAY_HOUR_UV, CollectHomePageViewBO.OPERATOR_NO),
                            MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP, ReportHomePageOnlineUserBO.class);
            if(!takeawayUv.getMappedResults().isEmpty()) {
                bo.setTakeawayHourUv(takeawayUv.getMappedResults().get(0).getTakeawayHourUv());
            }
        } catch (Exception e) {
            log.error("外卖首页流量统计异常", e);
        }
        log.info("结束统计外卖首页流量");

        try {
            mongoTemplate.save(bo);
        } catch (Exception e) {
            log.error("首页每小时流量入库失败", e);
        }

        //删除首页临时表数据
        deleteTempData(hourStartTime, endTime);
    }

    public void deleteTempData(Date startTime, Date endTime) {
        Date deleteTime = DateUtil.addMinute(startTime, 5);
        log.warn("开始删除首页埋点临时表, startTime:{}", startTime);
        do {
            try {
                if(!functionSwitchManager.getSwitchBool(FunctionSwitchConstant.DELETE_HOME_PAGE_TEMP_SWITCH)) {
                    break;
                }
                Query query = new Query();
                query.addCriteria(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).lt(deleteTime).gte(startTime));

                DeleteResult result = mongoTemplate.remove(query, MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
                log.info("删除集合 collect_buried_point_home_page_temp, 时间:{} ~ {}, 删除数量:{}",startTime, deleteTime, result.getDeletedCount());

                result =mongoTemplate.remove(query, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP);
                log.info("删除集合 collect_buried_point_takeaway_home_page_temp, 时间:{} ~ {}, 删除数量:{}",startTime, deleteTime, result.getDeletedCount());

                startTime = deleteTime;
                deleteTime = DateUtil.addMinute(startTime, 5);
            } catch (Exception e) {
                log.warn("删除临时表异常", e);
            }
        } while (endTime.getTime() >= deleteTime.getTime());
        log.warn("已完成首页埋点临时表删除操作, endTime:{}", deleteTime);
    }

    private Aggregation getAggregation(Date startTime, Date endTime, String totalFieldName, String... fields) {
        Criteria criteria = Criteria.where(CollectHomePageViewBO.CREATE_TIME)
                .gte(startTime)
                .lt(endTime);
        return Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group(fields),
                        Aggregation.count().as(totalFieldName))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
    }
}
