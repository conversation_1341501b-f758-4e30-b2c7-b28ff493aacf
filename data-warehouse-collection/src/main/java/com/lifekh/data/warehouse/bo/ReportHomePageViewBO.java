package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOME_PAGE_VIEW)
@Data
@Accessors(chain = true)
public class ReportHomePageViewBO implements Serializable {
    private static final long serialVersionUID = -790279729371724409L;

    public static final String DATA_TIME = "dataTime";
    public static final String LANGUAGE = "language";
    public static final String BUSINESS_LINE = "businessLine";
    public static final String BUSINESS_NAME = "businessName";
    public static final String DEVICE_TYPE = "deviceType";
    public static final String PV = "pv";
    public static final String UV = "uv";
    public static final String DV = "dv";

    private Date dataTime;
    private String language;
    private String businessLine;
    private String businessName;
    private String deviceType;
    private Integer pv;
    private Double pvRate;
    private Integer uv;
    private Double uvRate;
    private Integer dv;
    private Double dvRate;
    private Date createTime;
}
