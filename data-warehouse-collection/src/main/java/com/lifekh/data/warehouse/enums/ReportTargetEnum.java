package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportTargetEnum implements BaseEnum<ReportTargetEnum, String> {

    REMAIN_USER_COUNT("remain_user_count", "留存用户数"),
    ORDER_USER_COUNT("order_user_count", "下单用户数"),
    AVG_INVITE_ACTIVITY_COUNT("avg_invite_activity_count", "用户平均邀请数（邀请有礼）"),
    YUMNOW_ORDER_RATE("yumnow_order_conversion_rate", "外卖下单转化率"),
    YUMNOW_ORDER_ZH_RATE("yumnow_order_zh_conversion_rate", "外卖下单转化率中文"),
    YUMNOW_ORDER_EN_RATE("yumnow_order_en_conversion_rate", "外卖下单转化率英文"),
    YUMNOW_ORDER_KM_RATE("yumnow_order_km_conversion_rate", "外卖下单转化率柬文"),
    TINHNOW_ORDER_RATE("tinhnow_order_conversion_rate", "电商下单转化率"),
    TINHNOW_ORDER_ZH_RATE("tinhnow_order_zh_conversion_rate", "电商下单转化率中文"),
    TINHNOW_ORDER_EN_RATE("tinhnow_order_en_conversion_rate", "电商下单转化率英文"),
    TINHNOW_ORDER_KM_RATE("tinhnow_order_km_conversion_rate", "电商下单转化率柬文"),
    HOME_PAGE_ORDER_RATE("home_page_order_conversion_rate", "首页下单转化率"),
    YUMNOW_ORDER_ZH_LANGUAGE_RATE("yumnow_order_zh_conversion_language_rate", "外卖下单转化率中文(首页按语言)"),
    YUMNOW_ORDER_EN_LANGUAGE_RATE("yumnow_order_en_conversion_language_rate", "外卖下单转化率英文(首页按语言)"),
    YUMNOW_ORDER_KM_LANGUAGE_RATE("yumnow_order_km_conversion_language_rate", "外卖下单转化率柬文(首页按语言)"),
    TINHNOW_ORDER_ZH_LANGUAGE_RATE("tinhnow_order_zh_conversion_language_rate", "电商下单转化率中文(首页按语言)"),
    TINHNOW_ORDER_EN_LANGUAGE_RATE("tinhnow_order_en_conversion_language_rate", "电商下单转化率英文(首页按语言)"),
    TINHNOW_ORDER_KM_LANGUAGE_RATE("tinhnow_order_km_conversion_language_rate", "电商下单转化率柬文(首页按语言)"),
    OVERVIEW_ORDER_RATE("overview_order_conversion_rate", "整体概览下单转化率"),
    INVITE_ACTIVITY_RATE("invite_activity_rate", "邀请转化率（邀请有礼）"),
    ACTIVE_USER_COUNT("active_user_count", "活跃用户数"),
    DEVICE_REGISTER_RATE("device_register_rate", "设备注册率"),
    USER_DEVICE_REGISTER_RATE("user_device_register_rate", "用户设备注册率"),
    HOME_LOGIN_GUIDE_CLICK_COUNT("home_login_guide_click_count", "首页登录引导点击数"),
    HOME_LOGIN_GUIDE_DEVICE_COUNT("home_login_guide_device_count", "首页登录引导点击设备数"),
    HOME_LOGIN_GUIDE_REGISTER_RATE("home_login_guide_register_rate", "首页登录引导设备注册率"),
    REBACK_USER("reback_user", "回流用户"),
    SILENT_USER("silent_user", "沉默用户"),
    LOSS_USER("loss_user", "流失用户"),
    USER_STICKINESS("user_stickiness", "用户黏性值"),
    AVG_ACTIVE_MONTH("avg_active_day_in_month", "月平均活跃天数"),
    REGISTER_USER_ORDER_COUNT("register_user_order_count", "当天新注册用户且下单数"),
    ;

    private String code;

    private String message;
}
