package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.bson.types.Decimal128;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_OPERATIONAL_WEEKLY_REPORT)
@Data
@Accessors(chain = true)
public class ReportOperationalWeeklyBO implements Serializable {
    private static final long serialVersionUID = -7202233901113337740L;

    private Date dataTime;
    private String language;

    /**
     * 上周活跃用户数（7天累加）
     */
    private Integer userActiveSummaryCount;

    /**
     * 上周下单人数（7天累计）
     */
    private Integer userOrderSummaryCount;

    /**
     * 上周下单人数（7天累计）转化率
     */
    private Double orderSummaryRate = 0D;

    /**
     * 上周活跃人数
     */
    private Integer userActiveCount;
    /**
     * 上周下单人数
     */
    private Integer userOrderCount;

    /**
     * 上周下单人数转化率
     */
    private Double userOrderRate = 0D;
    /**
     * 上周新增用户数
     */
    private Integer newUserCount;
    /**
     * 上周新增用户且下单用户数
     */
    private Integer newUserOrderCount;

    /**
     * 新增用户下单转化率
     */
    private Double newUserOrderRate = 0D;
    private Date createTime;
}
