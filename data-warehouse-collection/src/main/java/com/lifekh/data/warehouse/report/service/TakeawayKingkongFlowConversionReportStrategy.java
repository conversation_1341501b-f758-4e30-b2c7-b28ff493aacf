package com.lifekh.data.warehouse.report.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.EntranceType;
import com.lifekh.data.warehouse.bo.ReportTakeawayEotConversionBO;
import com.lifekh.data.warehouse.bo.ReportTakeawayKingKongFlowConversionBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.CommonCountDTO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class TakeawayKingkongFlowConversionReportStrategy extends AbstractTakeawayFlowConversionReportStrategy<ReportTakeawayKingKongFlowConversionBO> implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;


    @Override
    String getMongoDbCollectionName() {
        return MongoDbCollectonName.REPORT_TAKEAWAY_KING_KONG_FLOW_CONVERSION;
    }

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ReportTakeawayKingKongFlowConversionBO> statExposureCount(Date startTime, Date endTime) {
        //collect_buried_point_takeaway_view_v2 event=takeawayKKDExposure
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(startTime).lte(endTime)
                .and(ClickReportConstant.EVENT).is("takeawayKKDExposure");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.areaName",
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("pv")
                        .first("ext.areaName").as("areaName")
                        .first(ClickReportConstant.CREATE_TIME).as("dataTime")
                        .first(ClickReportConstant.LANGUAGE).as("language")
                        .first(ClickReportConstant.PROVINCE_NAME_EN).as("provinceName")
                        .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as("deviceType")
                        .first(ClickReportConstant.DEVICEINFOBO_APP_VERSION).as("appVersion"),
                Aggregation.sort(Sort.Direction.DESC, "pv"),
                Aggregation.limit(50)
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());

        List<ReportTakeawayKingKongFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_VIEW_V2, ReportTakeawayKingKongFlowConversionBO.class).getMappedResults();
//        List<ReportTakeawayKingKongFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_view", ReportTakeawayKingKongFlowConversionBO.class).getMappedResults();
        for (ReportTakeawayKingKongFlowConversionBO mappedResult : mappedResults) {
            mappedResult.setLanguage(StringUtil.isEmpty(mappedResult.getLanguage()) ? "unknown" : mappedResult.getLanguage() );
            mappedResult.setProvinceName(StringUtil.isEmpty(mappedResult.getProvinceName()) ? "unknown" : mappedResult.getProvinceName() );
        }


        return mappedResults;
    }

    /**
     * 统计点击数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    @Override
    public void statClickCount(Date startTime, Date endTime, List<ReportTakeawayKingKongFlowConversionBO> conversionBOList) {
        //点击数量/曝光数量  collect_buried_point_takeaway_view_v2 event=clickBtn ext.clickType =banner
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK_BTN)
                .and("ext.clickType").is("KKD");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.areaName",
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.areaName"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_OTHER_V2, CommonCountDTO.class).getMappedResults();
//        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_other", CommonCountDTO.class).getMappedResults();
        Map<String, Integer> map = mappedResults.stream().filter(c -> c.getGroupKey() != null).collect(Collectors.toMap(CommonCountDTO::getGroupKey, CommonCountDTO::getCount, (v1, v2) -> v1));
        conversionBOList.forEach(r -> {
            Integer count = map.get(r.getGroupKey());
            r.setClickCount(count == null ? 0 : count);
        });
    }

    @Override
    public Aggregation getEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayKingKongFlowConversionBO> conversionBOList) {
        List<String> areaList = conversionBOList.stream().map(ReportTakeawayKingKongFlowConversionBO::getAreaName).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.KKD.getCode())
                .and(ClickReportConstant.ENTRANCE_NAME).in(areaList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_NAME,
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.entranceName"))
                .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayKingKongFlowConversionBO> conversionBOList) {
        List<String> areaList = conversionBOList.stream().map(ReportTakeawayKingKongFlowConversionBO::getAreaName).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.KKD.getCode())
                .and(ClickReportConstant.ENTRANCE_NAME).in(areaList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_NAME,
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("count")
                        .push("ext.orderNo").as("orderList"),
                Aggregation.project("count", "orderList")
                        .and(buildGroupKeyExpression("$_id.entranceName"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
