package com.lifekh.data.warehouse.report;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.report.service.ShortLinkReportService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
@ElasticJobConf(name = "short_link_report_job", cron = "0 0 3 * * ?", description = "短链报表统计任务", shardingTotalCount = 1)
public class ShortLinkReportJob extends AbstractSimpleJob {

    @Autowired
    private ShortLinkReportService shortLinkReportService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始统计短链报表, param: {}", shardingContext.getJobParameter());
        try {
            if (StringUtils.isBlank(shardingContext.getJobParameter())) {
                ReportDateDTO dateDTO = ReportDateDTO.yesterday();
                shortLinkReportService.statisticsShortLinkReport(dateDTO);
            } else {
                //指定统计日期: yyyy-MM-dd
                for (String day : shardingContext.getJobParameter().split(",")) {
                    Date date = DateUtil.parseDate(day);
                    ReportDateDTO dateDTO = new ReportDateDTO();
                    dateDTO.setDataDate(day);
                    dateDTO.setStartTime(DateUtil.beginOfDay(date));
                    dateDTO.setEndTime(DateUtil.endOfDay(date));
                    dateDTO.setDataTime(DateUtil.beginOfHour(DateUtil.offsetHour(dateDTO.getEndTime(), -12)));
                    shortLinkReportService.statisticsShortLinkReport(dateDTO);
                }
            }
        } catch (Exception e) {
            log.error("统计短链报表出现异常", e);
        }
        log.info("结束统计短链报表");
    }

}
