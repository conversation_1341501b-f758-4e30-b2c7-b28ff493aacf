package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.PageViewReportService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ElasticJobConf(name = "report_page_view", cron = "0 0 2 * * ? ", description = "定时统计页面访问报表",shardingTotalCount = 1)
public class PageViewReportJob extends AbstractSimpleJob {

    @Autowired
    private PageViewReportService pageViewReportService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始统计首页访问流量");
        try {
            pageViewReportService.homePageStatistics();
        } catch (Exception e) {
            log.error("统计首页访问流量出现异常", e);
        }

        log.info("开始统计页面浏览报表");
        try {
            pageViewReportService.pageViewStatistics();
        } catch (Exception e) {
            log.error("页面浏览报表统计出现异常", e);
        }

        //清除逻辑放到DwdAggregateOrderJob中,因为该job会用到
        //pageViewReportService.deleteViewEventRecord();

        log.info("页面浏览报表统计结束");
    }
}
