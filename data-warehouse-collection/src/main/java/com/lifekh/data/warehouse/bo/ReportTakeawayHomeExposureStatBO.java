package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_TAKEAWAY_HOME_EXPOSURE_STATISTICS)
@Data
@Accessors(chain = true)
public class ReportTakeawayHomeExposureStatBO implements Serializable {
    private static final long serialVersionUID = 6192353663647048926L;

    public static final String LANGUAGE = "language";
    public static final String BUSINESS_LINE = "businessLine";
    public static final String DATA_TIME = "dataTime";
    public static final String EVENT = "event";
    public static final String COUNT = "count";
    public static final String EXPOSURE_NAME = "exposureName";
    public static final String EXPOSURE_Id = "exposureId";



    private Date dataTime;

    private String language;

    private Date createTime;

    private String exposureName;

    private String exposureId;

    private String event;

    private Integer count;

}
