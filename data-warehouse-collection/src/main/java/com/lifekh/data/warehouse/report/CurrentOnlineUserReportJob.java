package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.constant.FunctionSwitchConstant;
import com.lifekh.data.warehouse.manage.FunctionSwitchManager;
import com.lifekh.data.warehouse.report.service.UserViewReportService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ElasticJobConf(name = "current_online_user_report_job", cron = "20 0 * * * ?", description = "统计每小时的活跃用户数")
public class CurrentOnlineUserReportJob extends AbstractSimpleJob {

    @Autowired
    private UserViewReportService userViewReportService;

    @Autowired
    private FunctionSwitchManager functionSwitchManager;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        if(functionSwitchManager.getSwitchBool(FunctionSwitchConstant.REPORT_HOME_PAGE_ACTIVE_USER_SWITCH)) {
            userViewReportService.currentOnlineUser();
        }
    }
}
