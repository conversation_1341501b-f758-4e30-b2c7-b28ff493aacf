package com.lifekh.data.warehouse.report.service;

import com.khsuper.takeaway.merchant.api.dto.BatchGetMerchantStoreReqDTO;
import com.khsuper.takeaway.merchant.api.dto.BatchGetMerchantStoreRespDTO;
import com.khsuper.takeaway.merchant.api.facade.MerchantStoreFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.EntranceType;
import com.lifekh.data.warehouse.bo.ReportTakeawayEotConversionBO;
import com.lifekh.data.warehouse.bo.ReportTakeawayKingKongFlowConversionBO;
import com.lifekh.data.warehouse.bo.ReportTakeawayWaterfallFlowConversionBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.CommonCountDTO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TakeawayWaterfallFlowConversionReportStrategy extends AbstractTakeawayFlowConversionReportStrategy<ReportTakeawayWaterfallFlowConversionBO> implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MerchantStoreFacade merchantStoreFacade;

    @Override
    String getMongoDbCollectionName() {
        return MongoDbCollectonName.REPORT_TAKEAWAY_WATERFALL_FLOW_CONVERSION;
    }

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ReportTakeawayWaterfallFlowConversionBO> statExposureCount(Date startTime, Date endTime) {
        //collect_buried_point_takeaway_view_v2 event=takeawayStoreExposure ext.type=nearStore
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(startTime).lte(endTime)
                .and(ClickReportConstant.EVENT).is("takeawayStoreExposure")
                .and("ext.type").is("nearStore");

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.storeNo",
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("pv")
                        .first("ext.storeNo").as("storeId")
                        .first(ClickReportConstant.CREATE_TIME).as("dataTime")
                        .first(ClickReportConstant.LANGUAGE).as("language")
                        .first(ClickReportConstant.PROVINCE_NAME_EN).as("provinceName")
                        .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as("deviceType")
                        .first(ClickReportConstant.DEVICEINFOBO_APP_VERSION).as("appVersion"),
                Aggregation.sort(Sort.Direction.DESC, "pv"),
                Aggregation.limit(200)
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportTakeawayWaterfallFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_VIEW_V2, ReportTakeawayWaterfallFlowConversionBO.class).getMappedResults();
//        List<ReportTakeawayWaterfallFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_view", ReportTakeawayWaterfallFlowConversionBO.class).getMappedResults();
        if (!mappedResults.isEmpty()) {
            List<String> storeList = mappedResults.stream().map(ReportTakeawayWaterfallFlowConversionBO::getStoreId).collect(Collectors.toList());
            if (storeList.isEmpty()) {
                return mappedResults;
            }
            BatchGetMerchantStoreReqDTO batchGetMerchantStoreReqDTO = new BatchGetMerchantStoreReqDTO();
            batchGetMerchantStoreReqDTO.setStoreNos(storeList);
            List<BatchGetMerchantStoreRespDTO> storeRespDTOS = merchantStoreFacade.batchGetMerchantStoreInfo(batchGetMerchantStoreReqDTO);

            Map<String, String> storeMap = storeRespDTOS.stream().collect(Collectors.toMap(BatchGetMerchantStoreRespDTO::getStoreNo, BatchGetMerchantStoreRespDTO::getStoreName));
            for (ReportTakeawayWaterfallFlowConversionBO mappedResult : mappedResults) {
                mappedResult.setStoreName(storeMap.get(mappedResult.getStoreId()));
                mappedResult.setLanguage(StringUtil.isEmpty(mappedResult.getLanguage()) ? "unknown" : mappedResult.getLanguage() );
                mappedResult.setProvinceName(StringUtil.isEmpty(mappedResult.getProvinceName()) ? "unknown" : mappedResult.getProvinceName() );

            }
        }

        return mappedResults;
    }

    @Override
    public void statClickCount(Date startTime, Date endTime, List<ReportTakeawayWaterfallFlowConversionBO> conversionBOList) {

    }

    @Override
    public Aggregation getEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayWaterfallFlowConversionBO> conversionBOList) {
        List<String> storeList = conversionBOList.stream().map(ReportTakeawayWaterfallFlowConversionBO::getStoreId).collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.WATERFALL.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(storeList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayWaterfallFlowConversionBO> conversionBOList) {
        List<String> storeList = conversionBOList.stream().map(ReportTakeawayWaterfallFlowConversionBO::getStoreId).collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.WATERFALL.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(storeList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("count")
                        .push("ext.orderNo").as("orderList"),
                Aggregation.project("count",  "orderList")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
