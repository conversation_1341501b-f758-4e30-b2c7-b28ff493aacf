package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.SmsReportStrategy;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ElasticJobConf(name = "user_sms_verify_rate", cron = "30 0 0 * * ?", description = "短信验证率统计",shardingTotalCount = 1)
public class SmsReportJob extends AbstractSimpleJob {

    @Autowired
    private SmsReportStrategy smsReportStrategy;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        smsReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
    }
}
