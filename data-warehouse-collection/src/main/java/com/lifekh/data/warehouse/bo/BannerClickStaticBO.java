package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.BANNER_CLICK_STATIC)
@Data
@Accessors(chain = true)
public class BannerClickStaticBO implements Serializable {
    private static final long serialVersionUID = 5210811483289150128L;

    public static final String PAGE = "page";
    public static final String AREA = "area";
    public static final String LANGUAGE = "language";
    public static final String COUNT = "count";

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    private String page;
    private String area;
    private String language;
    private Long count;

    private Date createTime;
}
