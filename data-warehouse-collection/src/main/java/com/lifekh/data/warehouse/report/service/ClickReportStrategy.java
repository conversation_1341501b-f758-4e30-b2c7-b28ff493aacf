package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.collection.CollectionUtil;
import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.bo.discovery.AdsViewLogBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
@Slf4j
public class ClickReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
        try {
            standardClickEventDayStatistics(reportBasicReqDTO);
            log.info("统计日标准点击事件完成,时间:{}",reportBasicReqDTO);
        } catch (Exception e) {
            try {
                standardClickEventDayStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计日标准点击事件完成异常", e);
            }
        }

        //统计标准点击事件（语言）
        try {
            standardClickEventDayLanguageStatistics(reportBasicReqDTO);
        } catch (Exception e) {
            try {
                standardClickEventDayLanguageStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计标准点击事件（语言）异常", e);
            }
        }

        //统计首页推荐报表
        statisticsHomeRecommend(reportBasicReqDTO);
        log.info("统计首页推荐报表完成,时间:{}",reportBasicReqDTO);

        //统计首页点击事件总数
        try {
            wownowHomePageClickDayStatistics(reportBasicReqDTO);
            log.info("统计日首页点击事件完成,时间:{}",reportBasicReqDTO);
        } catch (Exception e) {
            try {
                wownowHomePageClickDayStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计首页点击事件总数异常", e);
            }
        }

        //统计首页点击事件总数（语言）
        try {
            wownowHomePageClickLanguageDayStatistics(reportBasicReqDTO);
        } catch (Exception e) {
            try {
                wownowHomePageClickLanguageDayStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计首页点击事件总数（语言）异常", e);
            }
        }

       //统计我的页面点击事件总数
        try {
            wownowMineClickDayStatistics(reportBasicReqDTO);
            log.info("统计日我的页面点击事件完成,时间:{}", reportBasicReqDTO);
        } catch (Exception e) {
            try {
                wownowMineClickDayStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计我的页面点击事件总数异常", e);
            }
        }

        //统计我的页面点击事件总数（语言）
        try {
            wownowMineClickLanguageDayStatistics(reportBasicReqDTO);
        } catch (Exception e) {
            try {
                wownowMineClickLanguageDayStatistics(reportBasicReqDTO);
            } catch (Exception e1) {
                log.error("统计我的页面点击事件总数（语言）异常", e);
            }
        }

        //统计添加购物车
        try {
            shopOrderClickDayStatistics(reportBasicReqDTO, ClickReportConstant.ADD_SHOP_CART_EVEN);
            log.info("统计日统计添加购物车事件完成,时间:{}",reportBasicReqDTO);
        } catch (Exception e) {
            try {
                shopOrderClickDayStatistics(reportBasicReqDTO, ClickReportConstant.ADD_SHOP_CART_EVEN);
            } catch (Exception e1) {
                log.error("统计添加购物车异常", e);
            }
        }

        //统计下单
        try {
            shopOrderClickDayStatistics(reportBasicReqDTO, ClickReportConstant.ORDER_SUBMIT_EVEN);
            log.info("统计日下单事件完成,时间:{}", reportBasicReqDTO);
        } catch (Exception e) {
            try {
                shopOrderClickDayStatistics(reportBasicReqDTO, ClickReportConstant.ORDER_SUBMIT_EVEN);
            } catch (Exception e1) {
                log.error("统计下单异常", e);
            }
        }

        //统计添加购物车或下单汇总
        shopOrderClickFromWownowHome(reportBasicReqDTO, ClickReportConstant.ADD_CART_AND_ORDER);
        log.info("统计日添加购物车或下单汇总完成,时间:{}",reportBasicReqDTO);

        //统计banner点击数
        staticBannerClickCount(reportBasicReqDTO);
        log.info("统计banner点击数完成,时间:{}",reportBasicReqDTO);

        //统计首页业务线点击
        staticHomePageBusinessClick(reportBasicReqDTO);
        log.info("统计首页业务线点击报表完成,时间:{}",reportBasicReqDTO);

        //统计浮窗报表
        staticFloatWindowClick(reportBasicReqDTO);
        log.info("统计浮窗点击报表完成,时间:{}",reportBasicReqDTO);

        //统计首页banner业务线点击
        staticHomePageBannerClick(reportBasicReqDTO);
        log.info("统计首页banner业务线点击,时间:{}",reportBasicReqDTO);
        return null;
    }

    /**
     * 统计首页搜索框点击数
     *
     * @param reportBasicReqDTO
     */
    private void homePageSearchClickStatistics(ReportBasicReqDTO reportBasicReqDTO, String currentPage, String currentArea, String businessName) {
        try {
            //语言累加
            Criteria criteriaBusiness = new Criteria();
            criteriaBusiness.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime()).lte(reportBasicReqDTO.getEndTime())
                    .and(ClickReportDayBO.PAGE_NAME).is(currentPage)
                    .and(ClickReportDayBO.CARD_NAME).is(currentArea)
                    .and(ClickReportDayBO.NODE_NAME).is(businessName);
            Query queryReportDayQuery = new Query();
            queryReportDayQuery.addCriteria(criteriaBusiness);
            List<ClickReportDayBO> clickReportDayList = mongoTemplate.find(queryReportDayQuery, ClickReportDayBO.class, ClickReportDayBO.COLLECTION_NAME);

            //查询pv，dv和uv
            Long pv = mongoTemplate.count(getPvAggregationCriteria(reportBasicReqDTO, businessName), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK);
            Long uv = mongoPageHelper.queryTotal(getSearchUvAggregationCriteria(reportBasicReqDTO, businessName), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
            Long dv = mongoPageHelper.queryTotal(getDvAggregationCriteria(reportBasicReqDTO, businessName), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);

            //存在则删除原数据后新增
            ClickReportDayBO clickReportDayBo  = new ClickReportDayBO();
            if(!CollectionUtils.isEmpty(clickReportDayList)) {
                clickReportDayBo = clickReportDayList.get(0);
            }

            //更新pv,dv和uv
            clickReportDayBo.setPv(pv);
            clickReportDayBo.setDv(dv);
            clickReportDayBo.setUv(uv);
            clickReportDayBo.setPageName(currentPage);
            clickReportDayBo.setCardName(currentArea);
            clickReportDayBo.setNodeName(businessName);
            clickReportDayBo.setDataTime(reportBasicReqDTO.getDataTime());

            //更新总表
            mongoTemplate.upsert(queryReportDayQuery, getUpdateBo(clickReportDayBo), ClickReportDayBO.COLLECTION_NAME);
        } catch (Exception e) {
            log.error("统计首页点击数异常", e);
        }

        try {
            List<String> languageList = Arrays.asList(LanguageEnum.KM_KH.getCode(),LanguageEnum.EN_US.getCode(),LanguageEnum.ZH_CN.getCode());
            for(String language : languageList) {
                //按语言统计
                Criteria criteriaBusiness = new Criteria();
                criteriaBusiness.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime()).lte(reportBasicReqDTO.getEndTime())
                        .and(ClickReportDayBO.PAGE_NAME).is(currentPage)
                        .and(ClickReportDayBO.CARD_NAME).is(currentArea)
                        .and(ClickReportDayBO.NODE_NAME).is(businessName)
                        .and(ClickReportDayBO.LANGUAGE).is(language);
                Query queryReportDayQuery = new Query();
                queryReportDayQuery.addCriteria(criteriaBusiness);
                List<ClickReportDayBO> clickReportDayList = mongoTemplate.find(queryReportDayQuery, ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);

                //查询pv，dv和uv
                Long pv = mongoTemplate.count(getPvLanguageAggregationCriteria(reportBasicReqDTO, businessName, language), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK);
                Long uv = mongoPageHelper.queryTotal(getUvLanguageAggregationCriteria(reportBasicReqDTO, businessName, language), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                Long dv = mongoPageHelper.queryTotal(getDvLanguageAggregationCriteria(reportBasicReqDTO, businessName, language), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);

                //存在则删除原数据后新增
                ClickReportDayBO clickReportDayBo  = new ClickReportDayBO();
                if(!CollectionUtils.isEmpty(clickReportDayList)) {
                    clickReportDayBo = clickReportDayList.get(0);
                }

                //更新pv,dv和uv
                clickReportDayBo.setPv(pv);
                clickReportDayBo.setDv(dv);
                clickReportDayBo.setUv(uv);
                clickReportDayBo.setDataTime(reportBasicReqDTO.getDataTime());
                clickReportDayBo.setLanguage(language);
                clickReportDayBo.setPageName(currentPage);
                clickReportDayBo.setCardName(currentArea);
                clickReportDayBo.setNodeName(businessName);

                //更新总表
                mongoTemplate.upsert(queryReportDayQuery, getUpdateLanguageBo(clickReportDayBo), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
            }
        } catch (Exception e) {
            log.error("统计首页点击数异常", e);
        }
    }

    private void shopOrderClickFromWownowHome(ReportBasicReqDTO reportBasicReqDTO, Map<String, String> nodeNameMap) {
        //统计外卖首页加购总数
        nodeNameMap.keySet().forEach(name -> {
            try {
                ClickReportDayBO bo = getShopOrderClickFromWownowHomeQuery(reportBasicReqDTO, name);

                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickReportDayBO.PAGE_NAME).is(nodeNameMap.get(name))
                        .and(ClickReportDayBO.CARD_NAME).is(nodeNameMap.get(name))
                        .and(ClickReportDayBO.NODE_NAME).is(nodeNameMap.get(name)));
                bo.setDataTime(reportBasicReqDTO.getDataTime());
                bo.setPageName(nodeNameMap.get(name));
                bo.setNodeName(nodeNameMap.get(name));
                bo.setCardName(nodeNameMap.get(name));

                //更新总表
                mongoTemplate.upsert(query, getShopCardAndOrderUpdateBo(bo), ClickReportDayBO.COLLECTION_NAME);
            } catch (Exception e) {
                log.error("统计昨天加购或下单汇总统计异常, reportBasicReqDTO:{}, nodeName:{}", reportBasicReqDTO, name, e);
            }
        });
    }

    private ClickReportDayBO getShopOrderClickFromWownowHomeQuery(ReportBasicReqDTO reportBasicReqDTO, String name) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportDayBO.NODE_NAME).regex("^.*" + name);
        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(Fields.fields("1"))
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        AggregationResults<ClickReportDayBO> results = mongoTemplate.aggregate(Aggregation.newAggregation(querList), MongoDbCollectonName.REPORT_CLICK_DAY, ClickReportDayBO.class);

        ClickReportDayBO clickReportDayBO = new ClickReportDayBO();
        if(results == null || CollectionUtils.isEmpty(results.getMappedResults()) || results.getMappedResults().get(0) == null) {
            return clickReportDayBO;
        }
        ClickReportDayBO reportDayBO = results.getMappedResults().get(0);
        clickReportDayBO.setPv(reportDayBO.getPv());
        clickReportDayBO.setDv(reportDayBO.getDv());
        clickReportDayBO.setUv(reportDayBO.getUv());
        return clickReportDayBO;
    }

    private void shopOrderClickDayStatistics(ReportBasicReqDTO reportBasicReqDTO, String even) {
        long pageSize = 1000;
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getShopCartAndOrderAggregationCriteria(reportBasicReqDTO, even);

        Long total = null;
        for (int i = 0; ; i++) {
            //遍历查询PV
            PageRespDTO<ClickReportDayBO> pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, true, total, ClickReportDayBO.class);
            total = pageRespDTO.getTotal();
            if (CollectionUtils.isEmpty(pageRespDTO.getList())) {
                return;
            }

            //3.入库
            for (ClickReportDayBO clickReportDayBo : pageRespDTO.getList()) {
                try {
                    //查询UV
                    Long uv = mongoPageHelper.queryTotal(getShopCartAndOrderUvAggregationCriteria(reportBasicReqDTO, even, clickReportDayBo.getNodeName()), MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, true);
                    clickReportDayBo.setUv(uv);

                    //查询DV
                    Long dv = mongoPageHelper.queryTotal(getShopCartAndOrderDvAggregationCriteria(reportBasicReqDTO,
                            even, clickReportDayBo.getNodeName()), MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER, true);
                    clickReportDayBo.setDv(dv);

                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(ClickReportDayBO.PAGE_NAME).is(clickReportDayBo.getNodeName())
                            .and(ClickReportDayBO.CARD_NAME).is(clickReportDayBo.getNodeName())
                            .and(ClickReportDayBO.NODE_NAME).is(clickReportDayBo.getNodeName()));
                    clickReportDayBo.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getShopCardAndOrderUpdateBo(clickReportDayBo), ClickReportDayBO.COLLECTION_NAME);
                } catch (Exception e) {
                    log.error("统计昨天标准点击事件统计异常, reportBasicReqDTO:{}, clickReportDayBo:{}", reportBasicReqDTO, clickReportDayBo, e);
                }
            }
        }
    }

    /**
     * 统计我的页面点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowMineClickDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportDayBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getPageNameAggregationCriteria(reportBasicReqDTO, ClickReportConstant.WOWNOW_MINE_PAGE_LIST)), ClickReportDayBO.COLLECTION_NAME, ClickReportDayBO.class);

        List<ClickReportDayBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            //求和
            ClickReportDayBO sumBo = sumAggregationResults(list);

            //入库
            Query query = new Query();
            query.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                    .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                    .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE));

            try {
                UpdateResult updateResult = mongoTemplate.upsert(query, getWownowMinePageUpdateBo(reportBasicReqDTO.getDataTime(), sumBo.getPv(), sumBo.getUv(), sumBo.getDv()), ClickReportDayBO.COLLECTION_NAME);
                log.info("统计昨天我的页面点击事件总数 MatchedCount:{}, ModifiedCount:{}, UpsertedId:{}",
                        updateResult.getMatchedCount(), updateResult.getModifiedCount(), updateResult.getUpsertedId());
            } catch (Exception e) {
                log.error("统计昨天我的页面点击事件总数异常, reportBasicReqDTO:{}", reportBasicReqDTO, e);
            }
        }
    }

    /**
     * 统计我的页面点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowMineClickLanguageDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportDayBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getPageNameLanguageAggregationCriteria(reportBasicReqDTO, ClickReportConstant.WOWNOW_MINE_PAGE_LIST)), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY, ClickReportDayBO.class);

        List<ClickReportDayBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(bo -> {
                //入库
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                        .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                        .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                        .and(ClickReportDayBO.LANGUAGE).is(bo.getLanguage()));
                bo.setDataTime(reportBasicReqDTO.getDataTime());
                bo.setPageName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setCardName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setNodeName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setFullName(ClickReportConstant.WOWNOW_CLICK_MINE);
                try {
                    UpdateResult updateResult = mongoTemplate.upsert(query, getWownowMineLanguagePageUpdateBo(reportBasicReqDTO.getDataTime(), bo), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
                    log.info("统计昨天我的页面点击事件总数 MatchedCount:{}, ModifiedCount:{}, UpsertedId:{}",
                            updateResult.getMatchedCount(), updateResult.getModifiedCount(), updateResult.getUpsertedId());
                } catch (Exception e) {
                    log.error("统计昨天我的页面点击事件总数异常, reportBasicReqDTO:{}", reportBasicReqDTO, e);
                }
            });
        }
    }

    /**
     * 统计首页点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowMineClickWeekStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportWeekBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getMinePageWeekAggregationCriteria(reportBasicReqDTO)), ClickReportDayBO.COLLECTION_NAME, ClickReportWeekBO.class);

        List<ClickReportWeekBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            //入库
            Query query = new Query();
            query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportWeekBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                    .and(ClickReportWeekBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                    .and(ClickReportWeekBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE));

            ClickReportWeekBO weekBo = mongoTemplate.findOne(query, ClickReportWeekBO.class);
            if (Objects.isNull(weekBo)) {
                ClickReportWeekBO bo = new ClickReportWeekBO();
                bo.setCreateTime(new Date());
                bo.setUpdateTime(new Date());
                bo.setPageName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setCardName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setNodeName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setFullName(ClickReportConstant.WOWNOW_CLICK_MINE);
                bo.setPv(list.get(0).getPv());
                bo.setUv(list.get(0).getUv());
                bo.setDv(list.get(0).getDv());
                bo.setDataTime(reportBasicReqDTO.getDataTime());
                mongoTemplate.save(bo);
            } else {
                weekBo.setUpdateTime(new Date());
                weekBo.setPv(list.get(0).getPv());
                weekBo.setUv(list.get(0).getUv());
                weekBo.setDv(list.get(0).getDv());
                weekBo.setDataTime(reportBasicReqDTO.getDataTime());
                mongoTemplate.save(weekBo);
            }
            log.info("周WOWNOW我的页面点击总数{}", list);
        }
    }

    /**
     * 计算求和
     *
     * @param list
     * @return
     */
    private ClickReportDayBO sumAggregationResults(List<ClickReportDayBO> list) {
        long pv = 0, uv = 0, dv = 0;
        //累加
        for (ClickReportDayBO bo : list) {
            pv = pv + bo.getPv();
            uv = uv + bo.getUv();
            dv = dv + bo.getDv();
        }
        ClickReportDayBO reportBasicBO = new ClickReportDayBO();
        reportBasicBO.setPv(pv);
        reportBasicBO.setUv(uv);
        reportBasicBO.setDv(dv);
        return reportBasicBO;
    }

    /**
     * 计算求和
     *
     * @param list
     * @return
     */
    private ClickReportWeekBO sumAggregationWeekResults(List<ClickReportWeekBO> list) {
        long pv = 0, uv = 0, dv = 0;
        //累加
        for (ClickReportWeekBO bo : list) {
            pv = pv + bo.getPv();
            uv = uv + bo.getUv();
            dv = dv + bo.getDv();
        }
        ClickReportWeekBO reportBasicBO = new ClickReportWeekBO();
        reportBasicBO.setPv(pv);
        reportBasicBO.setUv(uv);
        reportBasicBO.setDv(dv);
        return reportBasicBO;
    }


    /**
     * 统计首页点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowHomePageClickDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportDayBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getPageNameAggregationCriteria(reportBasicReqDTO, WOWNOW_HOME_PAGE_ARR)), ClickReportDayBO.COLLECTION_NAME, ClickReportDayBO.class);

        List<ClickReportDayBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            //求和
            ClickReportDayBO sumBo = sumAggregationResults(list);

            try {
                //查询UV
                Long uv = mongoPageHelper.queryTotal(getUvAggregation(reportBasicReqDTO, Arrays.asList(WOWNOW_HOME_PAGE_ARR), null), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                sumBo.setUv(uv);

                //查询DV
                Long dv = mongoPageHelper.queryTotal(getDvAggregation(reportBasicReqDTO, Arrays.asList(WOWNOW_HOME_PAGE_ARR), null), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                sumBo.setDv(dv);
            } catch (Exception e) {
                log.error("查询首页uv和dv数出现异常", e);
            }

            //入库
            Query query = new Query();
            query.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                    .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                    .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL));
            try {
                UpdateResult updateResult = mongoTemplate.upsert(query, getWownowHomePageUpdateBo(reportBasicReqDTO.getDataTime(), sumBo.getPv(), sumBo.getUv(), sumBo.getDv()), ClickReportDayBO.COLLECTION_NAME);
                log.info("昨天WOWNOW首页点击总数 MatchedCount:{}, ModifiedCount:{}, UpsertedId:{}",
                        updateResult.getMatchedCount(), updateResult.getModifiedCount(), updateResult.getUpsertedId());
            } catch (Exception e) {
                log.error("昨天WOWNOW首页点击总数异常, reportBasicReqDTO:{}",reportBasicReqDTO, e);
            }
        }
    }

    /**
     * 统计首页点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowHomePageClickLanguageDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportDayBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getPageNameLanguageAggregationCriteria(reportBasicReqDTO, WOWNOW_HOME_PAGE_ARR)), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY, ClickReportDayBO.class);

        List<ClickReportDayBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(bo -> {
                //入库
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportDayBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                        .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                        .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                        .and(ClickReportDayBO.LANGUAGE).is(bo.getLanguage()));
                bo.setDataTime(reportBasicReqDTO.getDataTime());
                bo.setPageName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setCardName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setNodeName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setFullName(ClickReportConstant.WOWNOW_CLICK_TOTAL);

                try {
                    if (StringUtils.isNotBlank(bo.getLanguage())) {
                        //查询UV
                        Long uv = mongoPageHelper.queryTotal(getUvAggregation(reportBasicReqDTO, Arrays.asList(WOWNOW_HOME_PAGE_ARR), bo.getLanguage()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                        bo.setUv(uv);

                        //查询DV
                        Long dv = mongoPageHelper.queryTotal(getDvAggregation(reportBasicReqDTO, Arrays.asList(WOWNOW_HOME_PAGE_ARR), bo.getLanguage()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                        bo.setDv(dv);
                    }
                } catch (Exception e) {
                    log.error("查询首页语言uv和dv数出现异常", e);
                }

                try {
                    UpdateResult updateResult = mongoTemplate.upsert(query, getWownowHomePageLanguageUpdateBo(bo), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
                    log.info("昨天WOWNOW首页点击总数 MatchedCount:{}, ModifiedCount:{}, UpsertedId:{}",
                            updateResult.getMatchedCount(), updateResult.getModifiedCount(), updateResult.getUpsertedId());
                } catch (Exception e) {
                    log.error("昨天WOWNOW首页点击总数异常, reportBasicReqDTO:{}",reportBasicReqDTO, e);
                }
            });
        }
    }

    /**
     * 统计首页点击事件总数
     *
     * @param reportBasicReqDTO
     */
    private void wownowHomePageClickWeekStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //查询
        AggregationResults<ClickReportWeekBO> aggregationResults =
                mongoTemplate.aggregate(Aggregation.newAggregation(getHomePageWeekAggregationCriteria(reportBasicReqDTO)), ClickReportDayBO.COLLECTION_NAME, ClickReportWeekBO.class);

        List<ClickReportWeekBO> list = aggregationResults.getMappedResults();

        if (CollectionUtil.isNotEmpty(list)) {
            //入库
            Query query = new Query();
            query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(ClickReportWeekBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                    .and(ClickReportWeekBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                    .and(ClickReportWeekBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL));

            ClickReportWeekBO weekBo = mongoTemplate.findOne(query, ClickReportWeekBO.class);
            if (Objects.isNull(weekBo)) {
                ClickReportWeekBO bo = new ClickReportWeekBO();
                bo.setCreateTime(new Date());
                bo.setUpdateTime(new Date());
                bo.setPageName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setCardName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setNodeName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setFullName(ClickReportConstant.WOWNOW_CLICK_TOTAL);
                bo.setPv(list.get(0).getPv());
                bo.setUv(list.get(0).getUv());
                bo.setDv(list.get(0).getDv());
                bo.setDataTime(reportBasicReqDTO.getDataTime());
                mongoTemplate.save(bo);
            } else {
                weekBo.setUpdateTime(new Date());
                weekBo.setPv(list.get(0).getPv());
                weekBo.setUv(list.get(0).getUv());
                weekBo.setDv(list.get(0).getDv());
                weekBo.setDataTime(reportBasicReqDTO.getDataTime());
                mongoTemplate.save(weekBo);
            }
        }
    }

    /**
     * 统计标准点击事件
     *
     * @param reportBasicReqDTO
     */
    private void standardClickEventDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        long pageNum = 0, pageSize = 1000;
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getPvAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<ClickReportDayBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, null, ClickReportDayBO.class);

        if (pageRespDTO.getSize() < 1) {
            return;
        }

        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, pageRespDTO.getTotal(), ClickReportDayBO.class);
                if (pageRespDTO.getSize() < 1) {
                    return;
                }
            }

            //3.入库
            for (ClickReportDayBO clickReportDayBo : pageRespDTO.getList()) {
                try {
                    //查询UV
                    Long uv = mongoPageHelper.queryTotal(getUvAggregationCriteria(reportBasicReqDTO,
                            clickReportDayBo.getPageName(), clickReportDayBo.getCardName(), clickReportDayBo.getNodeName()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                    clickReportDayBo.setUv(uv);

                    //查询DV
                    Long dv = mongoPageHelper.queryTotal(getDvAggregationCriteria(reportBasicReqDTO,
                            clickReportDayBo.getPageName(), clickReportDayBo.getCardName(), clickReportDayBo.getNodeName()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                    clickReportDayBo.setDv(dv);


                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(ClickReportDayBO.PAGE_NAME).is(clickReportDayBo.getPageName())
                            .and(ClickReportDayBO.CARD_NAME).is(clickReportDayBo.getCardName())
                            .and(ClickReportDayBO.NODE_NAME).is(clickReportDayBo.getNodeName()));
                    clickReportDayBo.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getUpdateBo(clickReportDayBo), ClickReportDayBO.COLLECTION_NAME);
                } catch (Exception e) {
                    log.warn("统计昨天标准点击事件统计异常, reportBasicReqDTO:{}, clickReportDayBo:{}", reportBasicReqDTO, clickReportDayBo, e);
                }
            }
        }
    }

    /**
     * 统计标准点击事件
     *
     * @param reportBasicReqDTO
     */
    private void standardClickEventDayLanguageStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        long pageNum = 0, pageSize = 1000;
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getPvLanguageAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<ClickReportDayBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, null, ClickReportDayBO.class);

        if (pageRespDTO.getTotal() < 1) {
            return;
        }

        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, pageRespDTO.getTotal(), ClickReportDayBO.class);
                if (pageRespDTO.getTotal() < 1) {
                    return;
                }
            }

            //3.入库
            for (ClickReportDayBO clickReportDayBo : pageRespDTO.getList()) {
                try {
                    //查询UV
                    Long uv = mongoPageHelper.queryTotal(getUvLanguageAggregationCriteria(reportBasicReqDTO,
                            clickReportDayBo.getPageName(), clickReportDayBo.getCardName(), clickReportDayBo.getNodeName(), clickReportDayBo.getLanguage()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                    clickReportDayBo.setUv(uv);

                    //查询DV
                    Long dv = mongoPageHelper.queryTotal(getDvLanguageAggregationCriteria(reportBasicReqDTO,
                            clickReportDayBo.getPageName(), clickReportDayBo.getCardName(), clickReportDayBo.getNodeName(), clickReportDayBo.getLanguage()), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true);
                    clickReportDayBo.setDv(dv);


                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(ClickReportDayBO.PAGE_NAME).is(clickReportDayBo.getPageName())
                            .and(ClickReportDayBO.CARD_NAME).is(clickReportDayBo.getCardName())
                            .and(ClickReportDayBO.NODE_NAME).is(clickReportDayBo.getNodeName())
                            .and(ClickReportDayBO.LANGUAGE).is(clickReportDayBo.getLanguage()));
                    clickReportDayBo.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getUpdateLanguageBo(clickReportDayBo), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
                } catch (Exception e) {
                    log.warn("统计昨天标准点击事件统计异常, reportBasicReqDTO:{}, clickReportDayBo:{}", reportBasicReqDTO, clickReportDayBo, e);
                }
            }
        }
    }

    /**
     * 统计标准点击事件
     *
     * @param reportBasicReqDTO
     */
    private void standardClickEventWeekStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        long pageNum = 0, pageSize = 1000;
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getWeekAndMonthPvAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<ClickReportWeekBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), ClickReportDayBO.COLLECTION_NAME, true, null, ClickReportWeekBO.class);

        if (pageRespDTO.getSize() < 1) {
            return;
        }

        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), ClickReportDayBO.COLLECTION_NAME, true, pageRespDTO.getTotal(), ClickReportWeekBO.class);
                if (pageRespDTO.getSize() < 1) {
                    return;
                }
            }

            //3.入库
            for (ClickReportWeekBO clickReportWeekBo : pageRespDTO.getList()) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickReportWeekBO.PAGE_NAME).is(clickReportWeekBo.getPageName())
                        .and(ClickReportWeekBO.CARD_NAME).is(clickReportWeekBo.getCardName())
                        .and(ClickReportWeekBO.NODE_NAME).is(clickReportWeekBo.getNodeName()));

                ClickReportWeekBO weekBo = mongoTemplate.findOne(query, ClickReportWeekBO.class);

                try {
                    if (Objects.isNull(weekBo)) {
                        ClickReportWeekBO bo = new ClickReportWeekBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setPageName(clickReportWeekBo.getPageName());
                        bo.setCardName(clickReportWeekBo.getCardName());
                        bo.setNodeName(clickReportWeekBo.getNodeName());
                        if (ClickReportConstant.WOWNOW_CLICK_MINE.equals(clickReportWeekBo.getPageName())
                                || ClickReportConstant.WOWNOW_CLICK_TOTAL.equals(clickReportWeekBo.getPageName())) {
                            bo.setFullName(clickReportWeekBo.getPageName());
                        } else {
                            bo.setFullName(clickReportWeekBo.getPageName() +
                                    "@" + clickReportWeekBo.getCardName() +
                                    "@" + clickReportWeekBo.getNodeName());
                        }
                        bo.setPv(clickReportWeekBo.getPv());
                        bo.setUv(clickReportWeekBo.getUv());
                        bo.setDv(clickReportWeekBo.getDv());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        weekBo.setUpdateTime(new Date());
                        weekBo.setPv(clickReportWeekBo.getPv());
                        weekBo.setUv(clickReportWeekBo.getUv());
                        weekBo.setDv(clickReportWeekBo.getDv());
                        weekBo.setDataTime(reportBasicReqDTO.getDataTime());
                        weekBo.setFirstOfWeek(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(weekBo);
                    }

                } catch (Exception e) {
                    log.error("统计周标准点击事件统计异常,reportBasicReqDTO:{}, weekBo:{}", reportBasicReqDTO, weekBo, e);
                }
            }
        }
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getUvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String page, String card, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).is(page);
        criteria.and(ClickReportConstant.CURRENT_AREA).is(card);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }

    private List<AggregationOperation> getSearchUvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }

    private List<AggregationOperation> getUvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String page, String card, String node, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).is(page);
        criteria.and(ClickReportConstant.CURRENT_AREA).is(card);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.and(ClickReportConstant.LANGUAGE).is(language);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.LANGUAGE, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }

    private List<AggregationOperation> getUvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String node, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.and(ClickReportConstant.LANGUAGE).is(language);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }


    /**
     * 构造查询条件-uv
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getUvAggregation(ReportBasicReqDTO reportBasicReqDTO, List<String> pages, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).in(pages);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(""));

        if (StringUtils.isNotBlank(language)) {
            criteria.and(ClickReportConstant.LANGUAGE).is(language);
        }

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getShopCartAndOrderUvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String even, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(even);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.USERINFOBO_OPERATOR_NO)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.USERINFOBO_OPERATOR_NO).as(ClickReportConstant.OPERATOR_NO));
        return querList;
    }

    /**
     * 统计标准点击事件
     *
     * @param reportBasicReqDTO
     */
    private void standardClickEventMonthStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        long pageNum = 0, pageSize = 1000;
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getWeekAndMonthPvAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<ClickReportMonthBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), ClickReportDayBO.COLLECTION_NAME, true, null, ClickReportMonthBO.class);

        if (pageRespDTO.getSize() < 1) {
            return;
        }

        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), ClickReportDayBO.COLLECTION_NAME, true, pageRespDTO.getTotal(), ClickReportMonthBO.class);
                if (pageRespDTO.getSize() < 1) {
                    return;
                }
            }

            //3.入库
            for (ClickReportMonthBO clickReportMonthBo : pageRespDTO.getList()) {
                Query query = new Query();
                query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                        .gte(reportBasicReqDTO.getBeginTime())
                        .lt(reportBasicReqDTO.getEndTime())
                        .and(ClickReportMonthBO.PAGE_NAME).is(clickReportMonthBo.getPageName())
                        .and(ClickReportMonthBO.CARD_NAME).is(clickReportMonthBo.getCardName())
                        .and(ClickReportMonthBO.NODE_NAME).is(clickReportMonthBo.getNodeName()));

                ClickReportMonthBO monthBo = mongoTemplate.findOne(query, ClickReportMonthBO.class);
                try {
                    if (Objects.isNull(monthBo)) {
                        ClickReportMonthBO bo = new ClickReportMonthBO();
                        bo.setCreateTime(new Date());
                        bo.setUpdateTime(new Date());
                        bo.setPageName(clickReportMonthBo.getPageName());
                        bo.setCardName(clickReportMonthBo.getCardName());
                        bo.setNodeName(clickReportMonthBo.getNodeName());
                        if (ClickReportConstant.WOWNOW_CLICK_MINE.equals(clickReportMonthBo.getPageName())
                                || ClickReportConstant.WOWNOW_CLICK_TOTAL.equals(clickReportMonthBo.getPageName())) {
                            bo.setFullName(clickReportMonthBo.getPageName());
                        } else {
                            bo.setFullName(clickReportMonthBo.getPageName() +
                                    "@" + clickReportMonthBo.getCardName() +
                                    "@" + clickReportMonthBo.getNodeName());
                        }
                        bo.setPv(clickReportMonthBo.getPv());
                        bo.setUv(clickReportMonthBo.getUv());
                        bo.setDv(clickReportMonthBo.getDv());
                        bo.setDataTime(reportBasicReqDTO.getDataTime());
                        bo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(bo);
                    } else {
                        monthBo.setUpdateTime(new Date());
                        monthBo.setPv(clickReportMonthBo.getPv());
                        monthBo.setUv(clickReportMonthBo.getUv());
                        monthBo.setDv(clickReportMonthBo.getDv());
                        monthBo.setDataTime(reportBasicReqDTO.getDataTime());
                        monthBo.setFirstOfMonth(reportBasicReqDTO.getBeginTime());
                        mongoTemplate.save(monthBo);
                    }
                } catch (Exception e) {
                    log.error("统计月标准事件异常,reportBasicReqDTO:{}, clickReportMonthBo:{}", reportBasicReqDTO, clickReportMonthBo, e);
                }
            }
        }
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getPvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).ne(null);
        criteria.and(ClickReportConstant.CURRENT_AREA).ne(null);
        criteria.and(ClickReportConstant.BUSINESS_NAME).ne(null);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.BUSINESS_NAME)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .count().as(ClickReportDayBO.PV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    private Query getPvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String businessName) {
        //查询pv，dv和uv
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(businessName);
        Query query = new Query();
        query.addCriteria(criteria);
        return query;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getPvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).ne(null);
        criteria.and(ClickReportConstant.CURRENT_AREA).ne(null);
        criteria.and(ClickReportConstant.BUSINESS_NAME).ne(null);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .count().as(ClickReportDayBO.PV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    private Query getPvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String businessName, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(businessName);
        criteria.and(ClickReportConstant.LANGUAGE).is(language);

        Query query = new Query();
        query.addCriteria(criteria);
        return query;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getWeekAndMonthPvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportWeekBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME, ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.FULL_NAME).as(ClickReportDayBO.FULL_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    private List<AggregationOperation> getDvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getDvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String page, String card, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).is(page);
        criteria.and(ClickReportConstant.CURRENT_AREA).is(card);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(null)),
                criteria.and(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        return querList;
    }

    private List<AggregationOperation> getSearchDvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String page, String card, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID)
                .count().as(ClickReportDayBO.DV));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getDvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String node, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.and(ClickReportConstant.LANGUAGE).is(language);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getDvLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String page, String card, String node, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).is(page);
        criteria.and(ClickReportConstant.CURRENT_AREA).is(card);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.and(ClickReportConstant.LANGUAGE).is(language);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(null)),
                criteria.and(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.LANGUAGE, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.CURRENT_PAGE).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportConstant.CURRENT_AREA).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        return querList;
    }

    /**
     * 构造查询条件-dv
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getDvAggregation(ReportBasicReqDTO reportBasicReqDTO, List<String> pages, String language) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK);
        criteria.and(ClickReportConstant.CURRENT_PAGE).in(pages);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(null)),
                criteria.and(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(""));

        if (StringUtils.isNotBlank(language)) {
            criteria.and(ClickReportConstant.LANGUAGE).is(language);
        }

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getShopCartAndOrderDvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String even, String node) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(even);
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(node);
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(null)),
                criteria.and(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME, ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(ClickReportConstant.DEVICE_ID));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getShopCartAndOrderAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String even) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(even);
        criteria.and(ClickReportConstant.BUSINESS_NAME).ne(null);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.BUSINESS_NAME)
                .first(ClickReportConstant.BUSINESS_NAME).as(ClickReportDayBO.NODE_NAME)
                .count().as(ClickReportDayBO.PV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getPageNameAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportDayBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME, ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.FULL_NAME).as(ClickReportDayBO.FULL_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getPageNameLanguageAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String[] pageList) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        Criteria[] pageArr = new Criteria[pageList.length];

        //拼接查询条件
        for (int i = 0; i < pageList.length; i++) {
            pageArr[i] = Criteria.where(ClickReportDayBO.PAGE_NAME).is(pageList[i]);
        }
        criteria.andOperator(new Criteria().orOperator(pageArr));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.LANGUAGE)
                .first(ClickReportDayBO.LANGUAGE).as(ClickReportDayBO.LANGUAGE)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getHomePageWeekAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL)
                .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_TOTAL);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME, ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.FULL_NAME).as(ClickReportDayBO.FULL_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getMinePageWeekAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                .and(ClickReportDayBO.CARD_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE)
                .and(ClickReportDayBO.NODE_NAME).is(ClickReportConstant.WOWNOW_CLICK_MINE);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportDayBO.PAGE_NAME, ClickReportDayBO.CARD_NAME, ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.PAGE_NAME).as(ClickReportDayBO.PAGE_NAME)
                .first(ClickReportDayBO.CARD_NAME).as(ClickReportDayBO.CARD_NAME)
                .first(ClickReportDayBO.NODE_NAME).as(ClickReportDayBO.NODE_NAME)
                .first(ClickReportDayBO.FULL_NAME).as(ClickReportDayBO.FULL_NAME)
                .sum(ClickReportDayBO.PV).as(ClickReportDayBO.PV)
                .sum(ClickReportDayBO.DV).as(ClickReportDayBO.DV)
                .sum(ClickReportDayBO.UV).as(ClickReportDayBO.UV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ClickReportDayBO.ID));
        return querList;
    }

    private static Update getUpdateBo(ClickReportDayBO clickReportDayB0) {
        Update update = new Update();
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, clickReportDayB0.getPageName());
        update.set(ClickReportDayBO.CARD_NAME, clickReportDayB0.getCardName());
        update.set(ClickReportDayBO.NODE_NAME, clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.FULL_NAME, clickReportDayB0.getPageName() + "@" + clickReportDayB0.getCardName() + "@" + clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ClickReportDayBO.UV, clickReportDayB0.getUv());
        update.set(ClickReportDayBO.PV, clickReportDayB0.getPv());
        update.set(ClickReportDayBO.DV, clickReportDayB0.getDv());
        return update;
    }

    private static Update getUpdateLanguageBo(ClickReportDayBO clickReportDayB0) {
        Update update = new Update();
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, clickReportDayB0.getPageName());
        update.set(ClickReportDayBO.CARD_NAME, clickReportDayB0.getCardName());
        update.set(ClickReportDayBO.NODE_NAME, clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.FULL_NAME, clickReportDayB0.getPageName() + "@" + clickReportDayB0.getCardName() + "@" + clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ClickReportDayBO.LANGUAGE, clickReportDayB0.getLanguage());
        update.set(ClickReportDayBO.UV, clickReportDayB0.getUv());
        update.set(ClickReportDayBO.PV, clickReportDayB0.getPv());
        update.set(ClickReportDayBO.DV, clickReportDayB0.getDv());
        return update;
    }

    private static Update getUpdateLanguageBo(ReportHomePageRecommendBO clickReportDayB0) {
        Update update = new Update();
        update.set(ReportHomePageRecommendBO.UPDATE_TIME, new Date());
        update.set(ReportHomePageRecommendBO.CREATE_TIME, new Date());
        update.set(ReportHomePageRecommendBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ReportHomePageRecommendBO.LANGUAGE, clickReportDayB0.getLanguage());
        update.set(ReportHomePageRecommendBO.PV, clickReportDayB0.getPv());
        return update;
    }


    private static Update getUpdateLanguageBo(ReportHomePageBannerBO clickReportDayB0) {
        Update update = new Update();
        update.set(ReportHomePageRecommendBO.UPDATE_TIME, new Date());
        update.set(ReportHomePageRecommendBO.CREATE_TIME, new Date());
        update.set(ReportHomePageRecommendBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ReportHomePageRecommendBO.LANGUAGE, clickReportDayB0.getLanguage());
        update.set(ReportHomePageRecommendBO.PV, clickReportDayB0.getPv());
        return update;
    }

    private static Update getShopCardAndOrderUpdateBo(ClickReportDayBO clickReportDayB0) {
        Update update = new Update();
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.CARD_NAME, clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.NODE_NAME, clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.FULL_NAME, clickReportDayB0.getNodeName() + "@" + clickReportDayB0.getNodeName() + "@" + clickReportDayB0.getNodeName());
        update.set(ClickReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ClickReportDayBO.UV, clickReportDayB0.getUv() == null ? 0 : clickReportDayB0.getUv());
        update.set(ClickReportDayBO.PV, clickReportDayB0.getPv() == null ? 0 : clickReportDayB0.getPv());
        update.set(ClickReportDayBO.DV, clickReportDayB0.getDv() == null ? 0 : clickReportDayB0.getDv());
        return update;
    }


    public static Update getWownowHomePageUpdateBo(Date dataTime, Long pvTotal, Long uvTotal, Long dvTotal) {
        Update update = new Update();
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, ClickReportConstant.WOWNOW_CLICK_TOTAL);
        update.set(ClickReportDayBO.CARD_NAME, ClickReportConstant.WOWNOW_CLICK_TOTAL);
        update.set(ClickReportDayBO.NODE_NAME, ClickReportConstant.WOWNOW_CLICK_TOTAL);
        update.set(ClickReportDayBO.FULL_NAME, ClickReportConstant.WOWNOW_CLICK_TOTAL);
        update.set(ClickReportDayBO.DATA_TIME, dataTime);
        update.set(ClickReportDayBO.UV, uvTotal);
        update.set(ClickReportDayBO.PV, pvTotal);
        update.set(ClickReportDayBO.DV, dvTotal);
        return update;
    }

    public static Update getWownowHomePageLanguageUpdateBo(ClickReportDayBO clickReportDayBO) {
        Update update = new Update();
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, clickReportDayBO.getPageName());
        update.set(ClickReportDayBO.CARD_NAME, clickReportDayBO.getCardName());
        update.set(ClickReportDayBO.NODE_NAME, clickReportDayBO.getNodeName());
        update.set(ClickReportDayBO.FULL_NAME, clickReportDayBO.getFullName());
        update.set(ClickReportDayBO.DATA_TIME, clickReportDayBO.getDataTime());
        update.set(ClickReportDayBO.LANGUAGE, clickReportDayBO.getLanguage());
        update.set(ClickReportDayBO.UV, clickReportDayBO.getUv());
        update.set(ClickReportDayBO.PV, clickReportDayBO.getPv());
        update.set(ClickReportDayBO.DV, clickReportDayBO.getDv());
        return update;
    }

    public static Update getWownowMinePageUpdateBo(Date dataTime, Long pvTotal, Long uvTotal, Long dvTotal) {
        Update update = new Update();
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, ClickReportConstant.WOWNOW_CLICK_MINE);
        update.set(ClickReportDayBO.CARD_NAME, ClickReportConstant.WOWNOW_CLICK_MINE);
        update.set(ClickReportDayBO.NODE_NAME, ClickReportConstant.WOWNOW_CLICK_MINE);
        update.set(ClickReportDayBO.FULL_NAME, ClickReportConstant.WOWNOW_CLICK_MINE);
        update.set(ClickReportDayBO.DATA_TIME, dataTime);
        update.set(ClickReportDayBO.UV, uvTotal);
        update.set(ClickReportDayBO.PV, pvTotal);
        update.set(ClickReportDayBO.DV, dvTotal);
        return update;
    }

    private static Update getWownowMineLanguagePageUpdateBo(Date dataTime, ClickReportDayBO bo) {
        Update update = new Update();
        update.set(ClickReportDayBO.CREATE_TIME, new Date());
        update.set(ClickReportDayBO.UPDATE_TIME, new Date());
        update.set(ClickReportDayBO.PAGE_NAME, bo.getPageName());
        update.set(ClickReportDayBO.CARD_NAME, bo.getCardName());
        update.set(ClickReportDayBO.NODE_NAME, bo.getNodeName());
        update.set(ClickReportDayBO.FULL_NAME, bo.getPageName());
        update.set(ClickReportDayBO.DATA_TIME, dataTime);
        update.set(ClickReportDayBO.UV, bo.getUv());
        update.set(ClickReportDayBO.PV, bo.getPv());
        update.set(ClickReportDayBO.DV, bo.getDv());
        return update;
    }

    /**
     * banner点击数统计
     *
     * @param req
     */
    private void staticBannerClickCount(ReportBasicReqDTO req) {
        try {
            Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(req.getBeginTime()).lte(req.getEndTime())
                    .and(ClickReportConstant.EXT_ROUTE).is(ClickReportConstant.BANNER_CLICK_ROUTE);

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group(ClickReportConstant.CURRENT_PAGE, ClickReportConstant.CURRENT_AREA, ClickReportConstant.LANGUAGE)
                            .first(ClickReportConstant.CURRENT_PAGE).as(BannerClickStaticBO.PAGE)
                            .first(ClickReportConstant.CURRENT_AREA).as(BannerClickStaticBO.AREA)
                            .first(ClickReportConstant.LANGUAGE).as(BannerClickStaticBO.LANGUAGE)
                            .count().as(BannerClickStaticBO.COUNT)
            );
            List<BannerClickStaticBO> datas = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, BannerClickStaticBO.class).getMappedResults();
            datas.forEach(d -> {
                d.setDataTime(req.getDataTime())
                        .setCreateTime(new Date());
                mongoTemplate.insert(d, MongoDbCollectonName.BANNER_CLICK_STATIC);
            });
        } catch (Exception e) {
            log.error("统计banner点击数出现异常", e);
        }
    }

    /**
     * 首页业务线点击报表
     *
     * @param req
     */
    private void staticHomePageBusinessClick(ReportBasicReqDTO req) {
        ClickReportConstant.HOME_CLICK_BUSINESS_LIST.forEach(business -> {
            try {
                Criteria criteria = createCommHomePageCriteria(req, business);
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group(ClickReportConstant.LANGUAGE)
                                .first(ClickReportConstant.LANGUAGE).as(ReportHomePageClickBO.LANGUAGE)
                                .count().as(ReportHomePageClickBO.PV))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportHomePageClickBO> datas = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportHomePageClickBO.class).getMappedResults();
                datas.forEach(d -> {
                    Criteria cri = createCommHomePageCriteria(req, business).and(ClickReportConstant.LANGUAGE).is(d.getLanguage());
                    //uv
                    Aggregation uvAggregation = Aggregation.newAggregation(
                            Aggregation.match(cri),
                            Aggregation.group(ClickReportConstant.USERINFOBO_OPERATOR_NO),
                            Aggregation.count().as(ReportHomePageClickBO.UV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<ReportHomePageClickBO> uvDatas = mongoTemplate.aggregate(uvAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportHomePageClickBO.class).getMappedResults();
                    int uv = uvDatas.isEmpty() ? 0 : uvDatas.get(0).getUv();

                    //dv
                    Aggregation dvAggregation = Aggregation.newAggregation(
                            Aggregation.match(cri),
                            Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID),
                            Aggregation.count().as(ReportHomePageClickBO.DV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<ReportHomePageClickBO> dvDatas = mongoTemplate.aggregate(dvAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportHomePageClickBO.class).getMappedResults();
                    int dv = dvDatas.isEmpty() ? 0 : dvDatas.get(0).getDv();

                    d.setDataTime(req.getDataTime())
                            .setBusinessName(business)
                            .setUv(uv)
                            .setDv(dv)
                            .setCreateTime(new Date());
                    mongoTemplate.insert(d, MongoDbCollectonName.REPORT_HOME_PAGE_CLICK);
                });
            } catch (Exception e) {
                log.error("统计首页业务线点击报表出现异常, businessName:{}", business, e);
            }
        });
    }

    private Criteria createCommHomePageCriteria(ReportBasicReqDTO req, String business) {
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(req.getBeginTime()).lt(req.getEndTime())
                .and(ClickReportConstant.CURRENT_PAGE).in(Arrays.asList(WOWNOW_HOME_PAGE_ARR));
        if (!ClickReportConstant.HOME_CLICK_BUSINESS_LIST.get(0).equals(business)) {
            //海外购=包含电商并且不包含批发和一元购
            if ("海外购".equals(business)) {
                criteria.andOperator(Criteria.where(ClickReportConstant.BUSINESS_NAME).regex("^.*电商.*$"),
                        Criteria.where(ClickReportConstant.BUSINESS_NAME).regex("^((?!批发).)*$"),
                        Criteria.where(ClickReportConstant.BUSINESS_NAME).regex("^((?!一元购).)*$"));
            } else {
                criteria.and(ClickReportConstant.BUSINESS_NAME).regex("^.*" + business + ".*$");
            }
        }
        return criteria;
    }


    private void staticHomePageBannerClick(ReportBasicReqDTO reportBasicReqDTO){
        try {
            String pageName = "WOWNOW首页3.0";
            String cardName = "3.0金边首页新BANNER";

            Criteria criteria = new Criteria();
            criteria.and(ClickReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime());
            criteria.and("pageName").is(pageName);
            criteria.and("cardName").is(cardName);

            List<ClickReportDayBO> datas = mongoTemplate.find(Query.query(criteria), ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
            HashMap<String, Long> businessLineMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(datas)){
                datas.forEach(bo -> {
                    if(StringUtils.isNotBlank(bo.getNodeName())) {
                        String[] arr = bo.getNodeName().split("_");
                        if(arr.length > 0) {
                            String businessLine = arr[0];
                            String language = bo.getLanguage();
                            String key = language + ClickReportConstant.SPLIT_KEY + businessLine;
                            Long value = businessLineMap.putIfAbsent(key, 0L);
                            businessLineMap.put(key, Optional.ofNullable(value).orElse(0L) + bo.getPv());
                        }
                    }
                });
            }

            //入库
            for (String key : businessLineMap.keySet()) {
                String[] arr = key.split(ClickReportConstant.SPLIT_KEY);
                String language = arr[0];
                String businessLine = arr[1];
                try {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(ReportHomePageBannerBO.LANGUAGE).is(language)
                            .and(ReportHomePageBannerBO.BUSINESS_LINE).is(businessLine));

                    ReportHomePageBannerBO bo = new ReportHomePageBannerBO();
                    bo.setLanguage(language);
                    bo.setPv(businessLineMap.get(key));
                    bo.setBusinessLine(businessLine);
                    bo.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getUpdateLanguageBo(bo), MongoDbCollectonName.REPORT_HOME_PAGE_BANNER);
                } catch (Exception e) {
                    log.warn("首页banner统计异常, key:{}", key, e);
                }

            }
        } catch (Exception e) {
            log.error("统计首页banner业务线点击异常", e);
        }
    }

    private void staticFloatWindowClick(ReportBasicReqDTO req) {
        try {
            String businessName = "click_floatWindowPlugin";
            Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(req.getBeginTime()).lt(req.getEndTime())
                    .and(ClickReportConstant.BUSINESS_NAME).is(businessName);

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group(ClickReportConstant.LANGUAGE, ClickReportConstant.EXT_ROUTE)
                            .first(ClickReportConstant.LANGUAGE).as(ReportFloatWindowBO.LANGUAGE)
                            .first(ClickReportConstant.EXT_ROUTE).as(ReportFloatWindowBO.ROUTE)
                            .count().as(ReportFloatWindowBO.PV))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
            List<ReportFloatWindowBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportFloatWindowBO.class).getMappedResults();
            reports.forEach(report -> {
                if (StringUtils.isNotBlank(report.getRoute()) && StringUtils.isNotBlank(report.getLanguage())) {
                    Criteria cri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(req.getBeginTime()).lt(req.getEndTime())
                            .and(ClickReportConstant.BUSINESS_NAME).is(businessName)
                            .and(ClickReportConstant.LANGUAGE).is(report.getLanguage())
                            .and(ClickReportConstant.EXT_ROUTE).is(report.getRoute());
                    //uv
                    Aggregation uvAggregation = Aggregation.newAggregation(
                            Aggregation.match(cri),
                            Aggregation.group(ClickReportConstant.USERINFOBO_OPERATOR_NO),
                            Aggregation.count().as(ReportFloatWindowBO.UV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<ReportFloatWindowBO> uvDatas = mongoTemplate.aggregate(uvAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportFloatWindowBO.class).getMappedResults();
                    int uv = uvDatas.isEmpty() ? 0 : uvDatas.get(0).getUv();

                    //dv
                    Aggregation dvAggregation = Aggregation.newAggregation(
                            Aggregation.match(cri),
                            Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID),
                            Aggregation.count().as(ReportFloatWindowBO.DV))
                            .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                    List<ReportFloatWindowBO> dvDatas = mongoTemplate.aggregate(dvAggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, ReportFloatWindowBO.class).getMappedResults();
                    int dv = dvDatas.isEmpty() ? 0 : dvDatas.get(0).getDv();

                    report.setDataTime(req.getDataTime())
                            .setUv(uv)
                            .setDv(dv)
                            .setCreateTime(new Date());
                    mongoTemplate.insert(report);
                }
            });
        } catch (Exception e) {
            log.error("统计浮窗报表出现异常", e);
        }
    }

    private void statisticsHomeRecommend(ReportBasicReqDTO req) {
        //首页推荐报表
        try {
            Criteria criteria = Criteria.where(AdsViewLogBO.RECORD_TIME).gte(req.getBeginTime()).lt(req.getEndTime());
            List<AggregationOperation> uvAggs = Arrays.asList(Aggregation.match(criteria), Aggregation.group(AdsViewLogBO.OPERATOR_NO));
            List<AggregationOperation> dvAggs = Arrays.asList(Aggregation.match(criteria), Aggregation.group(AdsViewLogBO.DEVICE_ID));

            long pv = mongoTemplate.count(Query.query(criteria), MongoDbCollectonName.ADS_VIEW_LOG);
            long uv = mongoPageHelper.queryTotal(uvAggs, MongoDbCollectonName.ADS_VIEW_LOG, true);
            long dv = mongoPageHelper.queryTotal(dvAggs, MongoDbCollectonName.ADS_VIEW_LOG, true);

            String businessName = "首页推荐";
            Criteria reportCriteria = Criteria.where(ClickReportDayBO.DATA_TIME).gte(req.getBeginTime()).lte(req.getEndTime())
                    .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_HOME3)
                    .and(ClickReportDayBO.CARD_NAME).is(businessName)
                    .and(ClickReportDayBO.NODE_NAME).is(businessName);
            List<ClickReportDayBO> clickReportDayList = mongoTemplate.find(Query.query(reportCriteria), ClickReportDayBO.class, ClickReportDayBO.COLLECTION_NAME);
            ClickReportDayBO report = CollectionUtils.isEmpty(clickReportDayList) ? new ClickReportDayBO() : clickReportDayList.get(0);
            report.setPv(pv);
            report.setDv(dv);
            report.setUv(uv);
            report.setPageName(ClickReportConstant.WOWNOW_HOME3);
            report.setCardName(businessName);
            report.setNodeName(businessName);
            report.setDataTime(req.getDataTime());
            mongoTemplate.upsert(Query.query(reportCriteria), getUpdateBo(report), ClickReportDayBO.COLLECTION_NAME);
        } catch (Exception e) {
            log.error("统计首页推荐报表出现异常", e);
        }

        //首页推荐报表(按语言)
        try {
            Arrays.asList(LanguageEnum.values()).forEach(languageEnum -> {
                Criteria criteria = Criteria.where(AdsViewLogBO.RECORD_TIME).gte(req.getBeginTime()).lt(req.getEndTime()).and(AdsViewLogBO.LANGUAGE).is(languageEnum.getCode());
                List<AggregationOperation> uvAggs = Arrays.asList(Aggregation.match(criteria), Aggregation.group(AdsViewLogBO.OPERATOR_NO));
                List<AggregationOperation> dvAggs = Arrays.asList(Aggregation.match(criteria), Aggregation.group(AdsViewLogBO.DEVICE_ID));

                long pv = mongoTemplate.count(Query.query(criteria), MongoDbCollectonName.ADS_VIEW_LOG);
                long uv = mongoPageHelper.queryTotal(uvAggs, MongoDbCollectonName.ADS_VIEW_LOG, true);
                long dv = mongoPageHelper.queryTotal(dvAggs, MongoDbCollectonName.ADS_VIEW_LOG, true);

                String businessName = "首页推荐";
                Criteria reportCriteria = Criteria.where(ClickReportDayBO.DATA_TIME).gte(req.getBeginTime()).lte(req.getEndTime())
                        .and(ClickReportDayBO.PAGE_NAME).is(ClickReportConstant.WOWNOW_HOME3)
                        .and(ClickReportDayBO.CARD_NAME).is(businessName)
                        .and(ClickReportDayBO.NODE_NAME).is(businessName)
                        .and(ClickReportDayBO.LANGUAGE).is(languageEnum.getCode());
                List<ClickReportDayBO> clickReportDayList = mongoTemplate.find(Query.query(reportCriteria), ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
                ClickReportDayBO report = CollectionUtils.isEmpty(clickReportDayList) ? new ClickReportDayBO() : clickReportDayList.get(0);
                report.setPv(pv);
                report.setDv(dv);
                report.setUv(uv);
                report.setPageName(ClickReportConstant.WOWNOW_HOME3);
                report.setCardName(businessName);
                report.setNodeName(businessName);
                report.setLanguage(languageEnum.getCode());
                report.setDataTime(req.getDataTime());
                mongoTemplate.upsert(Query.query(reportCriteria), getUpdateLanguageBo(report), MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
            });
        } catch (Exception e) {
            log.error("统计首页推荐语言报表出现异常", e);
        }

        //首页推荐报表(按语言 + 业务线)
        try {
            long pageNum = 0, pageSize = 100;
            //构造查询条件 PV
            List<AggregationOperation> aggregationlist = getHomePageRecommendCriteria(req);
            PageRespDTO<ReportHomePageRecommendBO> pageRespDTO = mongoPageHelper.query(
                    aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.ADS_VIEW_LOG, true, null, ReportHomePageRecommendBO.class);

            if (pageRespDTO.getTotal() < 1) {
                return;
            }

            long pages = pageRespDTO.getPages();
            for (int i = 0; i < pages; i++) {
                //统计
                if (i > 0) {
                    //遍历查询PV
                    pageRespDTO = mongoPageHelper.query(
                            aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.ADS_VIEW_LOG, true, pageRespDTO.getTotal(), ReportHomePageRecommendBO.class);
                    if (pageRespDTO.getTotal() < 1) {
                        return;
                    }
                }

                //3.入库
                for (ReportHomePageRecommendBO clickReportDayBo : pageRespDTO.getList()) {
                    try {
                        Query query = new Query();
                        query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                                .gte(req.getBeginTime())
                                .lt(req.getEndTime())
                                .and(ReportHomePageRecommendBO.LANGUAGE).is(clickReportDayBo.getLanguage())
                                .and(ReportHomePageRecommendBO.BUSINESS_LINE).is(clickReportDayBo.getBusinessLine()));
                        clickReportDayBo.setDataTime(req.getDataTime());
                        //更新总表
                        mongoTemplate.upsert(query, getUpdateLanguageBo(clickReportDayBo), MongoDbCollectonName.REPORT_HOME_PAGE_RECOMMEND);
                    } catch (Exception e) {
                        log.warn("首页推荐报表(按语言 + 业务线)异常, reportBasicReqDTO:{}, clickReportDayBo:{}", req, clickReportDayBo, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("统计首页推荐语言+业务线报表出现异常", e);
        }
    }

    private List<AggregationOperation> getHomePageRecommendCriteria(ReportBasicReqDTO req) {
        Criteria criteria = Criteria.where(AdsViewLogBO.RECORD_TIME).gte(req.getBeginTime()).lt(req.getEndTime());
        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.SIMPLE_LANGUAGE, ClickReportConstant.ADS_SUBJECT)
                .first(ClickReportConstant.SIMPLE_LANGUAGE).as(ReportHomePageRecommendBO.LANGUAGE)
                .first(ClickReportConstant.ADS_SUBJECT).as(ReportHomePageRecommendBO.BUSINESS_LINE)
                .count().as(ReportHomePageRecommendBO.PV));
        return querList;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
//        standardClickEventWeekStatistics(reportBasicReqDTO);
//        log.info("统计周标准点击事件完成,时间:{}",reportBasicReqDTO);
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        //统计标准点击事件
//        standardClickEventMonthStatistics(reportBasicReqDTO);
//        log.info("统计月标准点击事件完成,时间:{}",reportBasicReqDTO);
        return null;
    }
}
