package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.api.req.CmsMerchantAdsPublishReqDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticDetailRespDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticRespDTO;
import com.outstanding.framework.core.PageInfoDTO;

public interface MerchantAdsPublishStaticService {
    void staticMerchantAdsPublish();

    PageInfoDTO<MerchantAdsPublishStaticRespDTO> seachMerchantNodePlanStaticList(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO);

    MerchantAdsPublishStaticDetailRespDTO  seachMerchantNodePlanStaticDetail(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO);
}
