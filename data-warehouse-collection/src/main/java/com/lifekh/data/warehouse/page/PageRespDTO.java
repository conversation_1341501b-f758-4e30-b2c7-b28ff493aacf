package com.lifekh.data.warehouse.page;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageRespDTO<T> implements Serializable {
    private static final long serialVersionUID = -4511648459565491861L;
    private int pageNum;
    private int pageSize;
    private int size;
    private int startRow;
    private int endRow;
    private long total;
    private long pages;
    private List<T> list;
    private int prePage;
    private int nextPage;
    private boolean isFirstPage = false;
    private boolean isLastPage = false;
    private boolean hasPreviousPage = false;
    private boolean hasNextPage = false;
    private int navigatePages;
    private int[] navigatepageNums;
    private int navigateFirstPage;
    private int navigateLastPage;
}
