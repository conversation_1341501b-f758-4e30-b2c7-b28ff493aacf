package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.MerchantAdsPublishStaticService;
import com.lifekh.data.warehouse.report.service.UserReportStaticService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 每天5点统计昨日商户广告投放数据
 * <AUTHOR>
 * @Date 2021/11/5 10:13
 * @Version 1.0
 **/
@Slf4j
@ElasticJobConf(name = "merchant_ads_publish_static", cron = "0 0 5 * * ?", description = "每天5点统计昨日商户广告投放数据",shardingTotalCount = 1)
public class MerchantAdsPublishJob extends AbstractSimpleJob {

    @Autowired
    private MerchantAdsPublishStaticService merchantAdsPublishStaticService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("统计商户广告投放开始");
        merchantAdsPublishStaticService.staticMerchantAdsPublish();
        log.info("统计商户广告投放结束");
    }
}
