package com.lifekh.data.warehouse.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ReportDateDTO {

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 返回昨天的时间
     *
     * @return
     */
    public static ReportDateDTO yesterday() {
        Date yesterday = DateUtil.yesterday();
        Date startTime = DateUtil.beginOfDay(yesterday);
        Date endTime = DateUtil.endOfDay(yesterday);
        Date dataTime = DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12));
        return new ReportDateDTO()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setDataTime(dataTime)
                .setDataDate(DateUtil.formatDate(dataTime));
    }

    /**
     * 最近N天
     *
     * @return
     */
    public static ReportDateDTO recent(int days) {
        Date now = new Date();
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -days));
        Date endTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -1));
        return new ReportDateDTO()
                .setDataDate(DateUtil.formatDate(endTime))
                .setDataTime(DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12))) //中午12点
                .setStartTime(startTime)
                .setEndTime(endTime);
    }
}
