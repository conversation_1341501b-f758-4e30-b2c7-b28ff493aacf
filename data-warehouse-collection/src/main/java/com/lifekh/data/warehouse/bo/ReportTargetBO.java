package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_TARGET)
@Data
@Accessors(chain = true)
public class ReportTargetBO implements Serializable {
    private static final long serialVersionUID = -2049795582598557512L;

    public final static String COUNT = "count";

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;
    public static String DATA_TIME = "dataTime";

    /**
     * 指标 {@link com.lifekh.data.warehouse.enums.ReportTargetEnum}
     */
    private String target;

    private String targetName;
    public static String TARGET_NAME = "targetName";

    /**
     * 指标统计天数
     */
    private Integer days;
    public static String DAYS = "days";

    /**
     * 数量
     */
    private Double count;

    private Date createTime;

    private Date updateTime;

}
