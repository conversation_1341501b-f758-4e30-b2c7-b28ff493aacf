package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOME_PAGE_CLICK)
@Data
@Accessors(chain = true)
public class ReportHomePageClickBO implements Serializable {
    private static final long serialVersionUID = 1699407501379156030L;

    public static final String DATA_TIME = "dataTime";
    public static final String BUSINESS_NAME = "businessName";
    public static final String LANGUAGE = "language";
    public static final String PV = "pv";
    public static final String UV = "uv";
    public static final String DV = "dv";

    private Date dataTime;
    private String businessName;
    private String language;
    private Integer pv;
    private Integer uv;
    private Integer dv;
    private Date createTime;
}
