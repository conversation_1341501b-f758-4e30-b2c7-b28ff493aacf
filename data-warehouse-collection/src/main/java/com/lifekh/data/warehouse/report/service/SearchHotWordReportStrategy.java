package com.lifekh.data.warehouse.report.service;

import com.chaos.common.enums.BusinessLineEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.ClickReportDayBO;
import com.lifekh.data.warehouse.bo.ClickReportWeekBO;
import com.lifekh.data.warehouse.bo.HotWordSearchReportDayBO;
import com.lifekh.data.warehouse.bo.ThematicSearchReportDayBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SearchHotWordReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //专题页搜索报表
        log.info("开始统计专题页搜索报表,reportBasicReqDTO:{}", reportBasicReqDTO);
        thematicSearchReport(reportBasicReqDTO);
        log.info("结束统计专题页搜索报表");

        //热搜词报表
        log.info("开始统计热搜词报表,reportBasicReqDTO:{}", reportBasicReqDTO);
        hotwordReport(reportBasicReqDTO);
        log.info("结束统计热搜词报表");
        return null;
    }

    /**
     * 热搜词报表
     *
     * @param reportBasicReqDTO
     */
    private void hotwordReport(ReportBasicReqDTO reportBasicReqDTO) {
        //构造查询条件 PV
        long pageNum = 0, pageSize = 1000;
        List<AggregationOperation> aggregationlist = getHotwordPvAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<HotWordSearchReportDayBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_SEARCH, true, null, HotWordSearchReportDayBO.class);

        log.info("hotwordReport 总页数：{}， 总条数：{}", pageRespDTO.getPages(), pageRespDTO.getTotal());
        if (pageRespDTO.getSize() < 1) {
            return;
        }

        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, pageRespDTO.getTotal(), HotWordSearchReportDayBO.class);
                if (pageRespDTO.getSize() < 1) {
                    return;
                }
            }

            //入库
            for (HotWordSearchReportDayBO reportDayBO : pageRespDTO.getList()) {
                try {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(HotWordSearchReportDayBO.LANGUAGE).is(reportDayBO.getLanguage())
                            .and(HotWordSearchReportDayBO.BUSINESSLINE).is(reportDayBO.getBusinessLine())
                            .and(HotWordSearchReportDayBO.KEYWORD).is(reportDayBO.getKeyWord()));
                    reportDayBO.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getHotwordUpdateBo(reportDayBO), HotWordSearchReportDayBO.COLLECTION_NAME);
                } catch (Exception e) {
                    log.warn("统计昨天热词搜索异常, reportBasicReqDTO:{}, reportDayBO:{}", reportBasicReqDTO, reportDayBO, e);
                }
            }
        }
    }


    /**
     * 专题页搜索报表
     *
     * @param reportBasicReqDTO
     */
    private void thematicSearchReport(ReportBasicReqDTO reportBasicReqDTO) {
        //构造查询条件 PV
        long pageNum = 0, pageSize = 1000;
        List<AggregationOperation> aggregationlist = getThematicSearchPvAggregationCriteria(reportBasicReqDTO);
        PageRespDTO<ThematicSearchReportDayBO> pageRespDTO = mongoPageHelper.query(
                aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_SEARCH, true, null, ThematicSearchReportDayBO.class);
        if (pageRespDTO.getSize() < 1) {
            return;
        }

        log.info("thematicSearchReport 总页数：{}， 总条数：{}", pageRespDTO.getPages(), pageRespDTO.getTotal());
        long pages = pageRespDTO.getPages();
        for (int i = 0; i < pages; i++) {
            //统计
            if (i > 0) {
                //遍历查询PV
                pageRespDTO = mongoPageHelper.query(
                        aggregationlist, new PageReqDTO(i, pageSize), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, true, pageRespDTO.getTotal(), ThematicSearchReportDayBO.class);
                if (pageRespDTO.getSize() < 1) {
                    return;
                }
            }

            //3.入库
            for (ThematicSearchReportDayBO reportDayBO : pageRespDTO.getList()) {
                try {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(ClickReportWeekBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(ThematicSearchReportDayBO.LANGUAGE).is(reportDayBO.getLanguage())
                            .and(ThematicSearchReportDayBO.THEMATICNAME).is(reportDayBO.getThematicName())
                            .and(ThematicSearchReportDayBO.CONTENT).is(reportDayBO.getContent()));
                    reportDayBO.setDataTime(reportBasicReqDTO.getDataTime());
                    //更新总表
                    mongoTemplate.upsert(query, getThematicSearchUpdateBo(reportDayBO), ThematicSearchReportDayBO.COLLECTION_NAME);
                } catch (Exception e) {
                    log.warn("统计昨天专题页搜索异常, reportBasicReqDTO:{}, reportDayBO:{}", reportBasicReqDTO, reportDayBO, e);
                }
            }
        }
    }

    private static Update getThematicSearchUpdateBo(ThematicSearchReportDayBO clickReportDayB0) {
        Update update = new Update();
        update.set(ThematicSearchReportDayBO.CREATE_TIME, new Date());
        update.set(ThematicSearchReportDayBO.LANGUAGE, clickReportDayB0.getLanguage());
        update.set(ThematicSearchReportDayBO.THEMATICNAME, clickReportDayB0.getThematicName());
        update.set(ThematicSearchReportDayBO.CONTENT, clickReportDayB0.getContent());
        update.set(ThematicSearchReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(ThematicSearchReportDayBO.PV, clickReportDayB0.getPv());
        return update;
    }

    private static Update getHotwordUpdateBo(HotWordSearchReportDayBO clickReportDayB0) {
        Update update = new Update();
        update.set(HotWordSearchReportDayBO.CREATE_TIME, new Date());
        update.set(HotWordSearchReportDayBO.LANGUAGE, clickReportDayB0.getLanguage());
        update.set(HotWordSearchReportDayBO.KEYWORD, clickReportDayB0.getKeyWord());
        update.set(HotWordSearchReportDayBO.BUSINESSLINE, clickReportDayB0.getBusinessLine());
        update.set(HotWordSearchReportDayBO.DATA_TIME, clickReportDayB0.getDataTime());
        update.set(HotWordSearchReportDayBO.PV, clickReportDayB0.getPv());
        return update;
    }
    private List<AggregationOperation> getThematicSearchPvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is("AggregateSearchKeyWordStat");
        criteria.and(ClickReportConstant.BUSINESS_NAME).is("首页专题搜索");
        criteria.and("ext.thematicName").ne(null);
        criteria.and("ext.content").ne(null);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group("userInfoBo.language", "ext.thematicName", "ext.content")
                .first("userInfoBo.language").as(ThematicSearchReportDayBO.LANGUAGE)
                .first("ext.thematicName").as(ThematicSearchReportDayBO.THEMATICNAME)
                .first("ext.content").as(ThematicSearchReportDayBO.CONTENT)
                .count().as(ThematicSearchReportDayBO.PV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, ThematicSearchReportDayBO.PV));
        return querList;
    }

    private List<AggregationOperation> getHotwordPvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is("AggregateSearchKeyWordStat");
        criteria.and(ClickReportConstant.BUSINESS_NAME).in(BusinessLineEnum.YUMNOW.getCode(), BusinessLineEnum.TINHNOW.getCode(), BusinessLineEnum.GROUP_BUY.getCode());
        criteria.and("ext.keyWord").ne(null);

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group("userInfoBo.language", "businessName", "ext.keyWord")
                .first("userInfoBo.language").as(HotWordSearchReportDayBO.LANGUAGE)
                .first("businessName").as(HotWordSearchReportDayBO.BUSINESSLINE)
                .first("ext.keyWord").as(HotWordSearchReportDayBO.KEYWORD)
                .count().as(HotWordSearchReportDayBO.PV));
        querList.add(Aggregation.sort(Sort.Direction.DESC, HotWordSearchReportDayBO.PV));
        return querList;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
