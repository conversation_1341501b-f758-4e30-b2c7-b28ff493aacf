package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.DWD_AGGREGATE_ORDER)
@Data
@Accessors(chain = true)
public class DwdAggregateOrderDO implements Serializable {
    private static final long serialVersionUID = 8236274310232423139L;

    @Id
    private ObjectId id;
    public static String ID = "_id";

    /**
     * 创建时间
     */
    private Date createTime;
    public static String CREATE_TIME = "createTime";

    /**
     * 更新时间
     */
    private Date updateTime;
    public static String UPDATE_TIME = "updateTime";

    /**
     * 下单时间
     */
    private Date orderTime;
    public static String ORDER_TIME = "orderTime";

    /**
     * 月份  yyyymm
     */
    private Long month;
    public static String MONTH = "month";

    /**
     * 日期  yyyymmdd
     */
    private Long day;
    public static String DAY = "day";

    /**
     * 查询时间，用于metabase查询
     */
    private Date searchTime;
    public static String SEARCH_TIME = "searchTime";

    /**
     * 聚合订单编号
     */
    private String aggregateOrderNo;
    public static String AGGREGATE_ORDER_NO = "aggregateOrderNo";

    /**
     * 支付单号
     */
    private String payOrderNo;
    public static String PAY_ORDER_NO = "payOrderNo";

    /**
     * 支付方式， 10-货到付款，11-在线付款
     */
    private Integer payType;
    public static String PAY_TYPE = "payType";

    /**
     * 支付渠道
     */
    private String payChannel;
    public static String PAY_CHANNEL = "payChannel";

    /**
     * 一级商户编号
     */
    private String firstMerchantNo;
    public static String FIRST_MERCHANT_NO = "firstMerchantNo";

    /**
     * 二级商户编号
     */
    private String merchantNo;
    public static String MERCHANT_NO = "merchantNo";

    /**
     * 门店编号
     */
    private String storeNo;
    public static String STORE_NO = "storeNo";

    /**
     * 门店名称
     */
    private String storeName;
    public static String STORE_NAME = "storeName";

    /**
     * 业务线
     */
    private String businessLine;
    public static String BUSINESS_LINE = "businessLine";

    /**
     * 订单类型
     */
    private Integer orderType;
    public static String ORDER_TYPE = "orderType";

    /**
     * 订单终态
     */
    private Integer aggregateOrderFinalState;
    public static String AGGREGATE_ORDER_FINAL_STATE = "aggregateOrderFinalState";

    /**
     * 聚合订单状态
     */
    private Integer aggregateOrderState;
    public static String AGGREGATE_ORDER_STATE = "aggregateOrderState";

    /**
     * 实付金额
     */
    private Decimal128 actualPayAmount;
    public static String ACTUAL_PAY_AMOUNT = "actualPayAmount";

    /**
     * 应付金额
     */
    private Decimal128 totalPayableAmount;
    public static String TOTAL_PAYABLE_AMOUNT = "totalPayableAmount";

    /**
     * 操作员编号
     */
    private String operatorNo;
    public static String OPERATOR_NO = "operatorNo";

    /**
     * 手机号  从用户登录号表取
     */
    private String mobile;
    public static String MOBILE = "mobile";

    /**
     * 注册时间
     */
    private Date registerTime;
    public static String REGISTER_TIME = "registerTime";

    /**
     * 语言 从活跃事件取
     */
    private String language;
    public static String LANGUAGE = "language";

    private Integer appNo;
    public static String APP_NO = "appNo";

    private String appVersion;
    public static String APP_VERSION = "appVersion";

    /**
     * 用户当前下单纬度 从活跃事件取
     */
    private Float latitude;
    public static String LATITUDE = "latitude";

    /**
     * 用户当前下单经度 从活跃事件取
     */
    private Float longitude;
    public static String LONGITUDE = "longitude";

    /**
     * 用户当前下单省 从活跃事件取
     */
    private String province;
    public static String PROVINCE = "province";

    /**
     * 用户当前下单区 从活跃事件取
     */
    private String district;
    public static String DISTRICT = "district";

    /**
     * 地址编号 从聚合订单表取
     */
    private String addressNo;
    public static String ADDRESS_NO = "addressNo";

    /**
     * 地址纬度 从地址表取
     */
    private Float addressLatitude;
    public static String ADDRESS_LATITUDE = "addressLatitude";

    /**
     * 地址经度 从地址表取
     */
    private Float addressLongitude;
    public static String ADDRESS_LONGITUDE = "addressLongitude";

    /**
     * 地址省 从地址表取
     */
    private String addressProvince;
    public static String ADDRESS_PROVINCE = "addressProvince";

    /**
     * 地址区 从地址表取
     */
    private String addressDistrict;
    public static String ADDRESS_DISTRICT = "addressDistrict";

    private String deviceId;
    public static String DEVICE_ID = "deviceId";

    /**
     * 数据插入时间
     */
    private Date insertTime;
}
