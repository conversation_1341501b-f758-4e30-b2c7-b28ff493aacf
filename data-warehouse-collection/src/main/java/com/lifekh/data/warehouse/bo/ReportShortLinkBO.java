package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_SHORT_LINK)
@Data
@Accessors(chain = true)
public class ReportShortLinkBO implements Serializable {
    private static final long serialVersionUID = 8770093591673345524L;

    public static final String DATA_TIME = "dataTime";
    public static final String SHORT_ID = "shortId";

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 短链id
     */
    private String shortId;

    /**
     * 短链浏览数
     */
    private Long pv;

    /**
     * 打开APP次数
     */
    private Long openAppCount;

    /**
     * 首次打开APP次数
     */
    private Long firstOpenAppCount;

    /**
     * 注册用户数
     */
    private Long registerCount;

    /**
     * 用户下单数
     */
    private Long orderCount;

    /**
     * 打开APP转化率
     */
    private Double openAppRate;

    /**
     * 注册转化率
     */
    private Double registerRate;

    /**
     * 下单转化率
     */
    private Double orderRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
