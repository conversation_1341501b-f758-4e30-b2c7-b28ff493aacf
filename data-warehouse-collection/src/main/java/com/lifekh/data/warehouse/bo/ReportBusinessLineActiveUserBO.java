package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.enums.ReportLanguageDataTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_BUSINESS_ACTIVE_USER)
@Data
@Accessors(chain = true)
public class ReportBusinessLineActiveUserBO implements Serializable {
    private static final long serialVersionUID = 8107859918954474996L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;
    public static final String DATA_TIME = "dataTime";
    /**
     * 业务线
     */
    private String businessLine;
    public final static String BUSINESS_LINE = "businessLine";

    /**
     * 语言
     */
    private String language;
    public final static String LANGUAGE = "language";

    /**
     * 总数
     */
    private Long total;
    public final static String TOTAL = "total";

    /**
     * 数量统计
     */
    private Long count;
    public final static String COUNT = "count";

    private Date createTime;

    private Date updateTime;

}
