package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.ShortLinkReportQueryReqDTO;
import com.lifekh.data.warehouse.api.resp.ShortLinkReportRespDTO;
import com.lifekh.data.warehouse.bo.ReportShortLinkBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Service
public class ShortLinkReportServiceImpl implements ShortLinkReportService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Value("${shortlink.baseUrl:}")
    private String shortlinkBaseUrl;

    @Value("${shortlink.reportUrl:}")
    private String shortlinkReportUrl;

    @Override
    public void statisticsShortLinkReport(ReportDateDTO dateDTO) {
        //查询短链pv
        Map<String, Long> pvMap = this.queryShortLinkPv(dateDTO);
        log.info("查询短链pv完成");

        pvMap.forEach((shortId, pv) -> {
            try {
                //查询打开APP次数
                Criteria openAppCri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lt(dateDTO.getEndTime())
                        .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_SESSION_START)
                        .and(ClickReportConstant.EXT_SHORTID).is(shortId);
                Long openAppCount = mongoTemplate.count(Query.query(openAppCri), MongoDbCollectonName.COLLECT_BEHAVIOR);
                log.info("查询短链打开APP次数完成, shortId: {}", shortId);

                //查询首次打开APP次数
                Criteria firstOpenAppCri = Criteria.where(ClickReportConstant.CREATE_TIME).gte(dateDTO.getStartTime()).lt(dateDTO.getEndTime())
                        .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_FIRST_OPEN)
                        .and(ClickReportConstant.EXT_SHORTID).is(shortId);
                Long firstOpenAppCount = mongoTemplate.count(Query.query(firstOpenAppCri), MongoDbCollectonName.COLLECT_BEHAVIOR);
                log.info("查询短链首次打开APP次数完成, shortId: {}", shortId);

                //查询注册用户数
                Long registerCount = oracleUserOperatorLoginInfoDAO.countUserByShortId(dateDTO.getStartTime(), dateDTO.getEndTime(), shortId);
                log.info("查询短链注册用户数完成, shortId: {}", shortId);

                //查询用户下单数
                Criteria orderCri = Criteria.where(ClickReportConstant.CREATE_TIME)
                        .gte(dateDTO.getStartTime())
                        .lt(dateDTO.getEndTime())
                        .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_ORDER_SUBMIT_V2)
                        .and(ClickReportConstant.EXT_SHORTID).is(shortId);
                Long orderCount = mongoTemplate.count(Query.query(orderCri), MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER_NEW);
                log.info("查询短链用户下单数完成, shortId:{},数量:{}", shortId, orderCount);

                Query query = Query.query(Criteria.where(ReportShortLinkBO.DATA_TIME).gte(dateDTO.getStartTime()).lt(dateDTO.getEndTime())
                        .and(ReportShortLinkBO.SHORT_ID).is(shortId));
                ReportShortLinkBO report = mongoTemplate.findOne(query, ReportShortLinkBO.class, MongoDbCollectonName.REPORT_SHORT_LINK);
                if (report == null) {
                    report = new ReportShortLinkBO()
                            .setCreateTime(new Date());
                }
                report.setShortId(shortId)
                        .setPv(pv)
                        .setOpenAppCount(openAppCount)
                        .setFirstOpenAppCount(firstOpenAppCount)
                        .setRegisterCount(registerCount)
                        .setOrderCount(orderCount)
                        .setOpenAppRate(pv == 0 ? 0 : NumberUtil.div(openAppCount.doubleValue(), pv.doubleValue(), 2))
                        .setRegisterRate(firstOpenAppCount == 0 ? 0 : NumberUtil.div(registerCount.doubleValue(), firstOpenAppCount.doubleValue(), 2))
                        .setOrderRate(openAppCount == 0 ? 0 : NumberUtil.div(orderCount.doubleValue(), openAppCount.doubleValue(), 2))
                        .setDataTime(dateDTO.getDataTime())
                        .setUpdateTime(new Date());
                mongoTemplate.save(report);
            } catch (Exception e) {
                log.error("短链报表统计出现异常, shortId: {}", shortId, e);
            }
        });
    }

    /**
     * 查询短链pv
     */
    private Map<String, Long> queryShortLinkPv(ReportDateDTO dateDTO) {
        Map<String, Long> pvMap = new HashMap<>();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("startTime", dateDTO.getStartTime().getTime());
            paramMap.put("endTime", dateDTO.getEndTime().getTime());
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(paramMap, headers);

            //调短链服务查询pv
            String url = shortlinkBaseUrl + shortlinkReportUrl;
            JSONObject result = restTemplate.postForObject(url, httpEntity, JSONObject.class);
            if (result != null && "00000".equals(result.getStr("rspCd"))) {
                JSONObject data = result.getJSONObject("data");
                if (data != null) {
                    JSONArray list = data.getJSONArray("list");
                    if (list != null && !list.isEmpty()) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject obj = list.getJSONObject(i);
                            String shortId = obj.getStr("shortID");
                            Long pv = obj.getLong("pv");
                            if (StringUtils.isNotBlank(shortId) && pv != null) {
                                //pv数相加
                                pvMap.merge(shortId, pv, (a, b) -> b + a);
                            }
                        }
                    }
                }
            } else {
                log.info("短链pv查询结果: {}", result);
            }
        } catch (Exception e) {
            log.error("查询短链pv出现异常", e);
        }
        return pvMap;
    }

    @Override
    public PageInfoDTO<ShortLinkReportRespDTO> queryReport(ShortLinkReportQueryReqDTO reqDTO) throws PendingException {
        List<ShortLinkReportRespDTO> reportList = new ArrayList<>();

        Criteria criteria = new Criteria();
        if (reqDTO.getStartTime() != null && reqDTO.getEndTime() != null) {
            criteria.and(ReportShortLinkBO.DATA_TIME).gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime());
        }
        if (StringUtils.isNotBlank(reqDTO.getShortId())) {
            criteria.and(ReportShortLinkBO.SHORT_ID).is(reqDTO.getShortId());
        }

        Query query = Query.query(criteria);
        long total = mongoTemplate.count(query, MongoDbCollectonName.REPORT_SHORT_LINK);
        if (total > 0) {
            query.with(Sort.by(Sort.Direction.DESC, ReportShortLinkBO.DATA_TIME));
            query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
            query.limit(reqDTO.getPageSize());
            reportList = mongoTemplate.find(query, ShortLinkReportRespDTO.class, MongoDbCollectonName.REPORT_SHORT_LINK);
        }

        PageInfoDTO<ShortLinkReportRespDTO> page = new PageInfoDTO<>();
        page.setPageSize(reqDTO.getPageSize());
        page.setPageNum(reqDTO.getPageNum());
        page.setSize(reportList.size());
        page.setTotal(total);
        page.setPages(((int) total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        page.setHasNextPage(page.getPageNum() < page.getPages());
        page.setList(reportList);
        return page;
    }

}
