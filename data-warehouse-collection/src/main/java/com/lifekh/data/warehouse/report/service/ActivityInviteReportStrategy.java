package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.date.DateUtil;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.DataStatusEnum;
import com.lifekh.data.warehouse.bo.InviteRegisterButNotOpenAppBO;
import com.lifekh.data.warehouse.bo.InviteRegisterReportDayBO;
import com.lifekh.data.warehouse.bo.ReportActivityInviteBO;
import com.lifekh.data.warehouse.bo.ReportInviteNewUserBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class ActivityInviteReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO req) {
        try {
            this.staticInviteNew(req);
        } catch (Exception e) {
            log.error("统计实时拉新数据出现异常", e);
        }

        try {
            this.staticInviteActivity(req);
        } catch (Exception e) {
            log.error("统计邀请有礼数据出现异常", e);
        }

        return null;
    }

    /**
     * 统计实时拉新
     *
     * @param req
     */
    private void staticInviteNew(ReportBasicReqDTO req) {
        //进入注册页面数
        long registerPageCount = this.getEventSumCount(req, false, Arrays.asList(ClickReportConstant.REGISTER_PAGE_PV, ClickReportConstant.PREREGISTER_PAGE_PV));
        //点击立即使用次数
        long clickGouseCount = this.getEventSumCount(req, false, Arrays.asList(ClickReportConstant.REGISTER_PAGE_CLICK_GOUSE, ClickReportConstant.PREREGISTER_PAGE_CLICK_GOUSE));
        //进入下载页面数
        long downloadPageCount = this.getEventSumCount(req, false, Collections.singletonList(ClickReportConstant.DOWNLOAD_PAGE_PV));
        //点击下载按钮次数
        long clickDownloadCount = this.getEventSumCount(req, false, Collections.singletonList(ClickReportConstant.DOWNLOAD_PAGE_CLICK_DOWNLOAD));
        //注册成功用户数
        long registerCount = this.getRegisterCount(req, false);

        ReportInviteNewUserBO report = new ReportInviteNewUserBO()
                .setDataTime(req.getDataTime())
                .setRegisterPageCount(registerPageCount)
                .setClickGouseCount(clickGouseCount)
                .setDownloadPageCount(downloadPageCount)
                .setClickDownloadCount(clickDownloadCount)
                .setRegisterCount(registerCount)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setVersion(0);
        mongoTemplate.insert(report);
    }

    /**
     * 统计邀请有礼
     *
     * @param req
     */
    private void staticInviteActivity(ReportBasicReqDTO req) {
        //进入邀请页面数
        long invitePageCount = this.getEventSumCount(req, null, Collections.singletonList(ClickReportConstant.INVITE_PAGE_PV));
        //点击分享按钮数
        long clickShareCount = this.getEventSumCount(req, null, Arrays.asList(ClickReportConstant.INVITE_PAGE_CLICK_IMMEDIATELY_INVITED, ClickReportConstant.INVITE_PAGE_CLICK_SCANCODE));
        //进入注册页面数
        long registerPageCount = this.getEventSumCount(req, true, Arrays.asList(ClickReportConstant.REGISTER_PAGE_PV, ClickReportConstant.PREREGISTER_PAGE_PV));
        //点击领取优惠数
        long clickGetCount = this.getEventSumCount(req, true, Arrays.asList(ClickReportConstant.REGISTER_PAGE_CLICK_GET, ClickReportConstant.PREREGISTER_PAGE_CLICK_GET));
        //进入下载页面数
        long downloadPageCount = this.getEventSumCount(req, true, Collections.singletonList(ClickReportConstant.DOWNLOAD_PAGE_PV));
        //注册成功用户数
        long registerCount = this.getRegisterCount(req, true);

        ReportActivityInviteBO report = new ReportActivityInviteBO()
                .setDataTime(req.getDataTime())
                .setInvitePageCount(invitePageCount)
                .setClickShareCount(clickShareCount)
                .setRegisterPageCount(registerPageCount)
                .setClickGetCount(clickGetCount)
                .setDownloadPageCount(downloadPageCount)
                .setRegisterCount(registerCount)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setVersion(0);
        mongoTemplate.insert(report);
    }

    /**
     * 获取事件数量
     *
     * @param req
     * @param isActivity
     * @param events
     * @return
     */
    private long getEventSumCount(ReportBasicReqDTO req, Boolean isActivity, List<String> events) {
        Criteria criteria = Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                .gte(req.getBeginTime())
                .lte(req.getEndTime())
                .and(InviteRegisterReportDayBO.EVEN).in(events);

        if (isActivity != null) {
            if (isActivity) {
                criteria.and(InviteRegisterReportDayBO.ACTIVITYNO).ne(null);
            } else {
                criteria.and(InviteRegisterReportDayBO.ACTIVITYNO).is(null);
            }
        }

        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group().sum(InviteRegisterReportDayBO.COUNT).as(InviteRegisterReportDayBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<InviteRegisterReportDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY, InviteRegisterReportDayBO.class);
        if (CollectionUtils.isNotEmpty(results.getMappedResults())) {
            return results.getMappedResults().get(0).getCount();
        } else {
            return 0;
        }
    }

    /**
     * 查询注册成功用户数
     *
     * @param req
     * @param isActivity
     * @return
     */
    private long getRegisterCount(ReportBasicReqDTO req, boolean isActivity) {
        String yesterday = DateUtil.formatDate(req.getDataTime());

        //查询当天更新，并且完成用户转化的记录
        Date startTime = DateUtil.beginOfDay(new Date());
        Criteria criteria = Criteria.where(InviteRegisterButNotOpenAppBO.UPDATE_TIME).gte(startTime)
                .and(InviteRegisterButNotOpenAppBO.STATUS).is(DataStatusEnum.DELETE.getCode());

        if (isActivity) {
            criteria.and(InviteRegisterButNotOpenAppBO.ACTIVITY_NO).ne(null);
        } else {
            criteria.and(InviteRegisterButNotOpenAppBO.ACTIVITY_NO).is(null);
        }

        //按天统计用户数
        Map<String, Long> countMap = new HashMap<>();
        List<InviteRegisterButNotOpenAppBO> datas = mongoTemplate.find(Query.query(criteria), InviteRegisterButNotOpenAppBO.class, MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP);
        datas.forEach(d -> {
            if (d.getActualRegisterTime() != null) {
                String dataDate = DateUtil.formatDate(d.getActualRegisterTime());
                Long count = countMap.get(dataDate);
                countMap.put(dataDate, count == null ? 1 : count + 1);
            }
        });

        //更新历史的用户注册数统计
        countMap.forEach((dataDate, count) -> {
            if (!dataDate.equals(yesterday)) {
                Date date = DateUtil.parseDate(dataDate);
                Date sTime = DateUtil.beginOfDay(date);
                Date eTime = DateUtil.endOfDay(date);
                Query query = Query.query(Criteria.where("dataTime").gte(sTime).lte(eTime));
                if (isActivity) {
                    //邀请有礼
                    ReportActivityInviteBO record = mongoTemplate.findOne(query, ReportActivityInviteBO.class);
                    if (record != null) {
                        record.setRegisterCount(record.getRegisterCount() + count)
                                .setUpdateTime(new Date())
                                .setVersion(record.getVersion() == null ? 1 : record.getVersion() + 1);
                        mongoTemplate.save(record);
                    }
                } else {
                    //实时拉新
                    ReportInviteNewUserBO record = mongoTemplate.findOne(query, ReportInviteNewUserBO.class);
                    if (record != null) {
                        record.setRegisterCount(record.getRegisterCount() + count)
                                .setUpdateTime(new Date())
                                .setVersion(record.getVersion() == null ? 1 : record.getVersion() + 1);
                        mongoTemplate.save(record);
                    }
                }
            }
        });

        //返回昨天的用户注册数
        Long yesterdayCount = countMap.get(yesterday);
        return yesterdayCount == null ? 0 : yesterdayCount;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
