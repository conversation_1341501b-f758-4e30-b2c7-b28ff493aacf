package com.lifekh.data.warehouse.report.service;
import java.util.Date;

import com.khsuper.takeaway.merchant.api.dto.advertising.AdvertisingRespDTO;
import com.khsuper.takeaway.merchant.api.facade.AdvertisingFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.EntranceType;
import com.lifekh.data.warehouse.bo.ReportTakeawayBannerFlowConversionBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.CommonCountDTO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TakeawayBannerFlowConversionReportStrategy extends AbstractTakeawayFlowConversionReportStrategy<ReportTakeawayBannerFlowConversionBO> implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AdvertisingFacade advertisingFacade;


    @Override
    String getMongoDbCollectionName() {
        return MongoDbCollectonName.REPORT_TAKEAWAY_BANNER_FLOW_CONVERSION;
    }

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ReportTakeawayBannerFlowConversionBO> statExposureCount(Date startTime, Date endTime) {
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and("eventBo.event").is("browseADS")
                .and("ext.type").is("ADS");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.plateId",
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("pv")
                        .first("ext.plateId").as("bannerId")
                        .first(ClickReportConstant.CREATE_TIME).as("dataTime")
                        .first(ClickReportConstant.LANGUAGE).as("language")
                        .first(ClickReportConstant.PROVINCE_NAME_EN).as("provinceName")
                        .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as("deviceType")
                        .first(ClickReportConstant.DEVICEINFOBO_APP_VERSION).as("appVersion"),
                Aggregation.sort(Sort.Direction.DESC, "pv"),
                Aggregation.limit(50)
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportTakeawayBannerFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_OTHER_V2, ReportTakeawayBannerFlowConversionBO.class).getMappedResults();
//        List<ReportTakeawayBannerFlowConversionBO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_other", ReportTakeawayBannerFlowConversionBO.class).getMappedResults();
        if (!mappedResults.isEmpty()) {
            List<Long> bannerList = mappedResults.stream().map(r->Long.parseLong(r.getBannerId())).collect(Collectors.toList());
            if (!bannerList.isEmpty()) {
                Map<Long, String> advertisingMap = advertisingFacade.findByIds(bannerList).stream().collect(Collectors.toMap(AdvertisingRespDTO::getId, AdvertisingRespDTO::getTitle));
                for (ReportTakeawayBannerFlowConversionBO mappedResult : mappedResults) {
                    mappedResult.setBusinessName(advertisingMap.get(Long.parseLong(mappedResult.getBannerId())));
                    mappedResult.setLanguage(StringUtil.isEmpty(mappedResult.getLanguage()) ? "unknown" : mappedResult.getLanguage() );
                    mappedResult.setProvinceName(StringUtil.isEmpty(mappedResult.getProvinceName()) ? "unknown" : mappedResult.getProvinceName() );
                }
            }
        }
        return mappedResults;
    }

    /**
     * 统计点击数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    @Override
    public void statClickCount(Date startTime, Date endTime, List<ReportTakeawayBannerFlowConversionBO> conversionBOList) {
        //点击数量/曝光数量  collect_buried_point_takeaway_view_v2 event=clickBtn ext.clickType =banner
//        List<String> bannerList = conversionBOList.stream().map(ReportTakeawayBannerFlowConversionBO::getBannerId).collect(Collectors.toList());
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(startTime).lte(endTime)
               .and(ClickReportConstant.EVENT).is(ClickReportConstant.CLICK_BTN)
                .and("ext.clickType").is("banner");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("ext.bannerId",
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.bannerId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());

        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_OTHER_V2, CommonCountDTO.class).getMappedResults();
//        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(aggregation, "collect_buried_point_takeaway_other", CommonCountDTO.class).getMappedResults();
        Map<String, Integer> map = mappedResults.stream().filter(c -> c.getGroupKey() != null).collect(Collectors.toMap(CommonCountDTO::getGroupKey, CommonCountDTO::getCount, (v1, v2) -> v1));
        conversionBOList.forEach(r -> {
            Integer count = map.get(r.getGroupKey());
            r.setClickCount(count == null ? 0 : count);
        });
    }

    @Override
    public Aggregation getEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayBannerFlowConversionBO> conversionBOList) {
        List<String> bannerList = conversionBOList.stream().map(ReportTakeawayBannerFlowConversionBO::getBannerId).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.BANNER.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(bannerList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                        ClickReportConstant.LANGUAGE,
                        ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                        ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                        ClickReportConstant.PROVINCE_NAME_EN).count().as("count"),
                Aggregation.project("count")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")

        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<ReportTakeawayBannerFlowConversionBO> conversionBOList) {
        List<String> bannerList = conversionBOList.stream().map(ReportTakeawayBannerFlowConversionBO::getBannerId).distinct().collect(Collectors.toList());
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .and(ClickReportConstant.ENTRANCE_TYPE).is(EntranceType.BANNER.getCode())
                .and(ClickReportConstant.ENTRANCE_ID).in(bannerList);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.ENTRANCE_ID,
                                ClickReportConstant.LANGUAGE,
                                ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE,
                                ClickReportConstant.DEVICEINFOBO_APP_VERSION,
                                ClickReportConstant.PROVINCE_NAME_EN).count().as("count")
                        .push("ext.orderNo").as("orderList"),
                Aggregation.project("count",  "orderList")
                        .and(buildGroupKeyExpression("$_id.entranceId"))
                        .as("groupKey")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return aggregation;
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
