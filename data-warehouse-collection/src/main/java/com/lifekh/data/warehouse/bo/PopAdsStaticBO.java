package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.POP_ADS_STATIC)
@Data
@Accessors(chain = true)
public class PopAdsStaticBO implements Serializable {
    private static final long serialVersionUID = 3236198894293965257L;

    @Id
    private String id;

    /**
     * 数据时间
     */
    private Date dataTime;

    private String popNo;
    private String popName;
    private String zhImgUrl;
    private Long count;

    private Date createTime;
}
