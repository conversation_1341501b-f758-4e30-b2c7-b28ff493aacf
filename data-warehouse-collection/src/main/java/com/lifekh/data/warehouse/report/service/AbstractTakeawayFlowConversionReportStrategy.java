package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.date.DateUtil;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.ReportTakeawayFlowConversionBO;
import com.lifekh.data.warehouse.dto.CommonCountDTO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public abstract class AbstractTakeawayFlowConversionReportStrategy<T extends ReportTakeawayFlowConversionBO> extends AbstractReportBasic implements ReportStrategy {

    @Resource
    private MongoTemplate mongoTemplate;


    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        Date startTime = reportBasicReqDTO.getBeginTime();
        Date endTime = reportBasicReqDTO.getEndTime();

        Criteria criteria = Criteria.where("dataTime").gte(startTime).lte(endTime).and("language").exists(true);
        long count = mongoTemplate.count(new Query(criteria), getMongoDbCollectionName());
        if (count > 0) {
            log.info("外卖首页流量转化任务已被执行");
            return null;
        }
        List<T> conversionBOList = statExposureCount(startTime, endTime);
        Date dataTime = DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12));
        for (T t : conversionBOList) {
            t.setDataTime(dataTime);
        }
        statClickCount(startTime, endTime, conversionBOList);
        statInStoreCount(startTime, endTime, conversionBOList);
        statSubmitCount(startTime, endTime, conversionBOList);
        statOrderCount(startTime, endTime, conversionBOList);
        statPayCount(startTime, endTime, conversionBOList);
        mongoTemplate.insertAll(conversionBOList);
        return null;
    }

    abstract String getMongoDbCollectionName();

    /**
     * 统计曝光量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    abstract List<T> statExposureCount(Date startTime, Date endTime);

    /**
     * 统计点击数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    abstract void statClickCount(Date startTime, Date endTime, List<T> conversionBOList);


    /**
     * 获取入口统计的聚合
     *
     * @param startTime
     * @param endTime
     * @return
     */
    abstract Aggregation getEntranceAggregation(Date startTime, Date endTime, List<T> conversionBOList);

    public AggregationExpression buildGroupKeyExpression(String keyExpress) {
        return (AggregationOperationContext context)
                -> StringOperators.Concat.valueOf(ConvertOperators.ToString.toString(keyExpress))
                .concat("_")
                .concat("$_id.language")
                .concat("_")
                .concat("$_id.deviceType")
                .concat("_")
                .concat("$_id.appVersion")
                .concat("_")
                .concatValueOf(ConditionalOperators.ifNull("$_id.provinceName")
                                .then("unknown")).toDocument(context);
    }

    /**
     * 获取下单入口统计的聚合
     *
     * @param startTime
     * @param endTime
     * @return
     */
    abstract Aggregation getOrderEntranceAggregation(Date startTime, Date endTime, List<T> conversionBOList);

    /**
     * 统计进店数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    private void statInStoreCount(Date startTime, Date endTime, List<T> conversionBOList) {
        //进店数量   collect_buried_point_takeaway_store_detail_pv  汇总统计
        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(getEntranceAggregation(startTime, endTime, conversionBOList), MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_STORE_DETAIL_PV, CommonCountDTO.class).getMappedResults();
        Map<String, Integer> map = mappedResults.stream().filter(c -> c.getGroupKey() != null).collect(Collectors.toMap(CommonCountDTO::getGroupKey, CommonCountDTO::getCount, (v1, v2) -> v1));
        conversionBOList.forEach(r -> {
            Integer count = map.get(r.getGroupKey());
            r.setIntoStoreCount(count == null ? 0 : count);
        });
    }

    /**
     * 统计结算数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    private void statSubmitCount(Date startTime, Date endTime, List<T> conversionBOList) {
        //结算数量 collect_buried_point_takeaway_order_submit_pv 汇总统计  按入口统计
        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(getEntranceAggregation(startTime, endTime, conversionBOList), "collect_buried_point_takeaway_order_submit_pv", CommonCountDTO.class).getMappedResults();
        Map<String, Integer> map = mappedResults.stream().filter(c -> c.getGroupKey() != null).collect(Collectors.toMap(CommonCountDTO::getGroupKey, CommonCountDTO::getCount, (v1, v2) -> v1));
        conversionBOList.forEach(r -> {
            Integer count = map.get(r.getGroupKey());
            r.setSubmitCount(count == null ? 0 : count);
        });

    }
    /**
     * 统计下单数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    private void statOrderCount(Date startTime, Date endTime, List<T> conversionBOList) {
        //下单数量 collect_buried_point_takeaway_order_submit 汇总统计
        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(getOrderEntranceAggregation(startTime, endTime, conversionBOList), "collect_buried_point_takeaway_order_submit", CommonCountDTO.class).getMappedResults();
        Map<String, CommonCountDTO> map = mappedResults.stream().filter(c -> c.getGroupKey() != null).collect(Collectors.toMap(CommonCountDTO::getGroupKey, Function.identity(),  (v1, v2) -> v1));
        conversionBOList.forEach(r -> {
            CommonCountDTO countDTO = map.get(r.getGroupKey());
            if (countDTO != null) {
                r.setOrderCount(countDTO.getCount() == null ? 0 : countDTO.getCount());
                r.setOrderList(countDTO.getOrderList());
            } else {
                r.setPayCount(0);
            }
        });
    }



    /**
     * 统计支付数量
     *
     * @param startTime
     * @param endTime
     * @param conversionBOList
     */
    private void statPayCount(Date startTime, Date endTime, List<T> conversionBOList) {
        //collect_buried_point_takeaway_pay_success_pv 汇总统计
//        List<CommonCountDTO> mappedResults = mongoTemplate.aggregate(getEntranceAggregation(startTime, endTime, conversionBOList), "collect_buried_point_takeaway_pay_success_pv", CommonCountDTO.class).getMappedResults();
//        Map<String, Integer> map = mappedResults.stream().collect(Collectors.toMap(CommonCountDTO::getGroupKey, CommonCountDTO::getCount));
//        conversionBOList.forEach(r -> {
//            Integer count = map.get(r.getGroupKey());
//            r.setPayCount(count == null ? 0 : count);
//        });

        for (T t : conversionBOList) {
            if (t.getOrderList() != null && t.getOrderList().size() > 0) {
                Criteria criteria = Criteria.where("AGGREGATE_ORDER_NO").in(t.getOrderList()).and("AGGREGATE_ORDER_FINAL_STATE").ne(13);
                long count = mongoTemplate.count(new Query(criteria), MongoDbCollectonName.AGGREGATE_ORDER);
                t.setPayCount((int) count);
            } else {
                t.setPayCount(0);
            }
            //清空字段
            t.setOrderList(null);
        }

    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
