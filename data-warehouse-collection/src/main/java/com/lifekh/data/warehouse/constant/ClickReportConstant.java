package com.lifekh.data.warehouse.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ClickReportConstant {

    String CREATE_TIME = "createTime";

    String RECORD_TIME = "recordTime";

    String BUSINESS_LINE = "businessLine";

    String EVENT = "eventBo.event";

    String PARENT_PAGE = "parentPage";

    String CURRENT_PAGE = "currentPage";

    String CURRENT_AREA = "currentArea";

    String BUSINESS_NAME = "businessName";

    String ADS_SUBJECT = "adsSubject";

    String FROM = "from";

    String EXT_PHONE = "ext.phone";

    String PHONE = "phone";

    String EXT_ACTIVITYNO = "ext.activityNo";

    String ACTIVITYNO = "activityNo";

    String FULL_BUSINESS_NAME = "fullBusinessName";

    String USERINFOBO_OPERATOR_NO = "userInfoBo.operatorNo";

    String USERINFOBO_LOGIN_NAME = "userInfoBo.loginName";

    String LOGIN_NAME = "loginName";

    String DEVICEINFOBO_DEVICE_ID = "deviceInfoBo.deviceId";

    String EXT_ORDERNO = "ext.orderNo";

    String DEVICEINFOBO_DEVICE_TYPE = "deviceInfoBo.deviceType";

    String DEVICEINFOBO_APP_VERSION = "deviceInfoBo.appVersion";

    String DEVICE_ID = "deviceId";

    String OPERATOR_NO = "operatorNo";

    String USER_NUM = "userNum";

    String LANGUAGE = "userInfoBo.language";

    String LOCATION_ZH = "locationBo.provinceNameZh";

    String PROVINCE_NAME_EN = "locationBo.provinceName";

    String LOCATIONBO_COORDINATES = "locationBo.coordinates";

    String AREA_NAME_EN = "locationBo.areaName";

    String PROVINCE_NAME = "provinceName";

    String AREA_NAME = "areaName";

    String SIMPLE_LANGUAGE = "language";

    String CLICK = "@click";

    String TOTAL = "total";

    String EXT_CHANNEL = "ext.channel";

    String EXT_SHORTID = "ext.shortID";

    String CHANNEL = "channel";

    String EXT_ROUTE = "ext.route";


    String ENTRANCE_TYPE = "entrance.entranceType";

    String ENTRANCE_NAME = "entrance.entranceName";

    String ENTRANCE_ID = "entrance.entranceId";

    String CLICK_BTN = "clickBtn";


    String WOWNOW_CLICK_TOTAL = "WOWNOW首页(总)";

    String WOWNOW_CLICK_MINE = "WOWNOW我的页面(总)";

    String[] WOWNOW_HOME_PAGE_LIST = new String[]{"WOWNOW首页", "WOWNOW首页O2O", "WOWNOW首页3.0", "WOWNOW首页4.0"};

    List<String> WOWNOW_HOME_PAGE_NAME_LIST = Arrays.asList("WNHomeViewController","HomeNewFragment");

    String WOWNOW_HOME3 = "WOWNOW首页3.0";

    String[] WOWNOW_MINE_PAGE_LIST = new String[]{"WOWNOW_我的","我的","Mine","របស់ខ្ញុំ","WOWNOW_MINE"};

    String ADD_SHOP_CART_EVEN = "add_shopcart";

    String ORDER_SUBMIT_EVEN = "order_submit";

    List<String> REGISTER_PAGE_EVENT_LIST = Arrays.asList(
            //注册页
            "register_page_pv","register_page_click_get","register_page_click_sendSMS","register_page_click_gouse","register_page_register_result",
            //预注册页
            "preRegister_page_pv","preRegister_page_click_get","preRegister_page_click_gouse","preRegister_page_register_result",
            //邀请页
            "invite_page_pv","invite_page_click_immediately_invited","invite_page_click_scanCode","invite_page_share_result",
            //下载页
            "download_page_pv","download_page_click_download");

    //发现页分享
    List<String> DISCOVERY_SHARE_EVENTS = Arrays.asList("discover_page_share_result", "discover_page_click_share");

    String SESSION_START_EVEN = "@login";

    String FIRST_OPEN_EVEN = "firstOpen";

    Map<String, String> REGISTER_PAGE_REGISTER_RESFULT_EVEN = new HashMap<String, String>(){{
        put("register_page_register_result", "注册页面注册成功");
    }};

    String REGISTER = "register";

    String PREREGISTER = "preRegister";

    Map<String, String> PREREGISTER_PAGE_REGISTER_RESULT_EVEN = new HashMap<String, String>(){{
        put("preRegister_page_register_result", "预注册页面手机号码提交成功");
    }};

    Map<String, String> REGISTER_PAGE_REGISTER_SUCCESS = new HashMap<String, String>(){{
        put("preRegister_success", "预注册页面注册成功");
        put("register_success", "注册页面注册成功");
    }};

    Map<String, String> OPEN_APP_SUCCESS = new HashMap<String, String>(){{
        put("register_openApp_success", "注册页面打开APP");
        put("preRegister_openApp_success", "预注册页面打开APP");
    }};

    String PRE_REGISTER_SUCCESS = "preRegister_success";
    String REGISTER_SUCCESS = "register_success";

    String PRE_REGISTER_OPENAPP = "preRegister_openapp";
    String REGISTER_OPENAPP = "register_openapp";

    Map<String, String> ADD_CART_AND_ORDER = new HashMap<String, String>(){{
        put("外卖_购物车_加购_外卖_英", "首页外卖加购_英");
        put("外卖_购物车_加购_外卖_中", "首页外卖加购_中");
        put("外卖_购物车_加购_外卖_柬", "首页外卖加购_柬");
        put("外卖_下单_外卖_英", YUNNOW_ORDER_EN);
        put("外卖_下单_外卖_中", YUNNOW_ORDER_ZH);
        put("外卖_下单_外卖_柬", YUNNOW_ORDER_KM);
        put("电商_购物车_加购_电商_英", "首页电商加购_英");
        put("电商_购物车_加购_电商_中", "首页电商加购_中");
        put("电商_购物车_加购_电商_柬", "首页电商加购_柬");
        put("电商_下单_电商_英", TINNOW_ORDER_EN);
        put("电商_下单_电商_中", TINNOW_ORDER_ZH);
        put("电商_下单_电商_柬", TINNOW_ORDER_KM);
    }};

    String YUNNOW_ORDER = "首页外卖下单_";

    String YUNNOW_ORDER_ZH = "首页外卖下单_英";

    String YUNNOW_ORDER_EN = "首页外卖下单_中";

    String YUNNOW_ORDER_KM = "首页外卖下单_柬";

    String TINHNOW_ORDER = "首页电商下单_";

    String TINNOW_ORDER_ZH = "首页电商下单_中";

    String TINNOW_ORDER_EN = "首页电商下单_英";

    String TINNOW_ORDER_KM = "首页电商下单_柬";

    String HOME_PAGE_ORDER = "^首页.*下单_.*";

    String REGISTER_PAGE_PV = "register_page_pv";
    String PREREGISTER_PAGE_PV = "preRegister_page_pv";
    String REGISTER_PAGE_CLICK_GOUSE = "register_page_click_gouse";
    String PREREGISTER_PAGE_CLICK_GOUSE = "preRegister_page_click_gouse";
    String DOWNLOAD_PAGE_PV = "download_page_pv";
    String DOWNLOAD_PAGE_CLICK_DOWNLOAD = "download_page_click_download";
    String INVITE_PAGE_PV = "invite_page_pv";
    String INVITE_PAGE_CLICK_IMMEDIATELY_INVITED = "invite_page_click_immediately_invited";
    String INVITE_PAGE_CLICK_SCANCODE = "invite_page_click_scanCode";
    String REGISTER_PAGE_CLICK_GET = "register_page_click_get";
    String PREREGISTER_PAGE_CLICK_GET = "preRegister_page_click_get";

    /**
     * banner点击事件链接
     */
    String BANNER_CLICK_ROUTE = "https://h5.lifekh.com/mobile-h5/common-cms-page?pageLabel=H5Lifeluodiye";

    String HOME_LOGIN_GUIDE_BUSINESS = "click_newUserMarketingPlugin_button";

    /**
     * 首页点击相关业务名，注：WOWNOW首页放在首位
     */
    List<String> HOME_CLICK_BUSINESS_LIST = Arrays.asList("WOWNOW首页", "外卖", "海外购", "批发", "一元购", "话费", "团购", "游戏", "酒店", "钱包", "会员", "优惠券");

    /**
     * 分割符
     */
    String SPLIT_KEY = ":";
}
