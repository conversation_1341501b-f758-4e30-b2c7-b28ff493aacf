package com.lifekh.data.warehouse.report;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.*;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@ElasticJobConf(name = "takeaway_flow_conversion_report", cron = "0 15 * * * ? ", description = "定时统计外卖流量转化报表",shardingTotalCount = 2)
public class TakeawayFlowConversionReportJob extends AbstractSimpleJob {

    @Autowired
    private TakeawayBannerFlowConversionReportStrategy takeawayBannerFlowConversionReportStrategy;

    @Autowired
    private TakeawayEotFlowConversionReportStrategy takeawayEotFlowConversionReportStrategy;

    @Autowired
    private TakeawayKingkongFlowConversionReportStrategy takeawayKingkongFlowConversionReportStrategy;

    @Autowired
    private TakeawayWaterfallFlowConversionReportStrategy takeawayWaterfallFlowConversionReportStrategy;

    @Autowired
    private TakeawayAdFlowConversionReportStrategy takeawayAdFlowConversionReportStrategy;

    @Autowired
    private TakeawayThemeFlowConversionReportStrategy takeawayThemeFlowConversionReportStrategy;


    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {

        log.info("外卖首页流量转化统计开始");
        ExecutorService executor = Executors.newFixedThreadPool(6); // 创建线程池（建议使用注入的线程池）
        // 创建四个异步任务
        CompletableFuture<Void>[] futures = new CompletableFuture[]{
                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（Banner）");
                    takeawayBannerFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（Banner）出现异常", ex);
                    return null;
                }),

                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（Eot）");
                    takeawayEotFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（Eot）出现异常", ex);
                    return null;
                }),

                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（Kingkong）");
                    takeawayKingkongFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（Kingkong）出现异常", ex);
                    return null;
                }),

                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（Waterfall）");
                    takeawayWaterfallFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（Waterfall）出现异常", ex);
                    return null;
                }),

                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（AD）");
                    takeawayAdFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（AD）出现异常", ex);
                    return null;
                }),

                CompletableFuture.runAsync(() -> {
                    log.info("开始统计外卖首页流量转化（Theme）");
                    takeawayThemeFlowConversionReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
                }, executor).exceptionally(ex -> {
                    log.error("统计外卖首页流量转化（Theme）出现异常", ex);
                    return null;
                })

        };

        // 等待所有任务完成并输出结束日志
        CompletableFuture.allOf(futures).thenRun(() -> {
            log.info("外卖首页流量转化统计结束");
            executor.shutdown(); // 关闭线程池（需确保所有任务完成）
        }).join();
    }
}
