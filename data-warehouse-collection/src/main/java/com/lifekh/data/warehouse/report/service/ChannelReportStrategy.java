package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.collection.ListUtil;
import com.chaos.common.enums.AppIdEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.InviteRegisterReportDayBO;
import com.lifekh.data.warehouse.bo.ReportChannelDayBO;
import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.enums.ReportChannelDataTypeEnum;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Date: 2022/4/20
 * @Description: 按渠道 + 每日的维度统计
 */
@Component
@Slf4j
public class ChannelReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO req) {
        try {
            this.staticChannelNewUserData(req);
        } catch (Exception e) {
            log.error("统计渠道每日新增用户出现异常", e);
        }

        try {
            this.staticChannelInviteData(req);
        } catch (Exception e) {
            log.error("统计每日渠道邀请数据（邀请有礼）出现异常", e);
        }

        try {
            this.staticChannelOrderData(req);
        } catch (Exception e) {
            log.error("统计每日渠道下单用户数出现异常", e);
        }

        return null;
    }

    /**
     * 统计渠道每日新增用户
     *
     * @param req
     */
    private void staticChannelNewUserData(ReportBasicReqDTO req) {
        Criteria criteria = Criteria.where("CREATE_TIME")
                .gte(req.getBeginTime())
                .lte(req.getEndTime())
                .and("APP_ID").is(AppIdEnum.SUPER_APP.getCode())
                .and("REGISTER_CHANNEL").ne(null);

        List<ReportChannelDayBO> results = this.queryChannelUserCount(criteria);

        List<ReportChannelDayBO> datas = new ArrayList<>();
        results.forEach(r ->
                datas.add(new ReportChannelDayBO()
                        .setChannel(r.getChannel())
                        .setCount(r.getCount())
                        .setDataTime(req.getDataTime())
                        .setDataType(ReportChannelDataTypeEnum.NEW_USER_COUNT.getCode())
                        .setDataTypeName(ReportChannelDataTypeEnum.NEW_USER_COUNT.getMessage())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date()))
        );

        if (!datas.isEmpty()) {
            mongoTemplate.insertAll(datas);
        }
    }

    /**
     * 统计每日渠道邀请数据（邀请有礼）
     *
     * @param req
     * @return
     */
    private void staticChannelInviteData(ReportBasicReqDTO req) {
        Criteria criteria = Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                .gte(req.getBeginTime())
                .lte(req.getEndTime())
                .and(InviteRegisterReportDayBO.EVEN).in(Arrays.asList(ClickReportConstant.INVITE_PAGE_CLICK_IMMEDIATELY_INVITED, ClickReportConstant.INVITE_PAGE_CLICK_SCANCODE))
                .and(InviteRegisterReportDayBO.ACTIVITYNO).ne(null); //查询活动事件
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(InviteRegisterReportDayBO.CHANNEL)
                        .first(InviteRegisterReportDayBO.CHANNEL).as(InviteRegisterReportDayBO.CHANNEL)
                        .sum(InviteRegisterReportDayBO.COUNT).as(InviteRegisterReportDayBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<InviteRegisterReportDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY, InviteRegisterReportDayBO.class);

        List<ReportChannelDayBO> reports = new ArrayList<>();
        results.getMappedResults().forEach(r -> {
            if (StringUtils.isNotBlank(r.getChannel())) {
                ReportChannelDayBO report = new ReportChannelDayBO()
                        .setDataTime(req.getDataTime())
                        .setDataType(ReportChannelDataTypeEnum.INVITE_REGISTER_ACTIVITY_COUNT.getCode())
                        .setDataTypeName(ReportChannelDataTypeEnum.INVITE_REGISTER_ACTIVITY_COUNT.getMessage())
                        .setChannel(r.getChannel())
                        .setCount(r.getCount())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                reports.add(report);
            }
        });

        if (!reports.isEmpty()) {
            mongoTemplate.insertAll(reports);
        }
    }

    /**
     * 统计每日渠道下单用户数
     *
     * @param req
     */
    private void staticChannelOrderData(ReportBasicReqDTO req) {
        //查询当天下单的用户id
        Criteria criteria = new Criteria();
        criteria.and("ORDER_TIME").gte(req.getBeginTime()).lte(req.getEndTime());
        criteria.and("AGGREGATE_ORDER_FINAL_STATE").is(11);
        AggregationOptions options = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("USER_ID")
                        .first("USER_ID").as("USER_ID")
                        .count().as("orderCount"))
                .withOptions(options);
        AggregationResults<AggregateOrderBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.AGGREGATE_ORDER, AggregateOrderBO.class);
        List<String> userIds = results.getMappedResults().stream().map(AggregateOrderBO::getUserId).collect(Collectors.toList());

        //查询用户所属的渠道
        if (!userIds.isEmpty()) {
            List<ReportChannelDayBO> allResults = new ArrayList<>();
            //分批查询
            List<List<String>> splitUserIds = ListUtil.split(userIds, 500);
            for (List<String> uids : splitUserIds) {
                Criteria userCriteria = Criteria.where("OPERATOR_NO").in(uids)
                        .and("APP_ID").is(AppIdEnum.SUPER_APP.getCode())
                        .and("REGISTER_CHANNEL").ne(null);
                List<ReportChannelDayBO> rs = this.queryChannelUserCount(userCriteria);
                //汇总查询结果
                if (!rs.isEmpty()) {
                    allResults.addAll(rs);
                }
            }

            //根据渠道分组
            allResults.stream().collect(Collectors.groupingBy(ReportChannelDayBO::getChannel))
                    .forEach((channel, rs) -> {
                        long userCount = rs.stream().mapToLong(ReportChannelDayBO::getCount).sum();

                        ReportChannelDayBO report = new ReportChannelDayBO()
                                .setDataTime(req.getDataTime())
                                .setDataType(ReportChannelDataTypeEnum.ORDER_USER_COUNT.getCode())
                                .setDataTypeName(ReportChannelDataTypeEnum.ORDER_USER_COUNT.getMessage())
                                .setChannel(channel)
                                .setCount(userCount)
                                .setCreateTime(new Date())
                                .setUpdateTime(new Date());
                        mongoTemplate.insert(report);
                    });
        }
    }

    /**
     * 查询每个渠道的用户数
     *
     * @param criteria
     * @return
     */
    private List<ReportChannelDayBO> queryChannelUserCount(Criteria criteria) {
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("REGISTER_CHANNEL")
                        .first("REGISTER_CHANNEL").as(ReportChannelDayBO.CHANNEL)
                        .count().as(ReportChannelDayBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<ReportChannelDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.USER_LABEL, ReportChannelDayBO.class);
        return results.getMappedResults().stream().filter(r -> StringUtils.isNotBlank(r.getChannel())).collect(Collectors.toList());
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
