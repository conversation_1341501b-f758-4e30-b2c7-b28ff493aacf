package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.outstanding.framework.core.PendingException;

public interface PageViewReportService {

    public void countThePVofTheCurrentPage() throws PendingException;

    /**
     * 页面浏览统计
     */
    public void pageViewStatistics();

    /**
     * 首页流量统计
     */
    public void homePageStatistics();

    /**
     * 首页流量转化统计
     */
    public void homeFlowConversionStatistics();

    /**
     * 保存浏览埋点至临时表
     */
    public void saveViewEventRecord(CollectSpmBO collectSpm);

    /**
     * 删除浏览临时表数据
     */
    public void deleteViewEventRecord();

    /**
     * 新设备首页转化率
     */
    void newDeviceConversionRate();
}
