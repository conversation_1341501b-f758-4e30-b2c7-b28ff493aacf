package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Document(collection = MongoDbCollectonName.REPORT_TAKEAWAY_THEME_FLOW_CONVERSION)
@Data
@Accessors(chain = true)
public class ReportTakeawayThemeFlowConversionBO extends ReportTakeawayFlowConversionBO implements Serializable {

    private static final long serialVersionUID = 6192353663647048926L;

    private String themeNo;

    private String themeType;

    public String getGroupKey() {
        return getThemeNo() + "_" + getLanguage() + "_" + getDeviceType() + "_" + getAppVersion() + "_" + getProvinceName();
    }

}
