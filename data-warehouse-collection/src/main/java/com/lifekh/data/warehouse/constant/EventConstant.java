package com.lifekh.data.warehouse.constant;

import java.util.Arrays;
import java.util.List;

public interface EventConstant {

    /**
     * 事件编码：首次打开
     */
    String EVENT_NO_FIRST_OPEN = "firstOpen";

    /**
     * 事件编码：打开APP
     */
    String EVENT_NO_SESSION_START = "@sessionStart";

    /**
     * 事件编码：关闭APP
     */
    String EVENT_NO_SESSION_END = "@sessionEnd";

    /**
     * 事件编码：活跃用户
     */
    String EVENT_NO_ACTIVE_USER = "active_user";

    /**
     * 发现页文章浏览(点击)数事件
     */
    String EVENT_DISCOVRY_DETAILS_CLICK = "discovry_details_click";

    /**
     * 好物点击数事件
     */
    String EVENT_DISCOVRY_GOODS_CLICK = "discovry_goods_click";

    /**
     * 加购数事件
     */
    String EVENT_ADD_SHOPCART = "add_shopcart";

    /**
     * 好物下单
     */
    String EVENT_ORDER_SUBMIT = "order_submit";

    String EVENT_ORDER_SUBMIT_V2 = "order_submitV2";


    /**
     * 发现页分享
     */
    String EVENT_DISCOVER_PAGE_CLICK_SHARE = "discover_page_click_share";

    /**
     * 排除保存到汇总表的事件
     */
    List<String> EXCLUDE_SAVE_TO_ALL_COLLECT = Arrays.asList("@DEBUG", "active_user", "diff_device_login");

    /**
     * 登录任务完成事件
     */
    List<String> LOGIN_TASK_EVENT = Arrays.asList(EVENT_NO_ACTIVE_USER, "luck_draw_page_pv");
}
