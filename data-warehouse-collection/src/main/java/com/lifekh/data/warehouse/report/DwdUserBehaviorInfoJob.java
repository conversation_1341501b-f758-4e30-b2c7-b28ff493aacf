package com.lifekh.data.warehouse.report;

import com.chaos.common.enums.LanguageEnum;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.github.pagehelper.PageHelper;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.DwdUserBehaviorDTO;
import com.lifekh.data.warehouse.enums.LoginMethodDataEnum;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorBO;
import com.lifekh.data.warehouse.oracle.bo.OracleUserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.report.service.LocationService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ElasticJobConf(name = "dwd_user_behavior_info_job", cron = "10 0 0 * * ?", description = "统计昨日用户行为数据", shardingTotalCount = 6)
public class DwdUserBehaviorInfoJob extends AbstractSimpleJob {

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 用户端Android，用户端iOS
     */
    private static List<Integer> APP_NO_LIST = Arrays.asList(10,11);

    @Autowired
    private LocationService locationService;

    @Value("${dwd.user.job.pagesize:2000}")
    private int pageSize;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始统计昨日用户行为数据,{}", shardingContext);
        //分页查询前一天登录过的用户
        Date startTime = DateUtil.getYesterdayStartDate();

        Integer pageNum=1;
        do {
            PageHelper.startPage(pageNum++, pageSize);
            //查询活跃的用户
            List<OracleOperatorBO> list = oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime(startTime, shardingContext.getShardingTotalCount(), shardingContext.getShardingItem());
            log.info("查询oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime,pageNum:{},pageSize:{}", pageNum, pageSize);
            //查询列表为空则退出遍历
            if(CollectionUtils.isEmpty(list)) {
                break;
            }
            //统计时间
            long startTimeLog = System.currentTimeMillis();
            for(OracleOperatorBO op : list) {
                try {
                    //如果为空则继续遍历下一个
                    if(op == null || StringUtils.isBlank(op.getOperatorNo())) {
                        continue;
                    }

                    //查询鉴权表
                    OracleUserOperatorLoginInfoBO bo = getOracleUserOperatorLoginInfoBo(startTime, op.getOperatorNo());

                    //查询dwd用户信息
                    DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO = queryDwdUserInfo(op.getOperatorNo());
                    log.info("查询DwdUserBehaviorInfoDO,operatorNo:{}", op);

                    //查询用户基本信息
                    queryUserBasicInfo(op.getOperatorNo(), dwdUserBehaviorInfoDO);

                    dwdUserBehaviorInfoDO.setOperatorNo(bo.getOperatorNo());
                    dwdUserBehaviorInfoDO.setOnlineTime(isTodayDate(bo.getOnlineTime()) ? dwdUserBehaviorInfoDO.getOnlineTime() : bo.getOnlineTime());
                    dwdUserBehaviorInfoDO.setLastOnlineTime(Optional.ofNullable(bo.getLastOnlineTime()).orElse(dwdUserBehaviorInfoDO.getOnlineTime()));
                    dwdUserBehaviorInfoDO.setAppNo(Optional.ofNullable(bo.getAppNo()).orElse(0));
                    dwdUserBehaviorInfoDO.setAppVersion(APP_NO_LIST.contains(bo.getAppNo()) ? bo.getAppVersion() : null);
                    dwdUserBehaviorInfoDO.setDeviceId(bo.getDeviceId());
                    dwdUserBehaviorInfoDO.setUdDeviceId(bo.getUdDeviceId());
                    dwdUserBehaviorInfoDO.setPhoneModel(APP_NO_LIST.contains(bo.getAppNo()) ? bo.getPhoneModel() : null);
                    dwdUserBehaviorInfoDO.setSeq(bo.getId());
                    log.debug("活跃用户信息:{}", dwdUserBehaviorInfoDO);

                    if(dwdUserBehaviorInfoDO.getId() == null) {
                        mongoTemplate.insert(dwdUserBehaviorInfoDO);
                    } else {
                        Query dwdUserInfoQuery = new Query();
                        dwdUserInfoQuery.addCriteria(Criteria.where(DwdUserBehaviorInfoDO.OPERATOR_NO).is(bo.getOperatorNo()));

                        Update update = new Update();
                        update.set(DwdUserBehaviorInfoDO.MOBILE, dwdUserBehaviorInfoDO.getMobile());
                        update.set(DwdUserBehaviorInfoDO.GENDER, dwdUserBehaviorInfoDO.getGender());
                        update.set(DwdUserBehaviorInfoDO.BIRTHDAY, dwdUserBehaviorInfoDO.getBirthday());
                        update.set(DwdUserBehaviorInfoDO.LATITUDE, dwdUserBehaviorInfoDO.getLatitude());
                        update.set(DwdUserBehaviorInfoDO.LONGITUDE,dwdUserBehaviorInfoDO.getLongitude());
                        update.set(DwdUserBehaviorInfoDO.PROVINCE, dwdUserBehaviorInfoDO.getProvince());
                        update.set(DwdUserBehaviorInfoDO.DISTRICT, dwdUserBehaviorInfoDO.getDistrict());
                        update.set(DwdUserBehaviorInfoDO.ONLINE_TIME, dwdUserBehaviorInfoDO.getOnlineTime());
                        update.set(DwdUserBehaviorInfoDO.LANGUAGE, dwdUserBehaviorInfoDO.getLanguage());
                        update.set(DwdUserBehaviorInfoDO.APP_NO, dwdUserBehaviorInfoDO.getAppNo());
                        update.set(DwdUserBehaviorInfoDO.APP_VERSION, dwdUserBehaviorInfoDO.getAppVersion());
                        update.set(DwdUserBehaviorInfoDO.DEVICE_ID, dwdUserBehaviorInfoDO.getDeviceId());
                        update.set(DwdUserBehaviorInfoDO.UD_DEVICE_ID, dwdUserBehaviorInfoDO.getUdDeviceId());
                        update.set(DwdUserBehaviorInfoDO.PHONE_MODEL, dwdUserBehaviorInfoDO.getPhoneModel());
                        update.set(DwdUserBehaviorInfoDO.SEQ, dwdUserBehaviorInfoDO.getSeq());
                        update.set(DwdUserBehaviorInfoDO.OP_LEVEL, dwdUserBehaviorInfoDO.getOpLevel());
                        update.set(DwdUserBehaviorInfoDO.LEVEL_NAEM, dwdUserBehaviorInfoDO.getLevelName());
                        update.set(DwdUserBehaviorInfoDO.AGE, dwdUserBehaviorInfoDO.getAge());
                        update.set(DwdUserBehaviorInfoDO.OPERATOR_STATUS, dwdUserBehaviorInfoDO.getOperatorStatus());
                        update.set(DwdUserBehaviorInfoDO.LOGIN_METHOD, dwdUserBehaviorInfoDO.getLoginMethod());
                        update.set(DwdUserBehaviorInfoDO.REG_LATITUDE, dwdUserBehaviorInfoDO.getRegLatitude());
                        update.set(DwdUserBehaviorInfoDO.REG_LONGITUDE, dwdUserBehaviorInfoDO.getRegLongitude());
                        update.set(DwdUserBehaviorInfoDO.REG_PROVINCE, dwdUserBehaviorInfoDO.getRegProvince());
                        update.set(DwdUserBehaviorInfoDO.REG_DISTRICT, dwdUserBehaviorInfoDO.getRegDistrict());
                        update.set(DwdUserBehaviorInfoDO.LAST_ONLINE_TIME, dwdUserBehaviorInfoDO.getLastOnlineTime());
                        mongoTemplate.upsert(dwdUserInfoQuery, update, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
                    }
                } catch (Exception e) {
                    log.error("统计昨日用户行为数据异常, operatorNo:{}", op, e);
                }
            }
            log.info("打印时间:{},size:{}", (System.currentTimeMillis() - startTimeLog), list.size());
        } while (true);
        log.info("结束统计昨日用户行为数据,{}",shardingContext);
    }

    /**
     * 判断是否是当前日期
     *
     * @param date
     * @return
     */
    private boolean isTodayDate(Date date) {
        return date.getTime() >= DateUtil.getTodayStartDate().getTime();
    }

    private OracleUserOperatorLoginInfoBO getOracleUserOperatorLoginInfoBo(Date startTime, String op) {
        return oracleUserOperatorLoginInfoDAO.queryUserByOperatorNoAndTime(startTime, op);
    }

    private List<String> getActiveUserList(Query query, long startIndex, Integer pageSize, Integer pageNum) {
        return mongoTemplate.findDistinct(query,
                ClickReportConstant.USERINFOBO_OPERATOR_NO,
                MongoDbCollectonName.COLLECT_BEHAVIOR,
                String.class).stream().sorted().skip(startIndex + ((long) pageSize * pageNum)).limit(pageSize).collect(Collectors.toList());
    }

    private DwdUserBehaviorInfoDO queryDwdUserInfo(String operatorNo) {
        DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO = null;
        Query dwdUserInfoQuery = new Query();
        dwdUserInfoQuery.addCriteria(Criteria.where(DwdUserBehaviorInfoDO.OPERATOR_NO).is(operatorNo));
        try {
            dwdUserBehaviorInfoDO = mongoTemplate.findOne(dwdUserInfoQuery, DwdUserBehaviorInfoDO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
        } catch (Exception e) {
            try {
                dwdUserBehaviorInfoDO = mongoTemplate.findOne(dwdUserInfoQuery, DwdUserBehaviorInfoDO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
            } catch (Exception ex) {
                log.error("查询用户行为数据异常,op:{}", operatorNo, ex);
            }
        }

        if(Objects.isNull(dwdUserBehaviorInfoDO)) {
            dwdUserBehaviorInfoDO = new DwdUserBehaviorInfoDO();
        }
        return dwdUserBehaviorInfoDO;
    }

    private void queryUserBasicInfo(String operatorNo, DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO) {
        //查询用户的性别，出生日期
        OracleUserOperatorLoginInfoBO userInfo = oracleUserOperatorLoginInfoDAO.queryUserByOperatorNo(operatorNo);

        if(userInfo != null) {
            dwdUserBehaviorInfoDO.setRegisterTime(userInfo.getRegisterTime());
            dwdUserBehaviorInfoDO.setGender(userInfo.getGender());
            dwdUserBehaviorInfoDO.setBirthday(userInfo.getBirthday());
            dwdUserBehaviorInfoDO.setAge(getAge(userInfo.getBirthday()));
            dwdUserBehaviorInfoDO.setOperatorStatus(userInfo.getOperatorStatus());
            dwdUserBehaviorInfoDO.setMobile(userInfo.getLoginName());
            dwdUserBehaviorInfoDO.setLanguage(Optional.ofNullable(userInfo.getLanguage()).orElse(LanguageEnum.EN_US.getCode()));

            //如果登录号为空则查询历史表
            if(StringUtils.isEmpty(userInfo.getLoginName())) {
                String loginName = oracleUserOperatorLoginInfoDAO.queryLoginhistoryByOperatorNo(operatorNo);
                dwdUserBehaviorInfoDO.setMobile(Optional.ofNullable(loginName).orElse(""));
            }

            if(userInfo.getRegLatitude() != null && userInfo.getRegLongitude() != null && StringUtils.isBlank(dwdUserBehaviorInfoDO.getRegProvince())) {
                LocationDTO locationDTO = locationService.getLocationByCoordinate(userInfo.getRegLongitude().toString(), userInfo.getRegLatitude().toString());
                dwdUserBehaviorInfoDO.setRegProvince(locationDTO.getProvinceName());
                dwdUserBehaviorInfoDO.setRegDistrict(locationDTO.getDistinctName());
                dwdUserBehaviorInfoDO.setRegLatitude(userInfo.getRegLatitude());
                dwdUserBehaviorInfoDO.setRegLongitude(userInfo.getRegLongitude());
            }

            //查询用户在线经纬度
            if(userInfo.getOnlineLatitude() != null && userInfo.getOnlineLongitude() != null) {
                LocationDTO locationDTO = locationService.getLocationByCoordinate(userInfo.getOnlineLongitude().toString(), userInfo.getOnlineLatitude().toString());
                dwdUserBehaviorInfoDO.setLatitude(userInfo.getOnlineLatitude());
                dwdUserBehaviorInfoDO.setLongitude(userInfo.getOnlineLongitude());
                dwdUserBehaviorInfoDO.setProvince(locationDTO.getProvinceName());
                dwdUserBehaviorInfoDO.setDistrict(locationDTO.getDistinctName());
            }
        }

        //查询用户等级
        UserLevelInfoBO levelInfoBo = oracleUserOperatorLoginInfoDAO.queryUserLevelByOperatorNo(operatorNo);
        dwdUserBehaviorInfoDO.setOpLevel(Optional.ofNullable(levelInfoBo).orElse(new UserLevelInfoBO(1, "大众会员")).getOpLevel());
        dwdUserBehaviorInfoDO.setLevelName(Optional.ofNullable(levelInfoBo).orElse(new UserLevelInfoBO(1, "大众会员")).getLevelName());

        //登录方式
        try {
            String loginMethod = oracleUserOperatorLoginInfoDAO.queryLoginMethod(operatorNo);
            if(!org.apache.commons.lang3.StringUtils.isBlank(loginMethod)) {
                dwdUserBehaviorInfoDO.setLoginMethod(LoginMethodDataEnum.getMessageByCode(loginMethod));
            }
        } catch (Exception e) {
            log.error("查询登录方式异常, operatorNo:{}", operatorNo, e);
        }
    }

    /**
     * 查询用户的经纬度
     *
     * @param bo
     * @param dwdUserBehaviorInfoDO
     */
    private void activeUserLocation(OracleUserOperatorLoginInfoBO bo, DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO) {
        AggregationResults<DwdUserBehaviorDTO> results = null;
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).is(bo.getOperatorNo());
        criteria.and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_ACTIVE_USER);
        criteria.and(ClickReportConstant.PROVINCE_NAME_EN).ne(null);
        criteria.and(ClickReportConstant.CREATE_TIME).gte(DateUtil.getYesterdayStartDate()).lt(DateUtil.getYesterdayEndTime());
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project(Fields.from(Fields.field("coordinates", "locationBo.coordinates"),
                        Fields.field(ClickReportConstant.SIMPLE_LANGUAGE, ClickReportConstant.LANGUAGE),
                        Fields.field(ClickReportConstant.PROVINCE_NAME, ClickReportConstant.PROVINCE_NAME_EN),
                        Fields.field(ClickReportConstant.AREA_NAME, ClickReportConstant.AREA_NAME_EN),
                        Fields.field(ClickReportConstant.CREATE_TIME, ClickReportConstant.CREATE_TIME))),
                Aggregation.sort(Sort.by(Sort.Direction.DESC,ClickReportConstant.CREATE_TIME)),
                Aggregation.limit(1)
        );
        try {
            results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
        } catch (Exception e) {
            try {
                results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
            } catch (Exception ex) {
                log.error("查询用户活跃数据异常,op:{}",bo.getOperatorNo(), e);
            }
        }

        if(Objects.nonNull(results) && !CollectionUtils.isEmpty(results.getMappedResults())) {
            DwdUserBehaviorDTO dwdUserBehaviorDTO = results.getMappedResults().get(0);

            //活跃地区
            if(Objects.nonNull(dwdUserBehaviorDTO.getCoordinates())
                    && dwdUserBehaviorDTO.getCoordinates().length == 2) {
                //纬度
                dwdUserBehaviorInfoDO.setLatitude(new BigDecimal(dwdUserBehaviorDTO.getCoordinates()[1]).setScale(8, BigDecimal.ROUND_HALF_UP).floatValue());
                //经度
                dwdUserBehaviorInfoDO.setLongitude(new BigDecimal(dwdUserBehaviorDTO.getCoordinates()[0]).setScale(8, BigDecimal.ROUND_HALF_UP).floatValue());
                dwdUserBehaviorInfoDO.setProvince(dwdUserBehaviorDTO.getProvinceName());
                dwdUserBehaviorInfoDO.setDistrict(dwdUserBehaviorDTO.getAreaName());
            }
        }
    }

    /**
     * 计算年龄
     */
    private static int getAge(Date birthDay) {
        int age = 0;

        if(birthDay == null) {
            return age;
        }

        try {
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) {
                return age;
            }
            int yearNow = cal.get(Calendar.YEAR);
            int monthNow = cal.get(Calendar.MONTH);
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
            cal.setTime(birthDay);

            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

            age = yearNow - yearBirth;

            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) {
                        age--;
                    }
                }else{
                    age--;
                }
            }
        } catch (Exception e) {
            log.error("计算年龄异常,birthDay:{}", birthDay, e);
        }
        return age;
    }
}
