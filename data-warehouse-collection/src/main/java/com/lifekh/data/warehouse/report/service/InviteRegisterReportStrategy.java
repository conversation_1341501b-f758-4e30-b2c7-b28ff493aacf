package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.chaos.common.enums.AppIdEnum;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.req.OperatorInfoQueryReqDTO;
import com.chaos.usercenter.api.dto.req.UpdateUserDeviceInfoReqDTO;
import com.chaos.usercenter.api.dto.resp.OperatorBatchQueryDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.DataStatusEnum;
import com.lifekh.data.warehouse.bo.InviteRegisterButNotOpenAppBO;
import com.lifekh.data.warehouse.bo.InviteRegisterRecordBO;
import com.lifekh.data.warehouse.bo.InviteRegisterReportDayBO;
import com.lifekh.data.warehouse.bo.behavior.UserInfoBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
@Slf4j
public class InviteRegisterReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    private static int pageSize = 500;

    @Autowired
    private UserOperatorFacade userOperatorFacade;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //将拉新数据洗到新表
        moveInviteRegisterData(reportBasicReqDTO, ClickReportConstant.REGISTER_PAGE_EVENT_LIST, MongoDbCollectonName.COLLECT_BURIED_POINT_ACTIVITY);
        log.info("统计日将拉新数据洗到新表完成,时间:{}",reportBasicReqDTO);

        moveInviteRegisterData(reportBasicReqDTO, ClickReportConstant.DISCOVERY_SHARE_EVENTS, MongoDbCollectonName.COLLECT_BURIED_POINT_DISCOVRY);
        log.info("统计日将发现页分享数据洗到新表完成,时间:{}",reportBasicReqDTO);

        //首次打开APP
        moveFirstOpen(reportBasicReqDTO);
        log.info("统计日首次打开APP完成,时间:{}",reportBasicReqDTO);

        //统计数据
        inviteRegisterClickDayStatistics(reportBasicReqDTO);
        log.info("统计拉新邀请事件完成,时间:{}",reportBasicReqDTO);

        //统计注册页注册用户打开APP数
        inviteRegisterOpenAppStatistics(reportBasicReqDTO);
        log.info("统计日注册页注册用户打开APP数完成,时间:{}",reportBasicReqDTO);

        //汇总统计，即每天累加
        summary(reportBasicReqDTO);
        log.info("汇总统日计完成,时间:{}",reportBasicReqDTO);

        return null;
    }

    private void moveFirstOpen(ReportBasicReqDTO reportBasicReqDTO) {
        //将首次打开APP记录迁到邀请记录表里
        Query query = getFirstOpenMoveAggregationCriteria(reportBasicReqDTO);

        for (int page=0; ;page ++) {
            query.with(PageRequest.of(page, pageSize));
            List<InviteRegisterRecordBO> registerRecords = mongoTemplate.find(query, InviteRegisterRecordBO.class, MongoDbCollectonName.COLLECT_BEHAVIOR);
            if (registerRecords.isEmpty()) {
                break;
            }
            try {
                registerRecords.forEach(dto -> {
                    //获取活动编号
                    String activityNo = getActivityNo(dto);
                    if(StringUtils.isNotBlank(activityNo)) {
                        dto.setActivityNo(activityNo);
                    }

                    //获取渠道
                    String channel = getChannel(dto.getExt());
                    if(StringUtils.isNotBlank(channel)) {
                        dto.setChannel(channel);
                    }

                    //获取手机号
                    String phone = getPhone(dto.getExt());
                    if(StringUtils.isNotBlank(phone)) {
                        UserInfoBO userInfoBO = dto.getUserInfoBo() == null ? new UserInfoBO() : dto.getUserInfoBo();
                        userInfoBO.setLoginName(phone);
                        dto.setUserInfoBo(userInfoBO);
                    }

                    dto.getEventBo().setEventName("首次打开APP");
                });

                mongoTemplate.insertAll(registerRecords);
            } catch (Exception e) {
                log.warn("邀请注册数据清洗失败", e);
            }
        }

    }

    private void summary(ReportBasicReqDTO reportBasicReqDTO) {
        int pageSize = 100;
        //构造查询条件 PV
        List<AggregationOperation> queryList = getSummaryRegisterPageOpenAppAggregationCriteria(reportBasicReqDTO);

        for (int page=0; ;page ++) {
            PageRespDTO<InviteRegisterReportDayBO> pageRespDTO = mongoPageHelper.query(
                    queryList, new PageReqDTO(page, pageSize), MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY, true, null,InviteRegisterReportDayBO.class);

            if(CollectionUtil.isEmpty(pageRespDTO.getList())){
                break;
            }

            for (InviteRegisterReportDayBO reportDayBO : pageRespDTO.getList()) {
                try {
                    //查询当前打开APP事件的数据
                    Query queryReportDay = new Query();
                    queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.EVEN).is(reportDayBO.getEven())
                            .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(reportDayBO.getBusinessName())
                            .and(InviteRegisterReportDayBO.LANGUAGE).is(reportDayBO.getLanguage())
                            .and(InviteRegisterReportDayBO.CHANNEL).is(reportDayBO.getChannel()));
                    InviteRegisterReportDayBO summaryBo = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_SUMMERY);


                    Long pvCount,uvCount;
                    if(summaryBo == null) {
                        pvCount = reportDayBO.getCount();
                        uvCount = reportDayBO.getUv();
                    } else {
                        pvCount = reportDayBO.getCount() + Optional.ofNullable(summaryBo.getCount()).orElse(0L);
                        uvCount = reportDayBO.getUv() + Optional.ofNullable(summaryBo.getUv()).orElse(0L);
                    }

                    mongoTemplate.upsert(queryReportDay,
                            getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO, reportDayBO.getEven(), reportDayBO.getBusinessName(), reportDayBO.getChannel(), pvCount, uvCount, reportDayBO.getLanguage()),
                            MongoDbCollectonName.REPORT_INVITE_REGISTER_SUMMERY);

                } catch (Exception e) {
                    log.warn("打开APP事件统计异常, reportBasicReqDTO:{}, reportDayBO:{}", reportBasicReqDTO, reportDayBO, e);
                }
            }
        }
    }

    /**
     * 统计注册页注册用户打开APP数
     *
     * @param reportBasicReqDTO
     */
    private void inviteRegisterOpenAppStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        int pageSize = 500;
        //构造查询条件 PV
        List<AggregationOperation> queryList = getRegisterPageOpenAppAggregationCriteria(reportBasicReqDTO);
        Map<String, Date> deleteMap = new HashMap<>();
        for (int page=0; ;page ++) {
            PageRespDTO<InviteRegisterButNotOpenAppBO> pageRespDTO = mongoPageHelper.query(
                    queryList, new PageReqDTO(page, pageSize), MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP, true, null, InviteRegisterButNotOpenAppBO.class);

            if(CollectionUtil.isEmpty(pageRespDTO.getList())){
                break;
            }

            for (InviteRegisterButNotOpenAppBO notOpenApp : pageRespDTO.getList()) {
                try {
                    //根据登录是否有打开APP
                    long openApp;
                    openApp = statisticsCheckOpenApp(reportBasicReqDTO, notOpenApp, ClickReportConstant.PRE_REGISTER_OPENAPP);
                    if(openApp < 1) {
                        openApp = statisticsCheckOpenApp(reportBasicReqDTO, notOpenApp, ClickReportConstant.REGISTER_OPENAPP);
                    }

                    //查询当前打开APP事件的数据
                    OperatorInfoQueryReqDTO reqDTO = new OperatorInfoQueryReqDTO();
                    reqDTO.setLoginName(notOpenApp.getLoginName());
                    reqDTO.setAppId(AppIdEnum.SUPER_APP.getCode());
                    OperatorBatchQueryDTO loginDto = userOperatorFacade.queryOperatorInfo(reqDTO);
                    if(loginDto == null || StringUtils.isBlank(loginDto.getOperatorNo())) {
                        continue;
                    } else {
                        //更新用户的注册渠道
                        if (StringUtils.isNotBlank(notOpenApp.getChannel())) {
                            UpdateUserDeviceInfoReqDTO updateDTO = new UpdateUserDeviceInfoReqDTO()
                                    .setOperatorNo(loginDto.getOperatorNo())
                                    .setRegisterChannel(notOpenApp.getChannel());
                            userOperatorFacade.updateUserDeviceInfo(updateDTO);
                        }
                    }

                    //清洗用户的注册来源
                    long count;
                    count = statisticsRegisterSource(reportBasicReqDTO, notOpenApp, ClickReportConstant.PRE_REGISTER_SUCCESS);
                    if(count < 1) {
                        statisticsRegisterSource(reportBasicReqDTO, notOpenApp, ClickReportConstant.REGISTER_SUCCESS);
                    }

//                    //当注册数大于0时，说明转化流程已结束，删除该手机号的临时数据
                    //if(count > 0) {
                    //    deleteList.add(notOpenApp.getLoginName());
                    //}
                    deleteMap.put(notOpenApp.getLoginName(), loginDto.getCreateTime());
                } catch (Exception e) {
                    log.error("打开APP事件统计异常, reportBasicReqDTO:{}, notOpenApp:{}", reportBasicReqDTO, notOpenApp, e);
                }
            }

            if(!deleteMap.isEmpty()) {
                deleteMap.forEach((loginName, actualRegisterTime) ->{
                    Criteria criteriaRemove = new Criteria();
                    criteriaRemove.and(InviteRegisterButNotOpenAppBO.LOGIN_NAME).is(loginName);
                    Query queryRemove = new Query(criteriaRemove);
                    Update update = new Update();
                    update.set(InviteRegisterButNotOpenAppBO.STATUS, DataStatusEnum.DELETE.getCode());
                    update.set(InviteRegisterButNotOpenAppBO.UPDATE_TIME, new Date());
                    update.set(InviteRegisterButNotOpenAppBO.ACTUAL_REGISTER_TIME, actualRegisterTime);
                    mongoTemplate.upsert(queryRemove, update, MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP);
                });
            }
        }
    }

    private long statisticsRegisterSource(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterButNotOpenAppBO notOpenApp, String even) {
        //查询当前打开APP事件的数据
        Query queryReportDay = new Query();
        queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(InviteRegisterReportDayBO.EVEN).is(even)
                .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(ClickReportConstant.REGISTER_PAGE_REGISTER_SUCCESS.get(even))
                .and(InviteRegisterReportDayBO.LANGUAGE).is(notOpenApp.getLanguage())
                .and(InviteRegisterReportDayBO.CHANNEL).is(notOpenApp.getChannel()));
        InviteRegisterReportDayBO reportDayBO = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY);

        if(reportDayBO == null) {
            return 0;
        }

        long count = 1;
        if(reportDayBO.getCount() != null) {
            count = reportDayBO.getCount() + 1;
        }

        //修改
        mongoTemplate.upsert(queryReportDay,
                getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO,
                        even, ClickReportConstant.REGISTER_PAGE_REGISTER_SUCCESS.get(even),
                        reportDayBO.getChannel(), count, count, notOpenApp.getLanguage()),
                MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY);
        return count;
    }

    private long statisticsCheckOpenApp(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterButNotOpenAppBO notOpenApp, String even) {
        Query queryOpenApp = new Query();
        queryOpenApp.addCriteria(Criteria.where(ClickReportConstant.CREATE_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportConstant.USERINFOBO_LOGIN_NAME).is(notOpenApp.getLoginName())
                .and(ClickReportConstant.EVENT).is(ClickReportConstant.FIRST_OPEN_EVEN));
        long openAppCount =  mongoTemplate.count(queryOpenApp, MongoDbCollectonName.INVITE_REGISTER_RECORD);

        //大于0则说明有打开APP
        if(openAppCount > 0 ) {
            //查询当前打开APP事件的数据
            Query queryReportDay = new Query();
            queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(InviteRegisterReportDayBO.EVEN).is(ClickReportConstant.FIRST_OPEN_EVEN)
                    .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(ClickReportConstant.OPEN_APP_SUCCESS.get(even))
                    .and(InviteRegisterReportDayBO.LANGUAGE).is(notOpenApp.getLanguage())
                    .and(InviteRegisterReportDayBO.CHANNEL).is(notOpenApp.getChannel()));
            InviteRegisterReportDayBO reportDayBO = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY);

            long count = 1;
            if(reportDayBO != null && reportDayBO.getCount() != null) {
                count = reportDayBO.getCount() + 1;
            }


            //修改
            mongoTemplate.upsert(queryReportDay,
                    getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO,
                            ClickReportConstant.FIRST_OPEN_EVEN, even,
                            notOpenApp.getChannel(), count,count, notOpenApp.getLanguage()),
                    MongoDbCollectonName.REPORT_INVITE_REGISTER_DAY);
        }
        return openAppCount;
    }

    private List<AggregationOperation> getRegisterPageOpenAppAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        List<AggregationOperation> querList = new ArrayList<>();
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(DateUtil.getSearchStaticTime(reportBasicReqDTO.getBeginTime(), Calendar.DATE, -30));
        criteria.and(InviteRegisterButNotOpenAppBO.STATUS).is(DataStatusEnum.NORMAL.getCode());
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private List<AggregationOperation> getSummaryRegisterPageOpenAppAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        List<AggregationOperation> querList = new ArrayList<>();
        Criteria criteria = new Criteria();
        criteria.and(InviteRegisterReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime()).lt(reportBasicReqDTO.getEndTime());
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private void moveInviteRegisterData(ReportBasicReqDTO reportBasicReqDTO, List<String> evens, String collectonName) {
        //构造查询条件 PV
        for (String event : evens) {
            Query query = getInviteRegisterMoveAggregationCriteria(reportBasicReqDTO, event);
            for (int page = 0; ; page++) {
                query.with(PageRequest.of(page, pageSize));
                List<InviteRegisterRecordBO> registerRecords = mongoTemplate.find(query, InviteRegisterRecordBO.class, collectonName);
                if (registerRecords.isEmpty()) {
                    break;
                }
                try {
                    registerRecords.forEach(dto -> {
                        //获取全称
//                    dto.setFullBusinessName(geFullBusinessName(dto.getExt(), dto.getBusinessName()));

                        //获取活动编号
                        String activityNo = getActivityNo(dto);
                        if (StringUtils.isNotBlank(activityNo)) {
                            dto.setActivityNo(activityNo);
                        }

                        //获取渠道
                        String channel = getChannel(dto.getExt());
                        if (StringUtils.isNotBlank(channel)) {
                            dto.setChannel(channel);
                        }

                        //获取from
                        String from = getFrom(dto.getExt());
                        if (StringUtils.isNotBlank(from)) {
                            dto.setFrom(from);
                        }

                        if (dto.getBusinessName().equals(ClickReportConstant.REGISTER_PAGE_REGISTER_RESFULT_EVEN.get(dto.getEventBo().getEvent()))) {
                            Query queryNotOpenApp = new Query();
                            queryNotOpenApp.addCriteria(Criteria.where(InviteRegisterButNotOpenAppBO.LOGIN_NAME).is(dto.getUserInfoBo().getLoginName()));

                            //将手机号临时存储到待打开APP列表
                            mongoTemplate.upsert(queryNotOpenApp, getInviteButNotOpenAppUpdateBo(dto, ClickReportConstant.REGISTER), MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP);
                        } else if (dto.getBusinessName().equals(ClickReportConstant.PREREGISTER_PAGE_REGISTER_RESULT_EVEN.get(dto.getEventBo().getEvent()))) {
                            Query queryNotOpenApp = new Query();
                            queryNotOpenApp.addCriteria(Criteria.where(InviteRegisterButNotOpenAppBO.LOGIN_NAME).is(dto.getUserInfoBo().getLoginName()));

                            //将手机号临时存储到待打开APP列表
                            mongoTemplate.upsert(queryNotOpenApp, getInviteButNotOpenAppUpdateBo(dto, ClickReportConstant.PREREGISTER), MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP);
                        }
                    });

                    mongoTemplate.insertAll(registerRecords);
                } catch (Exception e) {
                    log.warn("邀请注册数据清洗失败, 事件: {}", event, e);
                }
            }
        }
    }

    private String getActivityNo(InviteRegisterRecordBO record) {
        Map ext = record.getExt();
        if(ext == null) {
            return "";
        }
        String activityNo = "";

        //邀请有礼下载页事件获取活动编号
        if (ClickReportConstant.DOWNLOAD_PAGE_PV.equals(record.getEventBo().getEvent())
                || ClickReportConstant.DOWNLOAD_PAGE_CLICK_DOWNLOAD.equals(record.getEventBo().getEvent())) {
            String fromType = MapUtil.getStr(ext, "fromType", "");
            if ("invite".equals(fromType)) {
                String fromNo = MapUtil.getStr(ext, "fromNo", "");
                if (!"undefined".equals(fromNo)) {
                    activityNo = fromNo;
                }
            }
        } else {
            activityNo = (String) Optional.ofNullable(ext.get("activityNo")).orElse(new String(""));
        }
        return activityNo;
    }

    private String getFrom(Map ext) {
        if(ext == null) {
            return "";
        }
        return  (String) Optional.ofNullable(ext.get("from")).orElse(new String(""));
    }

    private String getChannel(Map ext) {
        if(ext == null) {
            return "";
        }

       return  (String) Optional.ofNullable(ext.get(ClickReportConstant.CHANNEL)).orElse(new String(""));
    }

    private String getPhone(Map ext) {
        if(ext == null) {
            return "";
        }

        return  (String) Optional.ofNullable(ext.get(ClickReportConstant.PHONE)).orElse(new String(""));
    }

    private String geFullBusinessName(Map ext, String businessName) {
        StringBuilder fullBusinessName = new StringBuilder();
        if(ext != null) {
            String from = (String) Optional.ofNullable(ext.get("from")).orElse(new String(""));
            if(StringUtils.isNotBlank(from)) {
                fullBusinessName.append(from).append("@");
            }
        }
        fullBusinessName.append(businessName);
//        if(ext != null) {
//            String activityNo = (String) Optional.ofNullable(ext.get("activityNo")).orElse(new String(""));
//            if (StringUtils.isNotBlank(activityNo)) {
//                fullBusinessName.append("@").append(activityNo);
//            }
//        }
        return fullBusinessName.toString();
    }

    private static Update getInviteButNotOpenAppUpdateBo(InviteRegisterRecordBO inviteRegisterRecordBO, String source) {
        Update update = new Update();
        update.set(InviteRegisterButNotOpenAppBO.CREATE_TIME, new Date());
        update.set(InviteRegisterButNotOpenAppBO.UPDATE_TIME, new Date());
        update.set(InviteRegisterButNotOpenAppBO.LOGIN_NAME, inviteRegisterRecordBO.getUserInfoBo().getLoginName());
        update.set(InviteRegisterButNotOpenAppBO.CHANNEL,inviteRegisterRecordBO.getChannel());
        update.set(InviteRegisterButNotOpenAppBO.LANGUAGE, inviteRegisterRecordBO.getUserInfoBo().getLanguage());
        update.set(InviteRegisterButNotOpenAppBO.REGISTER_TIME, inviteRegisterRecordBO.getCreateTime());
        update.set(InviteRegisterButNotOpenAppBO.SOURCE, source);
        update.set(InviteRegisterButNotOpenAppBO.ACTIVITY_NO, inviteRegisterRecordBO.getActivityNo());
        update.set(InviteRegisterButNotOpenAppBO.STATUS, DataStatusEnum.NORMAL.getCode());
        update.set(InviteRegisterButNotOpenAppBO.FROM, inviteRegisterRecordBO.getFrom());
        return update;
    }

    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getInviteRegisterAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.EVENT, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE, ClickReportConstant.EXT_CHANNEL)
                .first(ClickReportConstant.EVENT).as(InviteRegisterReportDayBO.EVEN)
                .first(ClickReportConstant.BUSINESS_NAME).as(InviteRegisterReportDayBO.BUSINESS_NAME)
                .first(ClickReportConstant.LANGUAGE).as(InviteRegisterReportDayBO.LANGUAGE)
                .first(ClickReportConstant.EXT_CHANNEL).as(InviteRegisterReportDayBO.CHANNEL)
                .count().as(InviteRegisterReportDayBO.COUNT));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private void inviteRegisterClickDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getInviteRegisterAggregationCriteria(reportBasicReqDTO);
        Long result = null;
        for (int pageNum = 0; ;pageNum++) {
            PageRespDTO<InviteRegisterReportDayBO> pageRespDTO = mongoPageHelper.query(
                    aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.INVITE_REGISTER_RECORD, true, result, InviteRegisterReportDayBO.class);
            result = pageRespDTO.getTotal();
            if(CollectionUtils.isEmpty(pageRespDTO.getList())){
                break;
            }

            //3.入库
            for (InviteRegisterReportDayBO reportDayBo : pageRespDTO.getList()) {
                try {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(InviteRegisterReportDayBO.EVEN).is(reportDayBo.getEven())
                            .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(reportDayBo.getBusinessName())
                            .and(InviteRegisterReportDayBO.LANGUAGE).is(reportDayBo.getLanguage())
                            .and(InviteRegisterReportDayBO.CHANNEL).is(reportDayBo.getChannel()));
                    reportDayBo.setDataTime(reportBasicReqDTO.getDataTime());

                    //查询uv
                    Long uv = mongoPageHelper.queryTotal(getUvAggregationCriteria(reportBasicReqDTO, reportDayBo), MongoDbCollectonName.INVITE_REGISTER_RECORD, true);
                    reportDayBo.setUv(uv);

                    //更新总表
                    mongoTemplate.upsert(query, getInviteRegisterUpdateBo(reportDayBo), InviteRegisterReportDayBO.COLLECTION_NAME);
                } catch (Exception e) {
                    log.warn("邀请拉新事件统计异常, reportBasicReqDTO:{}, reportDayBo:{}", reportBasicReqDTO, reportDayBo, e);
                }
            }
        }
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getUvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterReportDayBO reportDayBo) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(reportDayBo.getEven());
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(reportDayBo.getBusinessName());
        criteria.and(ClickReportConstant.LANGUAGE).is(reportDayBo.getLanguage());
        criteria.and(ClickReportConstant.CHANNEL).is(reportDayBo.getChannel());
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_LOGIN_NAME).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_LOGIN_NAME).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.EVENT, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE, ClickReportConstant.CHANNEL, ClickReportConstant.USERINFOBO_LOGIN_NAME)
                .first(ClickReportConstant.EVENT).as(InviteRegisterReportDayBO.EVEN)
                .first(ClickReportConstant.BUSINESS_NAME).as(InviteRegisterReportDayBO.BUSINESS_NAME)
                .first(ClickReportConstant.LANGUAGE).as(InviteRegisterReportDayBO.LANGUAGE)
                .first(ClickReportConstant.CHANNEL).as(InviteRegisterReportDayBO.CHANNEL)
                .first(ClickReportConstant.USERINFOBO_LOGIN_NAME).as(ClickReportConstant.LOGIN_NAME)
                .count().as(InviteRegisterReportDayBO.COUNT));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private static Update getInviteRegisterUpdateBo(InviteRegisterReportDayBO reportDayB0) {
        Update update = new Update();
        update.set(InviteRegisterReportDayBO.UPDATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.CREATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.EVEN, reportDayB0.getEven());
        update.set(InviteRegisterReportDayBO.BUSINESS_NAME, reportDayB0.getBusinessName());
        update.set(InviteRegisterReportDayBO.CHANNEL, reportDayB0.getChannel());
        update.set(InviteRegisterReportDayBO.LANGUAGE, reportDayB0.getLanguage());
        update.set(InviteRegisterReportDayBO.DATA_TIME, reportDayB0.getDataTime());
        update.set(InviteRegisterReportDayBO.COUNT, reportDayB0.getCount() == null ? 0 : reportDayB0.getCount());
        update.set(InviteRegisterReportDayBO.UV, reportDayB0.getUv() == null ? 0 : reportDayB0.getUv());
        return update;
    }

    private static Update getInviteRegisterOpenAppUpdateBo(ReportBasicReqDTO reqDTO, String even, String businessName, String channel, Long pvCount, Long uvCount, String language) {
        Update update = new Update();
        update.set(InviteRegisterReportDayBO.UPDATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.CREATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.EVEN, even);
        update.set(InviteRegisterReportDayBO.BUSINESS_NAME, businessName);
        update.set(InviteRegisterReportDayBO.CHANNEL, channel);
        update.set(InviteRegisterReportDayBO.DATA_TIME, reqDTO.getDataTime());
        update.set(InviteRegisterReportDayBO.COUNT, pvCount == null ? 0 : pvCount);
        update.set(InviteRegisterReportDayBO.UV, uvCount == null ? 0 : uvCount);
        update.set(InviteRegisterReportDayBO.LANGUAGE, language);
        return update;
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private Query getInviteRegisterMoveAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, String event) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
        .and(ClickReportConstant.EVENT).is(event);
        return new Query(criteria);
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private Query getFirstOpenMoveAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is("firstOpen");
        criteria.and(ClickReportConstant.EXT_PHONE).ne(null);
        return new Query(criteria);
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
