package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_YUMNOW_STORE_ORDER_CONVERSION_RATE)
@Data
@Accessors(chain = true)
public class ReportYumNowStoreOrderConversionRateBO implements Serializable {
    private static final long serialVersionUID = -6318611552254958566L;

    private Date createTime;
    public static final String CREATE_TIME = "createTime";

    private Date dataTime;
    public static final String DATA_TIME = "dataTime";

    /**
     * 语言
     */
    private String language;
    public static final String LANGUAGE = "language";

    /**
     * 门店页浏览用户数（用户维度）
     */
    private Long storeViewUv;
    public static final String STORE_VIEW_UV = "storeViewUv";

    /**
     * 日活用户数（用户维度）
     */
    private Long dau;
    public static final String DAU = "dau";

    /**
     * 门店进入率 = 门店页浏览用户数 / 日活用户数 （用户维度）
     */
    private Double storeViewRate;
    public static final String STORE_VIEW_RATE = "storeViewRate";

    /**
     * 下单用户数（用户维度）
     */
    private Long orderUserCount;
    public static final String ORDER_USER_COUNT = "orderUserCount";

    /**
     * 门店页下单转化率（用户维度）= 下单用户数（用户维度）/ 门店页浏览用户数
     */
    private Double orderStoreUserRate;
    public static final String ORDER_STORE_USER_RATE = "orderStoreUserRate";

    /**
     * 外卖订单支付数 （订单维度）
     */
    private Long orderPaidCount;
    public static final String ORDER_PAID_COUNT = "orderPaidCount";

    /**
     * 外卖总订单数 （订单维度）
     */
    private Long orderTotalCount;
    public static final String ORDER_TOTAL_COUNT = "orderTotalCount";

    /**
     *外卖订单支付率 = 外卖订单支付数/外卖总订单数 （订单维度）
     */
    private Double orderPaidRate;
    public static final String ORDER_PAID_RATE = "orderPaidRate";

    /**
     * 订单均价（订单维度）
     */
    private Double actualPayAveragePrice;
    public static final String ACTUAL_PAY_AVERAGE_PRICE = "actualPayAveragePrice";

    /**
     * 订单均价（订单维度）
     */
    private Double totalPayableAveragePrice;
    public static final String TOTAL_PAYABLE_AVERAGE_PRICE = "totalPayableAveragePrice";
}
