package com.lifekh.data.warehouse.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ReportTakeawayFlowConversionBO implements Serializable {
    private static final long serialVersionUID = 6192353663647148926L;

    private Date dataTime;

    private String language;

    private String provinceName;

    /**
     * 版本号
     */
    private String appVersion;

    /**
     * 设备类型
     */
    private String deviceType;


    private Date createTime;

    private String businessName;

    private Integer pv;

    private Integer uv;

    private Integer clickCount;

    private Double clickRate;


    private Integer intoStoreCount;

    private Double intoStoreRate;

    private Integer submitCount;

    private Double submitRate;

    private Integer orderCount;

    private Double orderRate;

    private Integer payCount;

    private Double payRate;

    private Double gmv;

    private Double marketingExpenses;

    @JsonIgnore
    private List<String> orderList;

    private String groupKey;

}
