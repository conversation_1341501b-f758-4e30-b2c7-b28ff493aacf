package com.lifekh.data.warehouse.report.service;

import com.chaos.common.enums.AppIdEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.ReportLocationDayBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorInfoBO;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.enums.ReportLocationDataTypeEnum;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LocationReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {

        this.staticLocationActiveUser(reportBasicReqDTO);

        try {
            this.staticLocationNewUser(reportBasicReqDTO);
        } catch (Exception e) {
            log.error("统计每日地区新增用户出现异常", e);
        }

        return null;
    }

    /**
     * 查询条件JSON [{"$match":{"eventBo.event":"active_user","locationBo.provinceNameZh":{"$ne":null}}},{"$group":{"_id":"$userInfoBo.operatorNo","provinceNameZh":{"$first":"$locationBo.provinceNameZh"}}},{"$group":{"_id":"$provinceNameZh","location":{"$first":"$provinceNameZh"},"count":{"$sum":1}}}]
     *
     * @param reqDTO
     * @return
     */
    private void staticLocationActiveUser(ReportBasicReqDTO reqDTO) {
        Criteria criteria = Criteria.where("eventBo.event").is("active_user").and("locationBo.provinceNameZh").ne(null)
                .and("createTime").gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime());

        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userInfoBo.operatorNo")
                        .first("locationBo.provinceNameZh").as("provinceNameZh"),

                Aggregation.group("provinceNameZh")
                        .first("provinceNameZh").as(ReportLocationDayBO.LOCATION)
                        .count().as(ReportLocationDayBO.COUNT)


        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());

        AggregationResults<ReportLocationDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.COLLECT_BEHAVIOR, ReportLocationDayBO.class);
        List<ReportLocationDayBO> locationDayBOList = results.getMappedResults();
        if (CollectionUtils.isNotEmpty(locationDayBOList)) {
            for (ReportLocationDayBO reportLocationDayBO : locationDayBOList) {
                reportLocationDayBO.setId(null)
                        .setDataTime(reqDTO.getDataTime())
                        .setDataType(ReportLocationDataTypeEnum.ACTIVE_USER_COUNT.getCode())
                        .setDataTypeName(ReportLocationDataTypeEnum.ACTIVE_USER_COUNT.getMessage())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
            }
            mongoTemplate.insertAll(locationDayBOList);
        }
    }

    /**
     * 统计每日地区新增用户
     *
     * @param req
     */
    private void staticLocationNewUser(ReportBasicReqDTO req) {
        //查询当天新增的用户ID
        Criteria userCriteria = Criteria.where("CREATE_TIME")
                .gte(req.getBeginTime())
                .lte(req.getEndTime())
                .and("APP_ID").is(AppIdEnum.SUPER_APP.getCode());
        List<UserOperatorInfoBO> userLabels = mongoTemplate.find(Query.query(userCriteria), UserOperatorInfoBO.class);
        List<String> operatorNos = userLabels.stream().map(UserOperatorInfoBO::getOperatorNo).collect(Collectors.toList());
        if (operatorNos.isEmpty()) {
            return;
        }

        //查埋点表获取地区
        Criteria criteria = Criteria.where("createTime").gte(req.getBeginTime()).lte(req.getEndTime())
                .and("userInfoBo.operatorNo").in(operatorNos)
                .and("eventBo.event").is(EventConstant.EVENT_NO_ACTIVE_USER)
                .and("locationBo.provinceNameZh").ne(null);
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userInfoBo.operatorNo", "locationBo.provinceNameZh")
                        .first("userInfoBo.operatorNo").as(ReportLocationDayBO.OPERATOR_NO)
                        .first("locationBo.provinceNameZh").as(ReportLocationDayBO.LOCATION))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<ReportLocationDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.COLLECT_BEHAVIOR, ReportLocationDayBO.class);
        List<ReportLocationDayBO> datas = results.getMappedResults();

        //按地区分组计数
        Map<String, Long> locationCountMap = datas.stream().collect(Collectors.groupingBy(ReportLocationDayBO::getLocation, Collectors.counting()));
        locationCountMap.forEach((location, count) -> {
            ReportLocationDayBO report = new ReportLocationDayBO()
                    .setDataTime(req.getDataTime())
                    .setDataType(ReportLocationDataTypeEnum.NEW_USER_COUNT.getCode())
                    .setDataTypeName(ReportLocationDataTypeEnum.NEW_USER_COUNT.getMessage())
                    .setLocation(location)
                    .setCount(count)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());
            mongoTemplate.insert(report);
        });
    }

    /**
     * @param reportBasicReqDTO
     * @return
     */
    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
