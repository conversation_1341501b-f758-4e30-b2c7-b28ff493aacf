package com.lifekh.data.warehouse.report.service;

import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.DwdUserBehaviorInfoDO;
import com.lifekh.data.warehouse.bo.ReportBusinessLineActiveUserBO;
import com.lifekh.data.warehouse.bo.ReportLanguageDayBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.enums.ReportLanguageDataTypeEnum;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorOnlineLanguageBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class LanguageReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            this.staticLanguageActiveUser(reportBasicReqDTO);
        } catch (Exception e) {
            log.error("统计日活用户数出现异常", e);
        }

        return null;
    }

    /**
     * 查询条件JSON
     *
     * @param reqDTO
     * @return
     */
    private void staticLanguageActiveUser(ReportBasicReqDTO reqDTO) {
       List<ReportLanguageDayBO> dayBOList = aggregateQueryOracle(reqDTO);
        if (CollectionUtils.isNotEmpty(dayBOList)) {
            for (ReportLanguageDayBO dayBO : dayBOList) {
                dayBO.setId(null)
                        .setDataTime(reqDTO.getDataTime())
                        .setDataType(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT.getCode())
                        .setDataTypeName(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT.getMessage())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
            }
            mongoTemplate.insertAll(dayBOList);
        }
    }

    /**
     * 统计业务线日活
     */
    public void statisticsBusinessActiveUser(){
        log.info("开始统计业务线日活");
        try {
            int dayOffset = 1;
            Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
            Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
            Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);
            ReportBasicReqDTO reqDTO = new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday);

            Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME)
                    .gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime()).and(ClickReportConstant.BUSINESS_LINE).ne(null);
            Aggregation agg = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(ClickReportConstant.USERINFOBO_OPERATOR_NO, ClickReportConstant.BUSINESS_LINE),
                            Aggregation.group(ClickReportConstant.BUSINESS_LINE)
                                    .first(ClickReportConstant.BUSINESS_LINE).as(ReportBusinessLineActiveUserBO.BUSINESS_LINE)
                                    .count().as(ReportBusinessLineActiveUserBO.COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());

            List<ReportBusinessLineActiveUserBO> reports = mongoTemplate.aggregate(agg, MongoDbCollectonName.COLLECT_BURIED_POINT_BUSINESS_ACTIVE, ReportBusinessLineActiveUserBO.class).getMappedResults();
            if(CollectionUtils.isNotEmpty(reports)) {
                long total = 0L;
                for (ReportBusinessLineActiveUserBO bo : reports) {
                    total = total + Optional.ofNullable(bo.getCount()).orElse(0L);
                }

                //赋值总数
                for (ReportBusinessLineActiveUserBO bo : reports) {
                    bo.setId(null);
                    bo.setCreateTime(new Date());
                    bo.setDataTime(yesterday);
                    bo.setTotal(total);
                }
                mongoTemplate.insertAll(reports);
            }
            log.info("结束统计业务线日活");
        } catch (Exception e) {
            log.error("统计业务线日活异常", e);
        }

        log.info("开始统计业务线按语言分组日活");
        try {
            int dayOffset = 1;
            Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
            Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
            Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);
            ReportBasicReqDTO reqDTO = new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday);

            Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME)
                    .gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime()).and(ClickReportConstant.BUSINESS_LINE).ne(null);
            Aggregation agg = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group(ClickReportConstant.BUSINESS_LINE,ClickReportConstant.LANGUAGE,ClickReportConstant.USERINFOBO_OPERATOR_NO),
                            Aggregation.group(ClickReportConstant.BUSINESS_LINE, ClickReportConstant.LANGUAGE)
                                    .first(ClickReportConstant.BUSINESS_LINE).as(ReportBusinessLineActiveUserBO.BUSINESS_LINE)
                                    .first(ClickReportConstant.LANGUAGE).as(ReportBusinessLineActiveUserBO.LANGUAGE)
                                    .count().as(ReportBusinessLineActiveUserBO.COUNT))
                    .withOptions(AggregationOptions.builder().allowDiskUse(true).build());

            List<ReportBusinessLineActiveUserBO> reports = mongoTemplate.aggregate(agg, MongoDbCollectonName.COLLECT_BURIED_POINT_BUSINESS_ACTIVE, ReportBusinessLineActiveUserBO.class).getMappedResults();
            if(CollectionUtils.isNotEmpty(reports)) {
                long total = 0L;
                for (ReportBusinessLineActiveUserBO bo : reports) {
                    total = total + Optional.ofNullable(bo.getCount()).orElse(0L);
                }

                //赋值总数
                for (ReportBusinessLineActiveUserBO bo : reports) {
                    bo.setId(null);
                    bo.setCreateTime(new Date());
                    bo.setDataTime(yesterday);
                    bo.setTotal(total);
                }
                mongoTemplate.insert(reports, MongoDbCollectonName.REPORT_BUSINESS_LANGUAGE_ACTIVE_USER);
            }
            log.info("结束统计业务线按语言分组日活");
        } catch (Exception e) {
            log.error("统计业务线日活异常", e);
        }
    }

    /**
     * 打开APP数
     */
    public void statisticsOpenAppCount() {
        try {
            int dayOffset = 1;
            Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
            Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
            Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);
            ReportBasicReqDTO reqDTO = new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday);
        Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime())
                .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_SESSION_END);

        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.LANGUAGE)
                        .first(ClickReportConstant.LANGUAGE).as(ReportLanguageDayBO.LANGUAGE)
                        .count().as(ReportLanguageDayBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<ReportLanguageDayBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.COLLECT_BEHAVIOR, ReportLanguageDayBO.class).getMappedResults();
        if (CollectionUtils.isNotEmpty(results)) {
            for (ReportLanguageDayBO report : results) {
                report.setId(null)
                        .setDataTime(reqDTO.getDataTime())
                        .setDataType(ReportLanguageDataTypeEnum.OPEN_APP_COUNT.getCode())
                        .setDataTypeName(ReportLanguageDataTypeEnum.OPEN_APP_COUNT.getMessage())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(report);
            }
        }
        } catch (Exception e) {
            log.error("统计打开APP数出现异常", e);
        }
    }

    /**
     * @param reportBasicReqDTO
     * @return
     */
    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            this.statisticsWeekActiveUser(reportBasicReqDTO);
        } catch (Exception e) {
            log.error("统计周活跃用户出现异常", e);
        }

        return null;
    }


    private void statisticsWeekActiveUser(ReportBasicReqDTO reqDTO) {
        List<ReportLanguageDayBO> results = this.aggregateQueryOracle(reqDTO);
        if (CollectionUtils.isNotEmpty(results)) {
            for (ReportLanguageDayBO result : results) {
                if (StringUtils.isBlank(result.getLanguage())) {
                    continue;
                }
                //查询该周的报表
                Query query = Query.query(Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime())
                        .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_WEEK.getCode())
                        .and(ReportLanguageDayBO.LANGUAGE).is(result.getLanguage()));
                ReportLanguageDayBO report = mongoTemplate.findOne(query, ReportLanguageDayBO.class);
                if (report == null) {
                    report = new ReportLanguageDayBO()
                            .setCreateTime(new Date());
                }
                report.setDataTime(DateUtil.getDataTime(reqDTO.getEndTime()))
                        .setDataType(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_WEEK.getCode())
                        .setDataTypeName(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_WEEK.getMessage())
                        .setLanguage(result.getLanguage())
                        .setCount(result.getCount())
                        .setUpdateTime(new Date());
                mongoTemplate.save(report);
            }
        }
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            this.statisticsMonthActiveUser(reportBasicReqDTO);
        } catch (Exception e) {
            log.error("统计月活跃用户出现异常", e);
        }

        return null;
    }

    private void statisticsMonthActiveUser(ReportBasicReqDTO reqDTO) {
        List<ReportLanguageDayBO> results = this.aggregateQueryOracle(reqDTO);
        if (CollectionUtils.isNotEmpty(results)) {
            for (ReportLanguageDayBO result : results) {
                if (StringUtils.isBlank(result.getLanguage())) {
                    continue;
                }
                //查询该月的报表
                Query query = Query.query(Criteria.where(ReportLanguageDayBO.DATA_TIME).gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime())
                        .and(ReportLanguageDayBO.DATA_TYPE).is(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_MONTH.getCode())
                        .and(ReportLanguageDayBO.LANGUAGE).is(result.getLanguage()));
                ReportLanguageDayBO report = mongoTemplate.findOne(query, ReportLanguageDayBO.class);
                if (report == null) {
                    report = new ReportLanguageDayBO()
                            .setCreateTime(new Date());
                }
                report.setDataTime(DateUtil.getDataTime(reqDTO.getEndTime()))
                        .setDataType(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_MONTH.getCode())
                        .setDataTypeName(ReportLanguageDataTypeEnum.ACTIVE_USER_COUNT_MONTH.getMessage())
                        .setLanguage(result.getLanguage())
                        .setCount(result.getCount())
                        .setUpdateTime(new Date());
                mongoTemplate.save(report);
            }
        }
    }

    private List<ReportLanguageDayBO> aggregateQuery(ReportBasicReqDTO reqDTO) {
        Criteria criteria = Criteria.where(DwdUserBehaviorInfoDO.ONLINE_TIME).gte(reqDTO.getBeginTime()).lte(reqDTO.getEndTime());
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(DwdUserBehaviorInfoDO.LANGUAGE)
                        .first(DwdUserBehaviorInfoDO.LANGUAGE).as(ReportLanguageDayBO.LANGUAGE)
                        .count().as(ReportLanguageDayBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return mongoTemplate.aggregate(agg, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO, ReportLanguageDayBO.class).getMappedResults();
    }

    public List<ReportLanguageDayBO> aggregateQueryOracle(ReportBasicReqDTO reqDTO) {
        List<OracleOperatorOnlineLanguageBO> reports = oracleUserOperatorLoginInfoDAO.queryActiveUserLanguageByTime(reqDTO.getBeginTime(), reqDTO.getEndTime());

        List<ReportLanguageDayBO> result = new ArrayList<>();
        Long en_US = 0L;
        if(CollectionUtils.isNotEmpty(reports)) {
            for(OracleOperatorOnlineLanguageBO bo : reports) {
                if(StringUtils.isBlank(bo.getLanguage())) {
                    //语言为空时默认英语
                    en_US = bo.getTotal();
                    break;
                }
            }

            for(OracleOperatorOnlineLanguageBO bo : reports) {
                if(StringUtils.isNotBlank(bo.getLanguage())) {
                    ReportLanguageDayBO report = new ReportLanguageDayBO();
                    report.setLanguage(bo.getLanguage());
                    report.setCount(StringUtils.compare(bo.getLanguage(), LanguageEnum.EN_US.getCode())==0 ? bo.getTotal() + en_US : bo.getTotal());
                    result.add(report);
                }
            }
        }
        return result;
    }

}
