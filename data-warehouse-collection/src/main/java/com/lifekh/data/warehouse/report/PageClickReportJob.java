package com.lifekh.data.warehouse.report;

import cn.hutool.json.JSONUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.*;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
@ElasticJobConf(name = "report_click_job", cron = "0 0 1 * * ?", description = "标准点击事件清洗定时任务",shardingTotalCount = 1)
public class PageClickReportJob extends AbstractSimpleJob {

    @Autowired
    private ClickReportStrategy clickReportStrategy;

    @Autowired
    private ClickTwoLayerReportStrategy clickTwoLayerReportStrategy;

    @Autowired
    private InviteRegisterReportStrategy inviteRegisterReportStrategy;

    @Autowired
    private InviteRegisterActivityNoReportStrategy inviteRegisterActivityNoReportStrategy;

    @Autowired
    private ActivityInviteReportStrategy activityInviteReportStrategy;

    @Autowired
    private ChannelReportStrategy channelReportStrategy;

    @Autowired
    private TargetReportService targetReportService;

//    @Autowired
//    private LocationReportStrategy locationReportStrategy;

    @Autowired
    private LanguageReportStrategy languageReportStrategy;

    @Autowired
    private AdsClickReportStrategy adsClickReportStrategy;

    @Autowired
    private SearchHotWordReportStrategy searchHotWordReportStrategy;

    @Autowired
    private HomePageClickReportStrategy homePageClickReportStrategy;

    @Value("${wownow.homePage:}")
    private String homePage;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("report_click_job,开始执行,{}",shardingContext);

        this.initParam();

        //标准事件统计
        clickReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        log.info("report_click_job,标准事件统计统计完成");

        //基于标准事件并且分为两级目录统计
        clickTwoLayerReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        log.info("report_click_job,基于标准事件并且分为两级目录统计完成");

        //邀请拉新统计
        inviteRegisterReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        log.info("report_click_job,邀请拉新统计完成");

        //邀请拉新统计 活动
        inviteRegisterActivityNoReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        log.info("report_click_job,邀请拉新活动统计完成");

        //统计运营活动报表（实时拉新，邀请有礼）
        activityInviteReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());

        //渠道每日数据统计
        channelReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());

        //统计指标数据（数据看板）
        targetReportService.staticTargetData();

        // 地区每日数据统计
//        locationReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        languageReportStrategy.statisticsOpenAppCount();

        //业务线活跃报表
        languageReportStrategy.statisticsBusinessActiveUser();

        //统计广告点击数
        adsClickReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());

        //统计专题搜索/热搜词报表
        searchHotWordReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());

        //统计首页各节点点击率
        homePageClickReportStrategy.statistics(shardingContext.getShardingItem(), shardingContext.getShardingTotalCount());
        log.info("report_click_job,执行结束,{}",shardingContext);
    }

    private void initParam() {
        if (StringUtils.isNotBlank(homePage)) {
            AbstractReportBasic.WOWNOW_HOME_PAGE_ARR = homePage.split(",");
        }
        log.info("WOWNOW首页名称数组: {}", JSONUtil.toJsonStr(AbstractReportBasic.WOWNOW_HOME_PAGE_ARR));
    }
}
