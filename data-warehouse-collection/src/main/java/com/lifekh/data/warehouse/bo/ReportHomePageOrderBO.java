package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_HOME_PAGE_ORDER)
@Data
public class ReportHomePageOrderBO implements Serializable {

    private Date createTime;
    public static final String CREATE_TIME = "createTime";

    private Date dataTime;
    public static final String DATA_TIME = "dataTime";

    private String language;
    public static final String LANGUAGE = "language";

    private Integer homePagePv;
    public static final String HOME_PAGE_PV = "homePagePv";

    private Integer homePageUv;
    public static final String HOME_PAGE_UV = "homePageUv";

    private Integer homePageDv;
    public static final String HOME_PAGE_DV = "homePageDv";

    private String businessLine;
    public static final String BUSINESS_LINE = "businessLine";

    private String businessName;
    public static final String BUSINESS_NAME = "businessName";

    private Integer totalLanguageOrderCount;
    public static final String TOTAL_LANGUAGE_ORDER_COUNT = "totalLanguageOrderCount";

    private Integer totalOrderCount;
    public static final String TOTAL_ORDER_COUNT = "totalOrderCount";

    private Integer homePageOrderCount;
    public static final String HOME_PAGE_ORDER_COUNT = "homePageOrderCount";

    private Long completeOrderCount;
    public static final String COMPLETEL_ORDER_COUNT = "completeOrderCount";

//    private Integer totalOrderUserCount;
//    public static final String TOTAL_ORDER_USER_COUNT = "totalOrderUserCount";
//
//    private Integer homePageOrderUserCount;
//    public static final String HOME_PAGE_ORDER_USER_COUNT = "homePageOrderUserCount";
//
//    private Integer completeOrderUserCount;
//    public static final String COMPLETE_ORDER_USER_COUNT = "completeOrderUserCount";
}
