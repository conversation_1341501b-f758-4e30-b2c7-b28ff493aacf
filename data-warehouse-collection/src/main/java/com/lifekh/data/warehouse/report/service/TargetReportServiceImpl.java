package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.bo.collection.ReportPVBO;
import com.lifekh.data.warehouse.bo.device.DevicePoolBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.DwdUserBehaviorDTO;
import com.lifekh.data.warehouse.dto.TargetBaseReqDTO;
import com.lifekh.data.warehouse.enums.ReportTargetEnum;
import com.lifekh.data.warehouse.oracle.dao.OracleAggregateOrderDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.lifekh.data.warehouse.constant.ClickReportConstant.WOWNOW_CLICK_TOTAL;

@Slf4j
@Service
public class TargetReportServiceImpl implements TargetReportService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Autowired
    private OracleAggregateOrderDAO oracleAggregateOrderDAO;

    @Override
    public void staticTargetData() {
        log.info("开始统计指标数据");
        try {
            //留存用户数统计 迁到 UserDataStaticReportJob
//            this.staticRemainUserCount(Arrays.asList(1, 7, 30));

            //用户平均邀请数统计（邀请有礼）
            this.staticAvgInviteActivity(Collections.singletonList(7));

            //邀请转化率（邀请有礼）
            this.staticInviteActivityRate(Collections.singletonList(7));

            //下单用户数统计
            this.staticOrderUserCount(Arrays.asList(1, 7, 30));

            //统计首页下单转化率
            totalOrderConversionRatePV(ClickReportConstant.YUNNOW_ORDER, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_RATE);
            totalOrderConversionRatePV(ClickReportConstant.YUNNOW_ORDER_ZH, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_ZH_RATE);
            totalOrderConversionRatePV(ClickReportConstant.YUNNOW_ORDER_EN, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_EN_RATE);
            totalOrderConversionRatePV(ClickReportConstant.YUNNOW_ORDER_KM, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_KM_RATE);
            totalOrderConversionRateLanguagePV(ClickReportConstant.YUNNOW_ORDER_ZH, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_ZH_LANGUAGE_RATE, LanguageEnum.ZH_CN.getCode());
            totalOrderConversionRateLanguagePV(ClickReportConstant.YUNNOW_ORDER_EN, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_EN_LANGUAGE_RATE, LanguageEnum.EN_US.getCode());
            totalOrderConversionRateLanguagePV(ClickReportConstant.YUNNOW_ORDER_KM, Arrays.asList(1, 7), ReportTargetEnum.YUMNOW_ORDER_KM_LANGUAGE_RATE, LanguageEnum.KM_KH.getCode());
            totalOrderConversionRatePV(ClickReportConstant.TINHNOW_ORDER, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_RATE);
            totalOrderConversionRatePV(ClickReportConstant.TINNOW_ORDER_ZH, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_ZH_RATE);
            totalOrderConversionRatePV(ClickReportConstant.TINNOW_ORDER_EN, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_EN_RATE);
            totalOrderConversionRatePV(ClickReportConstant.TINNOW_ORDER_KM, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_KM_RATE);
            totalOrderConversionRateLanguagePV(ClickReportConstant.TINNOW_ORDER_ZH, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_ZH_LANGUAGE_RATE, LanguageEnum.ZH_CN.getCode());
            totalOrderConversionRateLanguagePV(ClickReportConstant.TINNOW_ORDER_EN, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_EN_LANGUAGE_RATE, LanguageEnum.EN_US.getCode());
            totalOrderConversionRateLanguagePV(ClickReportConstant.TINNOW_ORDER_KM, Arrays.asList(1, 7), ReportTargetEnum.TINHNOW_ORDER_KM_LANGUAGE_RATE, LanguageEnum.KM_KH.getCode());
            homePageOrderConversionRateUV(ClickReportConstant.HOME_PAGE_ORDER, Arrays.asList(1), ReportTargetEnum.HOME_PAGE_ORDER_RATE);

            // 整体概览-下单转化率
            this.staticOverviewOrderConversionRateUV(Arrays.asList(1));

        } catch (Exception e) {
            log.error("统计指标数据出现异常", e);
        }
        log.info("指标数据统计结束");
    }

    @Override
    public void staticActiveUserCount(List<Integer> daysList) {
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO remainTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.ACTIVE_USER_COUNT.getCode())
                        .setTargetName(getActiveName(days))
                        .setDays(days)
                        .setCount(this.getActiveUserCount(reqDTO))
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(remainTarget);
            } catch (Exception e) {
                log.error("统计留存用户数出现异常, days: {}", days, e);
            }
        });
    }

    private String getActiveName(Integer days) {
        return ReportTargetEnum.ACTIVE_USER_COUNT.getMessage() +"_" + days + "天";
    }

    @Override
    public void staticActiveUserStickiness() {
        Integer day = 1, month = 30;
        ReportTargetBO monActiveNum = null;
        //统计用户粘性值
        try {
            TargetBaseReqDTO dayReqDTO = this.getTargetReq(day);
            Query dayQuery = new Query();
            dayQuery.addCriteria(Criteria.where(ReportTargetBO.DATA_TIME).gte(dayReqDTO.getStartTime()).lte(dayReqDTO.getEndTime())
                    .and(ReportTargetBO.DAYS).is(day).and(ReportTargetBO.TARGET_NAME).is(getActiveName(day)));
            ReportTargetBO dayActiveNum = mongoTemplate.findOne(dayQuery, ReportTargetBO.class);

            Query monthQuery = new Query();
            monthQuery.addCriteria(Criteria.where(ReportTargetBO.DATA_TIME).gte(dayReqDTO.getStartTime()).lte(dayReqDTO.getEndTime())
                    .and(ReportTargetBO.DAYS).is(month).and(ReportTargetBO.TARGET_NAME).is(getActiveName(month)));
            monActiveNum = mongoTemplate.findOne(monthQuery,ReportTargetBO.class);

            if(dayActiveNum != null && dayActiveNum.getCount() != null
                    && monActiveNum != null && monActiveNum.getCount() != null) {
                ReportTargetBO targetBO = new ReportTargetBO()
                        .setDataTime(dayReqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.USER_STICKINESS.getCode())
                        .setTargetName(ReportTargetEnum.USER_STICKINESS.getMessage())
                        .setDays(1)
                        .setCount(dayActiveNum.getCount() / monActiveNum.getCount())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());

                if(monActiveNum.getCount() <= 0) {
                    targetBO.setCount(0D);
                } else {
                    targetBO.setCount(dayActiveNum.getCount() / monActiveNum.getCount());
                }
                mongoTemplate.insert(targetBO);
            }
        } catch (Exception e) {
            log.error("统计用户粘性值异常", e);
        }

        //统计用户平均登录次数
        try {
            TargetBaseReqDTO monthReqDTO = this.getTargetReq(month);

            //累加30天活跃
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where(ReportTargetBO.DATA_TIME).gte(monthReqDTO.getStartTime()).lte(monthReqDTO.getEndTime())
                            .and(ReportTargetBO.DAYS).is(day).and(ReportTargetBO.TARGET_NAME).is(getActiveName(day))),
                    Aggregation.group(ReportTargetBO.TARGET_NAME)
                            .sum(ReportTargetBO.COUNT).as(ReportTargetBO.COUNT)
            );
            AggregationResults<ReportTargetBO> sumDayActiveNum = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.REPORT_TARGET, ReportTargetBO.class);

            if(sumDayActiveNum != null && sumDayActiveNum.getMappedResults().get(0) != null
                    && sumDayActiveNum.getMappedResults().get(0).getCount() != null && monActiveNum != null && monActiveNum.getCount() != null) {
                ReportTargetBO targetBO = new ReportTargetBO()
                        .setDataTime(monthReqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.AVG_ACTIVE_MONTH.getCode())
                        .setTargetName(ReportTargetEnum.AVG_ACTIVE_MONTH.getMessage())
                        .setDays(1)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());

                if(monActiveNum.getCount() <= 0) {
                    targetBO.setCount(0D);
                } else {
                    targetBO.setCount(sumDayActiveNum.getMappedResults().get(0).getCount() / monActiveNum.getCount());
                }
                mongoTemplate.insert(targetBO);
            }
        } catch (Exception e) {
            log.error("统计用户平均登录次数异常", e);
        }
    }

    @Override
    public void statisticsLossUser() {
        try {
            //统计总用户数
            double totalUsers = (double) oracleUserOperatorLoginInfoDAO.queryTotalUsers();

            //30天内活跃用户数
            TargetBaseReqDTO reqDTO = this.getTargetReq(30);
            double thirtyDaysActiveUser = this.getActiveUserCount(reqDTO);

            //流失用户 = 总用户数 - 30天内活跃用户数
            double lossUserCount = totalUsers - thirtyDaysActiveUser;

            ReportTargetBO remainTarget = new ReportTargetBO()
                    .setDataTime(reqDTO.getDataTime())
                    .setTarget(ReportTargetEnum.LOSS_USER.getCode())
                    .setTargetName(ReportTargetEnum.LOSS_USER.getMessage())
                    .setDays(1)
                    .setCount(lossUserCount)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());
            mongoTemplate.insert(remainTarget);
        } catch (Exception e) {
            log.error("统计流失用户数出现异常", e);
        }
    }

    @Override
    public void statisticsSilentUsers(double silentUsers) {
        try {
            log.info("统计沉默用户数为：{}", silentUsers);
            TargetBaseReqDTO reqDTO = this.getTargetReq(1);
            ReportTargetBO remainTarget = new ReportTargetBO()
                    .setDataTime(reqDTO.getDataTime())
                    .setTarget(ReportTargetEnum.SILENT_USER.getCode())
                    .setTargetName(ReportTargetEnum.SILENT_USER.getMessage())
                    .setDays(1)
                    .setCount(silentUsers)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            Query query = new Query();
            query.addCriteria(Criteria.where("target").is(remainTarget.getTarget()).and("dataTime").is(remainTarget.getDataTime()));
            Update update = new Update();
            update.set("dataTime", remainTarget.getDataTime());
            update.set("target", remainTarget.getTarget());
            update.set("targetName", remainTarget.getTargetName());
            update.set("days", remainTarget.getDays());
            update.set("count", remainTarget.getCount());
            update.set("createTime", remainTarget.getCreateTime());
            update.set("updateTime", remainTarget.getUpdateTime());
            mongoTemplate.upsert(query, update, MongoDbCollectonName.REPORT_TARGET);
        } catch (Exception e) {
            log.error("统计沉默用户数出现异常", e);
        }
    }

    @Override
    public void statisticsRebackUsers(double rebackUsers) {
        try {
            log.info("统计回流用户数为：{}", rebackUsers);
            TargetBaseReqDTO reqDTO = this.getTargetReq(1);
            ReportTargetBO remainTarget = new ReportTargetBO()
                    .setDataTime(reqDTO.getDataTime())
                    .setTarget(ReportTargetEnum.REBACK_USER.getCode())
                    .setTargetName(ReportTargetEnum.REBACK_USER.getMessage())
                    .setDays(1)
                    .setCount(rebackUsers)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            Query query = new Query();
            query.addCriteria(Criteria.where("target").is(remainTarget.getTarget()).and("dataTime").is(remainTarget.getDataTime()));
            Update update = new Update();
            update.set("dataTime", remainTarget.getDataTime());
            update.set("target", remainTarget.getTarget());
            update.set("targetName", remainTarget.getTargetName());
            update.set("days", remainTarget.getDays());
            update.set("count", remainTarget.getCount());
            update.set("createTime", remainTarget.getCreateTime());
            update.set("updateTime", remainTarget.getUpdateTime());
            mongoTemplate.upsert(query, update, MongoDbCollectonName.REPORT_TARGET);
        } catch (Exception e) {
            log.error("统计回流用户数出现异常", e);
        }
    }

    private void homePageOrderConversionRateUV(String businessName, List<Integer> daysList, ReportTargetEnum reportTargetEnum) {
        for (Integer days : daysList) {
            try {
                //查询首页总数
                TargetBaseReqDTO getTargetReq = getTargetReq(days);
                Criteria criteria = new Criteria();
                criteria.and(ClickReportDayBO.DATA_TIME).gte(getTargetReq.getStartTime()).lte(getTargetReq.getEndTime())
                        .and(ClickReportDayBO.NODE_NAME).is(WOWNOW_CLICK_TOTAL);
                List<ClickReportDayBO> homePageResultList = mongoTemplate.find(new Query(criteria), ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_DAY);
                //求和
                ClickReportDayBO homePageResult = sumAggregationResultsOfUV(homePageResultList);

                //查询业务单量
                double orderUserCount = this.getOrderUserCount(getTargetReq);

                BigDecimal homePage;
                BigDecimal business = new BigDecimal(orderUserCount);
                if (homePageResult == null || homePageResult.getUv() == 0) {
                    return;
                }

                homePage = new BigDecimal(homePageResult.getUv());
                BigDecimal result = business.divide(homePage, 4, BigDecimal.ROUND_HALF_UP);

                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO orderTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(reportTargetEnum.getCode())
                        .setTargetName(reportTargetEnum.getMessage())
                        .setDays(days)
                        .setCount(result.doubleValue())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(orderTarget);
            } catch (Exception e) {
                log.error("统计下单转化率异常,businessName:{}, day:{}, reportTargetEnum:{}", businessName, days, reportTargetEnum, e);
            }
        }
    }

    private void totalOrderConversionRatePV(String businessName, List<Integer> daysList, ReportTargetEnum reportTargetEnum) {
        for (Integer days : daysList) {
            try {
                //查询首页总数
                TargetBaseReqDTO getTargetReq = getTargetReq(days);
                Criteria criteria = new Criteria();
                criteria.and(ClickReportDayBO.DATA_TIME).gte(getTargetReq.getStartTime()).lte(getTargetReq.getEndTime())
                        .and(ClickReportDayBO.NODE_NAME).is(WOWNOW_CLICK_TOTAL);
                List<ClickReportDayBO> homePageResultList = mongoTemplate.find(new Query(criteria), ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_DAY);
                //求和
                ClickReportDayBO homePageResult = sumAggregationResultsOfPV(homePageResultList);

                //查询业务单量
                Criteria criteriaBusiness = new Criteria();
                criteriaBusiness.and(ClickReportDayBO.DATA_TIME).gte(getTargetReq.getStartTime()).lte(getTargetReq.getEndTime())
                        .and(ClickReportDayBO.NODE_NAME).regex("^" + businessName);
                Query queryReportDayQuery = new Query();
                queryReportDayQuery.addCriteria(criteriaBusiness);
                List<ClickReportDayBO> businessResult = mongoTemplate.find(queryReportDayQuery, ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_DAY);

                //求和
                ClickReportDayBO businessBo = sumAggregationResultsOfPV(businessResult);

                BigDecimal homePage;
                BigDecimal business;
                if (homePageResult == null || homePageResult.getPv() == 0) {
                    return;
                }

                if (businessBo == null) {
                    business = new BigDecimal(0);
                } else {
                    business = new BigDecimal(businessBo.getPv());
                }

                homePage = new BigDecimal(homePageResult.getPv());
                BigDecimal result = business.divide(homePage, 4, BigDecimal.ROUND_HALF_UP);

                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO orderTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(reportTargetEnum.getCode())
                        .setTargetName(reportTargetEnum.getMessage())
                        .setDays(days)
                        .setCount(result.doubleValue())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(orderTarget);
            } catch (Exception e) {
                log.error("统计下单转化率异常,businessName:{}, day:{}, reportTargetEnum:{}", businessName, days, reportTargetEnum, e);
            }
        }
    }

    private void totalOrderConversionRateLanguagePV(String businessName, List<Integer> daysList, ReportTargetEnum reportTargetEnum, String language) {
        for (Integer days : daysList) {
            try {
                //查询首页总数
                TargetBaseReqDTO getTargetReq = getTargetReq(days);
                Criteria criteria = new Criteria();
                criteria.and(ClickReportDayBO.DATA_TIME).gte(getTargetReq.getStartTime()).lte(getTargetReq.getEndTime())
                        .and(ClickReportDayBO.NODE_NAME).is(WOWNOW_CLICK_TOTAL)
                        .and(ClickReportDayBO.LANGUAGE).is(language);
                List<ClickReportDayBO> homePageResultList = mongoTemplate.find(new Query(criteria), ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_LANGUAGE_DAY);
                //求和
                ClickReportDayBO homePageResult = sumAggregationResultsOfPV(homePageResultList);

                //查询业务单量
                Criteria criteriaBusiness = new Criteria();
                criteriaBusiness.and(ClickReportDayBO.DATA_TIME).gte(getTargetReq.getStartTime()).lte(getTargetReq.getEndTime())
                        .and(ClickReportDayBO.NODE_NAME).regex("^" + businessName);
                Query queryReportDayQuery = new Query();
                queryReportDayQuery.addCriteria(criteriaBusiness);
                List<ClickReportDayBO> businessResult = mongoTemplate.find(queryReportDayQuery, ClickReportDayBO.class, MongoDbCollectonName.REPORT_CLICK_DAY);

                //求和
                ClickReportDayBO businessBo = sumAggregationResultsOfPV(businessResult);

                BigDecimal homePage;
                BigDecimal business;
                if (homePageResult == null || homePageResult.getPv() == 0) {
                    return;
                }

                if (businessBo == null) {
                    business = new BigDecimal(0);
                } else {
                    business = new BigDecimal(businessBo.getPv());
                }

                homePage = new BigDecimal(homePageResult.getPv());
                BigDecimal result = business.divide(homePage, 4, BigDecimal.ROUND_HALF_UP);

                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO orderTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(reportTargetEnum.getCode())
                        .setTargetName(reportTargetEnum.getMessage())
                        .setDays(days)
                        .setCount(result.doubleValue())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(orderTarget);
            } catch (Exception e) {
                log.error("统计下单转化率异常,businessName:{}, day:{}, reportTargetEnum:{}", businessName, days, reportTargetEnum, e);
            }
        }
    }

    /**
     * 计算求和 pv
     *
     * @param list
     * @return
     */
    private ClickReportDayBO sumAggregationResultsOfPV(List<ClickReportDayBO> list) {
        long pv = 0;

        if (!CollectionUtils.isEmpty(list)) {
            //累加
            for (ClickReportDayBO bo : list) {
                pv = pv + bo.getPv();
            }
        }
        ClickReportDayBO reportBasicBO = new ClickReportDayBO();
        reportBasicBO.setPv(pv);
        return reportBasicBO;
    }

    /**
     * 计算求和 uv
     *
     * @param list
     * @return
     */
    private ClickReportDayBO sumAggregationResultsOfUV(List<ClickReportDayBO> list) {
        long uv = 0;

        if (!CollectionUtils.isEmpty(list)) {
            //累加
            for (ClickReportDayBO bo : list) {
                uv = uv + bo.getUv();
            }
        }
        ClickReportDayBO reportBasicBO = new ClickReportDayBO();
        reportBasicBO.setUv(uv);
        return reportBasicBO;
    }

    /**
     * 公共参数
     *
     * @param days
     * @return
     */
    private TargetBaseReqDTO getTargetReq(int days) {
        Date now = new Date();
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -days));
        Date endTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -1));
        return new TargetBaseReqDTO()
                .setDataDate(DateUtil.formatDate(endTime))
                .setDataTime(DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12))) //中午12点
                .setStartTime(startTime)
                .setEndTime(endTime);
    }

    /**
     * 留存用户数统计
     *
     * @param daysList
     */
    @Override
    public void staticRemainUserCount(List<Integer> daysList) {
        //留存用户数统计，计算公式 = 活跃用户数 - 新增用户数
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO remainTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.REMAIN_USER_COUNT.getCode())
                        .setTargetName(ReportTargetEnum.REMAIN_USER_COUNT.getMessage())
                        .setDays(days)
                        .setCount(this.getActiveUserCount(reqDTO) - this.getNewUserCount(reqDTO) + 0.0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(remainTarget);
            } catch (Exception e) {
                log.error("统计留存用户数出现异常, days: {}", days, e);
            }
        });
    }

    /**
     * 用户平均邀请数统计（邀请有礼）
     *
     * @param daysList
     */
    private void staticAvgInviteActivity(List<Integer> daysList) {
        //用户平均邀请数统计，计算公式 = 发出邀请总次数 / 发出邀请总用户数
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                double inviteCount = this.getActivityInviteCount(reqDTO);
                double inviteUserCount = this.getActivityInviteUserCount(reqDTO);
                double avgInviteCount = 0.0;
                if (inviteUserCount > 0) {
                    avgInviteCount = NumberUtil.div(inviteCount, inviteUserCount, 0);
                }
                ReportTargetBO avgTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.AVG_INVITE_ACTIVITY_COUNT.getCode())
                        .setTargetName(ReportTargetEnum.AVG_INVITE_ACTIVITY_COUNT.getMessage())
                        .setDays(days)
                        .setCount(avgInviteCount)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(avgTarget);
            } catch (Exception e) {
                log.error("用户平均邀请数统计出现异常, days: {}", days, e);
            }
        });
    }

    /**
     * 下单用户数统计
     *
     * @param daysList
     */
    @Override
    public void staticOrderUserCount(List<Integer> daysList) {
        //下单用户数统计
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO orderTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.ORDER_USER_COUNT.getCode())
                        .setTargetName(ReportTargetEnum.ORDER_USER_COUNT.getMessage())
                        .setDays(days)
                        .setCount(this.getOrderUserCount(reqDTO) + 0.0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(orderTarget);
            } catch (Exception e) {
                log.error("统计下单用户数出现异常, days: {}", days, e);
            }
        });
    }

    /**
     * 统计几日内的活跃用户数
     *
     * @param req
     * @return
     */
    private double getActiveUserCount(TargetBaseReqDTO req) {
//        Criteria criteria = new Criteria();
//        criteria.and("UPDATE_TIME").gte(req.getStartTime()).lte(req.getEndTime())
//                .and("PROJECT_NAME").is(AppIdEnum.SUPER_APP.getCode());
//        Aggregation aggregation = Aggregation.newAggregation(
//                Aggregation.match(criteria),
//                Aggregation.group("OPERATOR_NO"),
//                Aggregation.count().as(ReportTargetBO.COUNT)
//        );
//        AggregationResults<ReportTargetBO> results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.SUP_MOBILE_TOKEN, ReportTargetBO.class);
        Long count = oracleUserOperatorLoginInfoDAO.queryActiveUserByTime(req.getStartTime(), req.getEndTime());
//        List<ReportTargetBO> datas = results.getMappedResults();
        return count == null ? 0 : count + 0.0;
    }

    /**
     * 获取新增用户数
     *
     * @param req
     * @return
     */
    private long getNewUserCount(TargetBaseReqDTO req) {
//        Criteria criteria = new Criteria();
//        criteria.and("CREATE_TIME").gte(req.getStartTime()).lte(req.getEndTime());
//        criteria.and("APP_ID").is(AppIdEnum.SUPER_APP.getCode());
//        Query query = new Query(criteria);
//        return mongoTemplate.count(query, MongoDbCollectonName.USER_OPERATOR_INFO);
        return oracleUserOperatorLoginInfoDAO.queryNewUserByTime(req.getStartTime(), req.getEndTime());
    }

    /**
     * 获取发出邀请总次数（邀请有礼）
     *
     * @param req
     * @return
     */
    private double getActivityInviteCount(TargetBaseReqDTO req) {
        Criteria criteria = new Criteria();
        criteria.and("dataTime").gte(req.getStartTime()).lte(req.getEndTime());

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group().sum(ReportActivityInviteBO.CLICK_SHARE_COUNT).as(ReportTargetBO.COUNT)
        );
        AggregationResults<ReportTargetBO> results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.REPORT_ACTIVITY_INVITE, ReportTargetBO.class);
        List<ReportTargetBO> datas = results.getMappedResults();
        return datas.isEmpty() ? 0 : datas.get(0).getCount() + 0.0;
    }

    /**
     * 获取发出邀请总用户数（邀请有礼）
     *
     * @param req
     * @return
     */
    private double getActivityInviteUserCount(TargetBaseReqDTO req) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(req.getStartTime()).lte(req.getEndTime())
                .and(ClickReportConstant.EVENT).in(ClickReportConstant.INVITE_PAGE_CLICK_IMMEDIATELY_INVITED, ClickReportConstant.INVITE_PAGE_CLICK_SCANCODE)
                .and(ClickReportConstant.ACTIVITYNO).ne(null);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(ClickReportConstant.USERINFOBO_LOGIN_NAME),
                Aggregation.count().as(ReportTargetBO.COUNT))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<ReportTargetBO> results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.INVITE_REGISTER_RECORD, ReportTargetBO.class);
        List<ReportTargetBO> datas = results.getMappedResults();
        return datas.isEmpty() ? 0 : datas.get(0).getCount() + 0.0;
    }

    /**
     * 查询下单用户数
     *
     * @param req
     * @return
     */
    private double getOrderUserCount(TargetBaseReqDTO req) {
        //查询当天下单的用户id
//        Criteria criteria = new Criteria();
//        criteria.and("ORDER_TIME").gte(req.getStartTime()).lte(req.getEndTime());
//        criteria.and("AGGREGATE_ORDER_FINAL_STATE").is(11);
//        Aggregation agg = Aggregation.newAggregation(
//                Aggregation.match(criteria),
//                Aggregation.group("USER_ID"),
//                Aggregation.count().as(ReportTargetBO.COUNT))
//                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
//        AggregationResults<ReportTargetBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.AGGREGATE_ORDER, ReportTargetBO.class);
//        List<ReportTargetBO> datas = results.getMappedResults();
        Long count = oracleAggregateOrderDAO.queryOrderUserByTime(req.getStartTime(), req.getEndTime());
        return count == null ? 0 : count + 0.0;
    }


    /**
     * 整体概览-下单转换率
     *
     * @param daysList
     */
    @Override
    public void staticOverviewOrderConversionRateUV(List<Integer> daysList) {
        //下单用户数统计
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                ReportTargetBO orderTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.OVERVIEW_ORDER_RATE.getCode())
                        .setTargetName(ReportTargetEnum.OVERVIEW_ORDER_RATE.getMessage())
                        .setDays(days)
                        .setCount(this.getOrderConversionRateUV(reqDTO) + 0.0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(orderTarget);
            } catch (Exception e) {
                log.error("统计整体概览-下单转换率出现异常, days: {}", days, e);
            }
        });
    }

    /**
     * 整体概览-下单转换率
     *
     * @param req
     * @return
     */
    private double getOrderConversionRateUV(TargetBaseReqDTO req) {
        // 下单人数
        double orderUserCount = this.getOrderUserCount(req);
        // 活跃用户
        Query queryActiveUser = new Query();
        queryActiveUser.addCriteria(Criteria.where("searchStaticTime")
                .gte(req.getStartTime())
                .lt(req.getEndTime()));
        ActiveUserStaticBO activeUserStaticBO = mongoTemplate.findOne(queryActiveUser, ActiveUserStaticBO.class, MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);

        BigDecimal ouc = new BigDecimal(orderUserCount);
        BigDecimal auc = new BigDecimal(activeUserStaticBO != null ? activeUserStaticBO.getActiveUserNum() : 0);
        if (auc.doubleValue() == 0) {
            return auc.doubleValue();
        }
        return ouc.divide(auc, 4, BigDecimal.ROUND_HALF_UP).doubleValue();
        //return orderUserCount / activeUserCount;
    }

    /**
     * 邀请转化率（邀请有礼）
     *
     * @param daysList
     */
    private void staticInviteActivityRate(List<Integer> daysList) {
        //计算公式 = 注册用户总数 / 打开邀请链接人数
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);
                Criteria criteria = new Criteria();
                criteria.and("dataTime").gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime());

                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group().sum(ReportActivityInviteBO.REGISTER_PAGE_COUNT).as(ReportActivityInviteBO.REGISTER_PAGE_COUNT)
                                .sum(ReportActivityInviteBO.REGISTER_COUNT).as(ReportActivityInviteBO.REGISTER_COUNT)
                );
                AggregationResults<ReportActivityInviteBO> results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.REPORT_ACTIVITY_INVITE, ReportActivityInviteBO.class);
                List<ReportActivityInviteBO> datas = results.getMappedResults();
                double rate = 0.0;

                if (!datas.isEmpty()) {
                    ReportActivityInviteBO data = datas.get(0);
                    long pageCount = data.getRegisterPageCount();
                    long registerCount = data.getRegisterCount();
                    if (pageCount > 0) {
                        rate = NumberUtil.div(registerCount, pageCount, 4);
                    }
                }
                ReportTargetBO target = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.INVITE_ACTIVITY_RATE.getCode())
                        .setTargetName(ReportTargetEnum.INVITE_ACTIVITY_RATE.getMessage())
                        .setDays(days)
                        .setCount(rate)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(target);
            } catch (Exception e) {
                log.error("邀请转化率统计出现异常, days: {}", days, e);
            }
        });
    }

    @Override
    public void staticDeviceRegisterRate(List<Integer> daysList) {
        log.info("开始统计设备注册率");
        daysList.forEach(days -> {
            try {

                TargetBaseReqDTO reqDTO = this.getTargetReq(days);

                //新增设备数
                Criteria newCriteria = Criteria.where(DevicePoolBO.RECORD_TIME).gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime());
                long newDeviceCount = mongoTemplate.count(Query.query(newCriteria), MongoDbCollectonName.DEVICE_POOL);

                //注册设备数
                Criteria registerCriteria = Criteria.where(DevicePoolBO.USE_TIME).gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime());
                long registerDeviceCount = mongoTemplate.count(Query.query(registerCriteria), MongoDbCollectonName.DEVICE_POOL);

                //注册用户数
                long newUserCount = oracleUserOperatorLoginInfoDAO.queryNewUserByTime(reqDTO.getStartTime(), reqDTO.getEndTime());

                if (newDeviceCount > 0) {
                    //设备注册率 =  注册设备数 / 新增设备总数
                    double deviceRegisterRate = NumberUtil.div(registerDeviceCount, newDeviceCount, 4);
                    ReportTargetBO deviceRate = new ReportTargetBO()
                            .setDataTime(reqDTO.getDataTime())
                            .setTarget(ReportTargetEnum.DEVICE_REGISTER_RATE.getCode())
                            .setTargetName(ReportTargetEnum.DEVICE_REGISTER_RATE.getMessage())
                            .setDays(days)
                            .setCount(deviceRegisterRate)
                            .setCreateTime(new Date())
                            .setUpdateTime(new Date());
                    mongoTemplate.insert(deviceRate);

                    //用户账号注册率 =  新增注册账号数 / 新增设备总数
                    double userRegisterRate = NumberUtil.div(newUserCount, newDeviceCount, 4);
                    ReportTargetBO userRate = new ReportTargetBO()
                            .setDataTime(reqDTO.getDataTime())
                            .setTarget(ReportTargetEnum.USER_DEVICE_REGISTER_RATE.getCode())
                            .setTargetName(ReportTargetEnum.USER_DEVICE_REGISTER_RATE.getMessage())
                            .setDays(days)
                            .setCount(userRegisterRate)
                            .setCreateTime(new Date())
                            .setUpdateTime(new Date());
                    mongoTemplate.insert(userRate);
                }
            } catch (Exception e) {
                log.error("统计设备注册率出现异常, days: {}", days, e);
            }
        });
        log.info("结束统计设备注册率");
    }

    @Override
    public void staticHomeLoginGuideData(List<Integer> daysList) {
        log.info("开始统计首页登录引导数据");
        daysList.forEach(days -> {
            try {
                TargetBaseReqDTO reqDTO = this.getTargetReq(days);

                Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime())
                        .and(ClickReportConstant.BUSINESS_NAME).is(ClickReportConstant.HOME_LOGIN_GUIDE_BUSINESS);

                //点击数
                long clickCount = mongoTemplate.count(Query.query(criteria), MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK);

                //设备数
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(DevicePoolBO.DEVICE_ID))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<DevicePoolBO> devices = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK, DevicePoolBO.class).getMappedResults();
                long deviceCount = devices.size();

                //当天注册设备数
                long registerCount = 0;
                if (!devices.isEmpty()) {
                    List<String> deviceIds = devices.stream().map(DevicePoolBO::getDeviceId).collect(Collectors.toList());
                    Criteria deviceCriteria = Criteria.where(DevicePoolBO.USE_TIME).gte(reqDTO.getStartTime()).lte(reqDTO.getEndTime()).and(DevicePoolBO.DEVICE_ID).in(deviceIds);
                    registerCount = mongoTemplate.count(Query.query(deviceCriteria), MongoDbCollectonName.DEVICE_POOL);
                }


                ReportTargetBO clickCountTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.HOME_LOGIN_GUIDE_CLICK_COUNT.getCode())
                        .setTargetName(ReportTargetEnum.HOME_LOGIN_GUIDE_CLICK_COUNT.getMessage())
                        .setDays(days)
                        .setCount(clickCount + 0.0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(clickCountTarget);

                ReportTargetBO deviceCountTarget = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.HOME_LOGIN_GUIDE_DEVICE_COUNT.getCode())
                        .setTargetName(ReportTargetEnum.HOME_LOGIN_GUIDE_DEVICE_COUNT.getMessage())
                        .setDays(days)
                        .setCount(deviceCount + 0.0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(deviceCountTarget);

                ReportTargetBO registerRate = new ReportTargetBO()
                        .setDataTime(reqDTO.getDataTime())
                        .setTarget(ReportTargetEnum.HOME_LOGIN_GUIDE_REGISTER_RATE.getCode())
                        .setTargetName(ReportTargetEnum.HOME_LOGIN_GUIDE_REGISTER_RATE.getMessage())
                        .setDays(days)
                        .setCount(deviceCount > 0 ? NumberUtil.div(registerCount, deviceCount, 4) : 0)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());
                mongoTemplate.insert(registerRate);
            } catch (Exception e) {
                log.error("统计首页登录引导数据出现异常, days: {}", days, e);
            }
        });
        log.info("结束统计首页登录引导数据");
    }

    @Override
    public void save(ReportTargetEnum targetEnum, int days, Double count) {
        try {
            TargetBaseReqDTO reqDTO = this.getTargetReq(days);
            ReportTargetBO remainTarget = new ReportTargetBO()
                    .setDataTime(reqDTO.getDataTime())
                    .setTarget(targetEnum.getCode())
                    .setTargetName(targetEnum.getMessage())
                    .setDays(days)
                    .setCount(count)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            Query query = new Query();
            query.addCriteria(Criteria.where("target").is(remainTarget.getTarget()).and("dataTime").is(remainTarget.getDataTime()));
            Update update = new Update();
            update.set("dataTime", remainTarget.getDataTime());
            update.set("target", remainTarget.getTarget());
            update.set("targetName", remainTarget.getTargetName());
            update.set("days", remainTarget.getDays());
            update.set("count", remainTarget.getCount());
            update.set("createTime", remainTarget.getCreateTime());
            update.set("updateTime", remainTarget.getUpdateTime());
            mongoTemplate.upsert(query, update, MongoDbCollectonName.REPORT_TARGET);
        } catch (Exception e) {
            log.error("统计沉默用户数出现异常", e);
        }
    }
}
