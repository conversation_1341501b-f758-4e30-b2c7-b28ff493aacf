package com.lifekh.data.warehouse.enums;

import com.outstanding.framework.core.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportLanguageDataTypeEnum implements BaseEnum<ReportLanguageDataTypeEnum, String> {

    ACTIVE_USER_COUNT("active_user_count", "日活跃用户数"),
    ACTIVE_USER_COUNT_WEEK("active_user_count_week", "周活跃用户数"),
    ACTIVE_USER_COUNT_MONTH("active_user_count_month", "月活跃用户数"),
    OPEN_APP_COUNT("open_app_count", "打开APP数"),
    ;

    private String code;

    private String message;
}
