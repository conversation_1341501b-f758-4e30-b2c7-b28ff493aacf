package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.bo.OpenScreenStaticBO;
import com.lifekh.data.warehouse.bo.PopAdsStaticBO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.oracle.bo.OracleOpenScreenClickBO;
import com.lifekh.data.warehouse.oracle.bo.OraclePopAdsClickBO;
import com.lifekh.data.warehouse.oracle.dao.OracleAdsClickRecordDAO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class AdsClickReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OracleAdsClickRecordDAO oracleAdsClickRecordDAO;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO req) {
        try {
            this.staticOpenScreenCount(req);
        } catch (Exception e) {
            log.error("统计开屏广告点击数出现异常", e);
        }

        try {
            this.staticPopAdsCount(req);
        } catch (Exception e) {
            log.error("统计弹窗点击数出现异常", e);
        }

        return null;
    }

    /**
     * 统计开屏广告点击数
     *
     * @param req
     */
    private void staticOpenScreenCount(ReportBasicReqDTO req) {
        List<OpenScreenStaticBO> staticList = new ArrayList<>();
        List<OracleOpenScreenClickBO> datas = oracleAdsClickRecordDAO.queryOpenScreenClick(req.getBeginTime(), req.getEndTime());
        datas.forEach(d -> {
            OpenScreenStaticBO staticBO = new OpenScreenStaticBO()
                    .setDataTime(req.getDataTime())
                    .setAdNo(d.getAdNo())
                    .setAdName(d.getAdName())
                    .setImageUrl(d.getImageUrl())
                    .setLanguage(d.getLanguage())
                    .setCount(d.getCount())
                    .setCreateTime(new Date());
            staticList.add(staticBO);
        });

        if (!staticList.isEmpty()) {
            mongoTemplate.insertAll(staticList);
        }
    }

    /**
     * 统计弹窗点击数
     *
     * @param req
     */
    private void staticPopAdsCount(ReportBasicReqDTO req) {
        List<PopAdsStaticBO> staticList = new ArrayList<>();
        List<OraclePopAdsClickBO> datas = oracleAdsClickRecordDAO.queryPopAdsClick(req.getBeginTime(), req.getEndTime());
        datas.forEach(d -> {
            PopAdsStaticBO staticBO = new PopAdsStaticBO()
                    .setDataTime(req.getDataTime())
                    .setPopNo(d.getPopNo())
                    .setPopName(d.getPopName())
                    .setZhImgUrl(d.getZhImgUrl())
                    .setCount(d.getCount())
                    .setCreateTime(new Date());
            staticList.add(staticBO);
        });

        if (!staticList.isEmpty()) {
            mongoTemplate.insertAll(staticList);
        }
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
