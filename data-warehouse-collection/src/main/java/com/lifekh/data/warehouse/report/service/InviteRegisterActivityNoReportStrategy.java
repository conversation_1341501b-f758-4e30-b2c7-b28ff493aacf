package com.lifekh.data.warehouse.report.service;

import cn.hutool.core.collection.CollectionUtil;
import com.chaos.common.enums.AppIdEnum;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.req.OperatorInfoQueryReqDTO;
import com.chaos.usercenter.api.dto.resp.OperatorBatchQueryDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.InviteRegisterButNotOpenAppBO;
import com.lifekh.data.warehouse.bo.InviteRegisterReportDayBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.page.MongoPageHelper;
import com.lifekh.data.warehouse.page.PageReqDTO;
import com.lifekh.data.warehouse.page.PageRespDTO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class InviteRegisterActivityNoReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoPageHelper mongoPageHelper;

    private static int pageSize = 500;

    @Autowired
    private UserOperatorFacade userOperatorFacade;

    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //统计数据
        inviteRegisterClickDayStatistics(reportBasicReqDTO);
        log.info("统计日带活动编号拉新邀请数据完成,时间:{}",reportBasicReqDTO);

        //统计注册页注册用户打开APP数
        registerOpenAppStatistics(reportBasicReqDTO);
        log.info("统计日带活动编号注册页注册用户打开APP数据完成,时间:{}",reportBasicReqDTO);

        //汇总统计，即每天累加
        summary(reportBasicReqDTO);
        log.info("统计日带活动编号汇总统计数据完成,时间:{}",reportBasicReqDTO);

        return null;
    }

    private List<AggregationOperation> getRegisterPageOpenAppAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        List<AggregationOperation> querList = new ArrayList<>();
        Criteria criteria = new Criteria();
        criteria.and(InviteRegisterButNotOpenAppBO.UPDATE_TIME).gte(reportBasicReqDTO.getBeginTime());
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    public void registerOpenAppStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        int pageSize = 100;
        //构造查询条件 PV
        List<AggregationOperation> queryList = getRegisterPageOpenAppAggregationCriteria(reportBasicReqDTO);

        for (int page=0; ;page ++) {
            PageRespDTO<InviteRegisterButNotOpenAppBO> pageRespDTO = mongoPageHelper.query(
                    queryList, new PageReqDTO(page, pageSize), MongoDbCollectonName.INVITE_REGISTER_BUT_NOT_OPEN_APP, true, null, InviteRegisterButNotOpenAppBO.class);

            if(CollectionUtil.isEmpty(pageRespDTO.getList())){
                break;
            }

            for (InviteRegisterButNotOpenAppBO notOpenApp : pageRespDTO.getList()) {
                try {
                    //根据登录是否有打开APP
                    long openApp;
                    openApp = statisticsCheckOpenApp(reportBasicReqDTO, notOpenApp, ClickReportConstant.PRE_REGISTER_OPENAPP);
                    if(openApp < 1) {
                        openApp = statisticsCheckOpenApp(reportBasicReqDTO, notOpenApp, ClickReportConstant.REGISTER_OPENAPP);
                    }

                    //查询当前打开APP事件的数据
                    OperatorInfoQueryReqDTO reqDTO = new OperatorInfoQueryReqDTO();
                    reqDTO.setLoginName(notOpenApp.getLoginName());
                    reqDTO.setAppId(AppIdEnum.SUPER_APP.getCode());
                    OperatorBatchQueryDTO loginDto = userOperatorFacade.queryOperatorInfo(reqDTO);
                    if(loginDto == null || StringUtils.isBlank(loginDto.getOperatorNo())) {
                        continue;
                    }

                    //清洗用户的注册来源
                    long count;
                    count = statisticsRegisterSource(reportBasicReqDTO, notOpenApp, ClickReportConstant.PRE_REGISTER_SUCCESS);
                    if(count < 1) {
                        statisticsRegisterSource(reportBasicReqDTO, notOpenApp, ClickReportConstant.REGISTER_SUCCESS);
                    }
                } catch (Exception e) {
                    log.error("打开APP事件统计异常, reportBasicReqDTO:{}, notOpenApp:{}", reportBasicReqDTO, notOpenApp, e);
                }
            }
        }
    }

    private long statisticsRegisterSource(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterButNotOpenAppBO notOpenApp, String even) {
        //查询当前打开APP事件的数据
        Query queryReportDay = new Query();
        queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(InviteRegisterReportDayBO.EVEN).is(even)
                .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(ClickReportConstant.REGISTER_PAGE_REGISTER_SUCCESS.get(even))
                .and(InviteRegisterReportDayBO.LANGUAGE).is(notOpenApp.getLanguage())
                .and(InviteRegisterReportDayBO.FROM).is(notOpenApp.getFrom())
                .and(InviteRegisterReportDayBO.ACTIVITYNO).is(notOpenApp.getActivityNo())
                .and(InviteRegisterReportDayBO.CHANNEL).is(notOpenApp.getChannel()));
        InviteRegisterReportDayBO reportDayBO = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY);

        if(reportDayBO == null) {
            return 0;
        }

        long count = 1;
        if(reportDayBO.getCount() != null) {
            count = reportDayBO.getCount() + 1;
        }

        //修改
        mongoTemplate.upsert(queryReportDay,
                getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO,
                        even, ClickReportConstant.REGISTER_PAGE_REGISTER_SUCCESS.get(even),
                        reportDayBO.getChannel(),notOpenApp.getFrom(), notOpenApp.getActivityNo(), count, count, notOpenApp.getLanguage()),
                MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY);
        return count;
    }

    private long statisticsCheckOpenApp(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterButNotOpenAppBO notOpenApp, String even) {
        Query queryOpenApp = new Query();
        queryOpenApp.addCriteria(Criteria.where(ClickReportConstant.CREATE_TIME)
                .gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime())
                .and(ClickReportConstant.USERINFOBO_LOGIN_NAME).is(notOpenApp.getLoginName())
                .and(ClickReportConstant.EVENT).is(ClickReportConstant.FIRST_OPEN_EVEN));
        long openAppCount =  mongoTemplate.count(queryOpenApp, MongoDbCollectonName.INVITE_REGISTER_RECORD);

        //大于0则说明有打开APP
        if(openAppCount > 0 ) {
            //查询当前打开APP事件的数据
            Query queryReportDay = new Query();
            queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                    .gte(reportBasicReqDTO.getBeginTime())
                    .lt(reportBasicReqDTO.getEndTime())
                    .and(InviteRegisterReportDayBO.EVEN).is(ClickReportConstant.FIRST_OPEN_EVEN)
                    .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(ClickReportConstant.OPEN_APP_SUCCESS.get(even))
                    .and(InviteRegisterReportDayBO.LANGUAGE).is(notOpenApp.getLanguage())
                    .and(InviteRegisterReportDayBO.FROM).is(notOpenApp.getFrom())
                    .and(InviteRegisterReportDayBO.ACTIVITYNO).is(notOpenApp.getActivityNo())
                    .and(InviteRegisterReportDayBO.CHANNEL).is(notOpenApp.getChannel()));
            InviteRegisterReportDayBO reportDayBO = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY);

            long count = 1;
            if(reportDayBO != null && reportDayBO.getCount() != null) {
                count = reportDayBO.getCount() + 1;
            }

            //修改
            mongoTemplate.upsert(queryReportDay,
                    getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO,
                            ClickReportConstant.FIRST_OPEN_EVEN, ClickReportConstant.OPEN_APP_SUCCESS.get(even),
                            notOpenApp.getChannel(),notOpenApp.getFrom(), notOpenApp.getActivityNo(), count, count, notOpenApp.getLanguage()),
                    MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY);
        }
        return openAppCount;
    }

    private void summary(ReportBasicReqDTO reportBasicReqDTO) {
        int pageSize = 100;
        //构造查询条件 PV
        List<AggregationOperation> queryList = getSummaryRegisterPageOpenAppAggregationCriteria(reportBasicReqDTO);

        for (int page=0; ;page ++) {
            PageRespDTO<InviteRegisterReportDayBO> pageRespDTO = mongoPageHelper.query(
                    queryList, new PageReqDTO(page, pageSize), MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY, true, null,InviteRegisterReportDayBO.class);

            if(CollectionUtil.isEmpty(pageRespDTO.getList())){
                break;
            }

            for (InviteRegisterReportDayBO reportDayBO : pageRespDTO.getList()) {
                try {
                    //查询当前打开APP事件的数据
                    Query queryReportDay = new Query();
                    queryReportDay.addCriteria(Criteria.where(InviteRegisterReportDayBO.EVEN).is(reportDayBO.getEven())
                            .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(reportDayBO.getBusinessName())
                            .and(InviteRegisterReportDayBO.LANGUAGE).is(reportDayBO.getLanguage())
                            .and(InviteRegisterReportDayBO.FROM).is(reportDayBO.getFrom())
                            .and(InviteRegisterReportDayBO.ACTIVITYNO).is(reportDayBO.getActivityNo())
                            .and(InviteRegisterReportDayBO.CHANNEL).is(reportDayBO.getChannel()));
                    InviteRegisterReportDayBO summaryBo = mongoTemplate.findOne(queryReportDay, InviteRegisterReportDayBO.class, MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_SUMMERY);

                    Long pvCount = reportDayBO.getCount();
                    Long uvCount = reportDayBO.getUv();
                    if(summaryBo != null) {
                        pvCount = reportDayBO.getCount() + summaryBo.getCount();
                        uvCount = reportDayBO.getUv() + summaryBo.getUv();
                    }

                    mongoTemplate.upsert(queryReportDay,
                            getInviteRegisterOpenAppUpdateBo(reportBasicReqDTO, reportDayBO.getEven(), reportDayBO.getBusinessName(), reportDayBO.getChannel(), reportDayBO.getFrom(), reportDayBO.getActivityNo(), pvCount, uvCount, reportDayBO.getLanguage()),
                            MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_SUMMERY);

                } catch (Exception e) {
                    log.warn("打开APP事件统计异常, reportBasicReqDTO:{}, reportDayBO:{}", reportBasicReqDTO, reportDayBO, e);
                }
            }
        }
    }

    private List<AggregationOperation> getSummaryRegisterPageOpenAppAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        List<AggregationOperation> querList = new ArrayList<>();
        Criteria criteria = new Criteria();
        criteria.and(InviteRegisterReportDayBO.DATA_TIME).gte(reportBasicReqDTO.getBeginTime()).lt(reportBasicReqDTO.getEndTime());
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }



    /**
     * 构造查询条件
     *
     * @return Aggregation
     */
    private List<AggregationOperation> getInviteRegisterAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.EVENT, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.FROM, ClickReportConstant.LANGUAGE, ClickReportConstant.EXT_CHANNEL, ClickReportConstant.ACTIVITYNO)
                .first(ClickReportConstant.EVENT).as(InviteRegisterReportDayBO.EVEN)
                .first(ClickReportConstant.BUSINESS_NAME).as(InviteRegisterReportDayBO.BUSINESS_NAME)
                .first(ClickReportConstant.FROM).as(InviteRegisterReportDayBO.FROM)
                .first(ClickReportConstant.LANGUAGE).as(InviteRegisterReportDayBO.LANGUAGE)
                .first(ClickReportConstant.EXT_CHANNEL).as(InviteRegisterReportDayBO.CHANNEL)
                .first(ClickReportConstant.ACTIVITYNO).as(InviteRegisterReportDayBO.ACTIVITYNO)
                .count().as(InviteRegisterReportDayBO.COUNT));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private void inviteRegisterClickDayStatistics(ReportBasicReqDTO reportBasicReqDTO) {
        //构造查询条件 PV
        List<AggregationOperation> aggregationlist = getInviteRegisterAggregationCriteria(reportBasicReqDTO);

        Long total = null;
        for (int pageNum = 0; ;pageNum++) {
            PageRespDTO<InviteRegisterReportDayBO> pageRespDTO = mongoPageHelper.query(
                    aggregationlist, new PageReqDTO(pageNum, pageSize), MongoDbCollectonName.INVITE_REGISTER_RECORD, true, total, InviteRegisterReportDayBO.class);
            total = pageRespDTO.getTotal();
            if(CollectionUtil.isEmpty(pageRespDTO.getList())){
                break;
            }

            //3.入库
            for (InviteRegisterReportDayBO reportDayBo : pageRespDTO.getList()) {
                try {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(InviteRegisterReportDayBO.DATA_TIME)
                            .gte(reportBasicReqDTO.getBeginTime())
                            .lt(reportBasicReqDTO.getEndTime())
                            .and(InviteRegisterReportDayBO.EVEN).is(reportDayBo.getEven())
                            .and(InviteRegisterReportDayBO.BUSINESS_NAME).is(reportDayBo.getBusinessName())
                            .and(InviteRegisterReportDayBO.FROM).is(reportDayBo.getFrom())
                            .and(InviteRegisterReportDayBO.LANGUAGE).is(reportDayBo.getLanguage())
                            .and(InviteRegisterReportDayBO.ACTIVITYNO).is(reportDayBo.getActivityNo())
                            .and(InviteRegisterReportDayBO.CHANNEL).is(reportDayBo.getChannel()));
                    reportDayBo.setDataTime(reportBasicReqDTO.getDataTime());

                    //查询uv
                    Long uv = mongoPageHelper.queryTotal(getUvAggregationCriteria(reportBasicReqDTO, reportDayBo), MongoDbCollectonName.INVITE_REGISTER_RECORD, true);
                    reportDayBo.setUv(uv);

                    //更新总表
                    mongoTemplate.upsert(query, getInviteRegisterUpdateBo(reportDayBo), MongoDbCollectonName.REPORT_INVITE_REGISTER_ACTIVITY_DAY);
                } catch (Exception e) {
                    log.warn("邀请拉新事件统计异常, reportBasicReqDTO:{}, reportDayBo:{}", reportBasicReqDTO, reportDayBo, e);
                }
            }
        }
    }

    /**
     * 构造查询条件
     *
     * @param reportBasicReqDTO 时间
     * @return Aggregation
     */
    private List<AggregationOperation> getUvAggregationCriteria(ReportBasicReqDTO reportBasicReqDTO, InviteRegisterReportDayBO reportDayBo) {
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.CREATE_TIME).gte(reportBasicReqDTO.getBeginTime())
                .lt(reportBasicReqDTO.getEndTime());
        criteria.and(ClickReportConstant.EVENT).is(reportDayBo.getEven());
        criteria.and(ClickReportConstant.BUSINESS_NAME).is(reportDayBo.getBusinessName());
        criteria.and(ClickReportConstant.LANGUAGE).is(reportDayBo.getLanguage());
        criteria.and(ClickReportConstant.FROM).is(reportDayBo.getFrom());
        criteria.and(ClickReportConstant.ACTIVITYNO).is(reportDayBo.getActivityNo());
        criteria.and(ClickReportConstant.CHANNEL).is(reportDayBo.getChannel());
        criteria.andOperator(new Criteria().orOperator(
                Criteria.where(ClickReportConstant.USERINFOBO_LOGIN_NAME).ne(null)),
                criteria.and(ClickReportConstant.USERINFOBO_LOGIN_NAME).ne(""));

        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group(ClickReportConstant.EVENT, ClickReportConstant.BUSINESS_NAME, ClickReportConstant.LANGUAGE, ClickReportConstant.FROM, ClickReportConstant.CHANNEL, ClickReportConstant.ACTIVITYNO, ClickReportConstant.USERINFOBO_LOGIN_NAME)
                .first(ClickReportConstant.EVENT).as(InviteRegisterReportDayBO.EVEN)
                .first(ClickReportConstant.BUSINESS_NAME).as(InviteRegisterReportDayBO.BUSINESS_NAME)
                .first(ClickReportConstant.LANGUAGE).as(InviteRegisterReportDayBO.LANGUAGE)
                .first(ClickReportConstant.FROM).as(InviteRegisterReportDayBO.FROM)
                .first(ClickReportConstant.CHANNEL).as(InviteRegisterReportDayBO.CHANNEL)
                .first(ClickReportConstant.ACTIVITYNO).as(InviteRegisterReportDayBO.ACTIVITYNO)
                .first(ClickReportConstant.USERINFOBO_LOGIN_NAME).as(ClickReportConstant.LOGIN_NAME)
                .count().as(InviteRegisterReportDayBO.COUNT));
        querList.add(Aggregation.sort(Sort.Direction.DESC, InviteRegisterReportDayBO.ID));
        return querList;
    }

    private static Update getInviteRegisterUpdateBo(InviteRegisterReportDayBO reportDayB0) {
        Update update = new Update();
        update.set(InviteRegisterReportDayBO.UPDATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.CREATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.EVEN, reportDayB0.getEven());
        update.set(InviteRegisterReportDayBO.BUSINESS_NAME, reportDayB0.getBusinessName());
        update.set(InviteRegisterReportDayBO.CHANNEL, reportDayB0.getChannel());
        update.set(InviteRegisterReportDayBO.LANGUAGE, reportDayB0.getLanguage());
        update.set(InviteRegisterReportDayBO.DATA_TIME, reportDayB0.getDataTime());
        update.set(InviteRegisterReportDayBO.COUNT, reportDayB0.getCount() == null ? 0 : reportDayB0.getCount());
        update.set(InviteRegisterReportDayBO.UV, reportDayB0.getUv() == null ? 0 : reportDayB0.getUv());
        return update;
    }

    private static Update getInviteRegisterOpenAppUpdateBo(ReportBasicReqDTO reqDTO, String even, String businessName, String channel, String from, String activityNo, Long pvCount, Long uvCount, String language) {
        Update update = new Update();
        update.set(InviteRegisterReportDayBO.UPDATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.CREATE_TIME, new Date());
        update.set(InviteRegisterReportDayBO.EVEN, even);
        update.set(InviteRegisterReportDayBO.BUSINESS_NAME, businessName);
        update.set(InviteRegisterReportDayBO.CHANNEL, channel);
        update.set(InviteRegisterReportDayBO.DATA_TIME, reqDTO.getDataTime());
        update.set(InviteRegisterReportDayBO.COUNT, pvCount == null ? 0 : pvCount);
        update.set(InviteRegisterReportDayBO.UV, uvCount == null ? 0 : uvCount);
        update.set(InviteRegisterReportDayBO.FROM, from);
        update.set(InviteRegisterReportDayBO.ACTIVITYNO, activityNo);
        update.set(InviteRegisterReportDayBO.LANGUAGE, language);
        return update;
    }


    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
