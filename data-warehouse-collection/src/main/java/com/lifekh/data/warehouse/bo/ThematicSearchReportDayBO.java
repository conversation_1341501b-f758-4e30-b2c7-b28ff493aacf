package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_THEMATIC_SEARCH)
@Data
public class ThematicSearchReportDayBO implements Serializable {

    public static String COLLECTION_NAME = MongoDbCollectonName.REPORT_THEMATIC_SEARCH;

    private static final long serialVersionUID = -2599035028656451031L;

    public static String ID = "_id";

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 专题名称
     */
    private String thematicName;
    public static String THEMATICNAME = "thematicName";

    /**
     * 搜索内容
     */
    private String content;
    public static String CONTENT = "content";

    /**
     * 数据日期
     */
    private Date dataTime;

    public static String DATA_TIME = "dataTime";


    /**
     * PV
     */
    private Long pv;

    public static String PV = "pv";

    /**
     * 语言
     */
    private String language;

    public static String LANGUAGE = "language";
}
