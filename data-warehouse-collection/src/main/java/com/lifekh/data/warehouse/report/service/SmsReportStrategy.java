package com.lifekh.data.warehouse.report.service;

import com.chaos.usercenter.api.enums.VerifyCodeSendMethodEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.ReportSmsType;
import com.lifekh.data.warehouse.bo.ReportSmsBizCountBO;
import com.lifekh.data.warehouse.bo.ReportSmsChannelRateBO;
import com.lifekh.data.warehouse.bo.ReportSmsVerifyRateBO;
import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;
import com.lifekh.data.warehouse.mysql.bo.MessageTemplateBO;
import com.lifekh.data.warehouse.mysql.bo.SmsRecordMidgroundBO;
import com.lifekh.data.warehouse.oracle.bo.UserOperatorVerifyCodeBO;
import com.lifekh.data.warehouse.mysql.dao.MysqlMessageTemplateDAO;
import com.lifekh.data.warehouse.mysql.dao.MysqlSmsRecordMidgroundDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorVerifyCodeDAO;
import com.lifekh.data.warehouse.report.AbstractReportBasic;
import com.lifekh.data.warehouse.report.strategy.ReportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SmsReportStrategy extends AbstractReportBasic implements ReportStrategy {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MysqlSmsRecordMidgroundDAO mysqlSmsRecordMidgroundDAO;

    @Autowired
    private OracleUserOperatorVerifyCodeDAO oracleUserOperatorVerifyCodeDAO;

    @Autowired
    private MysqlMessageTemplateDAO mysqlMessageTemplateDAO;


    @Override
    public ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO) {
        //短信发送业务数统计
        reportSmsCount(reportBasicReqDTO);

        //短信发送成和失败
        reportSmsSuccessAndFail(reportBasicReqDTO);

        //验证成功率
        reportVerifyRate(reportBasicReqDTO);

        //短信发送渠道验证率
        reportChannelRate(reportBasicReqDTO);
        return null;
    }


    /**
     * 短信发送渠道验证率
     *
     * @param reportBasicReqDTO
     */
    public void reportChannelRate(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            List<UserOperatorVerifyCodeBO> list = oracleUserOperatorVerifyCodeDAO.queryChannelRate(reportBasicReqDTO.getBeginTime(), reportBasicReqDTO.getEndTime());

            //取出模板编号
            Set<String> templateNoSet = list.stream().map(UserOperatorVerifyCodeBO::getTemplateNo).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(templateNoSet)) {
                log.warn("模板编号为空,{}", templateNoSet);
                return;
            }
            //根据模板编号查询模板表
            List<MessageTemplateBO> templateList = mysqlMessageTemplateDAO.findByTemplateNo(new ArrayList<>(templateNoSet));
            Map<String, String> templateMap = templateList.stream().collect(Collectors.toMap(MessageTemplateBO::getTemplateNo, MessageTemplateBO::getTemplateName));

            list.forEach(bo -> {
                if (StringUtils.isBlank(bo.getSendChannel()) || StringUtils.isBlank(bo.getTemplateNo())) {
                    log.warn("模板编号或者发送渠道为空,{}", bo);
                    return;
                }

                //入库
                mongoTemplate.save(new ReportSmsChannelRateBO().setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setDataTime(reportBasicReqDTO.getDataTime())
                        .setName(templateMap.get(bo.getTemplateNo()))
                        .setUsedCount(bo.getUsedCount())
                        .setNotUsedCount(bo.getNotUsedCount())
                        .setSendChannel(bo.getSendChannel())
                        .setRate(calculateRate(bo.getUsedCount(), bo.getCount()))
                        .setCarrierName(bo.getCarrierName())
                        .setCount(bo.getCount()), MongoDbCollectonName.REPORT_SMS_CHANNEL_RATE);
            });
        } catch (Exception e) {
            log.error("短信发送渠道验证率异常", e);
        }
    }

    private Double calculateRate(Integer newUserNum, Integer remainNum) {
        if (newUserNum == null || newUserNum == 0 || remainNum == null || remainNum == 0) {
            return 0.0;
        }
        BigDecimal a = new BigDecimal(newUserNum);
        BigDecimal b = new BigDecimal(remainNum);
        return a.divide(b, 4, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 短信发送业务数统计
     *
     * @param reportBasicReqDTO
     */
    private void reportSmsCount(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            List<SmsRecordMidgroundBO> listQueryBizCount = mysqlSmsRecordMidgroundDAO.queryBizCount(reportBasicReqDTO.getBeginTime(), reportBasicReqDTO.getEndTime());
            if (CollectionUtils.isNotEmpty(listQueryBizCount)) {
                listQueryBizCount.forEach(bo -> {
                    mongoTemplate.save(new ReportSmsBizCountBO().setCreateTime(new Date())
                            .setUpdateTime(new Date())
                            .setDataTime(reportBasicReqDTO.getDataTime())
                            .setName(bo.getBiz())
                            .setType(ReportSmsType.BIZ.getCode())
                            .setCount(bo.getCount()), MongoDbCollectonName.REPORT_SMS_BIZ_COUNT);
                });
            }
        } catch (Exception e) {
            log.error("短信发送次数统计异常", e);
        }
    }

    /**
     * 短信发送成和失败
     *
     * @param reportBasicReqDTO
     */
    private void reportSmsSuccessAndFail(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            //发送成功通道统计
            //select SUCCES_CHANNEL, count(1) from lifekh_mp_ords.SMS_RECORD_MIDGROUND srm  where srm.CREATE_TIME > to_date('2022-12-25','yyyy-mm-dd') and srm.CREATE_TIME < to_date('2022-12-28','yyyy-mm-dd') and srm.SUCCES_CHANNEL is not null group by srm.SUCCES_CHANNEL ;
            List<SmsRecordMidgroundBO> successReports = mysqlSmsRecordMidgroundDAO.queryChannelSuccessCount(reportBasicReqDTO.getBeginTime(), reportBasicReqDTO.getEndTime());

            //发送失败通道统计
            //select FAIL_CHANNEL, count(1) from lifekh_mp_ords.SMS_RECORD_MIDGROUND srm  where srm.CREATE_TIME > to_date('2022-12-25','yyyy-mm-dd') and srm.CREATE_TIME < to_date('2022-12-28','yyyy-mm-dd') and srm.FAIL_CHANNEL is not null group by srm.FAIL_CHANNEL;
            List<SmsRecordMidgroundBO> failReports = mysqlSmsRecordMidgroundDAO.queryChannelFailCount(reportBasicReqDTO.getBeginTime(), reportBasicReqDTO.getEndTime());
            successReports.forEach(bo -> {
                mongoTemplate.save(new ReportSmsBizCountBO().setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setDataTime(reportBasicReqDTO.getDataTime())
                        .setName(bo.getChannel())
                        .setType(ReportSmsType.CHANNEL_SUCCESS.getCode())
                        .setCount(bo.getCount()), MongoDbCollectonName.REPORT_SMS_BIZ_COUNT);
            });

            failReports.forEach(bo -> {
                mongoTemplate.save(new ReportSmsBizCountBO().setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setDataTime(reportBasicReqDTO.getDataTime())
                        .setName(bo.getChannel())
                        .setType(ReportSmsType.CHANNEL_FAIL.getCode())
                        .setCount(bo.getCount()), MongoDbCollectonName.REPORT_SMS_BIZ_COUNT);
            });
        } catch (Exception e) {
            log.error("短信成功和失败数统计异常", e);
        }
    }

    /**
     * 验证成功率
     *
     * @param reportBasicReqDTO
     */
    private void reportVerifyRate(ReportBasicReqDTO reportBasicReqDTO) {
        try {
            List<UserOperatorVerifyCodeBO> verifyReports = oracleUserOperatorVerifyCodeDAO.querySmsCount(reportBasicReqDTO.getBeginTime(), reportBasicReqDTO.getEndTime());
            ReportSmsVerifyRateBO rateBO = new ReportSmsVerifyRateBO();
            if (CollectionUtils.isNotEmpty(verifyReports)) {
                verifyReports.forEach(bo -> {
                    if (VerifyCodeSendMethodEnum.SMS.getCode().equals(bo.getMethod())) {
                        if (bo.getUsed().equals(1)) {
                            rateBO.setSmsSuccessCount(Optional.ofNullable(bo.getCount()).orElse(0));
                        } else {
                            rateBO.setSmsFailCount(Optional.ofNullable(bo.getCount()).orElse(0));
                        }
                    } else if (VerifyCodeSendMethodEnum.VOIP.getCode().equals(bo.getMethod())) {
                        if (bo.getUsed().equals(1)) {
                            rateBO.setVoipSuccessCount(Optional.ofNullable(bo.getCount()).orElse(0));
                        } else {
                            rateBO.setVoipFailCount(Optional.ofNullable(bo.getCount()).orElse(0));
                        }
                    }
                });

                rateBO.setCreateTime(reportBasicReqDTO.getDataTime());
                rateBO.setUpdateTime(reportBasicReqDTO.getDataTime());
                rateBO.setName("短信验证总数");
                rateBO.setSmsSuccessCount(Optional.ofNullable(rateBO.getSmsSuccessCount()).orElse(0));
                rateBO.setSmsFailCount(Optional.ofNullable(rateBO.getSmsFailCount()).orElse(0));
                rateBO.setVoipSuccessCount(Optional.ofNullable(rateBO.getVoipSuccessCount()).orElse(0));
                rateBO.setVoipFailCount(Optional.ofNullable(rateBO.getVoipFailCount()).orElse(0));
                rateBO.setSmsTotalCount(rateBO.getSmsSuccessCount() + rateBO.getSmsFailCount());
                rateBO.setVoipTotalCount(rateBO.getVoipSuccessCount() + rateBO.getVoipFailCount());
                rateBO.setDataTime(reportBasicReqDTO.getDataTime());
                mongoTemplate.save(rateBO, MongoDbCollectonName.REPORT_SMS_VERIFY_RATE);
            }
        } catch (Exception e) {
            log.error("短信验证率统计异常", e);
        }
    }

    @Override
    public ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }

    @Override
    public ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO) {
        return null;
    }
}
