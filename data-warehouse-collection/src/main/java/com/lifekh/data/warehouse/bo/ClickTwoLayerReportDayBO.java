package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_CLICK_TWO_LAYER_DAY)
@Data
public class ClickTwoLayerReportDayBO implements Serializable {
    private static final long serialVersionUID = -1901703107097113580L;

    @Id
    private ObjectId id;

    public static String ID = "_id";

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 更新日期
     */
    private Date updateTime;

    public static String UPDATE_TIME = "updateTime";

    /**
     * 页面名称
     */
    private String firstName;

    public static String FIRST_NAME = "firstName";


    /**
     * 区域名称
     */
    private String secondName;

    public static String SECOND_NAME = "secondName";

    /**
     * 别名
     */
    private String alias;

    public static String ALIAS = "alias";

    /**
     * 全称
     */
    private String fullName;

    public static String FULL_NAME = "fullName";

    /**
     * 数据日期
     */
    private Date dataTime;

    public static String DATA_TIME = "dataTime";

    /**
     * FUV
     */
    private Long fuv;

    public static String FUV = "fuv";

    /**
     * FPV
     */
    private Long fpv;

    public static String FPV = "fpv";

    /**
     * FDV
     */
    private Long fdv;

    public static String FDV = "fdv";

    /**
     * SUV
     */
    private Long suv;

    public static String SUV = "suv";

    /**
     * SPV
     */
    private Long spv;

    public static String SPV = "spv";

    /**
     * SDV
     */
    private Long sdv;

    public static String SDV = "sdv";
}
