package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReportBasicBO implements Serializable {

    private static final long serialVersionUID = 8143779708576256056L;

    public static String ID = "_id";

    /**
     * 创建日期
     */
    private Date createTime;

    public static String CREATE_TIME = "createTime";

    /**
     * 更新日期
     */
    private Date updateTime;

    public static String UPDATE_TIME = "updateTime";

    /**
     * 页面名称
     */
    private String pageName;

    public static String PAGE_NAME = "pageName";


    /**
     * 区域名称
     */
    private String cardName;

    public static String CARD_NAME = "cardName";

    /**
     * 节点名称
     */
    private String nodeName;

    public static String NODE_NAME = "nodeName";

    /**
     * 别名
     */
    private String alias;

    public static String ALIAS = "alias";

    /**
     * 全称
     */
    private String fullName;

    public static String FULL_NAME = "fullName";

    /**
     * 数据日期
     */
    private Date dataTime;

    public static String DATA_TIME = "dataTime";

    /**
     * UV
     */
    private Long uv;

    public static String UV = "uv";

    /**
     * PV
     */
    private Long pv;

    public static String PV = "pv";

    /**
     * DV
     */
    private Long dv;

    public static String DV = "dv";

    /**
     * 语言
     */
    private String language;

    public static String LANGUAGE = "language";
}
