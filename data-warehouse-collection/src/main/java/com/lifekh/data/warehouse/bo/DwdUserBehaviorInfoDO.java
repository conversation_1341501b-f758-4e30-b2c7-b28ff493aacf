package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;

@Document(collection = MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO)
@Data
public class DwdUserBehaviorInfoDO {

    @Id
    private ObjectId id;

    public static String ID = "_id";

    private Date registerTime;

    public static String REGISTER_TIME = "registerTime";

    private String operatorNo;
    public static String OPERATOR_NO = "operatorNo";

    private String mobile;
    public static String MOBILE = "mobile";

    private String gender;
    public static String GENDER = "gender";

    private Date birthday;
    public static String BIRTHDAY = "birthday";

    /**
     * 纬度
     */
    private Float latitude;
    public static String LATITUDE = "latitude";

    /**
     * 经度
     */
    private Float longitude;
    public static String LONGITUDE = "longitude";

    private String province;
    public static String PROVINCE = "province";

    private String district;
    public static String DISTRICT = "district";

    private Date onlineTime;
    public static String ONLINE_TIME = "onlineTime";

    private String language;
    public static String LANGUAGE = "language";

    private Integer appNo;
    public static String APP_NO = "appNo";

    private String appVersion;
    public static String APP_VERSION = "appVersion";

    private String deviceId;
    public static String DEVICE_ID = "deviceId";

    private String udDeviceId;
    public static String UD_DEVICE_ID = "udDeviceId";

    private String phoneModel;
    public static String PHONE_MODEL = "phoneModel";

    /**
     * 序列
     */
    private Long seq;
    public static String SEQ = "seq";

    /**
     * 用户等级
     */
    private Integer opLevel;
    public static String OP_LEVEL = "opLevel";

    /**
     * 用户等级名称
     */
    private String levelName;
    public static String LEVEL_NAEM = "levelName";

    /**
     * 年龄
     */
    private Integer age;
    public static String AGE = "age";


    /**
     * 操作员状态
     */
    private String operatorStatus;
    public static String OPERATOR_STATUS = "operatorStatus";

    private String loginMethod;
    public static String LOGIN_METHOD = "loginMethod";

    /**
     * 注册纬度
     */
    private Float regLatitude;

    public static String REG_LATITUDE = "regLatitude";

    /**
     * 注册经度
     */
    private Float regLongitude;

    public static String REG_LONGITUDE = "regLongitude";


    private String regProvince;
    public static String REG_PROVINCE = "regProvince";

    private String regDistrict;
    public static String REG_DISTRICT = "regDistrict";

    /**
     * 上一次在线时间
     */
    private Date lastOnlineTime;
    public static String LAST_ONLINE_TIME = "lastOnlineTime";
}
