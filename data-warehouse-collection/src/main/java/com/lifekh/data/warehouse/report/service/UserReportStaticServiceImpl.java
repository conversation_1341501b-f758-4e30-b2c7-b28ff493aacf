package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.UserRemainEnum;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorLoginMethodBO;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorRegisterMethodBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.mongodb.MongoTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/11/4 11:10
 * @Version 1.0
 **/
@Service("userReportStaticService")
@Slf4j
public class UserReportStaticServiceImpl implements UserReportStaticService{

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Override
    public void newUserReportStatic() {
//        Criteria criteria = new Criteria();
//        criteria.and("CREATE_TIME").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1))
//                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
//        criteria.and("APP_ID").is("SuperApp");
//        Query query = new Query(criteria);
//        long newUserNum = mongoTemplate.count(query, "USER_OPERATOR_INFO");

        Long newUserNum = oracleUserOperatorLoginInfoDAO.queryNewUserYesterday();
        NewUserStaticBO newUserStaticBO = new NewUserStaticBO();
        newUserStaticBO.setStaticDate(DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1)));
        newUserStaticBO.setNewUserNum(newUserNum);
        newUserStaticBO.setCreateTime(new Date());
        try {
            mongoTemplate.save(newUserStaticBO, MongoDbCollectonName.NEW_USER_STATIC);
        } catch (DataAccessResourceFailureException | MongoTimeoutException e) {
            log.warn("统计昨天注册用户超时补偿",e);
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (Exception e1) {
            }
            mongoTemplate.save(newUserStaticBO, MongoDbCollectonName.NEW_USER_STATIC);
        }
    }


    @Override
    public void activeUserReportStatic() {
        ActiveUserStaticBO activeUserStaticBO = new ActiveUserStaticBO();
        activeUserStaticBO.setCreateTime(new Date());
        Date searchStaticTime = DateUtil.getSearchStaticTime(new Date(),Calendar.DATE,-1);
        //统计上一天活跃
        long dayActiveNum = this.getActiveUserByDate(1);
        activeUserStaticBO.setActiveUserNum(dayActiveNum);
        activeUserStaticBO.setStaticDate(DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1)));
        activeUserStaticBO.setCreateTime(new Date());
        activeUserStaticBO.setSearchStaticTime(searchStaticTime);
        //查询上一日至对应月1号的活跃数
        int lastDayDateOfMonth = DateUtil.getOneDatePlus(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1));
        Long localMonthActiveNum = this.getActiveUserByDate(lastDayDateOfMonth);
        activeUserStaticBO.setLocalMonthActiveNum(localMonthActiveNum);
        //统计当前天到本周周一的日活数
        int toOneDayOfWeek = DateUtil.getMondayPlus();
        Long localWeekActiveNum = 0L;
        if (toOneDayOfWeek == 0){
            localWeekActiveNum = getActiveUserByDate(7);
        }else {
            localWeekActiveNum = getActiveUserByDate(toOneDayOfWeek);
        }
        activeUserStaticBO.setLocalWeekActiveNum(localWeekActiveNum);
        try {
            mongoTemplate.save(activeUserStaticBO, MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
        } catch (DataAccessResourceFailureException | MongoTimeoutException e) {
            log.warn("统计昨天日活用户超时补偿",e);
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (Exception e1) {
            }
            mongoTemplate.save(activeUserStaticBO, MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
        }

        if (toOneDayOfWeek == 0){
            //周一，统计上一周活跃数
            ActiveUserStaticBO weekActiveUserStaticBO = new ActiveUserStaticBO();
            String staticStartDate = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-7));
            String staticStartEnd = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1));
            String staticDate = staticStartDate + "/" + staticStartEnd;
            weekActiveUserStaticBO.setActiveUserNum(localWeekActiveNum);
            weekActiveUserStaticBO.setStaticDate(staticDate);
            weekActiveUserStaticBO.setCreateTime(new Date());
            weekActiveUserStaticBO.setSearchStaticTime(searchStaticTime);
            mongoTemplate.save(weekActiveUserStaticBO, MongoDbCollectonName.ACTIVE_USER_STATIC_WEEK);
        }
        //判断今天是否是1号
        if (DateUtil.getTodayNum() == 1){
            //统计上个月活跃用户
            Date lastMonthStartTime = DateUtil.getLastMonthStartTime();
            Date lastMonthEndTime = DateUtil.getLastMonthEndTime();
            Long lastActiveUser = this.countActiveUserByStartDateAndEndDate(lastMonthStartTime,lastMonthEndTime);
            String lastMonthStartTimeString = DateUtil.dataToString(lastMonthStartTime);
            String lastMonthEndTimeString = DateUtil.dataToString(lastMonthEndTime);
            String staticDate = lastMonthStartTimeString + "/" + lastMonthEndTimeString;
            ActiveUserStaticBO monthActiveUserStaticBO = new ActiveUserStaticBO();
            monthActiveUserStaticBO.setActiveUserNum(lastActiveUser);
            monthActiveUserStaticBO.setStaticDate(staticDate);
            monthActiveUserStaticBO.setCreateTime(new Date());
            monthActiveUserStaticBO.setSearchStaticTime(searchStaticTime);
            mongoTemplate.save(monthActiveUserStaticBO, MongoDbCollectonName.ACTIVE_USER_STATIC_MONTH);
            //更新上个月每天的月活跃数，用于粘度计算
            Criteria lastMonthCriteria = new Criteria();
            lastMonthCriteria.and("searchStaticTime").gte(lastMonthStartTime)
                    .lt(lastMonthEndTime);
            Query lastMonthQuery = new Query(lastMonthCriteria);
            List<ActiveUserStaticBO> lastMonthDaysList = mongoTemplate.find(lastMonthQuery,ActiveUserStaticBO.class,MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
            for (ActiveUserStaticBO activeUserStaticBO1:lastMonthDaysList){
                Query updateQuery = new Query();
                updateQuery.addCriteria(Criteria.where("_id").is(activeUserStaticBO1.getId()));
                Update update = new Update();
                update.set("localMonthActiveNum",lastActiveUser);
                mongoTemplate.updateMulti(updateQuery, update,MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
            }
        }
    }

    @Override
    public void userRemainReportStatic() {
        //统计1日留存
        this.countUserRemainByDay(UserRemainEnum.ONE);
        //统计2日留存
        this.countUserRemainByDay(UserRemainEnum.TWO);
        //统计3日留存
        this.countUserRemainByDay(UserRemainEnum.THREE);
        //统计4日留存
        this.countUserRemainByDay(UserRemainEnum.FOUR);
        //统计5日留存
        this.countUserRemainByDay(UserRemainEnum.FIVE);
        //统计6日留存
        this.countUserRemainByDay(UserRemainEnum.SIX);
        //统计7日留存
        this.countUserRemainByDay(UserRemainEnum.SEVEN);
        //统计15日留存
        this.countUserRemainByDay(UserRemainEnum.FIFTEEN);
        //统计30日留存
        this.countUserRemainByDay(UserRemainEnum.THIRTY);
    }

    @Override
    public void userLoginMethodReport() {
        List<OracleOperatorLoginMethodBO> reportList = oracleUserOperatorLoginInfoDAO.queryUserLoginMethodReport();
        if(CollectionUtils.isNotEmpty(reportList)) {
            Date dateTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);
            List<ReportOperatorLoginMethodBO> reports = new ArrayList<>();
            //计算总数
            int total = 0;
            for (OracleOperatorLoginMethodBO bo : reportList) {
                total = bo.getNum();
            }

            for(OracleOperatorLoginMethodBO bo : reportList) {
                ReportOperatorLoginMethodBO reportBo = new ReportOperatorLoginMethodBO();
                reportBo.setDataTime(dateTime);
                reportBo.setLoginMethod(bo.getLoginMethod());
                reportBo.setCount(bo.getNum());
                reportBo.setTotal(total);
                reportBo.setAppId(bo.getAppId());
                reportBo.setCreateTime(new Date());
                reports.add(reportBo);
            }

            //入库
            if(CollectionUtils.isNotEmpty(reports)) {
                mongoTemplate.insertAll(reports);
            }
        }
    }

    @Override
    public void userRegisterMethodReport() {
        List<OracleOperatorRegisterMethodBO> reportList = oracleUserOperatorLoginInfoDAO.queryUserRegisterMethodReport();
        if(CollectionUtils.isNotEmpty(reportList)) {
            Date dateTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);
            List<ReportOperatorRegisterMethodBO> reports = new ArrayList<>();
            //计算总数
            int total = 0;
            for (OracleOperatorRegisterMethodBO bo : reportList) {
                total = bo.getNum();
            }

            for(OracleOperatorRegisterMethodBO bo : reportList) {
                ReportOperatorRegisterMethodBO reportBo = new ReportOperatorRegisterMethodBO();
                reportBo.setDataTime(dateTime);
                reportBo.setRegisterMethod(Optional.ofNullable(bo.getRegisterMethod()).orElse("boss"));
                reportBo.setCount(bo.getNum());
                reportBo.setTotal(total);
                reportBo.setCreateTime(new Date());
                reports.add(reportBo);
            }

            //入库
            if(CollectionUtils.isNotEmpty(reports)) {
                mongoTemplate.insertAll(reports);
            }
        }
    }


    private Long getActiveUserByDate(Integer beforeDay){
        if (beforeDay < 0){
            beforeDay = Math.negateExact(beforeDay);
        }
        Date startTime = DateUtil.dateCalculation(new Date(), Calendar.DATE,-beforeDay);
        Date endTime = DateUtil.dateCalculation(new Date(), Calendar.DATE,-0);
        return this.countActiveUserByStartDateAndEndDate(startTime,endTime);
    }


    private Long countActiveUserByStartDateAndEndDate(Date startTime,Date endTime){
//        Criteria criteriaSum = new Criteria();
//        List<Criteria> criteriaList = new ArrayList<>();
//        Criteria criteria = new Criteria();
//        criteriaList.add(criteria.and("CREATE_TIME").gte(startTime)
//                .lt(endTime));
//        Criteria criteria2 = new Criteria();
//        criteriaList.add(criteria2.and("UPDATE_TIME").gte(startTime)
//                .lt(endTime));
//        criteriaSum.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
//        criteriaSum.and("PROJECT_NAME").is("SuperApp");
//        Query query = new Query(criteriaSum);
//        return mongoTemplate.count(query,"SUP_MOBILE_TOKEN");
        return oracleUserOperatorLoginInfoDAO.queryActiveUserByTime(startTime, endTime);
    }

    private void countUserRemainByDay(UserRemainEnum userRemainEnum){
//        Criteria criteria = new Criteria();
        //设置用户新增时间
//        criteria.and("CREATE_TIME").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,userRemainEnum.getBeforeDay()))
//                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,userRemainEnum.getBeforeDay() + 1));
//        criteria.and("APP_ID").is("SuperApp");
//        //结果筛选条件，result 必须与lookup的as的字符串相同
//        Criteria resultCriteria = new Criteria();
//        //每天统计前一天日活数据
//        resultCriteria.and("result.UPDATE_TIME").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1))
//                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
//        Aggregation aggregationCount = Aggregation.newAggregation(
//                Aggregation.match(criteria),
//                Aggregation.lookup(MongoDbCollectonName.SUP_MOBILE_TOKEN,"OPERATOR_NO","OPERATOR_NO","result"),
//                Aggregation.match(resultCriteria),
//                Aggregation.count().as("count")
//        );
//        AggregationResults<RemainUserAggregationBO> aggregate = mongoTemplate.aggregate(
//                aggregationCount,MongoDbCollectonName.USER_OPERATOR_INFO,RemainUserAggregationBO.class
//        );
        try {
            Long remainCount = oracleUserOperatorLoginInfoDAO.queryRemainUser(DateUtil.dateCalculation(new Date(), Calendar.DATE,userRemainEnum.getBeforeDay()), DateUtil.dateCalculation(new Date(), Calendar.DATE,userRemainEnum.getBeforeDay() + 1));
//        RemainUserAggregationBO remainUserAggregationBO = aggregate.getUniqueMappedResult();
            String staticDate = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,userRemainEnum.getBeforeDay()));
            Criteria staticCriteria = new Criteria();
            staticCriteria.and("staticDate").is(staticDate);
            Query staticQuery = new Query(staticCriteria);
            UserRemainStaticBO userRemainStaticBO = mongoTemplate.findOne(staticQuery,UserRemainStaticBO.class,MongoDbCollectonName.USER_REMAIN_STATIC);
            if (Objects.isNull(userRemainStaticBO)){
                Update update = new Update();
                //查询对应日期注册用户数
                Query newUserNumQuery = new Query(staticCriteria);
                NewUserStaticBO newUserStaticBO = mongoTemplate.findOne(newUserNumQuery,NewUserStaticBO.class,MongoDbCollectonName.NEW_USER_STATIC);
                update.set("newUserNum",newUserStaticBO == null ? 0 : newUserStaticBO.getNewUserNum());
                update.set(userRemainEnum.getCode(),remainCount != null ? remainCount : 0);
                Date searchStaticTime = DateUtil.getSearchStaticTime(new Date(),Calendar.DATE,userRemainEnum.getBeforeDay());
                update.set("searchStaticTime",searchStaticTime);
                update.set("createTime",new Date());
                update.set("updateTime",new Date());
                mongoTemplate.upsert(staticQuery, update, UserRemainStaticBO.class, MongoDbCollectonName.USER_REMAIN_STATIC);
            }else {
                Update update = new Update();
                update.set(userRemainEnum.getCode(),remainCount != null ? remainCount : 0);
                update.set("updateTime",new Date());
                mongoTemplate.upsert(staticQuery, update, UserRemainStaticBO.class, MongoDbCollectonName.USER_REMAIN_STATIC);
            }
        } catch (Exception e) {
            log.error("统计留存用户异常 userRemainEnum:{}", userRemainEnum, e);
        }
    }
}
