package com.lifekh.data.warehouse.report;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.dto.ReportBasicRespDTO;

/**
 * 统计接口
 */
public interface ReportBasic {

    /**
     * 统计昨天数据
     *
     * @param reportBasicReqDTO
     */
    ReportBasicRespDTO statisticsByDay(ReportBasicReqDTO reportBasicReqDTO);

    /**
     * 统计自然周数据
     *
     * @param reportBasicReqDTO
     */
    ReportBasicRespDTO statisticsByWeek(ReportBasicReqDTO reportBasicReqDTO);

    /**
     * 统计自然月数据
     *
     * @param reportBasicReqDTO
     */
    ReportBasicRespDTO statisticsByMonth(ReportBasicReqDTO reportBasicReqDTO);

    /**
     * 统计
     *
     * @param item
     * @param total
     */
    void statistics(Integer item, Integer total);
}
