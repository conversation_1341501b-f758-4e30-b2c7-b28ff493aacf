package com.lifekh.data.warehouse.report.service;

import com.lifekh.data.warehouse.enums.ReportTargetEnum;

import java.util.List;

/**
 * @Date: 2022/4/20
 * @Description: 指标统计服务，如昨天、过去7天、过去30天维度的指标
 */
public interface TargetReportService {

    /**
     * 统计指标数据
     */
    void staticTargetData();

    void staticActiveUserCount(List<Integer> daysList);

    /**
     * 统计用户粘性值
     */
    void staticActiveUserStickiness();

    /**
     * 统计流失用户
     */
    void statisticsLossUser();

    void statisticsSilentUsers(double silentUsers);

    void statisticsRebackUsers(double rebacUsers);

    void staticRemainUserCount(List<Integer> daysList);

    void staticOrderUserCount(List<Integer> daysList);

    void staticOverviewOrderConversionRateUV(List<Integer> daysList);

    /**
     * 统计设备注册率
     */
    void staticDeviceRegisterRate(List<Integer> daysList);

    /**
     * 统计首页登录引导
     */
    void staticHomeLoginGuideData(List<Integer> daysList);

    void save(ReportTargetEnum targetEnum, int days, Double count);
}
