package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.REPORT_DISCOVERY)
@Data
@Accessors(chain = true)
public class ReportDiscoveryBO implements Serializable {
    private static final long serialVersionUID = -6658614198998204395L;

    public static final String COUNT = "count";
    public static final String CONTENT_NO = "contentNo";
    public static final String CONTENT_TITLE = "contentTitle";
    public static final String CONTENT_LANGUAGE = "contentLanguage";
    public static final String CONTENT_BUSINESS_LINE = "contentBusinessLine";


    private Date dataTime;
    private Date createTime;
    private String dataType;
    private String dataTypeName;
    private Integer days;
    private String contentNo;
    private String contentTitle;
    private String contentLanguage;
    private String contentBusinessLine;
    private Integer count;

}
