package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleUserAddressBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

@DataSource(DataSourceType.LIFEKH_MP_CUSTOMER)
public interface OracleUserAddressDAO {

    @Select("SELECT * FROM USER_ADDRESS where ADDRESS_NO = #{addressNo, jdbcType=VARCHAR} and rownum = 1")
    @Results({
            @Result(property = "addressNo", column = "ADDRESS_NO"),
            @Result(property = "longitude", column = "LONGITUDE"),
            @Result(property = "latitude", column = "LATITUDE"),
            @Result(property = "provinceId", column = "PROVINCE_ID"),
            @Result(property = "cityId", column = "CITY_ID")
    })
    OracleUserAddressBO queryByAdddressNo(@Param("addressNo") String addressNo);

}
