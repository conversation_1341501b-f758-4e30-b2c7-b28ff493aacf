package com.lifekh.data.warehouse.aspect;

import com.lifekh.data.warehouse.config.route.DataBaseContextHolder;
import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
public class DataSourceSwitchAspect {

    @Pointcut("execution(* com.lifekh.data.warehouse.oracle.dao.*.*(..))")
    private void pointcut() {}


    @Around("pointcut()")
    public Object log(ProceedingJoinPoint joinPoint) throws Throwable {
        Object returnVal = null;
        try {
            //接口调用
            switchDataSource(joinPoint);
            returnVal = joinPoint.proceed();
        } catch (Throwable e) {
            log.warn("数据源切换失败:{}", e);
            throw e;
        }
        return returnVal;
    }

    private void switchDataSource(ProceedingJoinPoint joinPoint) {
        DataBaseContextHolder.setDataSourceType(getAnnotation(joinPoint));
    }

    private DataSourceType getAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        return targetMethod.getDeclaringClass().getDeclaredAnnotation(DataSource.class).value();
    }
}
