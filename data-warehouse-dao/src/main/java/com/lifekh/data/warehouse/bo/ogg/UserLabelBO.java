package com.lifekh.data.warehouse.bo.ogg;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
@Document(collection = MongoDbCollectonName.USER_LABEL)
@Data
public class UserLabelBO implements Serializable {

    @Field("ID")
    private Long id;

    @Field("CREATE_TIME")
    private Date createTime;

    @Field("UPDATE_TIME")
    private Date updateTime;

    @Field("DEL_STATE")
    private Integer delState;

    private Long version;

    @Field("LOGIN_NAME")
    private String loginName;

    @Field("OPERATOR_NO")
    private String operatorNo;

    @Field("APP_ID")
    private String appId;

    @Field("DEVICE_ID")
    private String deviceId;

    @Field("DEVICE_TYPE")
    private String deviceType;
    /**
     * IP
     */
    @Field("CLIENT_IP")
    private String clientIp;

    /**
     * WOWNOW用户端登陆次数
     */
    @Field("LOGIN_COUNT")
    private Integer loginCount;
    /**
     * 邀请渠道
     */
    @Field("INVITATION_SOURCE")
    private String invitationSource;
    /**
     * 邀请人编号
     */
    @Field("INVITATION_ACCOUNT")
    private String invitationAccount;
    /**
     * 邀请码
     */
    @Field("INVITATION_CODE")
    private String invitationCode;
    /**
     * 自己的邀请码
     */
    @Field("INVITATION_CODE_SELF")
    private String invitationCodeSelf;

    /**
     * 注册渠道
     */
    @Field("REGISTER_CHANNEL")
    private String registerChannel;

    /**
     * app语言
     */
    @Field("LANGUAGE")
    private String language;
}
