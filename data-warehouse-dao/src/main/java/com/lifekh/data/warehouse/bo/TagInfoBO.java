package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.enums.TagClassifyEnum;
import com.lifekh.data.warehouse.api.enums.TagStatusEnum;
import com.lifekh.data.warehouse.api.enums.TagTypeEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Document(collection = "tag_info")
@Data
public class TagInfoBO implements Serializable {
    public final static String TABLE_NAME = "tag_info";
    private static final long serialVersionUID = 7982534287905231004L;

    @Id
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;
    /**
     * 标签一级分类名
     */
    private String firstClassification;
    /**
     * 标签二级分类名
     */
    private String secondaryClassification;

    /**
     * 规则编号
     */
    private List<TagRuleBO> rule;
}
