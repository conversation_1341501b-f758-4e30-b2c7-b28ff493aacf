package com.lifekh.data.warehouse.bo.discovery;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.ADS_VIEW_LOG)
@Data
public class AdsViewLogBO implements Serializable {
    private static final long serialVersionUID = 432258804906957430L;

    public static final String ADS_NO = "adsNo";
    public static final String ADS_SUBJECT = "adsSubject";
    public static final String ADS_TYPE = "adsType";
    public static final String ADS_NAME = "adsName";
    public static final String ADS_TITLE = "adsTitle";
    public static final String USE_SCENE = "useScene";
    public static final String LANGUAGE = "language";
    public static final String CREATE_BY = "createBy";
    public static final String RECORD_TIME = "recordTime";
    public static final String LOGIN_NAME = "loginName";
    public static final String OPERATOR_NO = "operatorNo";
    public static final String DEVICE_ID = "deviceId";

    /**
     * 广告编号
     */
    private String adsNo;

    /**
     * 广告主体
     */
    private String adsSubject;

    /**
     * 广告类型
     */
    private String adsType;

    /**
     * 广告名称
     */
    private String adsName;

    /**
     * 广告标题
     */
    private String adsTitle;

    /**
     * 应用场景
     */
    private String useScene;

    /**
     * 语言
     */
    private String language;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 创建时间
     */
    private Date createTime;
}
