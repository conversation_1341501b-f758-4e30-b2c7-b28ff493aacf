package com.lifekh.data.warehouse.oracle.bo;

import com.outstanding.framework.core.DelStateEnum;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OracleUserOperatorLoginInfoBO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 乐观锁
     */
    private Integer version;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 默认更新时间
     */
    private Date updateTime;

    /**
     * 数据状态
     */
    private DelStateEnum delState;
    /**
     * 操作员编号
     */
    private String operatorNo;
    /**
     * 登陆号
     */
    private String loginName;
    /**
     * 登陆类型(MOBILE:手机号,EMAIL:邮箱,ACCOUNT:账号)
     */
    private String loginType;
    /**
     * 更新人
     */
    private String appId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 应用编号
     */
    private Integer appNo;

    /**
     * 易盾设备id
     */
    private String udDeviceId;

    /**
     * 手机型号
     */
    private String phoneModel;

    /**
     * 应用版本号
     */
    private String appVersion;

    private Date registerTime;

    private String gender;

    private Date birthday;

    /**
     * 操作员状态
     */
    private String operatorStatus;

    /**
     * 注册纬度
     */
    private Float regLatitude;

    /**
     * 注册经度
     */
    private Float regLongitude;

    /**
     * 在线时间
     */
    private Date onlineTime;

    /**
     * 上一次在线时间
     */
    private Date lastOnlineTime;

    /**
     * 语言
     */
    private String language;

    /**
     * 在线纬度
     */
    private Float onlineLatitude;

    /**
     * 在线经度
     */
    private Float onlineLongitude;
}
