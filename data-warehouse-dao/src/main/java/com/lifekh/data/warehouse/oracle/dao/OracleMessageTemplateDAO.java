package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleMessageTemplateBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataSource(DataSourceType.LIFEKH_MP_ORDS)
public interface OracleMessageTemplateDAO {

    @Select({"<script>",
                " SELECT ",
                " TEMPLATE_NO as templateNo, ",
                " TEMPLATE_NAME as templateName",
                " FROM MESSAGE_TEMPLATE WHERE TEMPLATE_TYPE = 'SMS' and LANGUAGE_TYPE  = 'en-US' and TEMPLATE_NO in ",
                "<foreach item='item' index='index' collection='templateNos' open='(' separator=',' close=')'>",
                    "#{item, jdbcType=VARCHAR}",
                "</foreach>",
            "</script>"})
    List<OracleMessageTemplateBO> findByTemplateNo(@Param("templateNos") List<String> templateNos);
}
