package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.ZoneBO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ZoneDAO extends MongoRepository<ZoneBO, String> {

    /**
     * 根据地区编码查询地区
     *
     * @param code
     * @return
     */
    ZoneBO findByCode(String code);

    /**
     * 根据地区英文名称查询编码
     *
     * @param msgEn
     * @return
     */
    List<ZoneBO> findByMsgEn(String msgEn);
}
