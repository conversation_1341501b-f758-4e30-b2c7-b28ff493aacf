package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.TagInfoV2BO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TagInfoV2DAO extends MongoRepository<TagInfoV2BO, String> {

    List<TagInfoV2BO> findByTagTypeAndTagStatus(String tagType, String tagStatus);



}
