package com.lifekh.data.warehouse.bo.device;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.DEVICE_NEW_STATIC_DAY)
@Data
@Accessors(chain = true)
public class DeviceNewStaticDay implements Serializable {
    private static final long serialVersionUID = 4869002710157218252L;

    public static final String DATA_DATE = "dataDate";

    @Id
    private String id;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 新增设备数量
     */
    private Integer deviceCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;
}
