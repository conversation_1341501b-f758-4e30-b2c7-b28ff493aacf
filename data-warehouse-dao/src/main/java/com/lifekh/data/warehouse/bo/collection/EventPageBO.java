package com.lifekh.data.warehouse.bo.collection;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Data
@Document(collection = MongoDbCollectonName.EVENT_PAGE)
public class EventPageBO implements Serializable {
    private static final long serialVersionUID = -2851571403318486484L;

    public static final String PAGE_CODE = "pageCode";
    public static final String PAGE_CLASSIFY = "pageClassify";

    @Id
    private String id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 页面标识
     */
    private String pageCode;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 页面分类
     */
    private String pageClassify;
}
