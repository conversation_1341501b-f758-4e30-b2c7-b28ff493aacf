package com.lifekh.data.warehouse.bo;

import com.outstanding.framework.core.AbstractObject;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Document(collection = "tag_user")
@Data
public class TagUserBO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 794111077367864976L;

    public final static String TABLE_NAME = "tag_user";

    public static final String TAG_NO = "tagNo";
    public static final String OPERATOR_NO = "operatorNo";
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String MOBILE = "mobile";
    public static final String NICKNAME = "nickname";

    @Id
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 标签编号
     */
    private List<String> tagNo;

}
