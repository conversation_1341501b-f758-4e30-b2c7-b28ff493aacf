package com.lifekh.data.warehouse.bo.ogg;

import com.outstanding.framework.core.DelStateEnum;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "USER_OPERATOR_LOGIN_INFO")
@Data
public class UserOperatorLoginInfoBO implements Serializable {

    private static final long serialVersionUID = 5503166675222115507L;

    @Field("ID")
    private Long id;

    @Field("CREATE_TIME")
    private Date createTime;

    @Field("UPDATE_TIME")
    private Date updateTime;

    @Field("DEL_STATE")
    private Integer delState;

    private Long version;

    @Field("OPERATOR_NO")
    private String operatorNo;

    @Field("LOGIN_NAME")
    private String loginName;

    @Field("LOGIN_TYPE")
    private String loginType;

    @Field("APP_ID")
    private String appId;
}
