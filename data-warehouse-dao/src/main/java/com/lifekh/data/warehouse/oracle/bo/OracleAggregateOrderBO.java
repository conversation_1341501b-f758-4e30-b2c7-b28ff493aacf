package com.lifekh.data.warehouse.oracle.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OracleAggregateOrderBO implements Serializable {
    //
//    @Field("ID")
    private Long id;

    private Long version;

    private Integer delState;

    private Date createTime;

    private Date updateTime;

    /**
     * 退款状态
     */
    private Integer refundOrderState;

    /**
     *
     */
    private Long changeNoTimes;

    /**
     * 聚合订单号
     */
    private String aggregateOrderNo;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 埋点定义的扩展字段
     */
    private String extensionInfo;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 展示图片地址
     */
    private String showUrl;

    private String businessContent;

    private String storeName;

    private String merchantNo;

    private String businessOrderId;

    private String businessLine;

    private Integer businessOrderState;

    private String notCheckAmount;

    private String remark;

    private String currency;

    private Integer commentOrderState;

    private BigDecimal actualPayAmount;

    private Integer aggregateOrderFinalState;

    private BigDecimal totalPayableAmount;

    private String payOrderNo;

    private BigDecimal basicAmount;

    private Integer aggregateOrderState;

    private Date orderTime;

    private String deviceId;

    private Integer orderCount;

    private String firstMerchantNo;

    private Integer orderType;

    private String addressNo;

    private String payChannel;

}
