package com.lifekh.data.warehouse.bo.collection;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = "report_pv_detail")
@Data
public class ReportPVByDayBO{

    @Id
    private ObjectId id;

    private Date createTime;

    private Date updateTime;

    /**
     * 天
     */
    private String day;

    /**
     * 访问数
     */
    private Long viewNum;

    /**
     * 设备数
     */
    private Long deviceNum;

    /**
     * 用户数
     */
    private Long userNum;

    /**
     * pv总表的ID
     */
    private ObjectId reportPVId;
}
