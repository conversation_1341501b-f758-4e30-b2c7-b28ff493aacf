package com.lifekh.data.warehouse.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "tag_zone")
public class ZoneBO implements Serializable {

    private static final long serialVersionUID = 3191683770247014189L;

    private String id;

    private Date createTime;

    private Date updateTime;

    private String msgEn;

    private String msgCb;

    private String msgZh;

    private String code;

    private Integer zLevel;
}
