package com.lifekh.data.warehouse.bo.ogg;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.bson.types.Decimal128;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
@Document(collection = MongoDbCollectonName.AGGREGATE_ORDER)
@Data
public class AggregateOrderBO implements Serializable {
//
//    @Field("ID")
    private Long id;

    @Field("VERSION")
    private Long version;

    @Field("DEL_STATE")
    private Integer delState;

    @Field("CREATE_TIME")
    private Date createTime;

    @Field("UPDATE_TIME")
    private Date updateTime;

    /**
     * 退款状态
     */
    private Integer refundOrderState;

    /**
     *
     */
    private Long changeNoTimes;

    /**
     * 聚合订单号
     */
    @Field("AGGREGATE_ORDER_NO")
    private String aggregateOrderNo;

    /**
     * 支付类型
     * 10 货到付款 11 在线支付
     */
    private String payType;

    /**
     * 埋点定义的扩展字段
     */
    private String extensionInfo;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 用户id
     */
    @Field("USER_ID")
    private String userId;

    /**
     * 展示图片地址
     */
    private String showUrl;

    private String businessContent;

    private String storeName;

    private String merchantNo;

    private String businessOrderId;

    @Field("BUSINESS_LINE")
    private String businessLine;

    private Integer businessOrderState;

    private String notCheckAmount;

    private String remark;

    private String currency;

    private Integer commentOrderState;

    @Field("ACTUAL_PAY_AMOUNT")
    private Decimal128 actualPayAmount;

    @Field("AGGREGATE_ORDER_FINAL_STATE")
    private Decimal128 aggregateOrderFinalState;

    private Double totalPayableAmount;

    private String payOrderNo;

    private Double basicAmount;

    private Integer aggregateOrderState;

    @Field("ORDER_TIME")
    private Date orderTime;

    private String deviceId;

    private Integer orderCount;

}
