package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleAggregateOrderBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataSource(DataSourceType.LIFEKH_MP_SHOP)
public interface OracleAggregateOrderDAO {

    @Select("SELECT t.* FROM AGGREGATE_ORDER t ORDER BY ID ASC")
    @Results({
            @Result(property = "userId", column = "USER_ID")
    })
    List<OracleAggregateOrderBO> query();

    /**
     * 查询昨天下单用户数
     */
    @Select("select count(DISTINCT t.USER_ID) from AGGREGATE_ORDER t where t.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} and t.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} and t.AGGREGATE_ORDER_STATE != 99 and (t.MERCHANT_NO not in ('090621455','090121277','08100211','091544503') or t.MERCHANT_NO is null)")
    Long queryOrderUserByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询用户首条订单
     */
    @Select("SELECT a.USER_ID,a.CREATE_TIME,a.BUSINESS_LINE FROM ( SELECT t.USER_ID,t.CREATE_TIME,t.BUSINESS_LINE FROM AGGREGATE_ORDER t where t.USER_ID = (select o.USER_ID FROM AGGREGATE_ORDER o where o.AGGREGATE_ORDER_NO = #{orderNo}) ORDER BY t.CREATE_TIME ASC ) a WHERE rownum = 1")
    @Results({
            @Result(property = "userId", column = "USER_ID"),
            @Result(property = "businessLine", column = "BUSINESS_LINE"),
            @Result(property = "aggregateOrderNo", column = "AGGREGATE_ORDER_NO")
    })
    OracleAggregateOrderBO queryUserFirstOrderByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据订单时间查询订单
     */
    @Select("SELECT t.ORDER_TIME, t.AGGREGATE_ORDER_NO, t.PAY_ORDER_NO, t.PAY_TYPE, t.FIRST_MERCHANTNO, t.MERCHANT_NO, t.STORE_ID, t.STORE_NAME, t.BUSINESS_LINE, t.ORDER_TYPE, t.AGGREGATE_ORDER_FINAL_STATE, t.AGGREGATE_ORDER_STATE, t.ACTUAL_PAY_AMOUNT, t.TOTAL_PAYABLE_AMOUNT, t.USER_ID, t.ADDRESS_NO, t.DEVICE_ID, p.PAY_CHANNEL " +
            " FROM AGGREGATE_ORDER t LEFT JOIN PAY_ORDER p ON t.PAY_ORDER_NO = p.PAY_ORDER_NO where t.AGGREGATE_ORDER_STATE != 99 and (t.MERCHANT_NO not in ('090621455','090121277','08100211','091544503') or t.MERCHANT_NO is null) and t.ORDER_TIME >= #{startTime, jdbcType=TIMESTAMP} and t.ORDER_TIME <= #{endTime, jdbcType=TIMESTAMP} ORDER BY t.ORDER_TIME ASC")
    @Results({
            @Result(property = "orderTime", column = "ORDER_TIME"),
            @Result(property = "aggregateOrderNo", column = "AGGREGATE_ORDER_NO"),
            @Result(property = "payOrderNo", column = "PAY_ORDER_NO"),
            @Result(property = "payType", column = "PAY_TYPE"),
            @Result(property = "firstMerchantNo", column = "FIRST_MERCHANTNO"),
            @Result(property = "merchantNo", column = "MERCHANT_NO"),
            @Result(property = "storeId", column = "STORE_ID"),
            @Result(property = "storeName", column = "STORE_NAME"),
            @Result(property = "businessLine", column = "BUSINESS_LINE"),
            @Result(property = "orderType", column = "ORDER_TYPE"),
            @Result(property = "aggregateOrderFinalState", column = "AGGREGATE_ORDER_FINAL_STATE"),
            @Result(property = "aggregateOrderState", column = "AGGREGATE_ORDER_STATE"),
            @Result(property = "actualPayAmount", column = "ACTUAL_PAY_AMOUNT"),
            @Result(property = "totalPayableAmount", column = "TOTAL_PAYABLE_AMOUNT"),
            @Result(property = "userId", column = "USER_ID"),
            @Result(property = "addressNo", column = "ADDRESS_NO"),
            @Result(property = "deviceId", column = "DEVICE_ID"),
            @Result(property = "payChannel", column = "PAY_CHANNEL")
    })
    List<OracleAggregateOrderBO> queryByOrderTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据订单号查询订单
     */
    @Select("SELECT t.ORDER_TIME, t.AGGREGATE_ORDER_NO, t.PAY_ORDER_NO, t.PAY_TYPE, t.FIRST_MERCHANTNO, t.MERCHANT_NO, t.STORE_ID, t.STORE_NAME, t.BUSINESS_LINE, t.ORDER_TYPE, t.AGGREGATE_ORDER_FINAL_STATE, t.AGGREGATE_ORDER_STATE, t.ACTUAL_PAY_AMOUNT, t.TOTAL_PAYABLE_AMOUNT, t.USER_ID, t.ADDRESS_NO, t.DEVICE_ID, p.PAY_CHANNEL " +
            " FROM AGGREGATE_ORDER t LEFT JOIN PAY_ORDER p ON t.PAY_ORDER_NO = p.PAY_ORDER_NO where t.AGGREGATE_ORDER_NO = #{aggregateOrderNo} ")
    @Results({
            @Result(property = "orderTime", column = "ORDER_TIME"),
            @Result(property = "aggregateOrderNo", column = "AGGREGATE_ORDER_NO"),
            @Result(property = "payOrderNo", column = "PAY_ORDER_NO"),
            @Result(property = "payType", column = "PAY_TYPE"),
            @Result(property = "firstMerchantNo", column = "FIRST_MERCHANTNO"),
            @Result(property = "merchantNo", column = "MERCHANT_NO"),
            @Result(property = "storeId", column = "STORE_ID"),
            @Result(property = "storeName", column = "STORE_NAME"),
            @Result(property = "businessLine", column = "BUSINESS_LINE"),
            @Result(property = "orderType", column = "ORDER_TYPE"),
            @Result(property = "aggregateOrderFinalState", column = "AGGREGATE_ORDER_FINAL_STATE"),
            @Result(property = "aggregateOrderState", column = "AGGREGATE_ORDER_STATE"),
            @Result(property = "actualPayAmount", column = "ACTUAL_PAY_AMOUNT"),
            @Result(property = "totalPayableAmount", column = "TOTAL_PAYABLE_AMOUNT"),
            @Result(property = "userId", column = "USER_ID"),
            @Result(property = "addressNo", column = "ADDRESS_NO"),
            @Result(property = "deviceId", column = "DEVICE_ID"),
            @Result(property = "payChannel", column = "PAY_CHANNEL")
    })
    OracleAggregateOrderBO queryByAggregateOrderNo(@Param("aggregateOrderNo") String aggregateOrderNo);

    /**
     * 查询当天更新的订单（排除当天新增）
     */
    @Select("SELECT t.AGGREGATE_ORDER_NO, t.AGGREGATE_ORDER_FINAL_STATE, t.AGGREGATE_ORDER_STATE " +
            " FROM AGGREGATE_ORDER t where t.UPDATE_TIME >= #{startTime, jdbcType=TIMESTAMP} and t.UPDATE_TIME <= #{endTime, jdbcType=TIMESTAMP} and t.ORDER_TIME < #{startTime, jdbcType=TIMESTAMP} and t.AGGREGATE_ORDER_STATE != 99 and (t.MERCHANT_NO not in ('090621455','090121277','08100211','091544503') or t.MERCHANT_NO is null) ORDER BY t.ORDER_TIME ASC")
    @Results({
            @Result(property = "aggregateOrderNo", column = "AGGREGATE_ORDER_NO"),
            @Result(property = "aggregateOrderFinalState", column = "AGGREGATE_ORDER_FINAL_STATE"),
            @Result(property = "aggregateOrderState", column = "AGGREGATE_ORDER_STATE")
    })
    List<OracleAggregateOrderBO> queryByUpdateTimeAndExcludeCreate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
