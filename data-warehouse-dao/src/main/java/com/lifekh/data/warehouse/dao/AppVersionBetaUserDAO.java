package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.ogg.AppVersionBetaUserBO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface AppVersionBetaUserDAO extends MongoRepository<AppVersionBetaUserBO, String> {
    @Query(value = "{ 'id':{$mod:[?0,?1]} } ")
    List<AppVersionBetaUserBO> findHistoryData(Integer total, Integer num,Pageable pageable);
}
