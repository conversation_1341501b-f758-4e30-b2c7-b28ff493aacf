package com.lifekh.data.warehouse.bo.discovery;

import com.lifekh.data.warehouse.bo.collection.CollectBaseBO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DiscoveryDetailBO extends CollectBaseBO implements Serializable {
    private static final long serialVersionUID = 8470841161859655014L;
    /**
     * 内容编号
     */
    private String contentNo;

    /**
     * 发布时间(上架时间)
     */
    private Date publishTime;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 内容描述
     */
    private String contentDescribe;

    /**
     * 好物种类  com.chaos.discovery.review.api.enums.GoodsTypeEnum
     */
    private String goodsType;

    /**
     * 好物名称
     */
    private String goodsName;

    /**
     * 好物图片
     */
    private String goodsImages;

    /**
     * 好物链接
     */
    private String goodsLink;

    /**
     * 内容语言
     */
    private String contentLanguage;

    /**
     * 内容所属业务线
     */
    private String contentBusinessLine;

    /**
     * 内容类别, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private String contentCategory;

    /**
     * 内容类别集合, 枚举：com.chaos.discovery.review.api.enums.ContentCategoryEnum
     */
    private List<String> contentCategoryList;

    /**
     * 内容类型, 枚举：com.chaos.discovery.review.api.enums.ContentTypeEnum
     */
    private String contentType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 内容创建时间
     */
    private Date contentCreateTime;

//****************************以下为点击事件原数据**************************************//
    /**
     * 业务名称
     */
    private String businessName;
    /**
     * 应用编号
     */
    private String appNo;

    /**
     * 子页
     */
    private String childPage;

    /**
     * 上级页面
     */
    private String parentPage;

    /**
     * 当前页面
     */
    private String currentPage;

    /**
     * 当前区域
     */
    private String currentArea;
    /**
     * 当前节点
     */
    private String node;

    /**
     * 当前区域所处页面位置---广告点击事件时传
     */
    private Integer currentAreaLocation;

    /**
     * 当前节点所处卡片位置---广告点击事件时传
     */
    private Integer currentNodeLocation;

    /**
     * 停留时间
     */
    private Long stayTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 计数
     */
    private Integer count;
}
