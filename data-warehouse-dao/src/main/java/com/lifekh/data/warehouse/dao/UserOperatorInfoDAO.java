package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorInfoBO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

public interface UserOperatorInfoDAO extends MongoRepository<UserOperatorInfoBO, String> {

    @Query("{ 'id':{$mod:[?0,?1]} } ")
    List<UserBehaviorInfoBO> findListByMod(Integer total, Integer num, Pageable pageable);

    UserOperatorInfoBO findByOperatorNo(String operatorNo);

    List<UserOperatorInfoBO> findByCreateTimeAfter(Date createTime,Pageable pageable);

    List<UserOperatorInfoBO> findByStateIsNotAndAppId(String state,String appId,Pageable pageable);
}
