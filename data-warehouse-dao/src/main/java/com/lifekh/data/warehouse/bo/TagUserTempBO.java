package com.lifekh.data.warehouse.bo;

import com.outstanding.framework.core.AbstractObject;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Document(collection = "tag_user_temp")
@Data
public class TagUserTempBO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 794111077367864976L;

    public final static String TABLE_NAME = "tag_user_temp";

    @Id
    private String id;

    private Date createTime;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 标签编号
     */
    private String tagNo;

}
