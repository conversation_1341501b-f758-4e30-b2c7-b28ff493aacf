package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleOpenScreenClickBO;
import com.lifekh.data.warehouse.oracle.bo.OraclePopAdsClickBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataSource(DataSourceType.LIFEKH_MP_APPCONFIG)
public interface OracleAdsClickRecordDAO {

    @Select("select a.AD_NO, a.AD_NAME, a.IMAGE_URL, a.LANGUAGE, count(1) count \n" +
            "from OPEN_SCREEN_AD a \n" +
            "left join USER_CLICK_RECORD r on a.AD_NO = r.ADS_ID \n" +
            "WHERE r.CREATE_TIME >= #{startTime} \n" +
            "AND r.CREATE_TIME <= #{endTime} \n" +
            "and r.LOCATION = 40 \n" +
            "GROUP BY a.AD_NO, a.AD_NAME, a.IMAGE_URL, a.LANGUAGE")
    @Results({
            @Result(property = "adNo", column = "AD_NO"),
            @Result(property = "adName", column = "AD_NAME"),
            @Result(property = "imageUrl", column = "IMAGE_URL"),
            @Result(property = "language", column = "LANGUAGE"),
            @Result(property = "count", column = "count")
    })
    List<OracleOpenScreenClickBO> queryOpenScreenClick(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Select("select a.POP_NO, a.POP_NAME, a.ZH_IMG_URL, count(1) count \n" +
            "from POP_ADVERTISEMENT a \n" +
            "left join USER_CLICK_RECORD r on a.POP_NO = r.ADS_ID \n" +
            "WHERE r.CREATE_TIME >= #{startTime} \n" +
            "AND r.CREATE_TIME <= #{endTime} \n" +
            "and r.LOCATION = 31 \n" +
            "GROUP BY a.POP_NO, a.POP_NAME, a.ZH_IMG_URL")
    @Results({
            @Result(property = "popNo", column = "POP_NO"),
            @Result(property = "popName", column = "POP_NAME"),
            @Result(property = "zhImgUrl", column = "ZH_IMG_URL"),
            @Result(property = "count", column = "count")
    })
    List<OraclePopAdsClickBO> queryPopAdsClick(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
