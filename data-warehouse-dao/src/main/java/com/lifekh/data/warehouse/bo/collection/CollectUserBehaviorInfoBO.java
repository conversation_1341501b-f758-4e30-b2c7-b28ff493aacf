package com.lifekh.data.warehouse.bo.collection;

import com.lifekh.data.warehouse.bo.behavior.*;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Document(collection = "collect_behavior")
@Data
public class CollectUserBehaviorInfoBO implements Serializable{
    private static final long serialVersionUID = 8939930084859303208L;
    @Id
    private String id;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 停留时间
     */
    private Long stayTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 用户信息
     */
    private UserInfoBO userInfoBo;

    /**
     * 应用信息
     */
    private ApplicationInfoBO applicationInfoBo;

    /**
     * 设备信息
     */
    private DeviceInfoBO deviceInfoBo;

    /**
     * 事件
     */
    private EventBO eventBo;

    /**
     * 位置信息
     */
    private LocationBO locationBo;

    /**
     * 网络
     */
    private NetworkBO networkBo;

    /**
     * 请求报文
     */
    private String requestBody;

    /**
     * sessionId
     */
    private String sessionId;
    /**
     * 当前页面
     */
    private String currntPage;
    /**
     * 计数
     */
    private Integer count;

    /**
     * 业务信息
     */
    private Map<String, Object> ext;
}
