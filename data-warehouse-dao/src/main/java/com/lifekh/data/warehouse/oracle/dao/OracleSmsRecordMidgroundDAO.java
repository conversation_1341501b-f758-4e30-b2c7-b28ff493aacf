package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.SmsRecordMidgroundBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 短信
 */
@DataSource(DataSourceType.LIFEKH_MP_ORDS)
public interface OracleSmsRecordMidgroundDAO {

    /**
     * 短信发送业务数统计
     */
    @Select("select mt.TEMPLATE_NAME as BIZ, count(1) as COUNT from SMS_RECORD_MIDGROUND srm left join MESSAGE_TEMPLATE mt on srm.TEMPLATE_NO = mt.TEMPLATE_NO where mt.LANGUAGE_TYPE = 'en-US' and srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} and srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} group by mt.TEMPLATE_NAME")
    @Results({
            @Result(property = "biz", column = "BIZ"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryBizCount(@Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    /**
     * 短信发送成功渠道统计
     */
    @Select("select SUCCES_CHANNEL as CHANNEL, count(1) as COUNT from SMS_RECORD_MIDGROUND srm  where srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} and srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} and srm.SUCCES_CHANNEL is not null group by srm.SUCCES_CHANNEL")
    @Results({
            @Result(property = "channel", column = "CHANNEL"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryChannelSuccessCount(@Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);

    /**
     * 短信发送失败渠道统计
     */
    @Select("select FAIL_CHANNEL as CHANNEL, count(1) as COUNT from SMS_RECORD_MIDGROUND srm  where srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} and srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} and srm.FAIL_CHANNEL is not null group by srm.FAIL_CHANNEL")
    @Results({
            @Result(property = "channel", column = "CHANNEL"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryChannelFailCount(@Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);
}
