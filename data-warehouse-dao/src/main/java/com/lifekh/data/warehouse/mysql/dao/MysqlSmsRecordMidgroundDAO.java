package com.lifekh.data.warehouse.mysql.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.mysql.bo.SmsRecordMidgroundBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 短信 - MySQL版本
 */
@DataSource(DataSourceType.LIFEKH_MP_ORDS)
public interface MysqlSmsRecordMidgroundDAO {

    /**
     * 短信发送业务数统计
     */
    @Select("SELECT mt.TEMPLATE_NAME as BIZ, COUNT(1) as COUNT " +
            "FROM SMS_RECORD_MIDGROUND srm " +
            "LEFT JOIN MESSAGE_TEMPLATE mt ON srm.TEMPLATE_NO = mt.TEMPLATE_NO " +
            "WHERE mt.LANGUAGE_TYPE = 'en-US' " +
            "AND srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} " +
            "AND srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} " +
            "GROUP BY mt.TEMPLATE_NAME")
    @Results({
            @Result(property = "biz", column = "BIZ"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryBizCount(@Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    /**
     * 短信发送成功渠道统计
     */
    @Select("SELECT SUCCES_CHANNEL as CHANNEL, COUNT(1) as COUNT " +
            "FROM SMS_RECORD_MIDGROUND srm " +
            "WHERE srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} " +
            "AND srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} " +
            "AND srm.SUCCES_CHANNEL IS NOT NULL " +
            "GROUP BY srm.SUCCES_CHANNEL")
    @Results({
            @Result(property = "channel", column = "CHANNEL"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryChannelSuccessCount(@Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);

    /**
     * 短信发送失败渠道统计
     */
    @Select("SELECT FAIL_CHANNEL as CHANNEL, COUNT(1) as COUNT " +
            "FROM SMS_RECORD_MIDGROUND srm " +
            "WHERE srm.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} " +
            "AND srm.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP} " +
            "AND srm.FAIL_CHANNEL IS NOT NULL " +
            "GROUP BY srm.FAIL_CHANNEL")
    @Results({
            @Result(property = "channel", column = "CHANNEL"),
            @Result(property = "count", column = "COUNT")
    })
    List<SmsRecordMidgroundBO> queryChannelFailCount(@Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);
}
