package com.lifekh.data.warehouse.mysql.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.mysql.bo.MessageTemplateBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 消息模板 - MySQL版本
 */
@DataSource(DataSourceType.LIFEKH_MP_ORDS)
public interface MysqlMessageTemplateDAO {

    @Select({"<script>",
                " SELECT ",
                " TEMPLATE_NO as templateNo, ",
                " TEMPLATE_NAME as templateName",
                " FROM MESSAGE_TEMPLATE WHERE TEMPLATE_TYPE = 'SMS' AND LANGUAGE_TYPE = 'en-US' AND TEMPLATE_NO IN ",
                "<foreach item='item' index='index' collection='templateNos' open='(' separator=',' close=')'>",
                    "#{item, jdbcType=VARCHAR}",
                "</foreach>",
            "</script>"})
    List<MessageTemplateBO> findByTemplateNo(@Param("templateNos") List<String> templateNos);
}
