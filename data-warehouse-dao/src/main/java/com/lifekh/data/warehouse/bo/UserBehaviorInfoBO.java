package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.bo.behavior.*;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "t_user_behavior_info")
@Data
public class UserBehaviorInfoBO implements Serializable{

    private static final long serialVersionUID = -3559033532735453451L;

    public final static String TABLE_NAME = "t_user_behavior_info";

    @Id
    private String id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 用户信息
     */
    private UserInfoBO userInfoBo;

    /**
     * 应用信息
     */
    private ApplicationInfoBO applicationInfoBo;

    /**
     * 设备信息
     */
    private DeviceInfoBO deviceInfoBo;

    /**
     * 事件
     */
    private EventBO eventBo;

    /**
     * 位置信息
     */
    private LocationBO locationBo;

    /**
     * 网络
     */
    private NetworkBO networkBo;

    /**
     * 请求报文
     */
    private String requestBody;
}
