package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.bo.TagRuleBO;
import org.springframework.data.mongodb.repository.MongoRepository;

public interface TagRuleDAO extends MongoRepository<TagRuleBO, String> {
    TagRuleDTO findByRuleValueAndRuleType(String ruleValue, String ruleType);

    TagRuleBO findByRuleNo(String ruleNo);
}
