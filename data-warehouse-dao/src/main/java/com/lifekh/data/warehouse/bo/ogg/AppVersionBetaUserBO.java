package com.lifekh.data.warehouse.bo.ogg;

import com.outstanding.framework.core.DelStateEnum;
import com.outstanding.framework.core.EntityObject;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * APP_VERSION_BETA_USER
 * <AUTHOR>
@Document(collection = "APP_VERSION_BETA_USER")
@Data
public class AppVersionBetaUserBO implements Serializable {

    private Long id;

    private Long version;

    @Field("DEL_STATE")
    private Integer delState;

    @Field("CREATE_TIME")
    private Date createTime;

    @Field("UPDATE_TIME")
    private Date updateTime;
    /**
     * 操作员编号
     */
    @Field("OPERATOR_NO")
    private String operatorNo;

    /**
     * 登录手机号
     */
    @Field("LOGIN_NAME")
    private String loginName;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 真实姓名
     */
    @Field("REAL_NAME")
    private String realName;

    /**
     * 设备类型
     */
    @Field("DEVICE_TYPE")
    private String deviceType;

    /**
     * 内测状态
     */
    @Field("STATUS")
    private String status;

    /**
     * 所属部门
     */
    private String department;

    private static final long serialVersionUID = 1L;
}