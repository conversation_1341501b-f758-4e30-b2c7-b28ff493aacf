package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleZoneBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * 地区
 */
@DataSource(DataSourceType.LIFEKH_MP_CUSTOMER)
public interface OracleZoneDAO {

    /**
     * 查询地区
     */
    @Select("SELECT MSG_EN, CODE FROM ZONE t WHERE matching_rules LIKE concat(concat('%',#{matchingRules, jdbcType=VARCHAR}),'%') AND z_level = #{zLevel,jdbcType=NUMERIC} and rownum = 1")
    @Results({
            @Result(property = "msgEn", column = "MSG_EN"),
            @Result(property = "code", column = "CODE")
    })
    OracleZoneBO queryZone(@Param("matchingRules") String matchingRules, @Param("zLevel") Integer zLevel);

    /**
     * 查询地区
     */
    @Select("SELECT MSG_EN, CODE FROM ZONE t WHERE matching_rules LIKE concat(concat('%',#{matchingRules, jdbcType=VARCHAR}),'%') AND PARENT = #{parent,jdbcType=NUMERIC} and rownum = 1")
    @Results({
            @Result(property = "msgEn", column = "MSG_EN"),
            @Result(property = "code", column = "CODE")
    })
    OracleZoneBO queryZoneByParent(@Param("matchingRules") String matchingRules, @Param("parent") String parent);
}
