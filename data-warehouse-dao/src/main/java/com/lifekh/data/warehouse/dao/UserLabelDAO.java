package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

public interface UserLabelDAO extends MongoRepository<UserLabelBO, String> {

    List<UserLabelBO> findByCreateTimeAfterOrUpdateTimeAfter(Date createTime,Date updateTime);

    @Query(value = "{ 'id':{$mod:[?0,?1]} } ")
    List<UserLabelBO> findHistoryData(Integer total, Integer num,Pageable pageable);

}
