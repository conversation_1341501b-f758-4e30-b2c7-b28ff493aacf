package com.lifekh.data.warehouse.bo.device;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.DEVICE_POOL)
@Data
@Accessors(chain = true)
public class DevicePoolBO implements Serializable {
    private static final long serialVersionUID = 5475164560756515225L;

    public static final String DEVICE_ID = "deviceId";
    public static final String DEVICE_TYPE = "deviceType";
    public static final String RECORD_TIME = "recordTime";
    public static final String LAST_ACTIVE_TIME = "lastActiveTime";
    public static final String USE_TIME = "useTime";
    public static final String LANGUAGE = "language";
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";
    public static final String PROVINCE = "province";
    public static final String DISTRICT = "district";
    public static final String COORDINATES = "coordinates";

    @Id
    private String id;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型：Android、iOS
     */
    private String deviceType;

    /**
     * 使用状态：unused未使用、used已使用
     */
    private String useStatus;

    /**
     * 记录时间（设备新增时间）
     */
    private Date recordTime;

    /**
     * 最后活跃时间
     */
    private Date lastActiveTime;

    /**
     * 设备使用时间（在此设备注册账号时间）
     */
    private Date useTime;

    /**
     * 语言
     */
    private String language;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 省
     */
    private String province;

    /**
     * 区
     */
    private String district;

    private Date createTime;

    private Date updateTime;

    private Integer version;

    /**
     * 经度和纬度
     */
    private Double[] coordinates;
}
