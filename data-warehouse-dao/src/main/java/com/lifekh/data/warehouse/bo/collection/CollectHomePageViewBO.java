package com.lifekh.data.warehouse.bo.collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollectHomePageViewBO implements Serializable {
    private static final long serialVersionUID = -4333227035036031966L;

    @Id
    private String id;

    private Date createTime;
    public static String CREATE_TIME = "createTime";

    private String operatorNo;
    public static String OPERATOR_NO = "operatorNo";

    private String deviceId;
    public static String DEVICE_ID = "deviceId";
}
