package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.bo.UserLevelInfoBO;
import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.*;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * 操作员信息管理DAO
 */
@DataSource(DataSourceType.LIFEKH_MP_CUSTOMER)
public interface OracleUserOperatorLoginInfoDAO {

    @Select("SELECT t.* FROM USER_OPERATOR_LOGIN_INFO t WHERE t.APP_ID = 'SuperApp' ORDER BY ID DESC")
    @Results({
            @Result(property = "operatorNo", column = "OPERATOR_NO"),
            @Result(property = "loginName", column = "LOGIN_NAME"),
            @Result(property = "loginType", column = "LOGIN_TYPE")
    })
    List<OracleUserOperatorLoginInfoBO> query();

    /**
     * 查询昨天活跃用户
     */
    @Select("select count(DISTINCT OPERATOR_NO) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME >= trunc(sysdate - 1) and ONLINE_TIME < trunc(sysdate)")
    Long queryActiveUserYesterday();

    /**
     * 查询7天内活跃用户
     */
    @Select("select count(DISTINCT OPERATOR_NO) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME >= trunc(sysdate - 7) and ONLINE_TIME < trunc(sysdate)")
    Long queryActiveUserSevenDays();

    /**
     * 查询30天内活跃用户
     */
    @Select("select count(DISTINCT OPERATOR_NO) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME >= trunc(sysdate - 30) and ONLINE_TIME < trunc(sysdate)")
    Long queryActiveUserThirtyDays();

    /**
     * 查询昨天活跃用户
     */
    @Select("select count(DISTINCT OPERATOR_NO) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME >= #{startTime, jdbcType=TIMESTAMP} and ONLINE_TIME < #{endTime, jdbcType=TIMESTAMP}")
    Long queryActiveUserByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("select u.LANGUAGE, count(DISTINCT t.OPERATOR_NO) as TOTAL from SUP_MOBILE_TOKEN t " +
            " left join user_label u on u.operator_no = t.operator_no " +
            " where t.PROJECT_NAME = 'SuperApp' and t.ONLINE_TIME >= #{startTime, jdbcType=TIMESTAMP} and t.ONLINE_TIME < #{endTime, jdbcType=TIMESTAMP} group by u.language")
    @Results({
            @Result(property = "language", column = "LANGUAGE"),
            @Result(property = "total", column = "TOTAL")
    })
    List<OracleOperatorOnlineLanguageBO> queryActiveUserLanguageByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询昨天活跃设备
     */
    @Select("select count(DISTINCT DEVICE_ID) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME > #{startTime, jdbcType=TIMESTAMP} and ONLINE_TIME < #{endTime, jdbcType=TIMESTAMP}")
    Long queryActiveDeviceByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询昨天活跃设备(易盾)
     */
    @Select("select count(DISTINCT UD_DEVICE_ID) from SUP_MOBILE_TOKEN t where PROJECT_NAME = 'SuperApp' and ONLINE_TIME > #{startTime, jdbcType=TIMESTAMP} and ONLINE_TIME < #{endTime, jdbcType=TIMESTAMP}")
    Long queryActiveUdDeviceByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 留存用户统计
     */
    @Select("SELECT count(DISTINCT OPERATOR_NO) FROM SUP_MOBILE_TOKEN smt WHERE smt.OPERATOR_NO IN (SELECT OPERATOR_NO FROM USER_OPERATOR_INFO uoi WHERE uoi.APP_ID = 'SuperApp' AND uoi.CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} AND uoi.CREATE_TIME < #{endTime, jdbcType=TIMESTAMP}) AND smt.PROJECT_NAME = 'SuperApp' AND smt.ONLINE_TIME > trunc(sysdate-1) AND smt.ONLINE_TIME < trunc(sysdate) ")
    Long queryRemainUser(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询昨天新增用户
     *
     * @return
     */
    @Select("select count(*) from USER_OPERATOR_INFO where APP_ID = 'SuperApp' and CREATE_TIME > trunc(sysdate - 1) and CREATE_TIME < trunc(sysdate)")
    Long queryNewUserYesterday();

    /**
     * 查询7天新增用户
     *
     * @return
     */
    @Select("select count(*) from USER_OPERATOR_INFO where APP_ID = 'SuperApp' and CREATE_TIME > trunc(sysdate - 7) and CREATE_TIME < trunc(sysdate)")
    Long queryNewUserSevenDays();

    /**
     * 查询30天新增用户
     *
     * @return
     */
    @Select("select count(*) from USER_OPERATOR_INFO where APP_ID = 'SuperApp' and CREATE_TIME > trunc(sysdate - 30) and CREATE_TIME < trunc(sysdate)")
    Long queryNewUserThirtyDays();

    /**
     * 查询总用户数
     *
     * @return
     */
    @Select("select count(1) from USER_OPERATOR_INFO where APP_ID = 'SuperApp' and DEL_STATE = 10")
    Long queryTotalUsers();

    /**
     * 查询注册用户
     */
    @Select("select count(*) from USER_OPERATOR_INFO where APP_ID = 'SuperApp' and CREATE_TIME >= #{startTime, jdbcType=TIMESTAMP} and CREATE_TIME <= #{endTime, jdbcType=TIMESTAMP}")
    Long queryNewUserByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("select ul.LANGUAGE, count(*) as num from USER_OPERATOR_INFO uoi " +
            " left join user_label ul on ul.operator_no = uoi.operator_no " +
            " where uoi.APP_ID = 'SuperApp' and uoi.create_time >= #{startTime, jdbcType=TIMESTAMP} and uoi.create_time <= #{endTime, jdbcType=TIMESTAMP} group by ul.language")
    @Results({
            @Result(property = "language", column = "LANGUAGE"),
            @Result(property = "num", column = "NUM")
    })
    List<OracleNewUserLangugeBO> queryNewUserLanguageByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询留存用户
     * @param startTime
     * @param endTime
     * @param totalCount
     * @param item
     * @return
     */
    @Select("select * from (SELECT ID,OPERATOR_NO,UPDATE_TIME,DEVICE_ID,UD_DEVICE_ID,PHONE_MODEL,APP_VERSION,APP_NO,ONLINE_TIME FROM (SELECT ID,OPERATOR_NO,UPDATE_TIME,DEVICE_ID,UD_DEVICE_ID,PHONE_MODEL,APP_VERSION,APP_NO,ONLINE_TIME,row_number ( ) over ( partition BY t.operator_no ORDER BY t.update_time DESC ) mm FROM (select ID,OPERATOR_NO,UPDATE_TIME,DEVICE_ID,UD_DEVICE_ID,PHONE_MODEL,APP_VERSION,APP_NO,ONLINE_TIME from SUP_MOBILE_TOKEN smt where smt.ONLINE_TIME > #{startTime, jdbcType=TIMESTAMP} and smt.ONLINE_TIME < #{endTime, jdbcType=TIMESTAMP} and smt.PROJECT_NAME = 'SuperApp') t) WHERE mm =1) where mod(ID, #{totalCount,jdbcType=NUMERIC}) = #{item,jdbcType=NUMERIC}")
    @Results({
            @Result(property = "id", column = "ID"),
            @Result(property = "operatorNo", column = "OPERATOR_NO"),
            @Result(property = "updateTime", column = "UPDATE_TIME"),
            @Result(property = "deviceId", column = "DEVICE_ID"),
            @Result(property = "udDeviceId", column = "UD_DEVICE_ID"),
            @Result(property = "phoneModel", column = "PHONE_MODEL"),
            @Result(property = "appVersion", column = "APP_VERSION"),
            @Result(property = "onlineTime", column = "ONLINE_TIME"),
            @Result(property = "appNo", column = "APP_NO")
    })
    List<OracleUserOperatorLoginInfoBO> queryUserLoginInfo(@Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("totalCount") Integer totalCount,
                                                           @Param("item") Integer item);

    /**
     * 查询留存用户
     * @param startTime
     * @param endTime
     * @param totalCount
     * @param item
     * @return
     */
    @Select("SELECT * FROM (SELECT ID,OPERATOR_NO,UPDATE_TIME,DEVICE_ID,UD_DEVICE_ID,PHONE_MODEL,APP_VERSION,APP_NO,ONLINE_TIME,LAST_ONLINE_TIME FROM SUP_MOBILE_TOKEN WHERE ONLINE_TIME > #{startTime, jdbcType=TIMESTAMP} AND OPERATOR_NO = #{operatorNo, jdbcType=VARCHAR} ORDER BY ONLINE_TIME DESC) WHERE ROWNUM = 1")
    @Results({
            @Result(property = "id", column = "ID"),
            @Result(property = "operatorNo", column = "OPERATOR_NO"),
            @Result(property = "updateTime", column = "UPDATE_TIME"),
            @Result(property = "deviceId", column = "DEVICE_ID"),
            @Result(property = "udDeviceId", column = "UD_DEVICE_ID"),
            @Result(property = "phoneModel", column = "PHONE_MODEL"),
            @Result(property = "appVersion", column = "APP_VERSION"),
            @Result(property = "onlineTime", column = "ONLINE_TIME"),
            @Result(property = "lastOnlineTime", column = "LAST_ONLINE_TIME"),
            @Result(property = "appNo", column = "APP_NO")
    })
    OracleUserOperatorLoginInfoBO queryUserByOperatorNoAndTime(@Param("startTime") Date startTime,
                                                              @Param("operatorNo") String operatorNo);

    @Select("select LOGIN_NAME from USER_OPERATOR_LOGIN_HISTORY t where t.OPERATOR_NO =  #{operatorNo, jdbcType=VARCHAR} and t.LOGIN_TYPE = 'MOBILE' and rownum = 1")
    @Results({
            @Result(property = "loginName", column = "LOGIN_NAME")
    })
    String queryLoginhistoryByOperatorNo(@Param("operatorNo") String operatorNo);

    /**
     * 查询用户的客户信息
     * @param operatorNo
     * @return
     */
    @Select("select uoi.CREATE_TIME, uci.BIRTHDAY, uci.GENDER, uoi.STATE, UL.REG_LATITUDE, UL.REG_LONGITUDE, UOLI.LOGIN_NAME, UL.LANGUAGE, UL.ONLINE_LONGITUDE, UL.ONLINE_LATITUDE from USER_OPERATOR_INFO uoi left join USER_CUSTOMER_INFO uci on uci.CUSTOMER_NO = uoi.OPERATOR_NO left join USER_LABEL UL ON UL.OPERATOR_NO = UOI.OPERATOR_NO AND UL.DEL_STATE = 10 left join USER_OPERATOR_LOGIN_INFO UOLI ON UOLI.OPERATOR_NO = uoi.operator_no and uoli.login_type = 'MOBILE' where uoi.OPERATOR_NO = #{operatorNo, jdbcType=VARCHAR} and rownum = 1")
    @Results({
            @Result(property = "registerTime", column = "CREATE_TIME"),
            @Result(property = "gender", column = "GENDER"),
            @Result(property = "birthday", column = "BIRTHDAY"),
            @Result(property = "operatorStatus", column = "STATE"),
            @Result(property = "regLatitude", column = "REG_LATITUDE"),
            @Result(property = "regLongitude", column = "REG_LONGITUDE"),
            @Result(property = "loginName", column = "LOGIN_NAME"),
            @Result(property = "language", column = "LANGUAGE"),
            @Result(property = "onlineLongitude", column = "ONLINE_LONGITUDE"),
            @Result(property = "onlineLatitude", column = "ONLINE_LATITUDE")
    })
    OracleUserOperatorLoginInfoBO queryUserByOperatorNo(@Param("operatorNo") String operatorNo);

    @Select("select DISTINCT OPERATOR_NO from USER_LABEL where ONLINE_TIME > #{startTime, jdbcType=TIMESTAMP} and APP_ID = 'SuperApp' and DEL_STATE = 10 and mod(ID, #{totalCount,jdbcType=NUMERIC}) = #{item,jdbcType=NUMERIC}")
    @Results({
            @Result(property = "operatorNo", column = "OPERATOR_NO")
    })
    List<OracleOperatorBO> queryUserByOnlineTime(@Param("startTime") Date startTime, @Param("totalCount") Integer totalCount, @Param("item") Integer item);

    /**
     * 查询用户的等级
     */
    @Select("select uol.OP_LEVEL, uli.NAME_ZH from USER_OPERATOR_LEVEL uol left join USER_LEVEL_INFO uli on uli.OP_LEVEL = uol.OP_LEVEL where uol.OPERATOR_NO = #{operatorNo, jdbcType=VARCHAR} and rownum = 1")
    @Results({
            @Result(property = "opLevel", column = "OP_LEVEL"),
            @Result(property = "levelName", column = "NAME_ZH")
    })
    UserLevelInfoBO queryUserLevelByOperatorNo(@Param("operatorNo") String operatorNo);

    /**
     * 查询登录方式
     */
    @Select("select LOGIN_METHOD from (select UOLL.LOGIN_METHOD from USER_OPERATOR_LOGIN_LOG UOLL where UOLL.CREATE_TIME >= trunc(sysdate - 1) and UOLL.OPERATOR_NO = #{operatorNo, jdbcType=VARCHAR} ORDER BY ID) where rownum = 1")
    @Results({
            @Result(property = "loginMethod", column = "LOGIN_METHOD")
    })
    String queryLoginMethod(@Param("operatorNo") String operatorNo);

    /**
     * 查询在线用户
     */
    @Select("select OPERATOR_NO from USER_LABEL where ONLINE_TIME >= #{startTime, jdbcType=TIMESTAMP} and APP_ID = 'SuperApp' and DEL_STATE = 10 ORDER BY ID ASC")
    @Results({
            @Result(property = "operatorNo", column = "OPERATOR_NO")
    })
    List<OracleOperatorBO> queryOnlineUser(@Param("startTime") Date startTime);

    /**
     * 查询不在线用户
     */
    @Select("select OPERATOR_NO from USER_LABEL where (ONLINE_TIME < #{startTime, jdbcType=TIMESTAMP} or ONLINE_TIME is null) and APP_ID = 'SuperApp' and DEL_STATE = 10 ORDER BY ID ASC")
    @Results({
            @Result(property = "operatorNo", column = "OPERATOR_NO")
    })
    List<OracleOperatorBO> queryOfflineUser(@Param("startTime") Date startTime);

    /**
     * 查询登录账号
     */
    @Select("select OPERATOR_NO from USER_OPERATOR_LOGIN_INFO where APP_ID = 'SuperApp' and DEL_STATE = 10 and LOGIN_NAME = #{loginName, jdbcType=VARCHAR}")
    @Results({
            @Result(property = "operatorNo", column = "OPERATOR_NO")
    })
    OracleOperatorBO queryByLoginName(String loginName);

    /**
     * 查询短链注册用户
     */
    @Select("select count(*) from USER_LABEL where APP_ID = 'SuperApp' and CREATE_TIME >= #{startTime, jdbcType=TIMESTAMP} and CREATE_TIME <= #{endTime, jdbcType=TIMESTAMP} and SHORT_ID = #{shortId, jdbcType=VARCHAR}")
    Long countUserByShortId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("shortId") String shortId);

    @Select("select APP_ID,LOGIN_METHOD, count(*) as TOTAL from USER_OPERATOR_LOGIN_LOG where CREATE_TIME >= trunc(sysdate-1) and CREATE_TIME < trunc(sysdate) group by APP_ID, LOGIN_METHOD")
    @Results({
            @Result(property = "appId", column = "APP_ID"),
            @Result(property = "loginMethod", column = "LOGIN_METHOD"),
            @Result(property = "num", column = "TOTAL")
    })
    List<OracleOperatorLoginMethodBO> queryUserLoginMethodReport();

    @Select("select REG_METHOD,count(*) as TOTAL from user_label where create_time > trunc(sysdate) and app_id = 'SuperApp' group by REG_METHOD")
    @Results({
            @Result(property = "registerMethod", column = "REG_METHOD"),
            @Result(property = "num", column = "TOTAL")
    })
    List<OracleOperatorRegisterMethodBO> queryUserRegisterMethodReport();
}
