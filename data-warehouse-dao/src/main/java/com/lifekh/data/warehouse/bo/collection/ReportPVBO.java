package com.lifekh.data.warehouse.bo.collection;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = "report_pv")
@Data
public class ReportPVBO {

    @Id
    private ObjectId id;

    private Date createTime;

    private Date updateTime;

    /**
     * 父级页面
     */
    private String parentPage;

    /**
     * 当前页面
     */
    private String currentPage;

    /**
     * 别名
     */
    private String alias;

    /**
     * 访问数
     */
    private Long viewNum;

    /**
     * 设备数
     */
    private Long deviceNum;

    /**
     * 用户数
     */
    private Long userNum;
}
