package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Document(collection = "tag_rule")
@Data
public class TagRuleV2BO implements Serializable {
    private static final long serialVersionUID = -3636295574547907682L;
    @Id
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则分类：（属性、行为）
     */
    private String classification;

    /**
     * 规则类型：RuleTypeEnum
     */
    private String ruleType;

    /**
     * 规则的值
     */
    private String ruleValue;

    /**
     * 时效性
     */
    private boolean timeliness;

    /**
     * 通用规则
     */
    private List<RuleDTO> generalRules;

    /**
     * 特殊规则
     */
    private List<RuleDTO> specialRules;

}
