package com.lifekh.data.warehouse.bo.ogg;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "USER_OPERATOR_INFO")
@Data
public class UserOperatorInfoBO implements Serializable {

    private static final long serialVersionUID = 5503166675222115507L;

    @Field("ID")
    private Long id;

    @Field("CREATE_TIME")
    private Date createTime;

    @Field("UPDATE_TIME")
    private Date updateTime;

    @Field("DEL_STATE")
    private Integer delState;

    private Long version;

    @Field("OPERATOR_NO")
    private String operatorNo;

    @Field("CUSTOMER_NO")
    private String customerNo;

    @Field("STATE")
    private String state;

    @Field("APP_ID")
    private String appId;

    @Field("HEAD_GROUP")
    private String headGroup;

    @Field("HEAD_FILE_ID")
    private String headFileId;

    @Field("EMAIL")
    private String email;

    @Field("MOBILE")
    private String mobile;

    @Field("NICKNAME")
    private String nickName;

    @Field("OPERATOR_TYPE")
    private String operatorType;

    @Field("HEAD_URL")
    private String headUrl;

    @Field("BUSINESS_LINE")
    private String businessLine;

    @Field("INVITATION_CODE")
    private String invitationCode;

    @Field("REMARK")
    private String remark;
}
