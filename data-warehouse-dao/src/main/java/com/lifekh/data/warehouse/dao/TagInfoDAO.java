package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.resp.TagDetailQueryRespDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TagInfoDAO extends MongoRepository<TagInfoBO, String> {


    TagDetailQueryRespDTO findByTagNo(String tagNo);

    TagInfoBO findByRuleContains(String rule);

    TagInfoDTO queryByTagNo(String tagNo);

    List<TagInfoBO> findByTagTypeAndTagStatus(String tagType, String tagStatus);



}
