package com.lifekh.data.warehouse.oracle.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserOperatorVerifyCodeBO implements Serializable {

    /**
     * 业务
     */
    private String biz;

    /**
     * 短信发送方式
     *
     * SMS(10, "短信"),
     * VOIP(11, "语音通话")
     */
    private Integer method;

    /**
     * 是否已验证
     */
    private Integer used;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 发送渠道
     */
    private String sendChannel;

    /**
     * 发送模板
     */
    private String templateNo;

    /**
     * 已验证通过数量
     */
    private Integer usedCount;

    /**
     * 未验证通过数量
     */
    private Integer notUsedCount;

    /**
     * 运营商名称
     */
    private String carrierName;
}
