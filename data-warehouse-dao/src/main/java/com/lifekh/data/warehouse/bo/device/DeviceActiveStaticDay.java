package com.lifekh.data.warehouse.bo.device;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "device_active_static_day")
@Data
@Accessors(chain = true)
public class DeviceActiveStaticDay implements Serializable {
    private static final long serialVersionUID = 8575238052683232733L;

    @Id
    private String id;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 活跃设备数量
     */
    private Integer activeCount;

    /**
     * 易盾活跃设备数
     */
    private Integer udActiveCount;

    /**
     * 创建时间
     */
    private Date createTime;
}
