package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "tag_rule")
@Data
public class TagRuleBO implements Serializable {
    private static final long serialVersionUID = -6493640878081541497L;

    @Id
    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 规则编号
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型：RuleTypeEnum
     */
    private String ruleType;

    /**
     * 规则的值
     */
    private String ruleValue;

    /**
     * 业务线
     */
    private String businessLine;

}
