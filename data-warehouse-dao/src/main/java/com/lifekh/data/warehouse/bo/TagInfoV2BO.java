package com.lifekh.data.warehouse.bo;

import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Document(collection = "tag_info")
@Data
public class TagInfoV2BO implements Serializable {

    private static final long serialVersionUID = 794111077367864976L;

    public final static String TABLE_NAME = "tag_info";

    public static final String TAG_NAME = "tagName";
    public static final String TAG_NAME_ZH = "tagName.zh-CN";
    public static final String TAG_NAME_EN = "tagName.en-US";
    public static final String TAG_NAME_KM = "tagName.km-KH";
    public static final String TAG_CLASSIFY = "tagClassify";
    public static final String TAG_NO = "tagNo";
    public static final String EXEC_STATUS = "execStatus";
    public static final String EXEC_TIME = "execTime";
    public static final String TOTAL_USER = "totalUser";
    public static final String TAG_TYPE = "tagType";
    public static final String TAG_CATALOGUE = "tagCatalogue";

    @Id
    private String id;

    private Date createTime;

    private Date updateTime;

    private String createBy;

    private String updateBy;

    /**
     * 标签编号
     */
    private String tagNo;

    /**
     * 标签名称
     */
    private Map<String, String> tagName;

    /**
     * 标签类型 枚举 TagTypeEnum
     */
    private String tagType;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;

    /**
     * 一级分类编号
     */
    private String firstClassificationNo;

    /**
     * 一级分类名称-中文
     */
    private String firstClassification;

    /**
     * 一级分类名称-英文
     */
    private String firstClassificationEn;

    /**
     * 一级分类名称-柬文
     */
    private String firstClassificationKm;

    /**
     * 二级分类编号
     */
    private String secondaryClassificationNo;

    /**
     * 二级分类名称-中文
     */
    private String secondaryClassification;

    /**
     * 二级分类名称-英文
     */
    private String secondaryClassificationEn;

    /**
     * 二级分类名称-柬文
     */
    private String secondaryClassificationKm;

    /**
     * 标签状态 枚举 TagStatusEnum
     */
    private String tagStatus;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 时效性（true：实时, false:定时）默认传false
     */
    private Boolean timeliness;

    /**
     * 已贴标签人数
     */
    private Long totalUser;

    /**
     * 规则编号
     */
    private List<RuleTagRuleReqV2DTO> rule;

    /**
     * 标签分类，枚举：TagCatalogueEnum
     */
    private String tagCatalogue;

    /**
     * 可见范围，枚举：TagScopeEnum
     */
    private String tagScope;

    /**
     * 更新方式，枚举：TagExecTypeEnum
     */
    private String execType;

    /**
     * 生效状态，枚举：TagExecStatusEnum
     */
    private String execStatus;

    /**
     * 执行时间
     */
    private Date execTime;
}
