package com.lifekh.data.warehouse.bo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "tag_classification")
@Data
public class TagClassificationBO implements Serializable {
    private static final long serialVersionUID = 4636575106086931723L;

    public final static String TABLE_NAME = "tag_classification";

    public static final String TAG_CLASSIFY = "tagClassify";
    public static final String FIRST_TAG_CLASSIFICATION_NO = "firstTagClassificationNo";
    public static final String SECONDARY_CLASSIFICATION_NAME = "secondaryClassificationName";
    public static final String SECONDARY_CLASSIFICATION_NAME_EN = "secondaryClassificationNameEn";
    public static final String SECONDARY_CLASSIFICATION_NAME_KM = "secondaryClassificationNameKm";

    @Id
    private String id;

    private Date createTime;

    /**
     * 一级分类编号
     */
    private String firstTagClassificationNo;

    /**
     * 一级分类名称-中文
     */
    private String firstClassificationName;

    /**
     * 一级分类名称-英文
     */
    private String firstClassificationNameEn;

    /**
     * 一级分类名称-柬文
     */
    private String firstClassificationNameKm;

    /**
     * 二级分类编号
     */
    private String secondTagClassificationNo;

    /**
     * 二级分类名称-中文
     */
    private String secondaryClassificationName;

    /**
     * 二级分类名称-英文
     */
    private String secondaryClassificationNameEn;

    /**
     * 二级分类名称-柬文
     */
    private String secondaryClassificationNameKm;

    /**
     * 标签分类 枚举 TagClassifyEnum
     */
    private String tagClassify;
}
