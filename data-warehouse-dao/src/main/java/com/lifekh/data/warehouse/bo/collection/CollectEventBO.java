package com.lifekh.data.warehouse.bo.collection;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class CollectEventBO  implements Serializable {
    private static final long serialVersionUID = 531600695506955L;

    private String id;

    private Date createTime;

    private Date updateTime;

    /**
     * 事件分组,登陆、注册、搜索、分享、下单
     */
    private String eventGroup;

    /**
     * 事件标识
     */
    private String eventNo;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 业务线
     */
    private String bussinessLine;

    /**
     * 状态
     */
    private String status;

    /**
     * 业务属性
     */
    private List<String> attributes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
