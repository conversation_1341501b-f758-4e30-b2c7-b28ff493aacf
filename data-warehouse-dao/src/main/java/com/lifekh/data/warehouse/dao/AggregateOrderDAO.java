package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Date;
import java.util.List;

public interface AggregateOrderDAO extends MongoRepository<AggregateOrderBO, String> {

    List<AggregateOrderBO> findByUpdateTimeAndAggregateOrderFinalState(Date updateTime,Integer aggregateOrderFinalState);
}
