package com.lifekh.data.warehouse.bo.behavior;

import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;

import java.io.Serializable;

@Data
public class LocationBO implements Serializable {
    private static final long serialVersionUID = -8557258811230243501L;

    /**
     * 经度和纬度
     */
    private Double[] coordinates;

    /**
     * 省名称
     */
    private String provinceName;

    private String provinceNameZh;

    private String provinceNameKm;

    /**
     * 区名称
     */
    private String areaName;

    /**
     * 省编号
     */
    private String provinceNo;

    /**
     * 纬度
     */
    private Float latitude;

    /**
     * 经度
     */
    private Float longitude;
}
