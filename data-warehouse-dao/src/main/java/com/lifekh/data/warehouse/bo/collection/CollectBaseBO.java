package com.lifekh.data.warehouse.bo.collection;

import com.lifekh.data.warehouse.bo.behavior.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CollectBaseBO implements Serializable {
    private static final long serialVersionUID = 6355503650364816139L;

    /**
     * 用户信息
     */
    private UserInfoBO userInfoBo;

    /**
     * 应用信息
     */
    private ApplicationInfoBO applicationInfoBo;

    /**
     * 设备信息
     */
    private DeviceInfoBO deviceInfoBo;

    /**
     * 事件
     */
    private EventBO eventBo;

    /**
     * 位置信息
     */
    private LocationBO locationBo;

    /**
     * 网络
     */
    private NetworkBO networkBo;

    /**
     * 业务信息
     */
    private Map<String, Object> ext;

    /**
     * 入口信息
     */
    private EntranceBO entrance;
}
