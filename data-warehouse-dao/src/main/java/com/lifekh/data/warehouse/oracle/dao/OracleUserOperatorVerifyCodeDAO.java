package com.lifekh.data.warehouse.oracle.dao;

import com.lifekh.data.warehouse.config.route.DataSource;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.UserOperatorVerifyCodeBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 操作员信息管理DAO
 */
@DataSource(DataSourceType.LIFEKH_MP_CUSTOMER)
public interface OracleUserOperatorVerifyCodeDAO {

    /**
     * 查询短信发送成功失败
     */
    @Select("select send_method as METHOD, is_used as USED, count(1) as COUNT from USER_OPERATOR_VERIFY_CODE uovc where uovc.create_time >= #{startTime, jdbcType=TIMESTAMP} and uovc.create_time <= #{endTime, jdbcType=TIMESTAMP} group by send_method, is_used")
    @Results({
            @Result(property = "method", column = "METHOD"),
            @Result(property = "used", column = "USED"),
            @Result(property = "count", column = "COUNT")
    })
    List<UserOperatorVerifyCodeBO> querySmsCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("select SEND_CHANNEL as sendChannel, SMS_TEMPLATE_NO as templateNo, CARRIER_NAME as carrierName, sum(used) as usedCount, sum(notUsed) as notUsedCount, count(1) as count from (select SMS_TEMPLATE_NO, SEND_CHANNEL, CARRIER_NAME, (case when is_used > 0 then 1 else 0 end) as used, (case when is_used < 1 then 1 else 0 end) as notUsed from USER_OPERATOR_VERIFY_CODE where CREATE_TIME > #{startTime, jdbcType=TIMESTAMP} and CREATE_TIME < #{endTime, jdbcType=TIMESTAMP}) group by SEND_CHANNEL, SMS_TEMPLATE_NO, CARRIER_NAME")
    @Results({
            @Result(property = "count", column = "count"),
            @Result(property = "templateNo", column = "templateNo"),
            @Result(property = "usedCount", column = "usedCount"),
            @Result(property = "notUsedCount", column = "notUsedCount"),
            @Result(property = "sendChannel", column = "sendChannel"),
            @Result(property = "carrierName", column = "carrierName")
    })
    List<UserOperatorVerifyCodeBO> queryChannelRate(@Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);
}
