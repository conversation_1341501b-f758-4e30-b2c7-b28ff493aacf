package com.lifekh.data.warehouse.bo.behavior;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@Data
public class SpmBO implements Serializable {
    private static final long serialVersionUID = -7017505102822257833L;

    /**
     * 应用编号
     */
    private String appNo;

    /**
     * 上上级页面
     */
    private String grandfatherPage;

    /**
     * 上级页面
     */
    private String parentPage;

    /**
     * 当前页面
     */
    private String currentPage;

    /**
     * 当前页面区
     */
    private String currentArea;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 停留时长
     */
    private String stayTime;
}
