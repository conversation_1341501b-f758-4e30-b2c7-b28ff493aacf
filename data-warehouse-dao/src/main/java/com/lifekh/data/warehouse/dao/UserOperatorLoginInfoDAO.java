package com.lifekh.data.warehouse.dao;

import com.lifekh.data.warehouse.bo.ogg.UserOperatorLoginInfoBO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface UserOperatorLoginInfoDAO extends MongoRepository<UserOperatorLoginInfoBO, String> {

    @Query("{ 'id':{$mod:[?0,?1]} } ")
    List<UserOperatorLoginInfoBO> findListByMod(Integer total, Integer num, Pageable pageable);

    List<UserOperatorLoginInfoBO> findByLoginNameAndAppId(String loginName, String appId);

    UserOperatorLoginInfoBO findByOperatorNoAndLoginType(String operatorNo, String loginType);
}
