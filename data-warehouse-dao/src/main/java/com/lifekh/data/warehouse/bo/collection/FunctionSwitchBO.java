package com.lifekh.data.warehouse.bo.collection;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = MongoDbCollectonName.COLLECT_FUNCTION_SWITCH_CONFIG)
@Data
public class FunctionSwitchBO implements Serializable {
    private static final long serialVersionUID = 6393007289666242287L;

    @Id
    private String id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    public static String UPDATE_TIME = "updateTime";

    /**
     * 更新人
     */
    private String updateBy;
    public static String UPDATE_BY = "updateBy";

    /**
     * 功能名称
     */
    private String name;
    public static String NAME = "name";

    /**
     * 开关，true:开, false:关
     */
    private Boolean enable;
    public static String ENABLE = "enable";

    /**
     * 备注
     */
    private String remark;
}
