package com.lifekh.data.warehouse.bo.collection;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/28 10:51
 * @Version 1.0
 **/
@Data
@Document(collection = MongoDbCollectonName.EVENT_TYPE)
public class EventTypeBO implements Serializable {
    private static final long serialVersionUID = -215091833341290214L;
    @Id
    private String id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 事件组
     */
    private String eventGroup;

    /**
     * 事件编号
     */
    private String eventNo;

    /**
     * 事件名
     */
    private String eventName;

    /**
     * 业务线
     */
    private String bussinessLine;

    /**
     * 状态：10-采集，11-停止
     */
    private Integer status;

    /**
     * EXT属性
     */
    private List<String> attributes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 事件存储表
     */
    private String table;
}
