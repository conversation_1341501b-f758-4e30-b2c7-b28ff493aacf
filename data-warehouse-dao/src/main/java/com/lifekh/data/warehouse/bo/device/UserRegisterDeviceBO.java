package com.lifekh.data.warehouse.bo.device;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document(collection = "user_register_device")
@Data
@Accessors(chain = true)
public class UserRegisterDeviceBO implements Serializable {
    private static final long serialVersionUID = 5475164560756515225L;

    @Id
    private String id;


    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型：Android、iOS
     */
    private String deviceType;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 处理次数
     */
    private Integer handleCount;

    private Date createTime;

    private Date updateTime;


}
