db.event_type.insert( {
	eventGroup: "other",
	eventNo: "Registration_LoginPageView",
	eventName: "注册/登录页PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SmsLoginPageView",
	eventName: "短信登录页PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );



db.event_type.insert( {
	eventGroup: "other",
	eventNo: "FastLoginPageView",
	eventName: "快速登录页PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );



db.event_type.insert( {
	eventGroup: "other",
	eventNo: "MyInformationPageView",
	eventName: "我的信息页面PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "EmailVerificationPageView",
	eventName: "邮箱验证页面PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );



db.event_type.insert( {
	eventGroup: "other",
	eventNo: "BindPhonePageView",
	eventName: "绑定手机号页面PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );



db.event_type.insert( {
	eventGroup: "other",
	eventNo: "PasswordLoginPageView",
	eventName: "密码登录页PV",
	remark: "",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );
