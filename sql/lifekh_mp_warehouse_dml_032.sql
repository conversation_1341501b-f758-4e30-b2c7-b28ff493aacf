db.device_active_static_day.find().forEach(
   function(item){
			db.device_active_static_day.update({"_id":item._id},{$set:{dataTime: ISODate(item.dataDate + "T05:00:00.000Z")}});
   }
);

db.device_new_static_day.find().forEach(
   function(item){
			db.device_new_static_day.update({"_id":item._id},{$set:{dataTime: ISODate(item.dataDate + "T05:00:00.000Z")}});
   }
);

db.discovery_content_info.find().forEach(
   function(item){
			db.discovry_details_click.update({contentNo:item.contentNo},{$set:{createBy: item.createBy}},{multi:true});
			db.discovry_goods_click.update({contentNo:item.contentNo},{$set:{createBy: item.createBy}},{multi:true});
   }
);

db.event_type.insert( {
eventGroup: "other",
eventNo: "takeawaySearch",
eventName: "外卖搜索栏点击搜索",
remark: "外卖搜索栏点击搜索",
table: "collect_buried_point_other_v2",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "takeawaySearchAtPlaceOrder",
eventName: "外卖搜索下单成功",
remark: "外卖搜索下单成功",
table: "collect_buried_point_other_v2",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_store_dwell_time",
eventName: "扫码点餐门店停留时间",
remark: "扫码点餐门店停留时间",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_store_back",
eventName: "扫码点餐门店返回WOWNOW",
remark: "扫码点餐门店返回WOWNOW",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_cart_click",
eventName: "扫码点餐查看购物车",
remark: "扫码点餐查看购物车",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_cart_back",
eventName: "扫码点餐购物车返回门店",
remark: "扫码点餐购物车返回门店",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_add_product",
eventName: "扫码点餐加购商品",
remark: "扫码点餐加购商品",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_confirm_order",
eventName: "扫码点餐确认订单",
remark: "扫码点餐确认订单",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_order_now",
eventName: "扫码点餐下单",
remark: "扫码点餐下单",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.event_type.insert( {
eventGroup: "other",
eventNo: "scan_order_click",
eventName: "扫码点餐我的订单",
remark: "扫码点餐我的订单",
table: "collect_buried_point_scan_order",
status: 10,
bussinessLine: "all",
createBy: "system",
createTime: new Date(),
updateBy: "system",
updateTime: new Date()
} );

db.collect_buried_point_scan_order.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_scan_order.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_scan_order.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_scan_order.createIndex({"userInfoBo.loginName":-1},{background:true});
db.discovery_content_info.createIndex({"publishTime":-1},{background:true});
db.discovery_content_info.createIndex({"updateTime":-1},{background:true});
