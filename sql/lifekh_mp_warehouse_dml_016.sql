db.getCollection("tag_info").insert({
"_id": ObjectId("623d6a1fe960fd0001b1db12"),
"createTime": ISODate("2022-03-25T07:07:11.465Z"),
"tagNo": "1507252740798099456",
"tagName": {
"zh-CN": "绿服务发布",
"en-US": "绿服务发布",
"km-KH": "绿服务发布"
},
"tagType": "basic",
"tagClassify": "public",
"tagStatus": "open",
"tagDescription": "蓝绿发布标签",
"firstClassification": "用户属性",
"secondaryClassification": "其他",
"rule": [
{
"_id": ObjectId("6115224ca16e7743e47f4ed7"),
"ruleNo": "1419936837561659397",
"ruleName": "自定义标签",
"ruleType": "customize",
"ruleValue": "customize"
}
],
"_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
});

db.getCollection("tag_info").insert({
"_id": ObjectId("623d6a0be960fd0001b1db0f"),
"createTime": ISODate("2022-03-25T07:06:51.823Z"),
"tagNo": "1507252658413580288",
"tagName": {
"zh-CN": "蓝服务发布",
"en-US": "蓝服务发布",
"km-KH": "蓝服务发布"
},
"tagType": "basic",
"tagClassify": "public",
"tagStatus": "open",
"tagDescription": "蓝绿发布标签",
"firstClassification": "用户属性",
"secondaryClassification": "其他",
"rule": [
{
"_id": ObjectId("6115224ca16e7743e47f4ed7"),
"ruleNo": "1419936837561659397",
"ruleName": "自定义标签",
"ruleType": "customize",
"ruleValue": "customize"
}
],
"_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
});

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "注册页面的PV统计数",
eventNo: "register_page_pv",
remark: "注册页面的PV统计数",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "注册页面点击立即领取",
eventNo: "register_page_click_get",
remark: "注册页面点击立即领取",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "注册页面发送验证码成功|失败",
eventNo: "register_page_click_sendSMS",
remark: "注册页面发送验证码成功|失败",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "注册页面立即使用点击",
eventNo: "register_page_click_gouse",
remark: "注册页面立即使用点击",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
createTime: ISODate("2021-09-28T03:21:15.191Z"),
updateTime: ISODate("2021-09-28T03:21:15.191Z"),
eventGroup: "login",
eventNo: "firstOpen",
eventName: "首次打开",
bussinessLine: "all",
status: 10,
remark: "首次打开",
createBy: "wgl",
updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "注册页面注册成功｜失败",
eventNo: "register_page_register_result",
remark: "注册页面注册成功｜失败",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "预注册页面浏览",
eventNo: "preRegister_page_pv",
remark: "预注册页面浏览",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "预注册页面点击立即领取",
eventNo: "preRegister_page_click_get",
remark: "预注册页面点击立即领取",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "预注册页面点击立即领取",
eventNo: "preRegister_page_click_get",
remark: "预注册页面点击立即领取",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "预注册页面点击立即使用",
eventNo: "preRegister_page_click_gouse",
remark: "预注册页面点击立即使用",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "预注册页面注册成功｜失败",
eventNo: "preRegister_page_register_result",
remark: "预注册页面注册成功｜失败",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "邀请有礼页面增加浏览量PV",
eventNo: "invite_page_pv",
remark: "邀请有礼页面增加浏览量PV",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "邀请有礼页面立即邀请按钮点击事件",
eventNo: "invite_page_click_immediately_invited",
remark: "邀请有礼页面立即邀请按钮点击事件",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "邀请有礼页面扫码邀请按钮点击事件",
eventNo: "invite_page_click_scanCode",
remark: "邀请有礼页面扫码邀请按钮点击事件",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "邀请有礼页面分享取消|成功",
eventNo: "invite_page_share_result",
remark: "邀请有礼页面分享取消|成功",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "下载页面浏览",
eventNo: "download_page_pv",
remark: "下载页面浏览",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "下载页面点击下载按钮",
eventNo: "download_page_click_download",
remark: "下载页面点击下载按钮",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "发现页面分享成功|失败",
eventNo: "discover_page_share_result",
remark: "	发现页面分享成功|失败",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "wgl",
createTime: ISODate("2022-03-02T09:45:51.837Z"),
eventGroup: "other",
eventName: "发现页面分享点击",
eventNo: "discover_page_click_share",
remark: "	发现页面分享点击",
status: 10,
updateBy: "wgl",
updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.report_invite_register_day.createIndex({'even':1,'businessName':1,'language':1,'channel':-1})
db.report_invite_register_day.createIndex({'dataTime':-1})
db.report_invite_register_day.createIndex({'channel':1})
db.report_invite_register_summary.createIndex({'even':1,'businessName':1,'language':1,'channel':-1},{unique: true})
db.report_invite_register_summary.createIndex({'dataTime':-1})
db.report_invite_register_summary.createIndex({'channel':1})
db.report_invite_register_summary.createIndex({'createTime':-1})
db.invite_register_but_not_open_app.createIndex({'loginName':-1})
db.invite_register_but_not_open_app.createIndex({'createTime':-1})
