db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477504",
    ruleName: "客单价",
    ruleType: "order_average_price",
    ruleValue: "order_average_price",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "order_average_price",
            symbol: "more than"
        },
        {
            ruleFiled: "order_average_price",
            symbol: "less than"
        },
        {
            ruleFiled: "order_average_price",
            symbol: "equal"
        },
        {
            ruleFiled: "order_average_price",
            symbol: "interval"
        },
         {
             ruleFiled: "order state",
             symbol: "equal"
         }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477505",
    ruleName: "在线支付完单数量",
    ruleType: "online_payment_complete_order",
    ruleValue: "online_payment_complete_order",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "online_payment_complete_order",
            symbol: "more than"
        },
        {
            ruleFiled: "online_payment_complete_order",
            symbol: "less than"
        },
        {
            ruleFiled: "online_payment_complete_order",
            symbol: "equal"
        },
        {
            ruleFiled: "online_payment_complete_order",
            symbol: "interval"
        },
          {
              ruleFiled: "order state",
              symbol: "equal"
          }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477506",
    ruleName: "货到付款完成订单",
    ruleType: "cash_payment_complete_order",
    ruleValue: "cash_payment_complete_order",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "cash_payment_complete_order",
            symbol: "more than"
        },
        {
            ruleFiled: "cash_payment_complete_order",
            symbol: "less than"
        },
        {
            ruleFiled: "cash_payment_complete_order",
            symbol: "equal"
        },
        {
            ruleFiled: "cash_payment_complete_order",
            symbol: "interval"
        },
          {
              ruleFiled: "order state",
              symbol: "equal"
          }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477507",
    ruleName: "下单数",
    ruleType: "submit_order",
    ruleValue: "submit_order",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "submit_order",
            symbol: "more than"
        },
        {
            ruleFiled: "submit_order",
            symbol: "less than"
        },
        {
            ruleFiled: "submit_order",
            symbol: "equal"
        },
        {
            ruleFiled: "submit_order",
            symbol: "interval"
        },
          {
              ruleFiled: "order state",
              symbol: "equal"
          }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477508",
    ruleName: "取消订单数",
    ruleType: "cancel_order",
    ruleValue: "cancel_order",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "cancel_order",
            symbol: "more than"
        },
        {
            ruleFiled: "cancel_order",
            symbol: "less than"
        },
        {
            ruleFiled: "cancel_order",
            symbol: "equal"
        },
        {
            ruleFiled: "cancel_order",
            symbol: "interval"
        },
          {
              ruleFiled: "order state",
              symbol: "equal"
          }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477509",
    ruleName: "拥有现金券数量",
    ruleType: "hava_voucher_coupon",
    ruleValue: "hava_voucher_coupon",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "hava_voucher_coupon",
            symbol: "more than"
        },
        {
            ruleFiled: "hava_voucher_coupon",
            symbol: "less than"
        },
        {
            ruleFiled: "hava_voucher_coupon",
            symbol: "equal"
        },
        {
            ruleFiled: "hava_voucher_coupon",
            symbol: "interval"
        }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477510",
    ruleName: "拥有运费券数量",
    ruleType: "hava_shipping_coupon",
    ruleValue: "hava_shipping_coupon",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "hava_shipping_coupon",
            symbol: "more than"
        },
        {
            ruleFiled: "hava_shipping_coupon",
            symbol: "less than"
        },
        {
            ruleFiled: "hava_shipping_coupon",
            symbol: "equal"
        },
        {
            ruleFiled: "hava_shipping_coupon",
            symbol: "interval"
        }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "6857004643477511",
    ruleName: "使用优惠码",
    ruleType: "use_promo_code",
    ruleValue: "use_promo_code",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "use_promo_code",
            symbol: "more than"
        },
        {
            ruleFiled: "use_promo_code",
            symbol: "less than"
        },
        {
            ruleFiled: "use_promo_code",
            symbol: "equal"
        },
        {
            ruleFiled: "use_promo_code",
            symbol: "interval"
        }
    ],
    classification: 11
} );