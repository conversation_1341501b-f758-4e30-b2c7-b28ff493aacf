db.getCollection('collect_function_switch_config').insert({"name":"collect_home_page_switch","enable":true,"remark":"是否采集首页访问开关","createTime":new Date()});
db.getCollection('collect_function_switch_config').insert({"name":"report_home_page_active_user_switch","enable":true,"remark":"是否统计首页流量开关","createTime":new Date()});
db.getCollection('collect_function_switch_config').insert({"name":"delete_home_page_temp_switch","enable":true,"remark":"是否删除首页临时表开关","createTime":new Date()});
db.getCollection('collect_function_switch_config').createIndex({"name":1},{unique:true},{background:true});
db.getCollection('collect_buried_point_home_page_temp').createIndex({'createTime':1,'operatorNo':1,'deviceId':1},{background:true});
db.getCollection('collect_buried_point_takeaway_home_page_temp').createIndex({'createTime':1,'operatorNo':1,'deviceId':1},{background:true});
db.getCollection('report_home_page_online_user').createIndex({'createTime':1},{background:true});

//回滚
//db.collect_function_switch_config.dropIndex("name_1");
//db.collect_buried_point_home_page_temp.dropIndex("createTime_1_operatorNo_1_deviceId_1");
//db.collect_buried_point_takeaway_home_page_temp.dropIndex("createTime_1_operatorNo_1_deviceId_1");
//db.report_home_page_online_user.dropIndex("createTime_1");

