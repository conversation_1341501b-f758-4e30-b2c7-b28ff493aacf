db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
	"firstClassificationName":"用户属性",
	"secondTagClassificationNo": "2021092314125082022",
	"secondaryClassificationName":"默认"
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
	"firstClassificationName":"用户属性",
	"secondTagClassificationNo": "2021092314225082022",
	"secondaryClassificationName":"其他"
})

db.tag_rule.update(
{_id: ObjectId("611e5d62df26057fb3ba1ab1")},
{$set:{ timeliness:false,classification:10}},false,true)

db.tag_rule.update(
{_id: ObjectId("611e5d62df26057fb3ba1ab2")},
{$set:{ timeliness:false,classification:10}},false,true)

db.tag_rule.update(
{_id: ObjectId("611e5d63df26057fb3ba1ab3")},
{$set:{ timeliness:false,classification:10}},false,true)``


db.tag_rule.update(
{"classification":"行为"},
{$set:{"classification":11}},false,true)

db.tag_rule.update(
{"classification":"属性"},
{$set:{"classification":10}},false,true)

db.tag_info.update(
{_id: ObjectId("611e5d79df26057fb3ba1ab5")},
{$set:{firstClassification:"用户属性",secondaryClassification:"默认"}},false,true)

db.tag_info.update(
{_id: ObjectId("61237ec4ec32ce000141be31")},
{$set:{firstClassification:"用户属性",secondaryClassification:"默认"}},false,true)

db.tag_info.update(
{_id: ObjectId("6139bfbf3836330001e56a31")},
{$set:{firstClassification:"用户属性",secondaryClassification:"其他"}},false,true)


db.tag_info.update(
{_id: ObjectId("611e663aec32ce00013ff7aa")},
{$set:{firstClassification:"用户属性",secondaryClassification:"其他"}},false,true)

db.tag_info.update(
{_id: ObjectId("612ee5609f0b600001c53eff")},
{$set:{firstClassification:"用户属性",secondaryClassification:"其他"}},false,true)
