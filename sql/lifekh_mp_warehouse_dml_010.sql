db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "click",
    eventName: "发现页详情点击(浏览)",
    eventNo: "discovry_details_click",
    remark: "发现页详情点击(浏览)",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );


db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "click",
    eventName: "好物点击数",
    eventNo: "discovry_goods_click",
    remark: "好物点击数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );


db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "click",
    eventName: "加购数",
    eventNo: "add_shopcart",
    remark: "加购数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "click",
    eventName: "好物下单数",
    eventNo: "order_submit",
    remark: "好物下单数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );


