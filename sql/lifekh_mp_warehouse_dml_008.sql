db.report_pv.update({"currentPage":"HotSaleFragment"},{$set:{"alias":"电商商品专题的热销商品"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"WMMineViewController"},{$set:{"alias":"外卖我的页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"MoreFragment"},{$set:{"alias":"电商”我的“页"}},false,true);
db.report_pv.update({"currentPage":"SALoginViewController"},{$set:{"alias":"短信登陆页"}},false,true);
db.report_pv.update({"currentPage":"TNSpecialActivityViewController"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"SACouponListViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"GNHomeViewController"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"OrderShopFragment"},{$set:{"alias":"电商订单页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"ShopMainFragment"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"WMMineViewController"},{$set:{"alias":"外卖”我的“页"}},false,true);
db.report_pv.update({"currentPage":"ClassificationFragment"},{$set:{"alias":"电商分类页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"WMStoreListViewController"},{$set:{"alias":"外卖门店列表页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"SAAddressListViewController"},{$set:{"alias":"地址列表页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderSubmitV2ViewController"},{$set:{"alias":"外卖订单提交页"}},false,true);
db.report_pv.update({"currentPage":"GoodsSpecialTopicSubFragment"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseLanguageViewController"},{$set:{"alias":"选择语言页"}},false,true);
db.report_pv.update({"currentPage":"SABusinessCouponListViewController"},{$set:{"alias":"外卖我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"SAMyCouponsViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"HomeShopPageFragment"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderSubmitChooseAddressViewController"},{$set:{"alias":"外卖下单选择地址页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SASettingsViewController"},{$set:{"alias":"设置页"}},false,true);
db.report_pv.update({"currentPage":"HomeShopFragment"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"WMMineViewController"},{$set:{"alias":"外卖”我的“页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"GoodsSpecialTopicFragment"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"ActivityFragment"},{$set:{"alias":"外卖活动页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderListViewController"},{$set:{"alias":"订单列表页"}},false,true);
db.report_pv.update({"currentPage":"CouponPastFragmentS"},{$set:{"alias":"失效优惠券"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"HomeMenuFragment"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseZoneViewController"},{$set:{"alias":"选择地区页"}},false,true);
db.report_pv.update({"currentPage":"CouponFragmentSNew"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"SAMyCouponsViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"MineInfoFragmentS"},{$set:{"alias":"我的信息页"}},false,true);
db.report_pv.update({"currentPage":"RxPermissionsFragment"},{$set:{"alias":"电商权限页"}},false,true);
db.report_pv.update({"currentPage":"SAMyInfomationViewController"},{$set:{"alias":"我的信息页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"WMMineViewController"},{$set:{"alias":"外卖“我的”页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"RegisterFragment"},{$set:{"alias":"验证码输入页"}},false,true);
db.report_pv.update({"currentPage":"SALoginWithPwdViewController"},{$set:{"alias":"密码登录页"}},false,true);

db.report_pv.update({"currentPage":"SALoginViewController"},{$set:{"alias":"短信登录"}},false,true);


db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"CartFragment"},{$set:{"alias":"外卖购物车页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderViewController"},{$set:{"alias":"外卖订单页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"SAAddressListViewController"},{$set:{"alias":"地址列表页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderDetailViewController"},{$set:{"alias":"外卖订单详情页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderViewController"},{$set:{"alias":"外卖订单页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"MerchantListWithCategoryFragment"},{$set:{"alias":"外卖门店列表页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderDetailViewController"},{$set:{"alias":"外卖订单详情页"}},false,true);
db.report_pv.update({"currentPage":"SAAddOrModifyAddressViewController"},{$set:{"alias":"新增修改地址页"}},false,true);
db.report_pv.update({"currentPage":"ValidateDeliveryCouponFragment"},{$set:{"alias":"外卖优惠券选择页面"}},false,true);
db.report_pv.update({"currentPage":"SAAddressListViewController"},{$set:{"alias":"地址列表页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"WMStoreDetailViewController"},{$set:{"alias":"外卖门店详情页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderDetailViewController"},{$set:{"alias":"外卖订单详情页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderResultController"},{$set:{"alias":"外卖货到付款结果"}},false,true);
db.report_pv.update({"currentPage":"WMStoreDetailViewController"},{$set:{"alias":"外卖门店详情页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"SettingFragmentS"},{$set:{"alias":"设置页"}},false,true);
db.report_pv.update({"currentPage":"ValidateCouponFragment"},{$set:{"alias":"外卖优惠券选择页面"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"CartAddressSelectFragment"},{$set:{"alias":"外卖订单地址选择也"}},false,true);
db.report_pv.update({"currentPage":"WMStoreListViewController"},{$set:{"alias":"外卖门店列表页"}},false,true);
db.report_pv.update({"currentPage":"ForgetPswFragment"},{$set:{"alias":"手机号绑定页"}},false,true);
db.report_pv.update({"currentPage":"AdvertisementFragment"},{$set:{"alias":"启动页广告页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"SAAddOrModifyAddressViewController"},{$set:{"alias":"新增修改地址页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseAddressMapViewController"},{$set:{"alias":"地图选择地址页"}},false,true);
db.report_pv.update({"currentPage":"LoginWithPswFragment"},{$set:{"alias":"密码登录页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"TNOrderDetailsViewController"},{$set:{"alias":"电商订单详情"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseZoneViewController"},{$set:{"alias":"选择地区页"}},false,true);
db.report_pv.update({"currentPage":"MainActivity"},{$set:{"alias":"App页面容器"}},false,true);
db.report_pv.update({"currentPage":"WMStoreDetailViewController"},{$set:{"alias":"外卖门店详情页"}},false,true);
db.report_pv.update({"currentPage":"PaySuccFragment"},{$set:{"alias":"外卖支付成功页"}},false,true);
db.report_pv.update({"currentPage":"SubOrderFragment"},{$set:{"alias":"外卖订单页"}},false,true);
db.report_pv.update({"currentPage":"RxPermissionsFragment"},{$set:{"alias":"外卖系统授权页"}},false,true);
db.report_pv.update({"currentPage":"SACouponListViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"SABusinessCouponListViewController"},{$set:{"alias":"业务线我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseAddressMapViewController"},{$set:{"alias":"地图选择地址页"}},false,true);
db.report_pv.update({"currentPage":"LoginFragment"},{$set:{"alias":"登录容器页"}},false,true);
db.report_pv.update({"currentPage":"SAMyCouponsViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderViewController"},{$set:{"alias":"外卖订单页"}},false,true);
db.report_pv.update({"currentPage":"WMStoreDetailViewController"},{$set:{"alias":"外卖门店详情页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderSubmitV2ViewController"},{$set:{"alias":"外卖订单提交页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterListViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"CartSubmitFragmentApollo"},{$set:{"alias":"外卖订单提交页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderListViewController"},{$set:{"alias":"订单列表页"}},false,true);
db.report_pv.update({"currentPage":"RxPermissionsFragment"},{$set:{"alias":"外卖系统授权页"}},false,true);
db.report_pv.update({"currentPage":"CartFragment"},{$set:{"alias":"外卖购物车页"}},false,true);
db.report_pv.update({"currentPage":"UserFragment"},{$set:{"alias":"外卖“我的”页"}},false,true);
db.report_pv.update({"currentPage":"OrderDeliveryFragment"},{$set:{"alias":"外卖订单页"}},false,true);
db.report_pv.update({"currentPage":"MainFragment"},{$set:{"alias":"首页容器页"}},false,true);
db.report_pv.update({"currentPage":"HomeDeliveryFragment"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"HomeMenuFragment"},{$set:{"alias":"外卖首页容器"}},false,true);
db.report_pv.update({"currentPage":"SACouponListViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"MerchantDetailFragment"},{$set:{"alias":"外卖门店详情页"}},false,true);
db.report_pv.update({"currentPage":"SpSubOrderFragment"},{$set:{"alias":"订单列表页"}},false,true);
db.report_pv.update({"currentPage":"OrderDetailFragment"},{$set:{"alias":"订单详情页"}},false,true);
db.report_pv.update({"currentPage":"MessageNewFragment"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"MineFragment"},{$set:{"alias":"我的信息页"}},false,true);
db.report_pv.update({"currentPage":"SupportRequestManagerFragment"},{$set:{"alias":"Glide图片库监听页"}},false,true);
db.report_pv.update({"currentPage":"HomeChangeFragment"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SpMainFragment"},{$set:{"alias":"WOWNOW首页容器"}},false,true);
db.report_pv.update({"currentPage":"RxPermissionsFragment"},{$set:{"alias":"WOWNOW系统授权页"}},false,true);
db.report_pv.update({"currentPage":"SpOrderFragment"},{$set:{"alias":"订单列表页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeContentViewController"},{$set:{"alias":"电商首页分类容器页"}},false,true);
db.report_pv.update({"currentPage":"SAChatMessageViewController"},{$set:{"alias":"聊天页"}},false,true);
db.report_pv.update({"currentPage":"WMSpecialActivesViewController"},{$set:{"alias":"外卖专题活动页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderSubmitV2ViewController"},{$set:{"alias":"外卖订单提交页"}},false,true);
db.report_pv.update({"currentPage":"SAPayResultViewController"},{$set:{"alias":"支付结果页"}},false,true);
db.report_pv.update({"currentPage":"TNClassificationViewController"},{$set:{"alias":"电商分类页"}},false,true);
db.report_pv.update({"currentPage":"HDCheckStandConfirmViewController"},{$set:{"alias":"收银台确认页"}},false,true);
db.report_pv.update({"currentPage":"TNSpeciaActivityContentViewController"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"SAChatMessageViewController"},{$set:{"alias":"聊天单聊页"}},false,true);
db.report_pv.update({"currentPage":"SAWalletViewController"},{$set:{"alias":"钱包页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderListViewController"},{$set:{"alias":"订单列表页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"HDCheckStandConfirmViewController"},{$set:{"alias":"收银台确认页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseMyAddressViewController"},{$set:{"alias":"新增修改地址页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"TNSpecialActivityViewController"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderDetailViewController"},{$set:{"alias":"外卖订单详情页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterListViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"TNOrderViewController"},{$set:{"alias":"电商订单页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"HDCheckStandChoosePaymentMethodViewController"},{$set:{"alias":"收银台支付方式选择页"}},false,true);
db.report_pv.update({"currentPage":"SATopUpOrderDetailViewController"},{$set:{"alias":"话费充值详情页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"OrderEvaluateFragment"},{$set:{"alias":"外卖订单评价页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"SASuggestionViewController"},{$set:{"alias":"建议与反馈"}},false,true);
db.report_pv.update({"currentPage":"TNStoreInfoViewController"},{$set:{"alias":"电商门店页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SAChooseAddressViewController"},{$set:{"alias":"选择地址页"}},false,true);
db.report_pv.update({"currentPage":"TNSearchViewController"},{$set:{"alias":"电商搜索页"}},false,true);
db.report_pv.update({"currentPage":"WMSpecialActivesViewController"},{$set:{"alias":"外卖专题活动页"}},false,true);
db.report_pv.update({"currentPage":"WMShoppingCartViewController"},{$set:{"alias":"外卖购物车页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"WMSpecialActivesViewController"},{$set:{"alias":"外卖专题活动页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"AddressSelectFragment"},{$set:{"alias":"外卖首页选择地址页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SAMyCouponsViewController"},{$set:{"alias":"我的优惠券页"}},false,true);
db.report_pv.update({"currentPage":"WMHomeViewController"},{$set:{"alias":"外卖首页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"LauncherLanguageFragment"},{$set:{"alias":"首次使用语言选择页"}},false,true);
db.report_pv.update({"currentPage":"TNSpecialActivityViewController"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"GroupOrderDetailFragment"},{$set:{"alias":"团购订单详情页"}},false,true);
db.report_pv.update({"currentPage":"SAStartupAdController"},{$set:{"alias":"启动广告页"}},false,true);
db.report_pv.update({"currentPage":"TNSpeciaActivityContentViewController"},{$set:{"alias":"电商专题活动页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"SubOrderGroupFragment"},{$set:{"alias":"团购订单页"}},false,true);
db.report_pv.update({"currentPage":"GroupMainFragment"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"GroupMerchantDetailFragment"},{$set:{"alias":"团购门店详情页"}},false,true);
db.report_pv.update({"currentPage":"MessageSystemFragment"},{$set:{"alias":"团购消息页"}},false,true);
db.report_pv.update({"currentPage":"RxPermissionsFragment"},{$set:{"alias":"团购系统授权页"}},false,true);
db.report_pv.update({"currentPage":"GroupHomeMenuFragment"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"GroupHomeFragment"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"GroupOrderFragment"},{$set:{"alias":"团购订单页"}},false,true);
db.report_pv.update({"currentPage":"GroupRecommendSubFragment"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeListViewController"},{$set:{"alias":"电商首页列表页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);
db.report_pv.update({"currentPage":"RefundDetailFragment"},{$set:{"alias":"退款详情页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeContentViewController"},{$set:{"alias":"电商首页分类容器页"}},false,true);
db.report_pv.update({"currentPage":"SAOrderViewController"},{$set:{"alias":"订单页"}},false,true);
db.report_pv.update({"currentPage":"SAMessageCenterViewController"},{$set:{"alias":"消息页"}},false,true);
db.report_pv.update({"currentPage":"SAMineViewController"},{$set:{"alias":"我的页"}},false,true);
db.report_pv.update({"currentPage":"TNHomeViewController"},{$set:{"alias":"电商首页"}},false,true);
db.report_pv.update({"currentPage":"TNMoreViewController"},{$set:{"alias":"电商更多"}},false,true);
db.report_pv.update({"currentPage":"ShopOrderSubmitSuccessFragment"},{$set:{"alias":"电商订单提交成功页"}},false,true);
db.report_pv.update({"currentPage":"OrderSubmitFragment"},{$set:{"alias":"电商订单页"}},false,true);
db.report_pv.update({"currentPage":"ShopCartFragment"},{$set:{"alias":"电商购物车页"}},false,true);
db.report_pv.update({"currentPage":"ShopOrderDetailFragment"},{$set:{"alias":"电商订单详情页"}},false,true);
db.report_pv.update({"currentPage":"SubOrderFragment"},{$set:{"alias":"电商订单列表页"}},false,true);
db.report_pv.update({"currentPage":"GNHomeViewController"},{$set:{"alias":"团购首页"}},false,true);
db.report_pv.update({"currentPage":"WMOrderViewController"},{$set:{"alias":"外卖订单"}},false,true);
db.report_pv.update({"currentPage":"WMShoppingCartViewController"},{$set:{"alias":"外卖购物车页"}},false,true);
db.report_pv.update({"currentPage":"TNMoreViewController"},{$set:{"alias":"电商更多页"}},false,true);
db.report_pv.update({"currentPage":"GNStoreDetailViewController"},{$set:{"alias":"团购门店详情页"}},false,true);
db.report_pv.update({"currentPage":"SANewHomeViewController"},{$set:{"alias":"WOWNOW首页"}},false,true);