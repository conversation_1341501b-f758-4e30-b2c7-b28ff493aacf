db.event_type.update({"eventNo": "@login"}, {$set:{"table": "collect_behavior_v2"}});
db.event_type.update({"eventNo": "@openApp"}, {$set:{"table": "collect_behavior_v2"}});
db.event_type.update({"eventNo": "@sessionEnd"}, {$set:{"table": "collect_behavior_v2"}});
db.event_type.update({"eventNo": "@sessionStart"}, {$set:{"table": "collect_behavior_v2"}});
db.event_type.update({"eventNo": "firstOpen"}, {$set:{"table": "collect_behavior_v2"}});
db.event_type.update({"eventNo": "active_user"}, {$set:{"table": "collect_behavior_v2"}});

db.event_type.update({"eventNo": "@click"}, {$set:{"table": "collect_buried_point_click_v2"}});

db.event_type.update({"eventNo": "@viewPage"}, {$set:{"table": "collect_buried_point_viewPage_v2"}});
db.event_type.update({"eventNo": "WebPageViews"}, {$set:{"table": "collect_buried_point_web_view"}});

db.event_type.update({"eventNo": "@DEBUG"}, {$set:{"table": "collect_buried_point_debug"}});

db.event_type.update({"eventNo": "click_forget_password_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_switch_language_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_rebuy_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_switch_language"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_on_orderList_category"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_messageCenter_category"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_message_list"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_messageCenter_readAll_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_my_coupon_category"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_use_coupon_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "click_invaid_coupons_button"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "browseTheme"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "browseEOT"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "browseADS"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "browseTopicPage"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "intoStore"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "clickBtn"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "browserHomeNotice"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "addShopCart"}, {$set:{"table": "collect_buried_point_other_v2"}});
db.event_type.update({"eventNo": "placeOrder"}, {$set:{"table": "collect_buried_point_other_v2"}});

db.event_type.update({"eventNo": "discovry_details_click"}, {$set:{"table": "collect_buried_point_discovry"}});
db.event_type.update({"eventNo": "discovry_goods_click"}, {$set:{"table": "collect_buried_point_discovry"}});
db.event_type.update({"eventNo": "discover_page_share_result"}, {$set:{"table": "collect_buried_point_discovry"}});
db.event_type.update({"eventNo": "discover_page_click_share"}, {$set:{"table": "collect_buried_point_discovry"}});

db.event_type.update({"eventNo": "register_page_pv"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "register_page_click_get"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "register_page_click_sendSMS"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "register_page_click_gouse"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "register_page_register_result"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "preRegister_page_pv"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "preRegister_page_click_get"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "preRegister_page_click_gouse"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "preRegister_page_register_result"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "invite_page_pv"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "invite_page_click_immediately_invited"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "invite_page_click_scanCode"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "invite_page_share_result"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "download_page_pv"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "download_page_click_download"}, {$set:{"table": "collect_buried_point_activity"}});

db.event_type.update({"eventNo": "sms_marketing_downloadPagePV"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "sms_marketing_welfareClick"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "sms_marketing_sendCouponsPagePV"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "LuckyEggsPV"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "HitEgg_ReceivePrize"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "LuckyEggs_ShareFriends_click"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "LuckyEggs_UseImmediately_click"}, {$set:{"table": "collect_buried_point_activity"}});
db.event_type.update({"eventNo": "LuckyEggs_DownloadPV"}, {$set:{"table": "collect_buried_point_activity"}});

db.event_type.update({"eventNo": "add_shopcart"}, {$set:{"table": "collect_buried_point_order"}});
db.event_type.update({"eventNo": "order_submit"}, {$set:{"table": "collect_buried_point_order"}});

db.collect_buried_point_web_view.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_debug.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_debug.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_debug.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_discovry.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_discovry.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_discovry.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_activity.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_order.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_order.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_order.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.invite_register_record.createIndex({"createTime":-1},{background:true});

db.USER_LABEL.createIndex({"CREATE_TIME":-1},{background:true});
