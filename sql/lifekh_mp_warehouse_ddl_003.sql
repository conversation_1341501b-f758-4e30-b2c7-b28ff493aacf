db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
		"firstClassificationName":"用户属性",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})

db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
		"firstClassificationName":"用户属性",
		"secondTagClassificationNo": "2021090629125082022",
		"secondaryClassificationName":"地区"
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
		"firstClassificationName":"用户属性",
		"secondTagClassificationNo": "2021090639125082022",
		"secondaryClassificationName":"语言"
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
		"firstClassificationName":"用户属性",
		"secondTagClassificationNo": "2021090649125082022",
		"secondaryClassificationName":"内测"
})

db.getCollection("tag_classification").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagClassificationBO",
    createTime: ISODate("2021-08-26T01:14:39.37Z"),
    firstTagClassificationNo: "1419569629325082021",
    firstClassificationName: "用户交易",
    secondTagClassificationNo: "",
    secondaryClassificationName: ""
} );
