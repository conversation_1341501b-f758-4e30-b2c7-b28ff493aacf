db.tag_classification.find().forEach(
   function(item){
			db.tag_classification.update({secondTagClassificationNo:item.secondTagClassificationNo},{$set:{secondaryClassificationNameEn: item.secondaryClassificationName,secondaryClassificationNameKm:item.secondaryClassificationName,tagClassify:'public'}},{multi:true});
			db.tag_info.update({secondaryClassification:item.secondaryClassificationName},
				{$set:{firstClassificationNo: item.firstTagClassificationNo,
				secondaryClassificationNo: item.secondTagClassificationNo,
				secondaryClassificationEn: item.secondaryClassificationName,
				secondaryClassificationKm: item.secondaryClassificationName
		}},{multi:true});
   }
);


db.tag_info.find({"rule.ruleType": 'customize'}).forEach(
   function(item){
		db.tag_info.update({tagNo:item.tagNo},
		{$set:{tagScope: 'everyone',
		execType: 'manual',
		execStatus: 'effective'
		}},{multi:true});
   }
);

db.tag_info.find({"rule.ruleType" : { $ne : "customize" }}).forEach(
   function(item){
		db.tag_info.update({tagNo:item.tagNo},
		{$set:{
		tagScope: 'everyone',
		execType: 'system',
		execStatus: 'effective'
		}},{multi:true});
   }
);

db.tag_info.find({ $or : [{"rule.ruleType" : "customize"}, {"tagType" : "rule"}] }).forEach(
   function(item){
	db.tag_info.update({tagNo:item.tagNo},{$set:{tagCatalogue: 'customize'}},{multi:true});
   }
);

db.tag_info.find({ $or : [{"tagCatalogue" : { $ne : "customize" }}, {"tagNo" : "2021110211551498496"}] }).forEach(
   function(item){
	db.tag_info.update({tagNo:item.tagNo},{$set:{tagCatalogue: 'system'}},{multi:true});
   }
);

db.tag_rule.update({ruleType:'order success'},{$set:{ruleName: '完成订单总次数'}},{multi:true});
db.tag_rule.update({ruleType:'order amount'},{$set:{ruleName: '完成订单累计金额'}},{multi:true});

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "Login/Register_Page_PV",
	eventName: "进入登录/注册页面浏览数pv",
	remark: "进入登录/注册页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "Password_Login_Page_PV",
	eventName: "进入密码登录页面浏览数pv",
	remark: "进入密码登录页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "Forgot_Password_Page_PV",
	eventName: "进入忘记密码页面浏览数pv",
	remark: "进入忘记密码页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "Forgot_PasswordPage_Set_Password_Page_PV",
	eventName: "忘记密码-设置密码页面浏览数PV",
	remark: "忘记密码-设置密码页面浏览数PV",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMS_Login_Page_PV",
	eventName: "进入短信登录页面浏览数pv",
	remark: "进入短信登录页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMSLoginPage_Get_Verification_Code_Page_PV",
	eventName: "进入短信获取验证码页面浏览数pv",
	remark: "进入短信获取验证码页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMSLoginPage_Set_Password_Page_PV",
	eventName: "短信登录-设置密码页面浏览数pv",
	remark: "短信登录-设置密码页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMS_Registered_Page_PV",
	eventName: "进入短信注册页面浏览数pv",
	remark: "进入短信注册页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMSRegisteredPage_Get_Verification_Code_Page_PV",
	eventName: "进入短信获取验证码页面浏览数pv",
	remark: "进入短信获取验证码页面浏览数pv",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "SMSRegisteredPage_Set_Password_Page_PV",
	eventName: "短信注册-设置密码页面浏览数",
	remark: "短信注册-设置密码页面浏览数",
	table: "collect_buried_point_login_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.collect_buried_point_login_page.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_login_page.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_login_page.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_login_page.createIndex({"userInfoBo.loginName":-1},{background:true});

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AggregateSearchKeyWordStat",
	eventName: "用户搜索",
	remark: "用户搜索",
	table: "collect_buried_point_search",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.collect_buried_point_search.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_search.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_search.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_search.createIndex({"userInfoBo.loginName":-1},{background:true});


db.getCollection("tag_classification").insert( {
    createTime: new Date(),
    firstTagClassificationNo: "1579732389059194881",
    firstClassificationName: "用户行为",
    secondTagClassificationNo: "",
    secondaryClassificationName: "",
    secondaryClassificationNameEn: "",
    secondaryClassificationNameKm: "",
    tagClassify: "public"
} );

db.getCollection("tag_classification").insert( {
    createTime: new Date(),
    firstTagClassificationNo: "1579732389904023553",
    firstClassificationName: "其他",
    secondTagClassificationNo: "",
    secondaryClassificationName: "",
    secondaryClassificationNameEn: "",
    secondaryClassificationNameKm: "",
    tagClassify: "public"
} );
