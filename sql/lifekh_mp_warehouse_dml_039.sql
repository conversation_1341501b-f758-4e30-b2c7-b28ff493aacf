
db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPage_PV",
	eventName: "每日有奖页面PV",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPageShare_Click",
	eventName: "每日有奖-分享按钮点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPageSignIn_Click",
	eventName: "每日有奖-签到有奖点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );


db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPageGoAndFinish_Click",
	eventName: "每日有奖-去完成任务点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPagePointsDraw_Click",
	eventName: "每日有奖-积分抽奖点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardPagePointsTreasure_Click",
	eventName: "每日有奖-积分夺宝点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "AwardSharePage_PV",
	eventName: "每日有奖分享出的页面PV",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );