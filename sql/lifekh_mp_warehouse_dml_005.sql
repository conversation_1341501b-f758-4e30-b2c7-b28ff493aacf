db.getCollection("event_type").insert( {
    createTime: ISODate("2021-09-28T03:21:15.191Z"),
    updateTime: ISODate("2021-09-28T03:21:15.191Z"),
    eventGroup: "login",
    eventNo: "@login",
    eventName: "登录",
    bussinessLine: "all",
    status: 10,
    remark: "登录事件",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-09-29T06:27:05.441Z"),
    updateTime: ISODate("2021-09-29T06:27:05.441Z"),
    eventGroup: "login",
    eventNo: "@openApp",
    eventName: "打开APP",
    bussinessLine: "all",
    status: 10,
    remark: "登录事件",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-11T01:19:49.902Z"),
    updateTime: ISODate("2021-10-11T01:19:49.902Z"),
    eventGroup: "viewPage",
    eventNo: "@viewPage",
    eventName: "页面访问",
    bussinessLine: "all",
    status: 10,
    remark: "页面访问",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.837Z"),
    updateTime: ISODate("2021-10-19T09:45:51.837Z"),
    eventGroup: "click",
    eventNo: "@click",
    eventName: "点击事件",
    bussinessLine: "all",
    status: 10,
    remark: "点击事件",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_forget_password_button",
    eventName: "忘记密码点击次数",
    bussinessLine: "all",
    status: 10,
    remark: "选择密码登录方式，点击忘记密码按钮触发",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.844Z"),
    updateTime: ISODate("2021-10-19T09:45:51.844Z"),
    eventGroup: "other",
    eventNo: "click_switch_language_button",
    eventName: "语言切换",
    bussinessLine: "all",
    status: 10,
    remark: "语言切换",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.848Z"),
    updateTime: ISODate("2021-10-19T09:45:51.848Z"),
    eventGroup: "other",
    eventNo: "click_rebuy_button",
    eventName: "订单-全部-再来一单",
    bussinessLine: "all",
    status: 10,
    remark: "订单-全部-再来一单",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.857Z"),
    updateTime: ISODate("2021-10-19T09:45:51.857Z"),
    eventGroup: "other",
    eventNo: "click_switch_language",
    eventName: "我的-语言",
    bussinessLine: "all",
    status: 10,
    remark: "我的-语言",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-09-28T03:21:15.191Z"),
    updateTime: ISODate("2021-09-28T03:21:15.191Z"),
    eventGroup: "login",
    eventNo: "@sessionStart",
    eventName: "打开APP",
    bussinessLine: "all",
    status: 10,
    remark: "登录事件-打开APP",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-09-28T03:21:15.191Z"),
    updateTime: ISODate("2021-09-28T03:21:15.191Z"),
    eventGroup: "login",
    eventNo: "@sessionEnd",
    eventName: "关闭APP",
    bussinessLine: "all",
    status: 10,
    remark: "登录事件-关闭APP",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_on_orderList_category",
    eventName: "待处理/待评价",
    bussinessLine: "all",
    status: 10,
    remark: "待处理/待评价",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_messageCenter_category",
    eventName: "优惠/个人/聊天",
    bussinessLine: "all",
    status: 10,
    remark: "优惠/个人/聊天",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_message_list",
    eventName: "优惠/个人/聊天",
    bussinessLine: "all",
    status: 10,
    remark: "营销/个人/聊天",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_messageCenter_readAll_button",
    eventName: "点击全部已读",
    bussinessLine: "all",
    status: 10,
    remark: "点击全部已读",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_my_coupon_category",
    eventName: "我的-优惠券-全部页面访问",
    bussinessLine: "all",
    status: 10,
    remark: "我的-优惠券-全部页面访问",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_use_coupon_button",
    eventName: "点击优惠券立即使用",
    bussinessLine: "all",
    status: 10,
    remark: "点击优惠券立即使用",
    createBy: "wgl",
    updateBy: "wgl"
} );
db.getCollection("event_type").insert( {
    createTime: ISODate("2021-10-19T09:45:51.841Z"),
    updateTime: ISODate("2021-10-19T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "click_invaid_coupons_button",
    eventName: "点击失效优惠券按钮",
    bussinessLine: "all",
    status: 10,
    remark: "点击失效优惠券按钮",
    createBy: "wgl",
    updateBy: "wgl"
} );
