db.event_type.insert( {eventGroup: "other",eventNo: "order_submitV2",eventName: "外卖_下单",remark: "",table: "collect_buried_point_takeaway_order_submit",status: 10,bussinessLine: "all",createBy: "system",createTime: new Date(),updateBy: "system",updateTime: new Date()});
db.event_type.insert( {eventGroup: "other",eventNo: "order_submit_pv",eventName: "订单提交页PV",remark: "",table: "collect_buried_point_takeaway_order_submit_pv",status: 10,bussinessLine: "all",createBy: "system",createTime: new Date(),updateBy: "system",updateTime: new Date()});
db.event_type.insert( {eventGroup: "other",eventNo: "add_shopcartV2",eventName: "外卖_购物车_加购",remark: "",table: "collect_buried_point_takeaway_add_shopcart",status: 10,bussinessLine: "all",createBy: "system",createTime: new Date(),updateBy: "system",updateTime: new Date()});
db.event_type.insert( {eventGroup: "other",eventNo: "product_detail_pv",eventName: "外卖商品PV",remark: "",table: "collect_buried_point_takeaway_product_detail_pv",status: 10,bussinessLine: "all",createBy: "system",createTime: new Date(),updateBy: "system",updateTime: new Date()});
db.event_type.insert( {eventGroup: "other",eventNo: "store_detail_pv",eventName: "外卖门店PV",remark: "",table: "collect_buried_point_takeaway_store_detail_pv",status: 10,bussinessLine: "all",createBy: "system",createTime: new Date(),updateBy: "system",updateTime: new Date()});
db.getCollection('collect_buried_point_takeaway_order_submit').createIndex({'createTime':1,'userInfoBo.operatorNo':1},{background:true});
db.getCollection('collect_buried_point_takeaway_add_shopcart').createIndex({'createTime':1,'userInfoBo.operatorNo':1},{background:true});
db.getCollection('collect_buried_point_takeaway_order_submit_pv').createIndex({'createTime':1,'userInfoBo.operatorNo':1},{background:true});
db.getCollection('collect_buried_point_takeaway_product_detail_pv').createIndex({'createTime':1,'userInfoBo.operatorNo':1},{background:true});
db.getCollection('collect_buried_point_takeaway_store_detail_pv').createIndex({'createTime':1,'ext.storeNo':1,'userInfoBo.operatorNo':1},{background:true});
//回滚
//db.collect_buried_point_takeaway_store_detail_pv.dropIndex("createTime_1_ext.storeNo_1_userInfoBo.operatorNo_1");
//db.collect_buried_point_takeaway_product_detail_pv.dropIndex("createTime_1_userInfoBo.operatorNo_1");
//db.collect_buried_point_takeaway_add_shopcart.dropIndex("createTime_1_userInfoBo.operatorNo_1");
//db.collect_buried_point_takeaway_order_submit_pv.dropIndex("createTime_1_userInfoBo.operatorNo_1");
//db.collect_buried_point_takeaway_order_submit.dropIndex("createTime_1_userInfoBo.operatorNo_1");
