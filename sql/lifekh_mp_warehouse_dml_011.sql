db.discovry_details_click.createIndex({'contentNo':1})
db.discovry_details_click.createIndex({'recordTime':-1})
db.discovry_details_click.createIndex({'userInfoBo.loginName':1})
db.discovry_details_click.createIndex({'deviceInfoBo.deviceId':1})
db.discovry_details_click.createIndex({'contentCategory':1,'contentLanguage':1,'contentBusinessLine':1})

db.discovry_goods_click.createIndex({'contentNo':1})
db.discovry_goods_click.createIndex({'recordTime':-1})
db.discovry_goods_click.createIndex({'goodsLink':1})
db.discovry_goods_click.createIndex({'userInfoBo.loginName':1})
db.discovry_goods_click.createIndex({'deviceInfoBo.deviceId':1})
db.discovry_goods_click.createIndex({'eventBo.event':1})
db.discovry_goods_click.createIndex({'contentCategory':1,'contentLanguage':1,'contentBusinessLine':1})


db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "other",
    eventName: "发现页详情点击(浏览)",
    eventNo: "discovry_details_click",
    remark: "发现页详情点击(浏览)",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "other",
    eventName: "好物点击数",
    eventNo: "discovry_goods_click",
    remark: "好物点击数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "other",
    eventName: "加购数",
    eventNo: "add_shopcart",
    remark: "加购数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );
db.getCollection("event_type").insert( {
    bussinessLine: "all",
    createBy: "wgl",
    createTime: ISODate("2022-02-15T09:45:51.837Z"),
    eventGroup: "other",
    eventName: "好物下单数",
    eventNo: "order_submit",
    remark: "好物下单数",
    status: 10,
    updateBy: "wgl",
    updateTime: ISODate("2022-02-15T09:45:51.837Z")
} );


db.collect_buried_point_other_v2.createIndex({'eventBo.event':1})
