db.event_type.update({table:'collect_buried_point_viewPage_v2'},{$set:{table: 'collect_buried_point_viewPage_v3'}},{multi:true});

db.collect_buried_point_viewPage_v3.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_viewPage_v3.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_viewPage_v3.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_viewPage_v3.createIndex({"currentPage":-1},{background:true});


db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009c5"),
    pageCode: "WQCodeScanner",
    pageName: "扫一扫",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009c6"),
    pageCode: "WNHomeViewController",
    pageName: "WOWNOW首页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "home"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009c7"),
    pageCode: "WMStoreSortViewController",
    pageName: "外卖门店分类页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009c8"),
    pageCode: "WMStoreSearchViewController",
    pageName: "外卖门店搜索页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009c9"),
    pageCode: "WMStoreReviewsViewController",
    pageName: "外卖门店评论页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ca"),
    pageCode: "WMStoreReviewAndInfoViewController",
    pageName: "外卖门店评论页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009cb"),
    pageCode: "WMStoreProductReviewListViewController",
    pageName: "外卖商品评论页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009cc"),
    pageCode: "WMStoreProductDetailViewController",
    pageName: "外卖商品详情页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009cd"),
    pageCode: "WMStoreListViewController",
    pageName: "外卖门店列表页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ce"),
    pageCode: "WMStoreInfoViewController",
    pageName: "外卖门店信息页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009cf"),
    pageCode: "WMStoreFavoutiteListController",
    pageName: "外卖门店收藏页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d0"),
    pageCode: "WMStoreDetailViewController",
    pageName: "外卖门店页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d1"),
    pageCode: "WMSpecialActivesViewController",
    pageName: "外卖专题页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d2"),
    pageCode: "WMShoppingCartViewController",
    pageName: "外卖购物车页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d3"),
    pageCode: "WMSelectCouponsViewController",
    pageName: "外卖选择优惠券页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d4"),
    pageCode: "WMRefundDetailViewController",
    pageName: "外卖退款详情页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d5"),
    pageCode: "WMProductSearchViewController",
    pageName: "外卖商品搜索页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d6"),
    pageCode: "WMProductPackingFeeViewController",
    pageName: "外卖包装费明细页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d7"),
    pageCode: "WMOrderViewController",
    pageName: "外卖订单列表页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d8"),
    pageCode: "WMOrderSubmitWriteNoteViewController",
    pageName: "外卖填写备注页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009d9"),
    pageCode: "WMOrderSubmitV2ViewController",
    pageName: "外卖提交订单页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009da"),
    pageCode: "WMOrderSubmitChooseCouponViewController",
    pageName: "外卖选择优惠券页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009db"),
    pageCode: "WMOrderSubmitChooseAddressViewController",
    pageName: "外卖选择地址页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009dc"),
    pageCode: "WMOrderResultController",
    pageName: "外卖货到付款结果页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009dd"),
    pageCode: "WMOrderRefundApplyViewController",
    pageName: "外卖退款申请页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009de"),
    pageCode: "WMOrderFeedBackViewController",
    pageName: "外卖问题反馈页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009df"),
    pageCode: "WMOrderEvaluationViewController",
    pageName: "外卖订单评价页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e0"),
    pageCode: "WMOrderDetailViewController",
    pageName: "外卖订单详情页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e1"),
    pageCode: "WMMyReviewsViewController",
    pageName: "外卖我的评论页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e2"),
    pageCode: "WMMineViewController",
    pageName: "外卖我的页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e3"),
    pageCode: "WMHomeViewController",
    pageName: "外卖首页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e4"),
    pageCode: "WMFeedBackDetailViewController",
    pageName: "外卖问题反馈详情页",
    businessLine: "YumNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e5"),
    pageCode: "TNWithdrawDetailViewController",
    pageName: "电商退款详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e6"),
    pageCode: "TNWithdrawBindViewController",
    pageName: "电商提现绑卡页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e7"),
    pageCode: "TNTransferViewController",
    pageName: "电商转账付款页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e8"),
    pageCode: "TNSubmitReviewController",
    pageName: "电商发表商品评论页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009e9"),
    pageCode: "TNStoreInfoViewController",
    pageName: "电商门店详情",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ea"),
    pageCode: "TNStoreFavoritesViewController",
    pageName: "电商店铺收藏",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009eb"),
    pageCode: "TNSpecialActivityViewController",
    pageName: "电商专题活动页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ec"),
    pageCode: "TNSpeciaActivityContentViewController",
    pageName: "电商专题活动页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ed"),
    pageCode: "TNShoppingCarViewController",
    pageName: "电商购物车页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ee"),
    pageCode: "TNSellerSearchViewController",
    pageName: "电商卖家搜索页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ef"),
    pageCode: "TNSellerOrderViewController",
    pageName: "电商卖家订单页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f0"),
    pageCode: "TNSellerApplyViewController",
    pageName: "电商卖家申请页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f1"),
    pageCode: "TNSearchViewController",
    pageName: "电商搜索页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f2"),
    pageCode: "TNRefundViewController",
    pageName: "电商退货页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f3"),
    pageCode: "TNRefundDetailViewController",
    pageName: "电商退款详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f4"),
    pageCode: "TNProductDetailsViewController",
    pageName: "电商商品详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f5"),
    pageCode: "TNProductDeliveryInfoViewController",
    pageName: "电商配送信息页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f6"),
    pageCode: "TNProductCenterViewController",
    pageName: "电商选品中心页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f7"),
    pageCode: "TNPreIncomeRecordViewController",
    pageName: "电商预估收益记录页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f8"),
    pageCode: "TNPictureSearchViewController",
    pageName: "电商图片搜索页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009f9"),
    pageCode: "TNPaymentResultViewController",
    pageName: "电商货到付款结果页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009fa"),
    pageCode: "TNOrderViewController",
    pageName: "电商订单列表页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009fb"),
    pageCode: "TNOrderSubmitViewController",
    pageName: "电商提交订单页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009fc"),
    pageCode: "TNOrderDetailsViewController",
    pageName: "电商订单详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009fd"),
    pageCode: "TNNotReviewViewController",
    pageName: "电商提示评论页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009fe"),
    pageCode: "TNMyReviewViewController",
    pageName: "电商我的评论页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab480360000100009ff"),
    pageCode: "TNMyFavoritesViewController",
    pageName: "电商我的收藏页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a00"),
    pageCode: "TNMoreViewController",
    pageName: "电商更多页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a01"),
    pageCode: "TNMicroShopViewController",
    pageName: "电商我的微店页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a02"),
    pageCode: "TNIncomeViewController",
    pageName: "电商我的收益页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a03"),
    pageCode: "TNIncomeRecordViewController",
    pageName: "电商我的收益记录页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a04"),
    pageCode: "TNIncomeDetailViewController",
    pageName: "电商我的收益详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a05"),
    pageCode: "TNHomeViewController",
    pageName: "电商首页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a06"),
    pageCode: "TNHomeListViewController",
    pageName: "电商首页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a07"),
    pageCode: "TNHomeContentViewController",
    pageName: "电商首页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a08"),
    pageCode: "TNHadReviewViewController",
    pageName: "电商已评价列表页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a09"),
    pageCode: "TNGuideViewController",
    pageName: "电商转账引导页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0a"),
    pageCode: "TNGoodReviewViewController",
    pageName: "电商评论页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0b"),
    pageCode: "TNGoodFavoritesViewController",
    pageName: "电商商品收藏页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0c"),
    pageCode: "TNFillMemoViewController",
    pageName: "电商备注填写页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0d"),
    pageCode: "TNExpressTrackingViewController",
    pageName: "电商物流跟踪页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0e"),
    pageCode: "TNExpressDetailsViewController",
    pageName: "电商物流详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a0f"),
    pageCode: "TNDeliveryAreaMapViewController",
    pageName: "电商配送区域查看页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a10"),
    pageCode: "TNClassificationViewController",
    pageName: "电商分类页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a11"),
    pageCode: "TNChooseCouponViewController",
    pageName: "电商选择优惠券页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a12"),
    pageCode: "TNBargainRuleViewController",
    pageName: "电商商品砍价规则页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a13"),
    pageCode: "TNBargainProductDetailViewController",
    pageName: "电商砍价商品详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a14"),
    pageCode: "TNBargainMyRecordViewController",
    pageName: "电商砍价记录页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a15"),
    pageCode: "TNBargainDetailViewController",
    pageName: "电商砍价详情页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a16"),
    pageCode: "TNBargainActivityViewController",
    pageName: "电商砍价活动页",
    businessLine: "TinhNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a17"),
    pageCode: "SAWaitPayResultViewController",
    pageName: "支付结果确认页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a18"),
    pageCode: "SAUserBillRefundDetailsViewController",
    pageName: "用户账单退款详情页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a19"),
    pageCode: "SAUserBillPaymentDetailsViewController",
    pageName: "用户账单支付详情页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1a"),
    pageCode: "SAUserBillListViewController",
    pageName: "用户账单列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1b"),
    pageCode: "SATopUpOrderDetailViewController",
    pageName: "话费充值订单详情",
    businessLine: "PhoneTopUp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1c"),
    pageCode: "SASuggestionViewController",
    pageName: "用户意见反馈页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1d"),
    pageCode: "SAStartupAdController",
    pageName: "启动广告页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1e"),
    pageCode: "SASettingsViewController",
    pageName: "设置页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a1f"),
    pageCode: "SASettingPayPwdViewController",
    pageName: "设置支付密码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a20"),
    pageCode: "SASetPasswordViewController",
    pageName: "设置密码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a21"),
    pageCode: "SASetNickNameViewController",
    pageName: "设置昵称页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a22"),
    pageCode: "SASetEmailViewController",
    pageName: "设置邮件页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a23"),
    pageCode: "SARichMessageDetailsViewController",
    pageName: "富文本消息详情页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a24"),
    pageCode: "SARegisterSMSCodeViewController",
    pageName: "注册登录输入验证码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a25"),
    pageCode: "SARegisterSetPwdViewController",
    pageName: "设置登录密码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a26"),
    pageCode: "SARegisterBindPhoneNoViewController",
    pageName: "设置绑定手机号页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a27"),
    pageCode: "SAPayResultViewController",
    pageName: "在线支付结果页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a28"),
    pageCode: "SAPasswordSettingOptionViewController",
    pageName: "密码设置页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a29"),
    pageCode: "SAOrderViewController",
    pageName: "订单列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "order"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2a"),
    pageCode: "SAOrderListViewController",
    pageName: "订单列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2b"),
    pageCode: "SANewHomeViewController",
    pageName: "首页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2c"),
    pageCode: "SAMyInfomationViewController",
    pageName: "我的信息页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2d"),
    pageCode: "SAMyCouponsViewController",
    pageName: "我的优惠券页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2e"),
    pageCode: "SAMineViewController",
    pageName: "我的页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "mine"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a2f"),
    pageCode: "SAMessageDetailViewController",
    pageName: "消息详情页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a30"),
    pageCode: "SAMessageCenterViewController",
    pageName: "消息页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "message"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a31"),
    pageCode: "SAMessageCenterListViewController",
    pageName: "消息页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a32"),
    pageCode: "SALoginWithPwdViewController",
    pageName: "密码登录页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a33"),
    pageCode: "SALoginViewController",
    pageName: "短信登录页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a34"),
    pageCode: "SALoginFrontPageViewController",
    pageName: "注册登录前置页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a35"),
    pageCode: "SALoginByVerificationCodeViewController",
    pageName: "新短信登录页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a36"),
    pageCode: "SALoginBySMSViewController",
    pageName: "短信登录页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a37"),
    pageCode: "SALoginByPasswordViewController",
    pageName: "新密码登录页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a38"),
    pageCode: "SAForgetLoginPwdSMSCodeViewController",
    pageName: "找回密码验证码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a39"),
    pageCode: "SAForgetLoginPwdSetPwdViewController",
    pageName: "找回密码设置密码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3a"),
    pageCode: "SAExpiredUsedCouponsViewController",
    pageName: "过期优惠券页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3b"),
    pageCode: "SACouponListViewController",
    pageName: "我的优惠券列表",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3c"),
    pageCode: "SACommonRefundDetailViewController",
    pageName: "通用退款页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3d"),
    pageCode: "SACMSWaterfallViewController",
    pageName: "cms容器",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "discovery"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3e"),
    pageCode: "SACMSContentViewController",
    pageName: "cms容器",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a3f"),
    pageCode: "SAChooseZoneViewController",
    pageName: "选择地区页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a40"),
    pageCode: "SAChoosePaymentMethodViewController",
    pageName: "选择支付方式页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a41"),
    pageCode: "SAChooseMyAddressViewController",
    pageName: "选择地址页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a42"),
    pageCode: "SAChooseLanguageViewController",
    pageName: "选择语言页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a43"),
    pageCode: "SAChooseAddressViewController",
    pageName: "外卖选择地址页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a44"),
    pageCode: "SAChooseAddressMapViewController",
    pageName: "地图选择地址页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a45"),
    pageCode: "SAChatMessageViewController",
    pageName: "IM聊天列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a46"),
    pageCode: "SAChangePayPwdInputSMSViewController",
    pageName: "修改支付密码验证码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a47"),
    pageCode: "SAChangePayPwdAskingViewController",
    pageName: "修改支付密码确认页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a48"),
    pageCode: "SAChangeLoginPwdViewController",
    pageName: "修改登录密码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a49"),
    pageCode: "SACancellationApplicationViewController",
    pageName: "注销账号申请页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4a"),
    pageCode: "SABusinessCouponListViewController",
    pageName: "业务线优惠券列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4b"),
    pageCode: "SABusinessBaseCouponListViewController",
    pageName: "业务线优惠券列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4c"),
    pageCode: "SAAvailablePaymentMethodViewController",
    pageName: "可用支付方式列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4d"),
    pageCode: "SAAppPayViewController",
    pageName: "App支付页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4e"),
    pageCode: "SAAppPayResultViewController",
    pageName: "App支付结果页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a4f"),
    pageCode: "SAAggregateSearchViewController",
    pageName: "首页聚合搜索页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a50"),
    pageCode: "SAAggregateSearchResultTableViewViewController",
    pageName: "首页聚合搜索页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a51"),
    pageCode: "SAAggregateSearchResultCollectionViewController",
    pageName: "首页聚合搜索页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a52"),
    pageCode: "SAAddressListViewController",
    pageName: "我的收货地址列表页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a53"),
    pageCode: "SAAddOrModifyAddressViewController",
    pageName: "添加修改地址页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a54"),
    pageCode: "PNWebViewController",
    pageName: "支付Web容器",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a55"),
    pageCode: "PNWaterPaymentResultViewController",
    pageName: "水费缴费结果页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a56"),
    pageCode: "PNWalletSettingViewController",
    pageName: "钱包设置页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a57"),
    pageCode: "PNWalletLimitVC",
    pageName: "钱包限额页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a58"),
    pageCode: "PNWalletController",
    pageName: "钱包首页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a59"),
    pageCode: "PNUtilitiesViewController",
    pageName: "账单支付首页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5a"),
    pageCode: "PNUploadImageViewController",
    pageName: "支付上传图片页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5b"),
    pageCode: "PNUpgradeResultViewController",
    pageName: "账户认证结果页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5c"),
    pageCode: "PNUpgradeAccountViewController",
    pageName: "升级账户页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5d"),
    pageCode: "PNTransPhoneController",
    pageName: "转账到手机号页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5e"),
    pageCode: "PNTransListViewController",
    pageName: "钱包转账首页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a5f"),
    pageCode: "PNTransAmountViewController",
    pageName: "转账输入金额页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a60"),
    pageCode: "PNTransAccountViewController",
    pageName: "转账输入账号页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a61"),
    pageCode: "PNTermsViewController",
    pageName: "钱包协议和条款页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a62"),
    pageCode: "PNSubmitWaterPaymentViewController",
    pageName: "账单支付提交页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a63"),
    pageCode: "PNSetAmountViewController",
    pageName: "钱包付款码设置金额页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a64"),
    pageCode: "PNReceiveCodeViewController",
    pageName: "钱包收款码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a65"),
    pageCode: "PNQueryWaterPaymentViewController",
    pageName: "查询水费账单页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a66"),
    pageCode: "PNPaymentOrderDetailsViewController",
    pageName: "账单支付订单详情页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a67"),
    pageCode: "PNPaymentCodeViewController",
    pageName: "钱包付款码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a68"),
    pageCode: "PNPasswordContactUSController",
    pageName: "修改支付密码联系客服页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a69"),
    pageCode: "PNOutletViewController",
    pageName: "CoolCash网点页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6a"),
    pageCode: "PNOrderResultViewController",
    pageName: "CoolCash交易结果页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6b"),
    pageCode: "PNOpenWalletVC",
    pageName: "开通钱包页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6c"),
    pageCode: "PNOpenWalletInputVC",
    pageName: "开通钱包输入基本信息页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6d"),
    pageCode: "PNMSTradePwdController",
    pageName: "商家服务交易密码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6e"),
    pageCode: "PNMSSettingViewController",
    pageName: "商家服务设置页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a6f"),
    pageCode: "PNMSPreBindController",
    pageName: "商家服务关联商户页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a70"),
    pageCode: "PNMSPasswordContactUSController",
    pageName: "商家服务修改支付密码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a71"),
    pageCode: "PNMSOrderListController",
    pageName: "商家服务订单列表页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a72"),
    pageCode: "PNMSOrderDetailsController",
    pageName: "商家服务订单详情页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a73"),
    pageCode: "PNMSOpenResultController",
    pageName: "商家服务开通结果页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a74"),
    pageCode: "PNMSOpenController",
    pageName: "商家服务开通申请页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a75"),
    pageCode: "PNMSMapAddressController",
    pageName: "商家服务地图页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a76"),
    pageCode: "PNMSIntroductionController",
    pageName: "商家服务介绍页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a77"),
    pageCode: "PNMSHomeController",
    pageName: "商家服务首页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab48036000010000a78"),
    pageCode: "PNMSCollectionController",
    pageName: "商家服务收款页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a79"),
    pageCode: "PNMSCaseInController",
    pageName: "商家服务入金页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7a"),
    pageCode: "PNMSAskTradePwdViewController",
    pageName: "商家服务修改交易密码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7b"),
    pageCode: "PNInterTransferResultViewController",
    pageName: "国际转账结果页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7c"),
    pageCode: "PNInterTransferRecordsViewController",
    pageName: "国际转账转账记录页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7d"),
    pageCode: "PNInterTransferReciverInfoViewController",
    pageName: "国际转账收款人信息页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7e"),
    pageCode: "PNInterTransferRateController",
    pageName: "国际转账汇率页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a7f"),
    pageCode: "PNInterTransferPayerInfoViewController",
    pageName: "国际转账付款方式选择页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a80"),
    pageCode: "PNInterTransferManageViewController",
    pageName: "国际转账转账管理页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a81"),
    pageCode: "PNInterTransferLimitController",
    pageName: "国际转账转账限额页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a82"),
    pageCode: "PNInterTransferIndexViewController",
    pageName: "国际转账首页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a83"),
    pageCode: "PNInterTransferConfirmViewController",
    pageName: "国际转账转账确认页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a84"),
    pageCode: "PNInterTransferAmountViewController",
    pageName: "国际转账输入金额页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a85"),
    pageCode: "PNInterRransferRecordDetailViewController",
    pageName: "国际转账转账详情页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a86"),
    pageCode: "PNForgotPasswordVerifySMSCodeController",
    pageName: "支付密码找回填写验证码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a87"),
    pageCode: "PNDepositViewController",
    pageName: "入金页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a88"),
    pageCode: "PNContactUSViewController",
    pageName: "Coolcash联系我们页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a89"),
    pageCode: "PNBillSupplierListViewController",
    pageName: "账单支付渠道列表页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8a"),
    pageCode: "PNBillListViewController",
    pageName: "钱包交易记录页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8b"),
    pageCode: "PNBankListViewController",
    pageName: "选择收款银行页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8c"),
    pageCode: "PNBankCardInputViewController",
    pageName: "银行卡号输入页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8d"),
    pageCode: "PNAddAndEditReciverInfoViewController",
    pageName: "添加编辑收款人信息页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8e"),
    pageCode: "PNAccountInfoViewController",
    pageName: "账户信息页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a8f"),
    pageCode: "PayHDCheckstandInputPwdViewController",
    pageName: "支付收银台输入密码页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a90"),
    pageCode: "PayHDCheckstandConfirmViewController",
    pageName: "支付收银台确认支付页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a91"),
    pageCode: "OpenWalletVC",
    pageName: "开通钱包页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a92"),
    pageCode: "NationalityOptionController",
    pageName: "选择国籍页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a93"),
    pageCode: "HDCheckStandInputPwdViewController",
    pageName: "收银台输入支付密码页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a94"),
    pageCode: "HDCheckStandConfirmViewController",
    pageName: "收银台确认页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a95"),
    pageCode: "HDCheckStandChoosePaymentMethodViewController",
    pageName: "收银台选择付款方式页",
    businessLine: "SuperApp",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a96"),
    pageCode: "HDBillListViewController",
    pageName: "钱包交易记录页",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a97"),
    pageCode: "HDBillDatePickViewController",
    pageName: "日期选择空间",
    businessLine: "PayNow",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a98"),
    pageCode: "GNTopicViewController",
    pageName: "团购主题页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a99"),
    pageCode: "GNSubmitOrderViewController",
    pageName: "团购提交订单页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9a"),
    pageCode: "GNStoreProductViewController",
    pageName: "团购门店商品页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9b"),
    pageCode: "GNStoreProductHomeController",
    pageName: "团购门店商品首页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9c"),
    pageCode: "GNStorePhotoViewController",
    pageName: "团购商家相册页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9d"),
    pageCode: "GNStoreMapViewContoller",
    pageName: "团购商家位置页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9e"),
    pageCode: "GNStoreDetailViewController",
    pageName: "团购商家详情页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000a9f"),
    pageCode: "GNSortViewController",
    pageName: "团购分类列表页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa0"),
    pageCode: "GNSearchViewController",
    pageName: "团购搜索页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa1"),
    pageCode: "GNReviewsViewController",
    pageName: "团购评论列表页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa2"),
    pageCode: "GNReserveViewController",
    pageName: "团购预约页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa3"),
    pageCode: "GNReserveDetailViewController",
    pageName: "团购预约详情页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa4"),
    pageCode: "GNOrderViewController",
    pageName: "团购订单列表页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa5"),
    pageCode: "GNOrderResultViewController",
    pageName: "团购下单结果页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa6"),
    pageCode: "GNOrderHomeViewController",
    pageName: "团购订单列表页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa7"),
    pageCode: "GNOrderDetailViewController",
    pageName: "团购订单详情页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa8"),
    pageCode: "GNOrderCancelViewController",
    pageName: "团购订单取消页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aa9"),
    pageCode: "GNNewsViewController",
    pageName: "团购消息列表页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aaa"),
    pageCode: "GNHomeViewController",
    pageName: "团购首页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aab"),
    pageCode: "GNCateListViewController",
    pageName: "团购分类页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2cab58036000010000aac"),
    pageCode: "GNArticleDetailViewController",
    pageName: "团购专题页",
    businessLine: "GroupBuy",
    deviceType: "iOS",
    pageClassify: "other"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2fcfc8036000010000aad"),
    pageCode: "HomeNewFragment",
    pageName: "WOWNOW首页",
    businessLine: "SuperApp",
    deviceType: "ANDROID",
    pageClassify: "home"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2fcfc8036000010000aae"),
    pageCode: "DiscoverFragment",
    pageName: "发现频道页",
    businessLine: "SuperApp",
    deviceType: "ANDROID",
    pageClassify: "discovery"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2fcfc8036000010000aaf"),
    pageCode: "SpOrderFragment",
    pageName: "订单页",
    businessLine: "SuperApp",
    deviceType: "ANDROID",
    pageClassify: "order"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2fcfc8036000010000ab0"),
    pageCode: "MessageNewFragment",
    pageName: "消息页",
    businessLine: "SuperApp",
    deviceType: "ANDROID",
    pageClassify: "message"
} );
db.getCollection("event_page").insert( {
    _id: ObjectId("63a2fcfc8036000010000ab1"),
    pageCode: "MineFragment",
    pageName: "我的页",
    businessLine: "SuperApp",
    deviceType: "ANDROID",
    pageClassify: "mine"
} );
