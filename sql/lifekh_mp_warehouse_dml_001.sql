db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419936837561659395",
    "ruleName": "柬文",
    "ruleType": "language",
    "ruleValue": "km-KH"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419936837561659394",
    "ruleName": "英文",
    "ruleType": "language",
    "ruleValue": "en-US"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419936837561659393",
    "ruleName": "beta",
    "ruleType": "beta",
    "ruleValue": "beta"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419936837561659392",
    "ruleName": "中文",
    "ruleType": "language",
    "ruleValue": "zh-CN"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630141448192",
    "ruleName": "Kandal",
    "ruleType": "zone",
    "ruleValue": "855080000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630116282368",
    "ruleName": "Siem Reap",
    "ruleType": "zone",
    "ruleValue": "855170000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630095310848",
    "ruleName": "Koh Kong",
    "ruleType": "zone",
    "ruleValue": "855090000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630070145024",
    "ruleName": "Kampong Cham",
    "ruleType": "zone",
    "ruleValue": "855030000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630040784896",
    "ruleName": "Preah Vihear",
    "ruleType": "zone",
    "ruleValue": "855130000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569630019813376",
    "ruleName": "Kracheh",
    "ruleType": "zone",
    "ruleValue": "855100000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629994647552",
    "ruleName": "Kampong Chhnang",
    "ruleType": "zone",
    "ruleValue": "855040000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629961093120",
    "ruleName": "Preah Sihanouk",
    "ruleType": "zone",
    "ruleValue": "855180000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629935927296",
    "ruleName": "Oddar Meancheay",
    "ruleType": "zone",
    "ruleValue": "855220000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629906567168",
    "ruleName": "Prey Veng",
    "ruleType": "zone",
    "ruleValue": "855140000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629881401344",
    "ruleName": "Kampong Thom",
    "ruleType": "zone",
    "ruleValue": "855060000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629852041216",
    "ruleName": "Phnom Penh",
    "ruleType": "zone",
    "ruleValue": "855120000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629826875392",
    "ruleName": "Banteay Meanchey",
    "ruleType": "zone",
    "ruleValue": "855010000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629797515264",
    "ruleName": "Pailin",
    "ruleType": "zone",
    "ruleValue": "855240000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
   "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629768155136",
    "ruleName": "Svay Rieng",
    "ruleType": "zone",
    "ruleValue": "855200000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629742989312",
    "ruleName": "Kampot",
    "ruleType": "zone",
    "ruleValue": "855070000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629717823488",
    "ruleName": "Ratanak Kiri",
    "ruleType": "zone",
    "ruleValue": "855160000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629688463360",
    "ruleName": "Kampong Speu",
    "ruleType": "zone",
    "ruleValue": "855050000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629659103232",
    "ruleName": "Mondul Kiri",
    "ruleType": "zone",
    "ruleValue": "855110000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629625548800",
    "ruleName": "Battambang",
    "ruleType": "zone",
    "ruleValue": "855020000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629591994368",
    "ruleName": "Tboung Khmum",
    "ruleType": "zone",
    "ruleValue": "855250000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629562634240",
    "ruleName": "Kep",
    "ruleType": "zone",
    "ruleValue": "855230000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629529079808",
    "ruleName": "Takeo",
    "ruleType": "zone",
    "ruleValue": "855210000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629499719680",
    "ruleName": "Stung Treng",
    "ruleType": "zone",
    "ruleValue": "855190000"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": new Date(),
    "updateTime": new Date(),
    "ruleNo": "1419569629382279168",
    "ruleName": "Pursat",
    "ruleType": "zone",
    "ruleValue": "855150000"
})

db.getCollection("tag_info").insert({
  "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
  "createTime": new Date(),
  "tagNo": "1419569630149836805",
  "tagName": {
    "en-US": "km-KH",
    "zh-CN": "柬文",
    "km-KH": "km-KH"
  },
  "tagType": "basic",
  "tagClassify": "public",
  "tagStatus": "open",
  "rule": [
    {
      "createTime": new Date(),
      "updateTime": new Date(),
      "ruleNo": "1419936837561659395",
      "ruleName": "柬文",
      "ruleType": "language",
      "ruleValue": "km-KH"
    }
  ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630149836804",
    "tagName": {
        "en-US": "en-US",
        "zh-CN": "英文",
        "km-KH": "en-US"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419936837561659394",
            "ruleName": "英文",
            "ruleType": "language",
            "ruleValue": "en-US"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630149836803",
    "tagName": {
        "en-US": "beta",
        "zh-CN": "内测",
        "km-KH": "beta"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419936837561659393",
            "ruleName": "beta",
            "ruleType": "beta",
            "ruleValue": "beta"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630149836802",
    "tagName": {
        "en-US": "zh-CN",
        "zh-CN": "中文",
        "km-KH": "zh-CN"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419936837561659392",
            "ruleName": "中文",
            "ruleType": "language",
            "ruleValue": "zh-CN"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630149836801",
    "tagName": {
        "en-US": "Kandal",
        "zh-CN": "甘丹省",
        "km-KH": "កណ្តាល"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630141448192",
            "ruleName": "Kandal",
            "ruleType": "zone",
            "ruleValue": "855080000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630124670977",
    "tagName": {
        "en-US": "Siem Reap",
        "zh-CN": "暹粒省",
        "km-KH": "សៀមរាប"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630116282368",
            "ruleName": "Siem Reap",
            "ruleType": "zone",
            "ruleValue": "855170000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630099505153",
    "tagName": {
        "en-US": "Koh Kong",
        "zh-CN": "戈公省",
        "km-KH": "កោះកុង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630095310848",
            "ruleName": "Koh Kong",
            "ruleType": "zone",
            "ruleValue": "855090000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630074339329",
    "tagName": {
        "en-US": "Kampong Cham",
        "zh-CN": "磅湛省",
        "km-KH": "កំពង់ចាម"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630070145024",
            "ruleName": "Kampong Cham",
            "ruleType": "zone",
            "ruleValue": "855030000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630049173505",
    "tagName": {
        "en-US": "Preah Vihear",
        "zh-CN": "柏威夏省",
        "km-KH": "ព្រះវិហារ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630040784896",
            "ruleName": "Preah Vihear",
            "ruleType": "zone",
            "ruleValue": "855130000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569630024007681",
    "tagName": {
        "en-US": "Kracheh",
        "zh-CN": "桔井省",
        "km-KH": "ក្រចេះ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569630019813376",
            "ruleName": "Kracheh",
            "ruleType": "zone",
            "ruleValue": "855100000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629998841857",
    "tagName": {
        "en-US": "Kampong Chhnang",
        "zh-CN": "磅清扬省",
        "km-KH": "កំពង់ឆ្នាំង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629994647552",
            "ruleName": "Kampong Chhnang",
            "ruleType": "zone",
            "ruleValue": "855040000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629969481729",
    "tagName": {
        "en-US": "Preah Sihanouk",
        "zh-CN": "西哈努克省",
        "km-KH": "ក្រុងសីហនុ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629961093120",
            "ruleName": "Preah Sihanouk",
            "ruleType": "zone",
            "ruleValue": "855180000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629944315905",
    "tagName": {
        "en-US": "Oddar Meancheay",
        "zh-CN": "奥多棉芷省",
        "km-KH": "ឧត្តរមានជ័យ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629935927296",
            "ruleName": "Oddar Meancheay",
            "ruleType": "zone",
            "ruleValue": "855220000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629914955777",
    "tagName": {
        "en-US": "Prey Veng",
        "zh-CN": "波罗勉省",
        "km-KH": "ព្រៃវែង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629906567168",
            "ruleName": "Prey Veng",
            "ruleType": "zone",
            "ruleValue": "855140000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629885595649",
    "tagName": {
        "en-US": "Kampong Thom",
        "zh-CN": "磅同省",
        "km-KH": "កំពង់ធំ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629881401344",
            "ruleName": "Kampong Thom",
            "ruleType": "zone",
            "ruleValue": "855060000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629860429825",
    "tagName": {
        "en-US": "Phnom Penh",
        "zh-CN": "金边市",
        "km-KH": "ភ្នំពេញ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629852041216",
            "ruleName": "Phnom Penh",
            "ruleType": "zone",
            "ruleValue": "855120000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629831069697",
    "tagName": {
        "en-US": "Banteay Meanchey",
        "zh-CN": "班迭棉吉省",
        "km-KH": "បន្ទាយមានជ័យ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629826875392",
            "ruleName": "Banteay Meanchey",
            "ruleType": "zone",
            "ruleValue": "855010000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629805903873",
    "tagName": {
        "en-US": "Pailin",
        "zh-CN": "拜林省",
        "km-KH": "ប៉ៃលិន"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629797515264",
            "ruleName": "Pailin",
            "ruleType": "zone",
            "ruleValue": "855240000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629776543745",
    "tagName": {
        "en-US": "Svay Rieng",
        "zh-CN": "柴桢省",
        "km-KH": "ស្វាយរៀង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629768155136",
            "ruleName": "Svay Rieng",
            "ruleType": "zone",
            "ruleValue": "855200000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629751377921",
    "tagName": {
        "en-US": "Kampot",
        "zh-CN": "贡布省",
        "km-KH": "កំពត"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629742989312",
            "ruleName": "Kampot",
            "ruleType": "zone",
            "ruleValue": "855070000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629726212097",
    "tagName": {
        "en-US": "Ratanak Kiri",
        "zh-CN": "腊塔纳基里省",
        "km-KH": "រតនគីរី"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629717823488",
            "ruleName": "Ratanak Kiri",
            "ruleType": "zone",
            "ruleValue": "855160000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629696851969",
    "tagName": {
        "en-US": "Kampong Speu",
        "zh-CN": "磅士卑省",
        "km-KH": "កំពង់ស្ពឺ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629688463360",
            "ruleName": "Kampong Speu",
            "ruleType": "zone",
            "ruleValue": "855050000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629667491841",
    "tagName": {
        "en-US": "Mondul Kiri",
        "zh-CN": "蒙多基里省",
        "km-KH": "មណ្ឌលគីរី"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629659103232",
            "ruleName": "Mondul Kiri",
            "ruleType": "zone",
            "ruleValue": "855110000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629633937409",
    "tagName": {
        "en-US": "Battambang",
        "zh-CN": "马德望省",
        "km-KH": "បាត់ដំបង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629625548800",
            "ruleName": "Battambang",
            "ruleType": "zone",
            "ruleValue": "855020000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629600382977",
    "tagName": {
        "en-US": "Tboung Khmum",
        "zh-CN": "特本克蒙省",
        "km-KH": "ខេត្តត្បូងឃ្មុំ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629591994368",
            "ruleName": "Tboung Khmum",
            "ruleType": "zone",
            "ruleValue": "855250000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629571022849",
    "tagName": {
        "en-US": "Kep",
        "zh-CN": "白马省",
        "km-KH": "កែប"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629562634240",
            "ruleName": "Kep",
            "ruleType": "zone",
            "ruleValue": "855230000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629537468417",
    "tagName": {
        "en-US": "Takeo",
        "zh-CN": "茶胶省",
        "km-KH": "តាកែវ"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629529079808",
            "ruleName": "Takeo",
            "ruleType": "zone",
            "ruleValue": "855210000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629508108289",
    "tagName": {
        "en-US": "Stung Treng",
        "zh-CN": "上丁省",
        "km-KH": "ស្ទឹងត្រែង"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629499719680",
            "ruleName": "Stung Treng",
            "ruleType": "zone",
            "ruleValue": "855190000"
        }
    ]
})
db.getCollection("tag_info").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO",
    "createTime": new Date(),
    "tagNo": "1419569629415833600",
    "tagName": {
        "en-US": "Pursat",
        "zh-CN": "菩萨省",
        "km-KH": "ពោធិសាត់"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "rule": [
        {
            "createTime": new Date(),
            "updateTime": new Date(),
            "ruleNo": "1419569629382279168",
            "ruleName": "Pursat",
            "ruleType": "zone",
            "ruleValue": "855150000"
        }
    ]
})

db.APP_VERSION_BETA_USER.ensureIndex({"OPERATOR_NO":1,"LOGIN_NAME":1})
db.SUP_MOBILE_TOKEN.ensureIndex({"OPERATOR_NO":1,"DEVICE_ID":1})
db.USER_AUTH_BIND.ensureIndex({"OPERATOR_NO":1,"USER_ID":1,"UNION_ID":1})
db.USER_BEHAVIOR_INFO.ensureIndex({"OPERATOR_NO":1,"LOGIN_NAME":1})
db.USER_CUSTOMER_INFO.ensureIndex({"CUSTOMER_NO":1,"MOBILE":1})
db.USER_LABEL.ensureIndex({"OPERATOR_NO":1,"LOGIN_NAME":1})
db.USER_OPERATOR_LOGIN_INFO.ensureIndex({"OPERATOR_NO":1,"LOGIN_NAME":1})
db.USER_OPERATOR_INFO.ensureIndex({"OPERATOR_NO":1});

db.tag_user.ensureIndex({"operatorNo":1,"nickname":1,"tagNo":1,"mobile":1},{unique:true})
db.tag_info.ensureIndex({"tagNo":1,"tagName":1},{unique:true})
db.tag_rule.ensureIndex({"ruleType":1,"ruleValue":1},{unique:true})
db.t_user_behavior_info.ensureIndex({"userInfoBo.operatorNo":1})
db.tag_zone.ensureIndex({"code":1},{unique:true})

db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86acc"),
createTime: ISODate("2021-08-04T02:18:49.087Z"),
updateTime: ISODate("2021-08-04T02:18:49.087Z"),
msgEn: "Pursat",
msgCb: "ពោធិសាត់",
msgZh: "菩萨省",
code: "855150000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86acf"),
createTime: ISODate("2021-08-04T02:18:49.288Z"),
updateTime: ISODate("2021-08-04T02:18:49.288Z"),
msgEn: "Stung Treng",
msgCb: "ស្ទឹងត្រែង",
msgZh: "上丁省",
code: "855190000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ad2"),
createTime: ISODate("2021-08-04T02:18:49.298Z"),
updateTime: ISODate("2021-08-04T02:18:49.298Z"),
msgEn: "Takeo",
msgCb: "តាកែវ",
msgZh: "茶胶省",
code: "855210000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ad5"),
createTime: ISODate("2021-08-04T02:18:49.307Z"),
updateTime: ISODate("2021-08-04T02:18:49.307Z"),
msgEn: "Kep",
msgCb: "កែប",
msgZh: "白马省",
code: "855230000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ad8"),
createTime: ISODate("2021-08-04T02:18:49.315Z"),
updateTime: ISODate("2021-08-04T02:18:49.315Z"),
msgEn: "Tboung Khmum",
msgCb: "ខេត្តត្បូងឃ្មុំ",
msgZh: "特本克蒙省",
code: "855250000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86adb"),
createTime: ISODate("2021-08-04T02:18:49.323Z"),
updateTime: ISODate("2021-08-04T02:18:49.323Z"),
msgEn: "Battambang",
msgCb: "បាត់ដំបង",
msgZh: "马德望省",
code: "855020000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ade"),
createTime: ISODate("2021-08-04T02:18:49.331Z"),
updateTime: ISODate("2021-08-04T02:18:49.331Z"),
msgEn: "Mondul Kiri",
msgCb: "មណ្ឌលគីរី",
msgZh: "蒙多基里省",
code: "855110000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ae1"),
createTime: ISODate("2021-08-04T02:18:49.339Z"),
updateTime: ISODate("2021-08-04T02:18:49.339Z"),
msgEn: "Kampong Speu",
msgCb: "កំពង់ស្ពឺ",
msgZh: "磅士卑省",
code: "855050000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ae4"),
createTime: ISODate("2021-08-04T02:18:49.347Z"),
updateTime: ISODate("2021-08-04T02:18:49.347Z"),
msgEn: "Ratanak Kiri",
msgCb: "រតនគីរី",
msgZh: "腊塔纳基里省",
code: "855160000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86ae7"),
createTime: ISODate("2021-08-04T02:18:49.355Z"),
updateTime: ISODate("2021-08-04T02:18:49.355Z"),
msgEn: "Kampot",
msgCb: "កំពត",
msgZh: "贡布省",
code: "855070000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86aea"),
createTime: ISODate("2021-08-04T02:18:49.363Z"),
updateTime: ISODate("2021-08-04T02:18:49.363Z"),
msgEn: "Svay Rieng",
msgCb: "ស្វាយរៀង",
msgZh: "柴桢省",
code: "855200000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86aed"),
createTime: ISODate("2021-08-04T02:18:49.371Z"),
updateTime: ISODate("2021-08-04T02:18:49.371Z"),
msgEn: "Pailin",
msgCb: "ប៉ៃលិន",
msgZh: "拜林省",
code: "855240000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86af0"),
createTime: ISODate("2021-08-04T02:18:49.379Z"),
updateTime: ISODate("2021-08-04T02:18:49.379Z"),
msgEn: "Banteay Meanchey",
msgCb: "បន្ទាយមានជ័យ",
msgZh: "班迭棉吉省",
code: "855010000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86af3"),
createTime: ISODate("2021-08-04T02:18:49.388Z"),
updateTime: ISODate("2021-08-04T02:18:49.388Z"),
msgEn: "Phnom Penh",
msgCb: "ភ្នំពេញ",
msgZh: "金边市",
code: "855120000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86af6"),
createTime: ISODate("2021-08-04T02:18:49.395Z"),
updateTime: ISODate("2021-08-04T02:18:49.395Z"),
msgEn: "Kampong Thom",
msgCb: "កំពង់ធំ",
msgZh: "磅同省",
code: "855060000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86af9"),
createTime: ISODate("2021-08-04T02:18:49.403Z"),
updateTime: ISODate("2021-08-04T02:18:49.403Z"),
msgEn: "Prey Veng",
msgCb: "ព្រៃវែង",
msgZh: "波罗勉省",
code: "855140000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86afc"),
createTime: ISODate("2021-08-04T02:18:49.412Z"),
updateTime: ISODate("2021-08-04T02:18:49.412Z"),
msgEn: "Oddar Meancheay",
msgCb: "ឧត្តរមានជ័យ",
msgZh: "奥多棉芷省",
code: "855220000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86aff"),
createTime: ISODate("2021-08-04T02:18:49.419Z"),
updateTime: ISODate("2021-08-04T02:18:49.419Z"),
msgEn: "Preah Sihanouk",
msgCb: "ក្រុងសីហនុ",
msgZh: "西哈努克省",
code: "855180000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b02"),
createTime: ISODate("2021-08-04T02:18:49.428Z"),
updateTime: ISODate("2021-08-04T02:18:49.428Z"),
msgEn: "Kampong Chhnang",
msgCb: "កំពង់ឆ្នាំង",
msgZh: "磅清扬省",
code: "855040000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b05"),
createTime: ISODate("2021-08-04T02:18:49.438Z"),
updateTime: ISODate("2021-08-04T02:18:49.438Z"),
msgEn: "Kracheh",
msgCb: "ក្រចេះ",
msgZh: "桔井省",
code: "855100000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b08"),
createTime: ISODate("2021-08-04T02:18:49.446Z"),
updateTime: ISODate("2021-08-04T02:18:49.446Z"),
msgEn: "Preah Vihear",
msgCb: "ព្រះវិហារ",
msgZh: "柏威夏省",
code: "855130000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b0b"),
createTime: ISODate("2021-08-04T02:18:49.453Z"),
updateTime: ISODate("2021-08-04T02:18:49.453Z"),
msgEn: "Kampong Cham",
msgCb: "កំពង់ចាម",
msgZh: "磅湛省",
code: "855030000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b0e"),
createTime: ISODate("2021-08-04T02:18:49.461Z"),
updateTime: ISODate("2021-08-04T02:18:49.461Z"),
msgEn: "Koh Kong",
msgCb: "កោះកុង",
msgZh: "戈公省",
code: "855090000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b11"),
createTime: ISODate("2021-08-04T02:18:49.468Z"),
updateTime: ISODate("2021-08-04T02:18:49.468Z"),
msgEn: "Siem Reap",
msgCb: "សៀមរាប",
msgZh: "暹粒省",
code: "855170000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
db.getCollection("tag_zone").insert( {
_id: ObjectId("6109f909a907d54fccb86b14"),
createTime: ISODate("2021-08-04T02:18:49.477Z"),
updateTime: ISODate("2021-08-04T02:18:49.477Z"),
msgEn: "Kandal",
msgCb: "កណ្តាល",
msgZh: "甘丹省",
code: "855080000",
zLevel: NumberInt("11"),
_class: "com.lifekh.data.warehouse.bo.ZoneBO"
} );
