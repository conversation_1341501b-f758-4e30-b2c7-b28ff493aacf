db.tag_classification.update({firstTagClassificationNo:'1419569629125082021'},{$set:{firstClassificationNameEn: 'User Attribute',firstClassificationNameKm:'គុណលក្ខណៈ​អ្នក​ប្រើ'}},{multi:true});
db.tag_classification.update({firstTagClassificationNo:'1419569629325082021'},{$set:{firstClassificationNameEn: 'User Transactions',firstClassificationNameKm:'ការ​បម្លែង​អ្នក​ប្រើ'}},{multi:true});
db.tag_classification.update({firstTagClassificationNo:'1579732389059194881'},{$set:{firstClassificationNameEn: 'User Behavior',firstClassificationNameKm:'ឥរិយាបថ​អ្នក​ប្រើ'}},{multi:true});
db.tag_classification.update({firstTagClassificationNo:'1579732389904023553'},{$set:{firstClassificationNameEn: 'Other',firstClassificationNameKm:'ផ្សេង​ទៀត'}},{multi:true});

db.tag_info.update({firstClassificationNo:'1419569629125082021'},{$set:{firstClassificationEn: 'User Attribute',firstClassificationKm:'គុណលក្ខណៈ​អ្នក​ប្រើ'}},{multi:true});
db.tag_info.update({firstClassificationNo:'1419569629325082021'},{$set:{firstClassificationEn: 'User Transactions',firstClassificationKm:'ការ​បម្លែង​អ្នក​ប្រើ'}},{multi:true});
db.tag_info.update({firstClassificationNo:'1579732389059194881'},{$set:{firstClassificationEn: 'User Behavior',firstClassificationKm:'ឥរិយាបថ​អ្នក​ប្រើ'}},{multi:true});
db.tag_info.update({firstClassificationNo:'1579732389904023553'},{$set:{firstClassificationEn: 'Other',firstClassificationKm:'ផ្សេង​ទៀត'}},{multi:true});

db.tag_classification.update({secondaryClassificationName:'地区'},{$set:{secondaryClassificationNameEn: 'Region',secondaryClassificationNameKm:'តំបន់'}},{multi:true});
db.tag_classification.update({secondaryClassificationName:'语言'},{$set:{secondaryClassificationNameEn: 'Language',secondaryClassificationNameKm:'ភាសា'}},{multi:true});
db.tag_classification.update({secondaryClassificationName:'内测'},{$set:{secondaryClassificationNameEn: 'Internal Test',secondaryClassificationNameKm:'សាកល្បង​ខាង​ក្នុង'}},{multi:true});
db.tag_classification.update({secondaryClassificationName:'默认'},{$set:{secondaryClassificationNameEn: 'Default',secondaryClassificationNameKm:'លំនាំដើម'}},{multi:true});
db.tag_classification.update({secondaryClassificationName:'其他'},{$set:{secondaryClassificationNameEn: 'Other',secondaryClassificationNameKm:'ផ្សេង​ទៀត'}},{multi:true});

db.tag_info.update({secondaryClassification:'地区'},{$set:{secondaryClassificationEn: 'Region',secondaryClassificationKm:'តំបន់'}},{multi:true});
db.tag_info.update({secondaryClassification:'语言'},{$set:{secondaryClassificationEn: 'Language',secondaryClassificationKm:'ភាសា'}},{multi:true});
db.tag_info.update({secondaryClassification:'内测'},{$set:{secondaryClassificationEn: 'Internal Test',secondaryClassificationKm:'សាកល្បង​ខាង​ក្នុង'}},{multi:true});
db.tag_info.update({secondaryClassification:'默认'},{$set:{secondaryClassificationEn: 'Default',secondaryClassificationKm:'លំនាំដើម'}},{multi:true});
db.tag_info.update({secondaryClassification:'其他'},{$set:{secondaryClassificationEn: 'Other',secondaryClassificationKm:'ផ្សេង​ទៀត'}},{multi:true});

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "luck_draw_page_pv",
	eventName: "抽奖页浏览量PV",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "luck_draw_page_click",
	eventName: "抽奖页立即抽奖按钮点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "luck_draw_page_task_click",
	eventName: "抽奖页完成任务按钮点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "luck_draw_page_share",
	eventName: "抽奖页分享点击",
	remark: "",
	table: "collect_buried_point_activity",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "new_user_guide_page_pv",
	eventName: "新用户引导页面PV",
	remark: "新用户引导页面PV",
	table: "collect_buried_point_guide_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "new_user_guide_page_submit",
	eventName: "新用户引导页面立即体验提交",
	remark: "新用户引导页面立即体验提交",
	table: "collect_buried_point_guide_page",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.collect_buried_point_guide_page.createIndex({"createTime":-1},{background:true});
db.collect_buried_point_guide_page.createIndex({"recordTime":-1},{background:true});
db.collect_buried_point_guide_page.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_guide_page.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_guide_page.createIndex({"userInfoBo.loginName":-1},{background:true});

db.collect_buried_point_activity.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.collect_buried_point_activity.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.collect_buried_point_activity.createIndex({"userInfoBo.loginName":-1},{background:true});

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "takeawayActivities",
	eventName: "外卖活动数据埋点",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );