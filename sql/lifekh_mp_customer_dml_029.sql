update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 1) and t.UPDATE_TIME < trunc(sysdate);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 2) and t.UPDATE_TIME < trunc(sysdate-1);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 3) and t.UPDATE_TIME < trunc(sysdate-2);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 4) and t.UPDATE_TIME < trunc(sysdate-3);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 8) and t.UPDATE_TIME < trunc(sysdate-4);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 13) and t.UPDATE_TIME < trunc(sysdate-8);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 20) and t.UPDATE_TIME < trunc(sysdate-13);
commit;
update SUP_MOBILE_TOKEN t set t.online_time = t.UPDATE_TIME where t.UPDATE_TIME >= trunc(sysdate - 31) and t.UPDATE_TIME < trunc(sysdate-20);
commit;