db.getCollection("new_user_static").update( { _id: ObjectId("624f50d0424dc30001c16c4b") }, {
    _id: ObjectId("624f50d0424dc30001c16c4b"),
    staticDate: "2022-04-07",
    newUserNum: NumberLong("821"),
    createTime: ISODate("2022-04-07T21:00:00.032Z"),
    _class: "com.lifekh.data.warehouse.bo.NewUserStaticBO"
} );

db.getCollection("new_user_static").update( { _id: ObjectId("6250a250424dc30001d7a7b2") }, {
    _id: ObjectId("6250a250424dc30001d7a7b2"),
    staticDate: "2022-04-08",
    newUserNum: NumberLong("830"),
    createTime: ISODate("2022-04-08T21:00:00.017Z"),
    _class: "com.lifekh.data.warehouse.bo.NewUserStaticBO"
} );

db.getCollection("new_user_static").update( { _id: ObjectId("6251f3d0424dc30001ed45cb") }, {
    _id: ObjectId("6251f3d0424dc30001ed45cb"),
    staticDate: "2022-04-09",
    newUserNum: NumberLong("846"),
    createTime: ISODate("2022-04-09T21:00:00.017Z"),
    _class: "com.lifekh.data.warehouse.bo.NewUserStaticBO"
} );

db.getCollection("new_user_static").update( { _id: ObjectId("62534550424dc3000102405f") }, {
    _id: ObjectId("62534550424dc3000102405f"),
    staticDate: "2022-04-10",
    newUserNum: NumberLong("822"),
    createTime: ISODate("2022-04-10T21:00:00.022Z"),
    _class: "com.lifekh.data.warehouse.bo.NewUserStaticBO"
} );


db.new_user_static.remove({"_id":ObjectId("6255e850a1360d0001a19baf")});
db.active_user_static_day.remove({"_id":ObjectId("6255e854424dc30001300375")});


db.device_pool.createIndex({"deviceId":1});
db.device_pool.createIndex({"recordTime":-1});
db.device_pool.createIndex({"lastActiveTime":-1});

db.collect_behavior_v2.createIndex({"eventBo.event":1});
