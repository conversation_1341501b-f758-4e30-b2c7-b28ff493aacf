db.event_type.insert( {
	eventGroup: "other",
	eventNo: "wm_store_exposure",
	eventName: "外卖门店曝光",
	remark: "",
	table: "collect_buried_point_takeaway_store_exposure",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.getCollection('collect_buried_point_takeaway_store_exposure').createIndex({'createTime':1,'eventBo.event':1,'userInfoBo.operatorNo':1},{background:true});
