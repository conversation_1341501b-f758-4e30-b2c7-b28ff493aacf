db.getCollection("tag_rule").insert({
    createTime: ISODate("2021-09-02T05:48:37.696Z"),
    updateTime: ISODate("2021-09-02T05:57:51.843Z"),
    ruleNo: "2021090311902092021",
    ruleName: "下单成功(次数)",
    ruleType: "order success",
    ruleValue: "{\"symbol\":\"interval\",\"min\":0,\"max\":0,\"days\":0,\"behavior\":\"order success\"}",
    businessLine: "",
	timeliness:false,
	generalRules:[{"ruleFiled":"businessLine","symbol":"equal"}],
	specialRules:[
        {"ruleFiled":"days","symbol":"more than"},
        {"ruleFiled":"order success","symbol":"more than"},
        {"ruleFiled":"order success","symbol":"less than"},
        {"ruleFiled":"order success","symbol":"equal"},
        {"ruleFiled":"order success","symbol":"interval"},
        {"ruleFiled":"order state","symbol":"equal"}
    ],
	classification:"行为",
    _class: "com.lifekh.data.warehouse.bo.TagRuleV2BO"
});

db.getCollection("tag_rule").insert({
    createTime: ISODate("2021-09-02T05:48:37.696Z"),
    updateTime: ISODate("2021-09-02T05:57:51.843Z"),
    ruleNo: "2021090321902092023",
    ruleName: "订单金额(元)",
    ruleType: "order amount",
    ruleValue: "{\"symbol\":\"interval\",\"min\":0,\"max\":0,\"days\":0,\"behavior\":\"order success\"}",
    businessLine: "",
		timeliness:false,
		generalRules:[{"ruleFiled":"businessLine","symbol":"equal"}],
		specialRules:[
        {"ruleFiled":"days","symbol":"more than"},
        {"ruleFiled":"order amount","symbol":"more than"},
        {"ruleFiled":"order amount","symbol":"less than"},
        {"ruleFiled":"order amount","symbol":"equal"},
        {"ruleFiled":"order amount","symbol":"interval"},
        {"ruleFiled":"order state","symbol":"equal"}
    ],
	classification:"行为",
    _class: "com.lifekh.data.warehouse.bo.TagRuleV2BO"
});


db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d27") }, {
    _id: ObjectId("610bb8ab64f9e54800374d27"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.869Z"),
    updateTime: ISODate("2021-08-05T10:08:43.869Z"),
    ruleNo: "1419936837561659395",
    ruleName: "柬文",
    ruleType: "language",
    ruleValue: "km-KH",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"language","symbol":"equal","min":"km-KH"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d28") }, {
    _id: ObjectId("610bb8ab64f9e54800374d28"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.881Z"),
    updateTime: ISODate("2021-08-05T10:08:43.881Z"),
    ruleNo: "1419936837561659394",
    ruleName: "英文",
    ruleType: "language",
    ruleValue: "en-US",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"language","symbol":"equal","min":"en-US"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d29") }, {
    _id: ObjectId("610bb8ab64f9e54800374d29"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.881Z"),
    updateTime: ISODate("2021-08-05T10:08:43.881Z"),
    ruleNo: "1419936837561659393",
    ruleName: "beta",
    ruleType: "beta",
    ruleValue: "beta",
	timeliness:false,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"beta","symbol":"equal","min":"beta"}
	]
});
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2a") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2a"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.882Z"),
    updateTime: ISODate("2021-08-05T10:08:43.882Z"),
    ruleNo: "1419936837561659392",
    ruleName: "中文",
    ruleType: "language",
    ruleValue: "zh-CN",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"language","symbol":"equal","min":"zh-CN"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2b") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2b"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.882Z"),
    updateTime: ISODate("2021-08-05T10:08:43.882Z"),
    ruleNo: "1419569630141448192",
    ruleName: "Kandal",
    ruleType: "zone",
    ruleValue: "855080000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855080000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2c") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2c"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.882Z"),
    updateTime: ISODate("2021-08-05T10:08:43.882Z"),
    ruleNo: "1419569630116282368",
    ruleName: "Siem Reap",
    ruleType: "zone",
    ruleValue: "855170000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855170000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2d") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2d"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.883Z"),
    updateTime: ISODate("2021-08-05T10:08:43.883Z"),
    ruleNo: "1419569630095310848",
    ruleName: "Koh Kong",
    ruleType: "zone",
    ruleValue: "855090000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855090000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2e") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2e"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.883Z"),
    updateTime: ISODate("2021-08-05T10:08:43.883Z"),
    ruleNo: "1419569630070145024",
    ruleName: "Kampong Cham",
    ruleType: "zone",
    ruleValue: "855030000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855030000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d2f") }, {
    _id: ObjectId("610bb8ab64f9e54800374d2f"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.883Z"),
    updateTime: ISODate("2021-08-05T10:08:43.883Z"),
    ruleNo: "1419569630040784896",
    ruleName: "Preah Vihear",
    ruleType: "zone",
    ruleValue: "855130000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855130000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d30") }, {
    _id: ObjectId("610bb8ab64f9e54800374d30"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.884Z"),
    updateTime: ISODate("2021-08-05T10:08:43.884Z"),
    ruleNo: "1419569630019813376",
    ruleName: "Kracheh",
    ruleType: "zone",
    ruleValue: "855100000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855100000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d31") }, {
    _id: ObjectId("610bb8ab64f9e54800374d31"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.884Z"),
    updateTime: ISODate("2021-08-05T10:08:43.884Z"),
    ruleNo: "1419569629994647552",
    ruleName: "Kampong Chhnang",
    ruleType: "zone",
    ruleValue: "855040000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855040000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d32") }, {
    _id: ObjectId("610bb8ab64f9e54800374d32"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.884Z"),
    updateTime: ISODate("2021-08-05T10:08:43.884Z"),
    ruleNo: "1419569629961093120",
    ruleName: "Preah Sihanouk",
    ruleType: "zone",
    ruleValue: "855180000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855180000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d33") }, {
    _id: ObjectId("610bb8ab64f9e54800374d33"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.884Z"),
    updateTime: ISODate("2021-08-05T10:08:43.884Z"),
    ruleNo: "1419569629935927296",
    ruleName: "Oddar Meancheay",
    ruleType: "zone",
    ruleValue: "855220000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855220000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d34") }, {
    _id: ObjectId("610bb8ab64f9e54800374d34"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.885Z"),
    updateTime: ISODate("2021-08-05T10:08:43.885Z"),
    ruleNo: "1419569629906567168",
    ruleName: "Prey Veng",
    ruleType: "zone",
    ruleValue: "855140000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855140000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d35") }, {
    _id: ObjectId("610bb8ab64f9e54800374d35"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.885Z"),
    updateTime: ISODate("2021-08-05T10:08:43.885Z"),
    ruleNo: "1419569629881401344",
    ruleName: "Kampong Thom",
    ruleType: "zone",
    ruleValue: "855060000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855060000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d36") }, {
    _id: ObjectId("610bb8ab64f9e54800374d36"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.885Z"),
    updateTime: ISODate("2021-08-05T10:08:43.885Z"),
    ruleNo: "1419569629852041216",
    ruleName: "Phnom Penh",
    ruleType: "zone",
    ruleValue: "855120000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855120000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d37") }, {
    _id: ObjectId("610bb8ab64f9e54800374d37"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.886Z"),
    updateTime: ISODate("2021-08-05T10:08:43.886Z"),
    ruleNo: "1419569629826875392",
    ruleName: "Banteay Meanchey",
    ruleType: "zone",
    ruleValue: "855010000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855010000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d38") }, {
    _id: ObjectId("610bb8ab64f9e54800374d38"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.886Z"),
    updateTime: ISODate("2021-08-05T10:08:43.886Z"),
    ruleNo: "1419569629797515264",
    ruleName: "Pailin",
    ruleType: "zone",
    ruleValue: "855240000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855240000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d39") }, {
    _id: ObjectId("610bb8ab64f9e54800374d39"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.887Z"),
    updateTime: ISODate("2021-08-05T10:08:43.887Z"),
    ruleNo: "1419569629768155136",
    ruleName: "Svay Rieng",
    ruleType: "zone",
    ruleValue: "855200000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855200000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3a") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3a"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.887Z"),
    updateTime: ISODate("2021-08-05T10:08:43.887Z"),
    ruleNo: "1419569629742989312",
    ruleName: "Kampot",
    ruleType: "zone",
    ruleValue: "855070000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855070000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3b") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3b"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.887Z"),
    updateTime: ISODate("2021-08-05T10:08:43.887Z"),
    ruleNo: "1419569629717823488",
    ruleName: "Ratanak Kiri",
    ruleType: "zone",
    ruleValue: "855160000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855160000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3c") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3c"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.888Z"),
    updateTime: ISODate("2021-08-05T10:08:43.888Z"),
    ruleNo: "1419569629688463360",
    ruleName: "Kampong Speu",
    ruleType: "zone",
    ruleValue: "855050000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855050000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3d") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3d"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.889Z"),
    updateTime: ISODate("2021-08-05T10:08:43.889Z"),
    ruleNo: "1419569629659103232",
    ruleName: "Mondul Kiri",
    ruleType: "zone",
    ruleValue: "855110000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855110000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3e") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3e"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.889Z"),
    updateTime: ISODate("2021-08-05T10:08:43.889Z"),
    ruleNo: "1419569629625548800",
    ruleName: "Battambang",
    ruleType: "zone",
    ruleValue: "855020000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855020000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d3f") }, {
    _id: ObjectId("610bb8ab64f9e54800374d3f"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.89Z"),
    updateTime: ISODate("2021-08-05T10:08:43.89Z"),
    ruleNo: "1419569629591994368",
    ruleName: "Tboung Khmum",
    ruleType: "zone",
    ruleValue: "855250000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855250000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d40") }, {
    _id: ObjectId("610bb8ab64f9e54800374d40"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.891Z"),
    updateTime: ISODate("2021-08-05T10:08:43.891Z"),
    ruleNo: "1419569629562634240",
    ruleName: "Kep",
    ruleType: "zone",
    ruleValue: "855230000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855230000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d41") }, {
    _id: ObjectId("610bb8ab64f9e54800374d41"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.892Z"),
    updateTime: ISODate("2021-08-05T10:08:43.892Z"),
    ruleNo: "1419569629529079808",
    ruleName: "Takeo",
    ruleType: "zone",
    ruleValue: "855210000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855210000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d42") }, {
    _id: ObjectId("610bb8ab64f9e54800374d42"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.893Z"),
    updateTime: ISODate("2021-08-05T10:08:43.893Z"),
    ruleNo: "1419569629499719680",
    ruleName: "Stung Treng",
    ruleType: "zone",
    ruleValue: "855190000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855190000"}
	]
} );
db.getCollection("tag_rule").update( { _id: ObjectId("610bb8ab64f9e54800374d43") }, {
    _id: ObjectId("610bb8ab64f9e54800374d43"),
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-08-05T10:08:43.893Z"),
    updateTime: ISODate("2021-08-05T10:08:43.893Z"),
    ruleNo: "1419569629382279168",
    ruleName: "Pursat",
    ruleType: "zone",
    ruleValue: "855150000",
	timeliness:true,
	classification:"属性",
	generalRules:[],
	specialRules:[
	  {"ruleFiled":"zone","symbol":"equal","min":"855150000"}
	]
} );



db.getCollection("tag_info").update({ _id: ObjectId("612efedbec32ce000145c803") },{
    "createTime": ISODate("2021-09-01T12:17:31.130+08:00"),
    "updateTime": ISODate("2021-09-01T12:17:31.130+08:00"),
    "tagNo": "1432920528932524034",
    "tagName": {
        "zh-CN": "过去一天订单金额累计大于5",
        "en-US": "过去一天订单金额累计大于5",
        "km-KH": "过去一天订单金额累计大于5"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
    "tagStatus": "open",
	"firstClassification": "用户交易",
    "tagDescription": "过去一天订单金额累计大于5",
	"timeliness": false,
	"totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "1",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "5",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efe9b9f0b600001c55df3") },{
    "createTime": ISODate("2021-09-01T12:16:27.007+08:00"),
    "updateTime": ISODate("2021-09-01T12:16:27.007+08:00"),
    "tagNo": "1432920259979460610",
    "tagName": {
        "zh-CN": "过去3天订单金额累计大于15",
        "en-US": "过去3天订单金额累计大于15",
        "km-KH": "过去3天订单金额累计大于15"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去3天订单金额累计大于15",
     "timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "3",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "15",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efe66ec32ce000145c6c3") },{
    "createTime": ISODate("2021-09-01T12:15:34.141+08:00"),
    "updateTime": ISODate("2021-09-01T12:15:34.141+08:00"),
    "tagNo": "1432920038245093378",
    "tagName": {
        "zh-CN": "过去7天订单金额累计大于50",
        "en-US": "过去7天订单金额累计大于50",
        "km-KH": "过去7天订单金额累计大于50"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去7天订单金额累计大于50",
     "timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "7",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "50",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})
db.getCollection("tag_info").update({ _id: ObjectId("612efd67ec32ce000145c29c") },{
    "createTime": ISODate("2021-09-01T12:11:19.319+08:00"),
    "updateTime": ISODate("2021-09-01T12:11:19.319+08:00"),
    "tagNo": "1432918969444159490",
    "tagName": {
        "zh-CN": "过去10天订单金额累计大于100",
        "en-US": "过去10天订单金额累计大于100",
        "km-KH": "过去10天订单金额累计大于100"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天订单金额累计大于100",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "100",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efd179f0b600001c55804") },{
    "createTime": ISODate("2021-09-01T12:09:59.772+08:00"),
    "updateTime": ISODate("2021-09-01T12:09:59.772+08:00"),
    "tagNo": "1432918635798151170",
    "tagName": {
        "zh-CN": "过去10天订单金额累计大于120",
        "en-US": "过去10天订单金额累计大于120",
        "km-KH": "过去10天订单金额累计大于120"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天订单金额累计大于120",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "120",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efccc9f0b600001c556ad") },{
    "createTime": ISODate("2021-09-01T12:08:44.494+08:00"),
    "updateTime": ISODate("2021-09-01T12:08:44.494+08:00"),
    "tagNo": "1432918320059334658",
    "tagName": {
        "zh-CN": "过去3天订单金额累计大于30",
        "en-US": "过去3天订单金额累计大于30",
        "km-KH": "过去3天订单金额累计大于30"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去3天订单金额累计大于30",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "3",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "30",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efc859f0b600001c5543f") },{
    "createTime": ISODate("2021-09-01T12:07:33.759+08:00"),
    "updateTime": ISODate("2021-09-01T12:07:33.759+08:00"),
    "tagNo": "1432918023375241218",
    "tagName": {
        "zh-CN": "过去3天订单累计大于3",
        "en-US": "过去3天订单累计大于3",
        "km-KH": "过去3天订单累计大于3"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去3天订单累计大于3",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090311902092021",
            "ruleName": "下单成功(次数)",
            "ruleType": "order success",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "3",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order success",
                    "min": "3",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efc4a9f0b600001c5511b") },{
    "createTime": ISODate("2021-09-01T12:06:34.916+08:00"),
    "updateTime": ISODate("2021-09-01T12:06:34.916+08:00"),
    "tagNo": "1432917776569810946",
    "tagName": {
        "zh-CN": "过去7天订单金额累计大于100",
        "en-US": "过去7天订单金额累计大于100",
        "km-KH": "过去7天订单金额累计大于100"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去7天订单金额累计大于100",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "7",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "100",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efc09ec32ce000145b6a1") },{
    "createTime": ISODate("2021-09-01T12:05:29.063+08:00"),
    "updateTime": ISODate("2021-09-01T12:05:29.063+08:00"),
    "tagNo": "1432917500364017666",
    "tagName": {
        "zh-CN": "过去7天订单累计大于7",
        "en-US": "过去7天订单累计大于7",
        "km-KH": "过去7天订单累计大于7"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去7天订单累计大于7",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090311902092021",
            "ruleName": "下单成功(次数)",
            "ruleType": "order success",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "7",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order success",
                    "min": "7",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efbd4ec32ce000145b423") },{
    "createTime": ISODate("2021-09-01T12:04:36.356+08:00"),
    "updateTime": ISODate("2021-09-01T12:04:36.356+08:00"),
    "tagNo": "1432917279294836738",
    "tagName": {
        "zh-CN": "过去10天订单金额累计大于160",
        "en-US": "过去10天订单金额累计大于160",
        "km-KH": "过去10天订单金额累计大于160"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天订单金额累计大于160",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
            "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "160",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efb669f0b600001c54948") },{
    "createTime": ISODate("2021-09-01T12:02:46.088+08:00"),
    "updateTime": ISODate("2021-09-01T12:02:46.088+08:00"),
    "tagNo": "1432916816795615234",
    "tagName": {
        "zh-CN": "过去10天订单累计大于12",
        "en-US": "过去10天订单累计大于12",
        "km-KH": "过去10天订单累计大于12"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天订单累计大于12",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090311902092021",
             "ruleName": "下单成功(次数)",
            "ruleType": "order success",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order success",
                    "min": "12",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efad8ec32ce000145b180") },{
    "createTime": ISODate("2021-09-01T12:00:24.769+08:00"),
    "updateTime": ISODate("2021-09-01T12:00:24.769+08:00"),
    "tagNo": "1432916224062476289",
    "tagName": {
        "zh-CN": "过去10天订单金额大于200",
        "en-US": "过去10天订单金额大于200",
        "km-KH": "过去10天订单金额大于200"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天订单金额累计大于200",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090321902092023",
           "ruleName": "下单金额",
            "ruleType": "order amount",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order amount",
                    "min": "200",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})

db.getCollection("tag_info").update({ _id: ObjectId("612efa369f0b600001c5479a") },{
    "createTime": ISODate("2021-09-01T11:57:42.684+08:00"),
    "updateTime": ISODate("2021-09-01T11:57:42.684+08:00"),
    "tagNo": "1432915544227004417",
    "tagName": {
        "zh-CN": "过去10天 订单累计大于15",
        "en-US": "过去10天 订单累计大于15",
        "km-KH": "过去10天 订单累计大于15"
    },
    "tagType": "rule",
    "tagClassify": "YumNow",
	"firstClassification": "用户交易",
    "tagStatus": "open",
    "tagDescription": "过去10天 订单累计大于15",
	"timeliness": false,
    "totalUser": NumberLong(0),
    "rule": [
        {
            "ruleNo": "2021090311902092021",
            "ruleName": "下单成功(次数)",
            "ruleType": "order success",
			"timeliness": false,
            "classification": "11",
			"generalRules": [
                {
                    "ruleFiled": "businessLine",
                    "min": "YumNow",
                    "symbol": "equal"
                }
            ],
			"specialRules": [
                {
                    "ruleFiled": "days",
                    "min": "10",
                    "symbol": "equal"
                },
                {
                    "ruleFiled": "order success",
                    "min": "15",
                    "max": "",
                    "symbol": "more than"
                }
            ]
        }
    ],
    "_class": "com.lifekh.data.warehouse.bo.TagInfoBO"
})