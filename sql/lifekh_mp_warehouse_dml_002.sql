db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629125082021",
		"firstClassificationName":"用户属性",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629225082021",
		"firstClassificationName":"用户活跃",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629325082021",
		"firstClassificationName":"用户交易",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629425082021",
		"firstClassificationName":"用户消费",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569622525082021",
		"firstClassificationName":"用户价值",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})
db.getCollection("tag_classification").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagClassificationBO",
    "createTime": new Date(),
    "firstTagClassificationNo": "1419569629625082021",
		"firstClassificationName":"其他",
		"secondTagClassificationNo": "",
		"secondaryClassificationName":""
})