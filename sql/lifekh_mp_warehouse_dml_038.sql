db.view_event_record.createIndex({"createTime":-1},{background:true});
db.view_event_record.createIndex({"deviceInfoBo.deviceId":-1},{background:true});
db.view_event_record.createIndex({"userInfoBo.operatorNo":-1},{background:true});
db.view_event_record.createIndex({"currentPage":-1},{background:true});

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "shareIcon",
	eventName: "外卖门店分享-分享icon点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "collectIcon",
	eventName: "外卖门店分享-收藏icon点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "searchIcon",
	eventName: "外卖门店分享-搜索icon点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "messageIcon",
	eventName: "外卖门店分享-消息icon点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "generatePictureIcon",
	eventName: "外卖门店分享-生成图片icon点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "thirdPartyShare",
	eventName: "外卖门店分享-第三方渠道分享",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "enterH5Page",
	eventName: "外卖门店分享-进入H5门店承载页",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "opeanAppIcon",
	eventName: "外卖门店分享-拉起APP按钮点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "enterRemarkDetail",
	eventName: "外卖用户端订单-进入备注详情",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );

db.event_type.insert( {
	eventGroup: "other",
	eventNo: "touchFastIocn",
	eventName: "外卖用户端订单-快捷备注点击",
	remark: "",
	table: "collect_buried_point_takeaway",
	status: 10,
	bussinessLine: "all",
	createBy: "system",
	createTime: new Date(),
	updateBy: "system",
	updateTime: new Date()
} );