﻿db.report_click_day.remove({"pageName": "WOWNOW首页","dataTime": ISODate("2022-06-30T05:00:00.000Z")});
db.report_click_day.remove({"pageName": "WOWNOW首页O2O","dataTime": ISODate("2022-06-30T05:00:00.000Z")});
db.report_click_day.remove({"pageName": "WOWNOW首页(总)","dataTime": ISODate("2022-06-30T05:00:00.000Z")});
db.report_click_day.remove({"pageName": "WOWNOW首页","dataTime": ISODate("2022-06-29T05:00:00.000Z")});
db.report_click_day.remove({"pageName": "WOWNOW首页O2O","dataTime": ISODate("2022-06-29T05:00:00.000Z")});
db.report_click_day.remove({"pageName": "WOWNOW首页(总)","dataTime": ISODate("2022-06-29T05:00:00.000Z")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "Hotel 20% EN Version","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "外卖_英_周末活动","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "外卖团购_英_探店星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "外卖外卖_英_周末1for1","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("12"),"uv": NumberLong("5"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-KM","nodeName": "品牌对外_柬_游戏活动","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-KM","nodeName": "外卖团购_柬_探店星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-KM","nodeName": "电商批发_柬_百威活动","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("3"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("12"),"uv": NumberLong("5"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "YumNow Entrance","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("18"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "YumNow Entrance - KH","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("2"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "បញ្ចុះតម្លៃរហូតដល់50%","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("20"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ភេសជ្ជ:","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ហាងថ្មី","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ហាងពេញនិយម","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_5折优惠","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("35"),"uv": NumberLong("22"),"dv": NumberLong("31")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_上榜好店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("16"),"uv": NumberLong("12"),"dv": NumberLong("13")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("15"),"uv": NumberLong("8"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("5"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("81"),"uv": NumberLong("38"),"dv": NumberLong("60")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_新店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_本地美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("3"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("41"),"uv": NumberLong("18"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Beverage","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Beverages","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Cuisines","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Discount up to 50%","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("3"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Khmer","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Khmer Foods","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "New Stores","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "YumNow Entrance","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("2"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_5折优惠","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("21"),"uv": NumberLong("6"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_上榜好店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("4"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("56"),"uv": NumberLong("29"),"dv": NumberLong("49")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_新店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_本地美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("3"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("27"),"uv": NumberLong("16"),"dv": NumberLong("26")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "品牌_中_话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "外卖_中_团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("16"),"uv": NumberLong("3"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "航空_中_酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "Local Service","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_ce快递","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "外卖_柬_团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "支付_柬_钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "游戏_柬_游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "电商_柬_批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "航空_柬_游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("4"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "航空_柬_酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "话费_柬_话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "Coupon","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "Local Services","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "品牌_英_CE快递","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "品牌_英_优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "品牌_英_话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "支付_英_钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "电商_英_批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "航空_英_游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "航空_英_酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "Taobao-KH","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "ភេសជ្ជៈនិងអាហារសម្រន់","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "សម្ភារៈក្នុងផ្ទះ","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_啤酒饮料","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_家居生活","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_时尚潮流","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_母婴专区","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("21"),"uv": NumberLong("8"),"dv": NumberLong("16")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("9"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_调料批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("5"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "Drinks & Snacks","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "Taobao -EN Ver.","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "TinhNow Entrance","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_啤酒饮料","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_运动系列","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_酒水批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_柬文","nodeName": "外卖外卖_柬_周末1for1","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "Tuesday Deal EN","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "电商金刚区","nodeName": "大图","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "首页banner 2 ","nodeName": "外卖团购_中_探店星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1018"),"uv": NumberLong("640"),"dv": NumberLong("651")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("23"),"uv": NumberLong("21"),"dv": NumberLong("21")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("176"),"uv": NumberLong("120"),"dv": NumberLong("122")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2183"),"uv": NumberLong("1087"),"dv": NumberLong("1095")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("68"),"uv": NumberLong("51"),"dv": NumberLong("53")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_美味星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("15"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("185"),"uv": NumberLong("154"),"dv": NumberLong("154")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_5折优惠","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2502"),"uv": NumberLong("1686"),"dv": NumberLong("1816")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_上榜好店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1173"),"uv": NumberLong("818"),"dv": NumberLong("878")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("696"),"uv": NumberLong("510"),"dv": NumberLong("554")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1311"),"uv": NumberLong("935"),"dv": NumberLong("987")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("13156"),"uv": NumberLong("6543"),"dv": NumberLong("6764")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_新店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_本地美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1217"),"uv": NumberLong("725"),"dv": NumberLong("765")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2939"),"uv": NumberLong("1920"),"dv": NumberLong("2043")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_5折优惠","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1573"),"uv": NumberLong("1052"),"dv": NumberLong("1149")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_上榜好店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("326"),"uv": NumberLong("233"),"dv": NumberLong("243")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("568"),"uv": NumberLong("409"),"dv": NumberLong("427")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("659"),"uv": NumberLong("472"),"dv": NumberLong("500")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_外卖入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("11300"),"uv": NumberLong("5734"),"dv": NumberLong("5913")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_本地美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("744"),"uv": NumberLong("487"),"dv": NumberLong("512")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2047"),"uv": NumberLong("1355"),"dv": NumberLong("1431")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW工具栏(柬)","nodeName": "电商_柬_批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW工具栏_柬文_默认","nodeName": "电商_柬_酒水","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW工具栏_英文_默认","nodeName": "电商_英_批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_家居生活","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("37"),"uv": NumberLong("32"),"dv": NumberLong("33")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("303"),"uv": NumberLong("152"),"dv": NumberLong("155")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_时尚潮流","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("22"),"dv": NumberLong("24")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("75"),"uv": NumberLong("54"),"dv": NumberLong("58")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("215"),"uv": NumberLong("143"),"dv": NumberLong("143")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_调料批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("36"),"uv": NumberLong("30"),"dv": NumberLong("31")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_酒水批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("22"),"uv": NumberLong("18"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_啤酒饮料","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_天天领券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_家居生活","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("202"),"uv": NumberLong("171"),"dv": NumberLong("180")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3899"),"uv": NumberLong("1633"),"dv": NumberLong("1624")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_时尚潮流","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("297"),"uv": NumberLong("230"),"dv": NumberLong("245")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1378"),"uv": NumberLong("756"),"dv": NumberLong("814")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_生活家居","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2547"),"uv": NumberLong("1290"),"dv": NumberLong("1353")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_调料批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("209"),"uv": NumberLong("167"),"dv": NumberLong("178")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_酒水批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("271"),"uv": NumberLong("190"),"dv": NumberLong("204")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_啤酒饮料","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_天天领券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_家居生活","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("65"),"uv": NumberLong("49"),"dv": NumberLong("57")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_快消品批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("523"),"uv": NumberLong("299"),"dv": NumberLong("308")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_时尚潮流","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("59"),"uv": NumberLong("47"),"dv": NumberLong("53")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_母婴专区","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_淘宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("654"),"uv": NumberLong("378"),"dv": NumberLong("411")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_生活家居","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_电商入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1162"),"uv": NumberLong("601"),"dv": NumberLong("635")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_调料批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("38"),"uv": NumberLong("31"),"dv": NumberLong("31")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_酒水批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_鞋帽箱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("86"),"uv": NumberLong("64"),"dv": NumberLong("68")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_中文","nodeName": "品牌_中_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_柬文","nodeName": "品牌_柬_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("64"),"uv": NumberLong("46"),"dv": NumberLong("50")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("34"),"uv": NumberLong("30"),"dv": NumberLong("30")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "外卖_英_machi","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "外卖外卖_英_周末1for1","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "外卖外卖_英_宋干节专题","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_中_banner1","nodeName": "品牌_中_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("9"),"uv": NumberLong("8"),"dv": NumberLong("8")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_柬_banner1","nodeName": "品牌_柬_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("65"),"uv": NumberLong("43"),"dv": NumberLong("47")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_柬_banner2","nodeName": "品牌发现_柬_发现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_英_banner1","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_英_banner2","nodeName": "外卖_英_福利三重奏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_上榜好店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("11"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("272"),"uv": NumberLong("158"),"dv": NumberLong("162")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_主入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("495"),"uv": NumberLong("285"),"dv": NumberLong("289")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("35"),"uv": NumberLong("25"),"dv": NumberLong("26")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("14"),"uv": NumberLong("13"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("43"),"uv": NumberLong("29"),"dv": NumberLong("29")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_中_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_柬_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("23"),"uv": NumberLong("15"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_英_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("9"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_中_品牌CE","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("17"),"uv": NumberLong("13"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_柬_品牌CE","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("91"),"uv": NumberLong("62"),"dv": NumberLong("66")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_英_品牌CE","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("14"),"uv": NumberLong("10"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌优惠券_柬_品牌优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("175"),"uv": NumberLong("139"),"dv": NumberLong("145")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌优惠券_英_品牌优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("27"),"dv": NumberLong("28")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_中_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("47"),"uv": NumberLong("29"),"dv": NumberLong("30")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_柬_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("282"),"uv": NumberLong("188"),"dv": NumberLong("198")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_英_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("53"),"uv": NumberLong("41"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌领券中心_中_品牌领券中心","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("15"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_中_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("18"),"uv": NumberLong("15"),"dv": NumberLong("17")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_柬_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("85"),"uv": NumberLong("65"),"dv": NumberLong("72")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_英_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("28"),"uv": NumberLong("25"),"dv": NumberLong("25")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_中_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("20"),"uv": NumberLong("16"),"dv": NumberLong("16")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_柬_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("637"),"uv": NumberLong("311"),"dv": NumberLong("325")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_英_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("46"),"uv": NumberLong("28"),"dv": NumberLong("28")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_中_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("29"),"uv": NumberLong("22"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_柬_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("525"),"uv": NumberLong("263"),"dv": NumberLong("270")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_英_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("35"),"uv": NumberLong("26"),"dv": NumberLong("27")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_中_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_柬_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("177"),"uv": NumberLong("107"),"dv": NumberLong("123")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_英_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("56"),"uv": NumberLong("43"),"dv": NumberLong("43")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_中_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("17"),"uv": NumberLong("11"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_柬_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("50"),"uv": NumberLong("41"),"dv": NumberLong("44")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_英_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("28"),"uv": NumberLong("18"),"dv": NumberLong("19")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_中_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_柬_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("15"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_英_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("9"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_中_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("12"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_柬_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("40"),"uv": NumberLong("33"),"dv": NumberLong("35")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_英_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("12"),"dv": NumberLong("12")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "外卖外卖_柬_周末1for1","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_中_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_柬_兑换现金券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_柬_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("61"),"uv": NumberLong("47"),"dv": NumberLong("47")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_英_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("17"),"uv": NumberLong("14"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("237"),"uv": NumberLong("133"),"dv": NumberLong("142")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_主入口","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("484"),"uv": NumberLong("234"),"dv": NumberLong("240")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("5"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("55"),"uv": NumberLong("28"),"dv": NumberLong("29")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("19"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_美味星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("27"),"dv": NumberLong("27")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_中_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_柬_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("22"),"uv": NumberLong("22"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_英_发现频道","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_中_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("21"),"uv": NumberLong("19"),"dv": NumberLong("19")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_柬_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("38"),"uv": NumberLong("35"),"dv": NumberLong("35")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_英_太子银行","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("41"),"uv": NumberLong("37"),"dv": NumberLong("37")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_中_美发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("22"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_柬_优惠码","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_柬_食在金边","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("59"),"uv": NumberLong("58"),"dv": NumberLong("58")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_英_探店星选","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_英_食在金边","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("24"),"uv": NumberLong("23"),"dv": NumberLong("23")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_中_加多宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_柬_加多宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("24"),"uv": NumberLong("17"),"dv": NumberLong("17")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_柬_胜狮活动","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_英_加多宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("8"),"dv": NumberLong("8")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_中_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("15"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_柬_兑换现金券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_柬_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("37"),"uv": NumberLong("34"),"dv": NumberLong("34")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_兑换现金券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_加多宝","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_惊喜再现","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("25"),"uv": NumberLong("19"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_中_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_柬_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_英_会员","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("29"),"uv": NumberLong("21"),"dv": NumberLong("21")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE_柬_品牌CE","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("102"),"uv": NumberLong("75"),"dv": NumberLong("76")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE_英_品牌CE","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("52"),"uv": NumberLong("42"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE速递_中_品牌CE速递","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("49"),"uv": NumberLong("37"),"dv": NumberLong("36")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_中_品牌亚彩","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("9"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_柬_品牌亚彩","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("24"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_英_品牌亚彩","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("21"),"uv": NumberLong("12"),"dv": NumberLong("12")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌优惠券_柬_品牌优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("203"),"uv": NumberLong("170"),"dv": NumberLong("178")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌优惠券_英_品牌优惠券","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("97"),"uv": NumberLong("85"),"dv": NumberLong("85")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_中_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("70"),"uv": NumberLong("52"),"dv": NumberLong("53")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_柬_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("174"),"uv": NumberLong("124"),"dv": NumberLong("136")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_英_品牌话费","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("112"),"uv": NumberLong("89"),"dv": NumberLong("95")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌领券中心_中_品牌领券中心","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("29"),"uv": NumberLong("26"),"dv": NumberLong("26")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_中_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("94"),"uv": NumberLong("67"),"dv": NumberLong("70")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_柬_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("120"),"uv": NumberLong("89"),"dv": NumberLong("89")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_英_外卖团购","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("125"),"uv": NumberLong("90"),"dv": NumberLong("97")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_中_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("37"),"uv": NumberLong("25"),"dv": NumberLong("25")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_柬_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("530"),"uv": NumberLong("266"),"dv": NumberLong("268")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_英_支付钱包","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("132"),"uv": NumberLong("91"),"dv": NumberLong("92")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_中_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("175"),"uv": NumberLong("74"),"dv": NumberLong("76")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_柬_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("652"),"uv": NumberLong("318"),"dv": NumberLong("327")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_英_电商批发","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("125"),"uv": NumberLong("73"),"dv": NumberLong("74")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_中_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("14"),"uv": NumberLong("13"),"dv": NumberLong("13")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_柬_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("101"),"uv": NumberLong("75"),"dv": NumberLong("78")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_英_航空游戏","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("85"),"uv": NumberLong("63"),"dv": NumberLong("64")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_中_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("34"),"uv": NumberLong("21"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_柬_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("44"),"uv": NumberLong("36"),"dv": NumberLong("36")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_英_航空酒店","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("41"),"uv": NumberLong("27"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页(总)","cardName": "WOWNOW首页(总)","nodeName": "WOWNOW首页(总)","createTime": ISODate("2022-06-29T17:53:02.209Z"),"dataTime": ISODate("2022-06-29T05:00:00.000Z"),"pv":NumberLong("64902"),"uv": NumberLong("36926"),"dv": NumberLong("38630")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "外卖团购_英_探店星选","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-EN","nodeName": "外卖外卖_英_周末1for1","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "Homepage-Banner2-KM","nodeName": "外卖团购_柬_探店星选","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("9"),"uv": NumberLong("5"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "YumNow Entrance - KH","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("2"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "បញ្ចុះតម្លៃរហូតដល់50%","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ភេសជ្ជ:","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ម្ហូបចិន","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "ហាងពេញនិយម","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_5折优惠","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("30"),"uv": NumberLong("12"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_上榜好店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("6"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("8"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("39"),"uv": NumberLong("23"),"dv": NumberLong("33")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_新店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_本地美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("4"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("23"),"uv": NumberLong("16"),"dv": NumberLong("19")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Beverages","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Cuisine","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "Discount up to 50%","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "YumNow Entrance","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("3"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_5折优惠","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("20"),"uv": NumberLong("9"),"dv": NumberLong("19")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_上榜好店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("56"),"uv": NumberLong("30"),"dv": NumberLong("43")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_新店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_本地美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("4"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("18"),"uv": NumberLong("7"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "品牌_中_优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "品牌_中_话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "外卖_中_团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("5"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "航空_中_游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏","nodeName": "航空_中_酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("50"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_ce快递","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "品牌_柬_话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "外卖_柬_团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "支付_柬_钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "电商_柬_批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "航空_柬_游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("6"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(柬)","nodeName": "金边_柬_外卖_0.25秒杀","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "Game Top Up","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "品牌_英_优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "品牌_英_话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "外卖_英_团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW工具栏(英文)","nodeName": "航空_英_酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_时尚潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("14"),"uv": NumberLong("6"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口","nodeName": "电商_中_调料批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "Taobao-KH","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "TinhNow Entrance-KH","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "តំបន់ម្តាយ នឹងទារក","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "ភេសជ្ជៈនិងអាហារសម្រន់","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "ស្រាបៀ និង ភេសជ្ជះ","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_啤酒饮料","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_女生潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_快消品批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("9"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_时尚潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_母婴专区","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("7"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_生活家居","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("18"),"uv": NumberLong("11"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_调料批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_运动系列","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_鞋帽箱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "Drinks & Snacks","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "Free Shipping","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_快消品批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_母婴专区","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("5"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_生活家居","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("1"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_柬文","nodeName": "品牌_柬_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "航空_英_酒店_7.5折活动","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("981"),"uv": NumberLong("612"),"dv": NumberLong("617")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("32"),"uv": NumberLong("23"),"dv": NumberLong("23")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("115"),"uv": NumberLong("95"),"dv": NumberLong("95")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2088"),"uv": NumberLong("1051"),"dv": NumberLong("1058")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("48"),"uv": NumberLong("42"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_美味星选","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("11"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("189"),"uv": NumberLong("141"),"dv": NumberLong("142")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_5折优惠","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2267"),"uv": NumberLong("1558"),"dv": NumberLong("1659")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_上榜好店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1122"),"uv": NumberLong("794"),"dv": NumberLong("846")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("614"),"uv": NumberLong("450"),"dv": NumberLong("478")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1307"),"uv": NumberLong("901"),"dv": NumberLong("945")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("12965"),"uv": NumberLong("6368"),"dv": NumberLong("6558")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_新店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_本地美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1313"),"uv": NumberLong("775"),"dv": NumberLong("801")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(柬)","nodeName": "外卖_柬_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2584"),"uv": NumberLong("1722"),"dv": NumberLong("1816")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_5折优惠","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1361"),"uv": NumberLong("921"),"dv": NumberLong("968")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_上榜好店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("363"),"uv": NumberLong("262"),"dv": NumberLong("272")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("510"),"uv": NumberLong("387"),"dv": NumberLong("394")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("615"),"uv": NumberLong("436"),"dv": NumberLong("452")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_外卖入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10729"),"uv": NumberLong("5454"),"dv": NumberLong("5593")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_新店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_本地美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("750"),"uv": NumberLong("479"),"dv": NumberLong("502")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW外卖入口(英文)","nodeName": "外卖_英_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1859"),"uv": NumberLong("1253"),"dv": NumberLong("1301")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW工具栏_柬文_默认","nodeName": "航空_柬_游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("23"),"uv": NumberLong("21"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_快消品批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("326"),"uv": NumberLong("165"),"dv": NumberLong("166")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_时尚潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("24"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("59"),"uv": NumberLong("44"),"dv": NumberLong("47")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("171"),"uv": NumberLong("132"),"dv": NumberLong("136")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_调料批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("34"),"uv": NumberLong("31"),"dv": NumberLong("31")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口","nodeName": "电商_中_鞋帽箱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("16"),"uv": NumberLong("15"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_啤酒饮料","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("149"),"uv": NumberLong("116"),"dv": NumberLong("128")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_快消品批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3299"),"uv": NumberLong("1444"),"dv": NumberLong("1448")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_时尚潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("203"),"uv": NumberLong("158"),"dv": NumberLong("167")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1250"),"uv": NumberLong("773"),"dv": NumberLong("819")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2207"),"uv": NumberLong("1188"),"dv": NumberLong("1230")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_调料批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("179"),"uv": NumberLong("141"),"dv": NumberLong("146")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_运动系列","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_酒水批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(柬)","nodeName": "电商_柬_鞋帽箱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("244"),"uv": NumberLong("179"),"dv": NumberLong("189")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_啤酒饮料","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_家居生活","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("49"),"uv": NumberLong("44"),"dv": NumberLong("44")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_快消品批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("401"),"uv": NumberLong("241"),"dv": NumberLong("245")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_时尚潮流","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("45"),"uv": NumberLong("41"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_淘宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("555"),"uv": NumberLong("338"),"dv": NumberLong("360")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_生活家居","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_电商入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("971"),"uv": NumberLong("526"),"dv": NumberLong("545")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_调料批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("40"),"uv": NumberLong("33"),"dv": NumberLong("33")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_酒水批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW电商入口(英文)","nodeName": "电商_英_鞋帽箱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("85"),"uv": NumberLong("44"),"dv": NumberLong("46")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_中文","nodeName": "品牌_中_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("10"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_柬文","nodeName": "品牌_柬_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("70"),"uv": NumberLong("59"),"dv": NumberLong("59")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "WOWNOW首页_最新_单张轮播_英文","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("35"),"uv": NumberLong("30"),"dv": NumberLong("30")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_中_banner1","nodeName": "品牌_中_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("9"),"uv": NumberLong("6"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_柬_banner1","nodeName": "品牌_柬_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("56"),"uv": NumberLong("38"),"dv": NumberLong("41")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_英_banner1","nodeName": "品牌_英_邀请有礼","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("9"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_英_banner2","nodeName": "品牌话费_英_柬新年","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "default_英_banner2","nodeName": "外卖_英_周五专题","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_上榜好店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("198"),"uv": NumberLong("137"),"dv": NumberLong("140")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_主入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("504"),"uv": NumberLong("273"),"dv": NumberLong("275")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("9"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("45"),"uv": NumberLong("31"),"dv": NumberLong("31")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("11"),"uv": NumberLong("9"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国外卖金刚区中文卡片","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("30"),"uv": NumberLong("25"),"dv": NumberLong("25")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_中_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_柬_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "会员_英_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_中_品牌CE","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("5"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_柬_品牌CE","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("62"),"uv": NumberLong("45"),"dv": NumberLong("48")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌CE_英_品牌CE","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("12"),"uv": NumberLong("10"),"dv": NumberLong("12")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌优惠券_柬_品牌优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("142"),"uv": NumberLong("115"),"dv": NumberLong("116")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌优惠券_英_品牌优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("37"),"uv": NumberLong("28"),"dv": NumberLong("28")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_中_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("37"),"uv": NumberLong("28"),"dv": NumberLong("28")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_柬_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("207"),"uv": NumberLong("136"),"dv": NumberLong("141")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌话费_英_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("58"),"uv": NumberLong("37"),"dv": NumberLong("38")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "品牌领券中心_中_品牌领券中心","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("8"),"dv": NumberLong("8")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_中_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("21"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_柬_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("40"),"uv": NumberLong("30"),"dv": NumberLong("37")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "外卖团购_英_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("25"),"uv": NumberLong("20"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_中_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("20"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_柬_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("456"),"uv": NumberLong("232"),"dv": NumberLong("239")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "支付钱包_英_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("63"),"uv": NumberLong("41"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_中_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("65"),"uv": NumberLong("31"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_柬_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("495"),"uv": NumberLong("239"),"dv": NumberLong("243")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "电商批发_英_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("44"),"uv": NumberLong("28"),"dv": NumberLong("28")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_中_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_柬_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("129"),"uv": NumberLong("93"),"dv": NumberLong("96")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空游戏_英_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("30"),"dv": NumberLong("30")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_中_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("7"),"uv": NumberLong("5"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_柬_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("35"),"uv": NumberLong("31"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国金刚区卡片模型","nodeName": "航空酒店_英_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("34"),"uv": NumberLong("15"),"dv": NumberLong("16")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_中_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_柬_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("25"),"uv": NumberLong("22"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌发现_英_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("5"),"dv": NumberLong("5")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_中_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_柬_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("32"),"uv": NumberLong("29"),"dv": NumberLong("29")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "品牌对外_英_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("20"),"uv": NumberLong("18"),"dv": NumberLong("18")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_中_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_柬_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("79"),"uv": NumberLong("59"),"dv": NumberLong("61")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_英_兑换现金券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "全国首页新BANNER","nodeName": "航空会员_英_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("9"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_中国美食","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("210"),"uv": NumberLong("114"),"dv": NumberLong("120")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_主入口","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("493"),"uv": NumberLong("230"),"dv": NumberLong("227")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_品牌街","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("8"),"uv": NumberLong("8"),"dv": NumberLong("8")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_商超便利","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("43"),"uv": NumberLong("30"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_小吃烧烤","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_美味星选","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("5"),"uv": NumberLong("3"),"dv": NumberLong("3")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "西港外卖金刚区中文的卡片","nodeName": "外卖_中_饮品甜品","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("32"),"uv": NumberLong("26"),"dv": NumberLong("26")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_中_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_柬_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("12"),"uv": NumberLong("11"),"dv": NumberLong("12")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌发现_英_发现频道","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("10"),"dv": NumberLong("10")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_中_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("16"),"uv": NumberLong("16"),"dv": NumberLong("16")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_柬_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("51"),"uv": NumberLong("41"),"dv": NumberLong("42")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "品牌对外_英_太子银行","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("30"),"dv": NumberLong("30")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_中_美发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("28"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_柬_食在金边","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("44"),"uv": NumberLong("40"),"dv": NumberLong("40")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "外卖团购_英_食在金边","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("33"),"uv": NumberLong("32"),"dv": NumberLong("32")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_柬_加多宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("4"),"uv": NumberLong("4"),"dv": NumberLong("4")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商批发_英_加多宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("2"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "电商海外购_英_代金券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_中_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("19"),"uv": NumberLong("17"),"dv": NumberLong("17")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_柬_加多宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("3"),"uv": NumberLong("2"),"dv": NumberLong("2")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_柬_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("49"),"uv": NumberLong("42"),"dv": NumberLong("44")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_兑换现金券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_加多宝","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空会员_英_惊喜再现","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("41"),"uv": NumberLong("36"),"dv": NumberLong("36")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页新BANNER","nodeName": "航空游戏_英_电竞比赛","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("1"),"uv": NumberLong("1"),"dv": NumberLong("1")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_中_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("6"),"uv": NumberLong("6"),"dv": NumberLong("6")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_柬_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("13"),"uv": NumberLong("11"),"dv": NumberLong("11")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "会员_英_会员","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("25"),"uv": NumberLong("14"),"dv": NumberLong("14")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE_柬_品牌CE","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("78"),"uv": NumberLong("62"),"dv": NumberLong("64")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE_英_品牌CE","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("58"),"uv": NumberLong("41"),"dv": NumberLong("41")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌CE速递_中_品牌CE速递","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("48"),"uv": NumberLong("27"),"dv": NumberLong("27")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_中_品牌亚彩","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("7"),"dv": NumberLong("7")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_柬_品牌亚彩","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("28"),"uv": NumberLong("22"),"dv": NumberLong("22")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌亚彩_英_品牌亚彩","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("18"),"uv": NumberLong("15"),"dv": NumberLong("15")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌优惠券_柬_品牌优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("155"),"uv": NumberLong("142"),"dv": NumberLong("140")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌优惠券_英_品牌优惠券","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("105"),"uv": NumberLong("89"),"dv": NumberLong("88")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_中_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("78"),"uv": NumberLong("51"),"dv": NumberLong("51")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_柬_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("151"),"uv": NumberLong("111"),"dv": NumberLong("114")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌话费_英_品牌话费","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("108"),"uv": NumberLong("76"),"dv": NumberLong("76")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "品牌领券中心_中_品牌领券中心","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("30"),"uv": NumberLong("23"),"dv": NumberLong("23")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_中_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("77"),"uv": NumberLong("59"),"dv": NumberLong("60")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_柬_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("86"),"uv": NumberLong("70"),"dv": NumberLong("73")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "外卖团购_英_外卖团购","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("128"),"uv": NumberLong("84"),"dv": NumberLong("92")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_中_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("38"),"uv": NumberLong("20"),"dv": NumberLong("20")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_柬_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("447"),"uv": NumberLong("227"),"dv": NumberLong("229")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "支付钱包_英_支付钱包","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("142"),"uv": NumberLong("77"),"dv": NumberLong("77")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_中_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("240"),"uv": NumberLong("90"),"dv": NumberLong("90")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_柬_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("643"),"uv": NumberLong("300"),"dv": NumberLong("305")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "电商批发_英_电商批发","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("125"),"uv": NumberLong("77"),"dv": NumberLong("77")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_中_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("10"),"uv": NumberLong("9"),"dv": NumberLong("9")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_柬_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("95"),"uv": NumberLong("78"),"dv": NumberLong("80")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空游戏_英_航空游戏","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("69"),"uv": NumberLong("54"),"dv": NumberLong("54")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_中_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("16"),"uv": NumberLong("13"),"dv": NumberLong("13")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_柬_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("39"),"uv": NumberLong("33"),"dv": NumberLong("36")});
db.report_click_day.insert({"pageName":"WOWNOW首页O2O","cardName": "首页金刚区卡片","nodeName": "航空酒店_英_航空酒店","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("77"),"uv": NumberLong("41"),"dv": NumberLong("41")});
db.report_click_day.insert({"pageName":"WOWNOW首页(总)","cardName": "WOWNOW首页(总)","nodeName": "WOWNOW首页(总)","createTime": ISODate("2022-06-30T17:53:02.209Z"),"dataTime": ISODate("2022-06-30T05:00:00.000Z"),"pv":NumberLong("60208"),"uv": NumberLong("34441"),"dv": NumberLong("35618")});