db.t_user_behavior_info.createIndex({"createTime":-1},{"name":'idx_createTime',background:true})

db.AGGREGATE_ORDER.ensureIndex({"AGGREGATE_ORDER_NO":1,"ORDER_TIME":-1,"USER_ID":1})
db.tag_info.createIndex({'tagNo':1},{unique:true})
db.tag_user.createIndex({'mobile':1})

db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "updateTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "ruleNo": "1419936837561659396",
    "ruleName": "内部员工",
    "ruleType": "internal user",
    "ruleValue": "internal user"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "updateTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "ruleNo": "1419936837561659397",
    "ruleName": "自定义标签",
    "ruleType": "customize",
    "ruleValue": "customize"
})
db.getCollection("tag_rule").insert({
    "_class": "com.lifekh.data.warehouse.bo.TagRuleBO",
    "createTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "updateTime": ISODate("2021-08-05T14:59:42.214+08:00"),
    "ruleNo": "1419936837561659398",
    "ruleName": "普通用户",
    "ruleType": "general user",
    "ruleValue": "general user"
})
db.getCollection("tag_rule").insert({
    "createTime": ISODate("2021-08-11T13:48:37.696+08:00"),
    "updateTime": ISODate("2021-08-11T13:57:51.843+08:00"),
    "ruleNo": "1419936837561659399",
    "ruleName": "rule",
    "ruleType": "rule",
    "ruleValue": "{\"symbol\":\"interval\",\"min\":0,\"max\":0,\"days\":0,\"behavior\":\"order success\"}",
})
db.getCollection("tag_info").insert({
    "createTime": ISODate("2021-08-13T10:54:25.898+08:00"),
    "updateTime": ISODate("2021-08-13T12:49:17.332+08:00"),
    "tagNo": "1419569630149836806",
    "tagName": {
        "zh-CN": "普通用户",
        "en-US": "general user",
        "km-KH": "general user"
    },
    "tagType": "basic",
    "tagClassify": "public",
    "tagStatus": "open",
    "tagDescription": "普通用户",
    "rule": [
        {
            "createTime": ISODate("2021-08-13T10:54:25.897+08:00"),
            "updateTime": ISODate("2021-08-13T10:54:25.897+08:00"),
            "ruleNo": "1419936837561659398",
            "ruleName": "普通用户",
            "ruleType": "general user",
            "ruleValue": "general user",
            "businessLine": "YumNow"
        }
    ]
})

console.log("脚本初始化开始")
   var updateCount = 0;
   var insertCount = 0;
   db.USER_OPERATOR_INFO.find().forEach( function(u) {
            var res = db.tag_user.update( {operatorNo: u.OPERATOR_NO},{$addToSet:{tagNo:"1419569630149836806"}},{upsert:true});

            var cursor = db.USER_OPERATOR_LOGIN_INFO.find({OPERATOR_NO:u.OPERATOR_NO,APP_ID:"SuperApp"})
            if(cursor != null && cursor.size() != 0){
                db.tag_user.update({operatorNo:u.OPERATOR_NO,mobile:null},{$set:{_class:"com.lifekh.data.warehouse.bo.TagUserBO",nickname:u.NICKNAME,createTime:new Date(),mobile:cursor[0].LOGIN_NAME}})
            }
            updateCount +=res.nModified;
            insertCount +=res.nUpserted;
        } );
console.log("插入:",insertCount,"更新:",updateCount)
console.log("脚本初始化完成")