db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.19,"createTime": ISODate("2023-11-13T05:00:00.000Z"),"dataTime": ISODate("2023-11-13T05:00:00.000Z"),"dau":5784,"orderPaidCount": 2530,"orderPaidRate": 1.0,"orderStoreUserRate": 0.8586,"orderTotalCount": 2530,"orderUserCount": 1888,"storeViewRate": 0.3802,"storeViewUv": 2199,"totalPayableAveragePrice": 10.86});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.2,"createTime": ISODate("2023-11-13T05:00:00.000Z"),"dataTime": ISODate("2023-11-13T05:00:00.000Z"),"dau":29127,"orderPaidCount": 7087,"orderPaidRate": 1.0,"orderStoreUserRate": 0.645,"orderTotalCount": 7087,"orderUserCount": 5907,"storeViewRate": 0.3144,"storeViewUv": 9158,"totalPayableAveragePrice": 6.55});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.03,"createTime": ISODate("2023-11-13T05:00:00.000Z"),"dataTime": ISODate("2023-11-13T05:00:00.000Z"),"dau":47040,"orderPaidCount": 9713,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6474,"orderTotalCount": 9713,"orderUserCount": 7901,"storeViewRate": 0.2595,"storeViewUv": 12205,"totalPayableAveragePrice": 6.22});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.29,"createTime": ISODate("2023-11-14T05:00:00.000Z"),"dataTime": ISODate("2023-11-14T05:00:00.000Z"),"dau":5833,"orderPaidCount": 2633,"orderPaidRate": 1.0,"orderStoreUserRate": 0.8517,"orderTotalCount": 2633,"orderUserCount": 1935,"storeViewRate": 0.3895,"storeViewUv": 2272,"totalPayableAveragePrice": 11.11});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.23,"createTime": ISODate("2023-11-14T05:00:00.000Z"),"dataTime": ISODate("2023-11-14T05:00:00.000Z"),"dau":29053,"orderPaidCount": 7186,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6277,"orderTotalCount": 7186,"orderUserCount": 5954,"storeViewRate": 0.3265,"storeViewUv": 9485,"totalPayableAveragePrice": 6.66});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.12,"createTime": ISODate("2023-11-14T05:00:00.000Z"),"dataTime": ISODate("2023-11-14T05:00:00.000Z"),"dau":46709,"orderPaidCount": 10202,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6282,"orderTotalCount": 10202,"orderUserCount": 8198,"storeViewRate": 0.2794,"storeViewUv": 13050,"totalPayableAveragePrice": 6.37});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.73,"createTime": ISODate("2023-11-15T05:00:00.000Z"),"dataTime": ISODate("2023-11-15T05:00:00.000Z"),"dau":5429,"orderPaidCount": 2538,"orderPaidRate": 1.0,"orderStoreUserRate": 0.8346,"orderTotalCount": 2538,"orderUserCount": 1903,"storeViewRate": 0.42,"storeViewUv": 2280,"totalPayableAveragePrice": 11.47});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.23,"createTime": ISODate("2023-11-15T05:00:00.000Z"),"dataTime": ISODate("2023-11-15T05:00:00.000Z"),"dau":27882,"orderPaidCount": 7035,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6028,"orderTotalCount": 7035,"orderUserCount": 5889,"storeViewRate": 0.3504,"storeViewUv": 9770,"totalPayableAveragePrice": 6.52});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.98,"createTime": ISODate("2023-11-15T05:00:00.000Z"),"dataTime": ISODate("2023-11-15T05:00:00.000Z"),"dau":43023,"orderPaidCount": 9670,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6132,"orderTotalCount": 9670,"orderUserCount": 7883,"storeViewRate": 0.2988,"storeViewUv": 12855,"totalPayableAveragePrice": 6.07});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.04,"createTime": ISODate("2023-11-16T05:00:00.000Z"),"dataTime": ISODate("2023-11-16T05:00:00.000Z"),"dau":5876,"orderPaidCount": 2527,"orderPaidRate": 1.0,"orderStoreUserRate": 0.8094,"orderTotalCount": 2527,"orderUserCount": 1907,"storeViewRate": 0.401,"storeViewUv": 2356,"totalPayableAveragePrice": 10.73});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.18,"createTime": ISODate("2023-11-16T05:00:00.000Z"),"dataTime": ISODate("2023-11-16T05:00:00.000Z"),"dau":29351,"orderPaidCount": 7165,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6096,"orderTotalCount": 7165,"orderUserCount": 5917,"storeViewRate": 0.3307,"storeViewUv": 9707,"totalPayableAveragePrice": 6.28});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.98,"createTime": ISODate("2023-11-16T05:00:00.000Z"),"dataTime": ISODate("2023-11-16T05:00:00.000Z"),"dau":46300,"orderPaidCount": 9560,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6128,"orderTotalCount": 9560,"orderUserCount": 7828,"storeViewRate": 0.2759,"storeViewUv": 12775,"totalPayableAveragePrice": 5.97});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.32,"createTime": ISODate("2023-11-17T05:00:00.000Z"),"dataTime": ISODate("2023-11-17T05:00:00.000Z"),"dau":5406,"orderPaidCount": 2575,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7876,"orderTotalCount": 2575,"orderUserCount": 1898,"storeViewRate": 0.4458,"storeViewUv": 2410,"totalPayableAveragePrice": 11.06});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.19,"createTime": ISODate("2023-11-17T05:00:00.000Z"),"dataTime": ISODate("2023-11-17T05:00:00.000Z"),"dau":27292,"orderPaidCount": 7053,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6124,"orderTotalCount": 7053,"orderUserCount": 5866,"storeViewRate": 0.351,"storeViewUv": 9579,"totalPayableAveragePrice": 6.4});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.96,"createTime": ISODate("2023-11-17T05:00:00.000Z"),"dataTime": ISODate("2023-11-17T05:00:00.000Z"),"dau":41860,"orderPaidCount": 9412,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6118,"orderTotalCount": 9412,"orderUserCount": 7646,"storeViewRate": 0.2986,"storeViewUv": 12498,"totalPayableAveragePrice": 6.01});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.51,"createTime": ISODate("2023-11-18T05:00:00.000Z"),"dataTime": ISODate("2023-11-18T05:00:00.000Z"),"dau":5395,"orderPaidCount": 2544,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7658,"orderTotalCount": 2544,"orderUserCount": 1841,"storeViewRate": 0.4456,"storeViewUv": 2404,"totalPayableAveragePrice": 11.07});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.35,"createTime": ISODate("2023-11-18T05:00:00.000Z"),"dataTime": ISODate("2023-11-18T05:00:00.000Z"),"dau":26200,"orderPaidCount": 6247,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5812,"orderTotalCount": 6247,"orderUserCount": 5108,"storeViewRate": 0.3355,"storeViewUv": 8789,"totalPayableAveragePrice": 6.51});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.09,"createTime": ISODate("2023-11-18T05:00:00.000Z"),"dataTime": ISODate("2023-11-18T05:00:00.000Z"),"dau":41527,"orderPaidCount": 9076,"orderPaidRate": 1.0,"orderStoreUserRate": 0.588,"orderTotalCount": 9076,"orderUserCount": 7327,"storeViewRate": 0.3,"storeViewUv": 12460,"totalPayableAveragePrice": 6.17});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.77,"createTime": ISODate("2023-11-19T05:00:00.000Z"),"dataTime": ISODate("2023-11-19T05:00:00.000Z"),"dau":5835,"orderPaidCount": 2759,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7856,"orderTotalCount": 2759,"orderUserCount": 1986,"storeViewRate": 0.4332,"storeViewUv": 2528,"totalPayableAveragePrice": 11.35});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.42,"createTime": ISODate("2023-11-19T05:00:00.000Z"),"dataTime": ISODate("2023-11-19T05:00:00.000Z"),"dau":27166,"orderPaidCount": 6074,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5545,"orderTotalCount": 6074,"orderUserCount": 4908,"storeViewRate": 0.3258,"storeViewUv": 8851,"totalPayableAveragePrice": 6.6});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.14,"createTime": ISODate("2023-11-19T05:00:00.000Z"),"dataTime": ISODate("2023-11-19T05:00:00.000Z"),"dau":43600,"orderPaidCount": 9316,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5843,"orderTotalCount": 9316,"orderUserCount": 7428,"storeViewRate": 0.2916,"storeViewUv": 12713,"totalPayableAveragePrice": 6.26});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.31,"createTime": ISODate("2023-11-20T05:00:00.000Z"),"dataTime": ISODate("2023-11-20T05:00:00.000Z"),"dau":5769,"orderPaidCount": 2609,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7694,"orderTotalCount": 2609,"orderUserCount": 1955,"storeViewRate": 0.4405,"storeViewUv": 2541,"totalPayableAveragePrice": 11.0});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.01,"createTime": ISODate("2023-11-20T05:00:00.000Z"),"dataTime": ISODate("2023-11-20T05:00:00.000Z"),"dau":28362,"orderPaidCount": 7218,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5996,"orderTotalCount": 7218,"orderUserCount": 6010,"storeViewRate": 0.3534,"storeViewUv": 10023,"totalPayableAveragePrice": 6.2});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.92,"createTime": ISODate("2023-11-20T05:00:00.000Z"),"dataTime": ISODate("2023-11-20T05:00:00.000Z"),"dau":44519,"orderPaidCount": 9448,"orderPaidRate": 0.9999,"orderStoreUserRate": 0.5966,"orderTotalCount": 9449,"orderUserCount": 7692,"storeViewRate": 0.2896,"storeViewUv": 12894,"totalPayableAveragePrice": 5.99});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.47,"createTime": ISODate("2023-11-21T05:00:00.000Z"),"dataTime": ISODate("2023-11-21T05:00:00.000Z"),"dau":5566,"orderPaidCount": 2642,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7599,"orderTotalCount": 2642,"orderUserCount": 1921,"storeViewRate": 0.4542,"storeViewUv": 2528,"totalPayableAveragePrice": 11.03});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.1,"createTime": ISODate("2023-11-21T05:00:00.000Z"),"dataTime": ISODate("2023-11-21T05:00:00.000Z"),"dau":27905,"orderPaidCount": 7063,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5973,"orderTotalCount": 7063,"orderUserCount": 5867,"storeViewRate": 0.352,"storeViewUv": 9823,"totalPayableAveragePrice": 6.28});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.0,"createTime": ISODate("2023-11-21T05:00:00.000Z"),"dataTime": ISODate("2023-11-21T05:00:00.000Z"),"dau":42213,"orderPaidCount": 9352,"orderPaidRate": 1.0,"orderStoreUserRate": 0.598,"orderTotalCount": 9352,"orderUserCount": 7595,"storeViewRate": 0.3009,"storeViewUv": 12701,"totalPayableAveragePrice": 6.04});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.42,"createTime": ISODate("2023-11-22T05:00:00.000Z"),"dataTime": ISODate("2023-11-22T05:00:00.000Z"),"dau":5877,"orderPaidCount": 2545,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7372,"orderTotalCount": 2545,"orderUserCount": 1891,"storeViewRate": 0.4364,"storeViewUv": 2565,"totalPayableAveragePrice": 11.02});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.08,"createTime": ISODate("2023-11-22T05:00:00.000Z"),"dataTime": ISODate("2023-11-22T05:00:00.000Z"),"dau":29211,"orderPaidCount": 7098,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5969,"orderTotalCount": 7098,"orderUserCount": 5925,"storeViewRate": 0.3398,"storeViewUv": 9927,"totalPayableAveragePrice": 6.32});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.89,"createTime": ISODate("2023-11-22T05:00:00.000Z"),"dataTime": ISODate("2023-11-22T05:00:00.000Z"),"dau":46233,"orderPaidCount": 9316,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5799,"orderTotalCount": 9316,"orderUserCount": 7569,"storeViewRate": 0.2823,"storeViewUv": 13053,"totalPayableAveragePrice": 5.94});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.52,"createTime": ISODate("2023-11-23T05:00:00.000Z"),"dataTime": ISODate("2023-11-23T05:00:00.000Z"),"dau":5566,"orderPaidCount": 2542,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7357,"orderTotalCount": 2542,"orderUserCount": 1907,"storeViewRate": 0.4657,"storeViewUv": 2592,"totalPayableAveragePrice": 11.17});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.09,"createTime": ISODate("2023-11-23T05:00:00.000Z"),"dataTime": ISODate("2023-11-23T05:00:00.000Z"),"dau":27739,"orderPaidCount": 7237,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6167,"orderTotalCount": 7237,"orderUserCount": 5953,"storeViewRate": 0.348,"storeViewUv": 9653,"totalPayableAveragePrice": 6.28});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.95,"createTime": ISODate("2023-11-23T05:00:00.000Z"),"dataTime": ISODate("2023-11-23T05:00:00.000Z"),"dau":42320,"orderPaidCount": 9291,"orderPaidRate": 1.0,"orderStoreUserRate": 0.606,"orderTotalCount": 9291,"orderUserCount": 7558,"storeViewRate": 0.2947,"storeViewUv": 12471,"totalPayableAveragePrice": 6.02});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.71,"createTime": ISODate("2023-11-24T05:00:00.000Z"),"dataTime": ISODate("2023-11-24T05:00:00.000Z"),"dau":4998,"orderPaidCount": 2601,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7351,"orderTotalCount": 2601,"orderUserCount": 1929,"storeViewRate": 0.525,"storeViewUv": 2624,"totalPayableAveragePrice": 11.45});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.29,"createTime": ISODate("2023-11-24T05:00:00.000Z"),"dataTime": ISODate("2023-11-24T05:00:00.000Z"),"dau":26925,"orderPaidCount": 7492,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6347,"orderTotalCount": 7492,"orderUserCount": 6168,"storeViewRate": 0.3609,"storeViewUv": 9718,"totalPayableAveragePrice": 6.71});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.01,"createTime": ISODate("2023-11-24T05:00:00.000Z"),"dataTime": ISODate("2023-11-24T05:00:00.000Z"),"dau":40836,"orderPaidCount": 9682,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6245,"orderTotalCount": 9682,"orderUserCount": 7860,"storeViewRate": 0.3082,"storeViewUv": 12586,"totalPayableAveragePrice": 6.21});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 9.44,"createTime": ISODate("2023-11-25T05:00:00.000Z"),"dataTime": ISODate("2023-11-25T05:00:00.000Z"),"dau":5330,"orderPaidCount": 2463,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7356,"orderTotalCount": 2463,"orderUserCount": 1789,"storeViewRate": 0.4563,"storeViewUv": 2432,"totalPayableAveragePrice": 12.21});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.59,"createTime": ISODate("2023-11-25T05:00:00.000Z"),"dataTime": ISODate("2023-11-25T05:00:00.000Z"),"dau":25018,"orderPaidCount": 5999,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5865,"orderTotalCount": 5999,"orderUserCount": 4790,"storeViewRate": 0.3264,"storeViewUv": 8167,"totalPayableAveragePrice": 6.91});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.17,"createTime": ISODate("2023-11-25T05:00:00.000Z"),"dataTime": ISODate("2023-11-25T05:00:00.000Z"),"dau":39780,"orderPaidCount": 9082,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6068,"orderTotalCount": 9082,"orderUserCount": 7142,"storeViewRate": 0.2959,"storeViewUv": 11769,"totalPayableAveragePrice": 6.38});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 9.01,"createTime": ISODate("2023-11-26T05:00:00.000Z"),"dataTime": ISODate("2023-11-26T05:00:00.000Z"),"dau":5264,"orderPaidCount": 2503,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7259,"orderTotalCount": 2503,"orderUserCount": 1796,"storeViewRate": 0.47,"storeViewUv": 2474,"totalPayableAveragePrice": 11.69});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.7,"createTime": ISODate("2023-11-26T05:00:00.000Z"),"dataTime": ISODate("2023-11-26T05:00:00.000Z"),"dau":26313,"orderPaidCount": 5863,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5653,"orderTotalCount": 5863,"orderUserCount": 4582,"storeViewRate": 0.3081,"storeViewUv": 8106,"totalPayableAveragePrice": 7.3});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.17,"createTime": ISODate("2023-11-26T05:00:00.000Z"),"dataTime": ISODate("2023-11-26T05:00:00.000Z"),"dau":42654,"orderPaidCount": 9171,"orderPaidRate": 1.0,"orderStoreUserRate": 0.583,"orderTotalCount": 9171,"orderUserCount": 7127,"storeViewRate": 0.2866,"storeViewUv": 12225,"totalPayableAveragePrice": 6.67});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 9.17,"createTime": ISODate("2023-11-27T05:00:00.000Z"),"dataTime": ISODate("2023-11-27T05:00:00.000Z"),"dau":5297,"orderPaidCount": 2588,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7054,"orderTotalCount": 2588,"orderUserCount": 1834,"storeViewRate": 0.4908,"storeViewUv": 2600,"totalPayableAveragePrice": 12.01});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.66,"createTime": ISODate("2023-11-27T05:00:00.000Z"),"dataTime": ISODate("2023-11-27T05:00:00.000Z"),"dau":25271,"orderPaidCount": 5940,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5402,"orderTotalCount": 5940,"orderUserCount": 4633,"storeViewRate": 0.3394,"storeViewUv": 8576,"totalPayableAveragePrice": 7.35});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.27,"createTime": ISODate("2023-11-27T05:00:00.000Z"),"dataTime": ISODate("2023-11-27T05:00:00.000Z"),"dau":39681,"orderPaidCount": 9273,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5688,"orderTotalCount": 9273,"orderUserCount": 7228,"storeViewRate": 0.3203,"storeViewUv": 12708,"totalPayableAveragePrice": 6.73});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.77,"createTime": ISODate("2023-11-28T05:00:00.000Z"),"dataTime": ISODate("2023-11-28T05:00:00.000Z"),"dau":5931,"orderPaidCount": 2754,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7237,"orderTotalCount": 2754,"orderUserCount": 1954,"storeViewRate": 0.4552,"storeViewUv": 2700,"totalPayableAveragePrice": 11.67});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.81,"createTime": ISODate("2023-11-28T05:00:00.000Z"),"dataTime": ISODate("2023-11-28T05:00:00.000Z"),"dau":26815,"orderPaidCount": 6314,"orderPaidRate": 1.0,"orderStoreUserRate": 0.566,"orderTotalCount": 6314,"orderUserCount": 4976,"storeViewRate": 0.3279,"storeViewUv": 8792,"totalPayableAveragePrice": 7.57});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.21,"createTime": ISODate("2023-11-28T05:00:00.000Z"),"dataTime": ISODate("2023-11-28T05:00:00.000Z"),"dau":42284,"orderPaidCount": 9837,"orderPaidRate": 1.0,"orderStoreUserRate": 0.606,"orderTotalCount": 9837,"orderUserCount": 7629,"storeViewRate": 0.2977,"storeViewUv": 12589,"totalPayableAveragePrice": 6.75});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.21,"createTime": ISODate("2023-11-29T05:00:00.000Z"),"dataTime": ISODate("2023-11-29T05:00:00.000Z"),"dau":5688,"orderPaidCount": 2774,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7371,"orderTotalCount": 2774,"orderUserCount": 1999,"storeViewRate": 0.4768,"storeViewUv": 2712,"totalPayableAveragePrice": 10.92});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.28,"createTime": ISODate("2023-11-29T05:00:00.000Z"),"dataTime": ISODate("2023-11-29T05:00:00.000Z"),"dau":27504,"orderPaidCount": 7704,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6065,"orderTotalCount": 7704,"orderUserCount": 6292,"storeViewRate": 0.3772,"storeViewUv": 10374,"totalPayableAveragePrice": 6.65});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.02,"createTime": ISODate("2023-11-29T05:00:00.000Z"),"dataTime": ISODate("2023-11-29T05:00:00.000Z"),"dau":42369,"orderPaidCount": 10286,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6096,"orderTotalCount": 10286,"orderUserCount": 8151,"storeViewRate": 0.3156,"storeViewUv": 13371,"totalPayableAveragePrice": 6.16});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.44,"createTime": ISODate("2023-11-30T05:00:00.000Z"),"dataTime": ISODate("2023-11-30T05:00:00.000Z"),"dau":5985,"orderPaidCount": 2678,"orderPaidRate": 1.0,"orderStoreUserRate": 0.6981,"orderTotalCount": 2678,"orderUserCount": 1984,"storeViewRate": 0.4749,"storeViewUv": 2842,"totalPayableAveragePrice": 11.24});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.41,"createTime": ISODate("2023-11-30T05:00:00.000Z"),"dataTime": ISODate("2023-11-30T05:00:00.000Z"),"dau":28374,"orderPaidCount": 7409,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5841,"orderTotalCount": 7409,"orderUserCount": 6095,"storeViewRate": 0.3677,"storeViewUv": 10434,"totalPayableAveragePrice": 6.83});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.01,"createTime": ISODate("2023-11-30T05:00:00.000Z"),"dataTime": ISODate("2023-11-30T05:00:00.000Z"),"dau":43561,"orderPaidCount": 9787,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5616,"orderTotalCount": 9787,"orderUserCount": 7850,"storeViewRate": 0.3209,"storeViewUv": 13978,"totalPayableAveragePrice": 6.19});
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.39,"createTime": ISODate("2023-12-01T05:00:00.000Z"),"dataTime": ISODate("2023-12-01T05:00:00.000Z"),"dau":5764,"orderPaidCount": 2761,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7197,"orderTotalCount": 2761,"orderUserCount": 2000,"storeViewRate": 0.4821,"storeViewUv": 2779,"totalPayableAveragePrice": 11.0})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.14,"createTime": ISODate("2023-12-01T05:00:00.000Z"),"dataTime": ISODate("2023-12-01T05:00:00.000Z"),"dau":27887,"orderPaidCount": 7362,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5654,"orderTotalCount": 7362,"orderUserCount": 6042,"storeViewRate": 0.3832,"storeViewUv": 10687,"totalPayableAveragePrice": 6.21})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.95,"createTime": ISODate("2023-12-01T05:00:00.000Z"),"dataTime": ISODate("2023-12-01T05:00:00.000Z"),"dau":42035,"orderPaidCount": 9789,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5614,"orderTotalCount": 9789,"orderUserCount": 7890,"storeViewRate": 0.3343,"storeViewUv": 14054,"totalPayableAveragePrice": 5.87})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 9.63,"createTime": ISODate("2023-12-02T05:00:00.000Z"),"dataTime": ISODate("2023-12-02T05:00:00.000Z"),"dau":5820,"orderPaidCount": 2798,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7083,"orderTotalCount": 2798,"orderUserCount": 1972,"storeViewRate": 0.4784,"storeViewUv": 2784,"totalPayableAveragePrice": 12.29})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.5,"createTime": ISODate("2023-12-02T05:00:00.000Z"),"dataTime": ISODate("2023-12-02T05:00:00.000Z"),"dau":27239,"orderPaidCount": 6765,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5451,"orderTotalCount": 6765,"orderUserCount": 5461,"storeViewRate": 0.3678,"storeViewUv": 10019,"totalPayableAveragePrice": 6.61})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.11,"createTime": ISODate("2023-12-02T05:00:00.000Z"),"dataTime": ISODate("2023-12-02T05:00:00.000Z"),"dau":43248,"orderPaidCount": 10065,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5416,"orderTotalCount": 10065,"orderUserCount": 7947,"storeViewRate": 0.3393,"storeViewUv": 14674,"totalPayableAveragePrice": 6.18})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.67,"createTime": ISODate("2023-12-03T05:00:00.000Z"),"dataTime": ISODate("2023-12-03T05:00:00.000Z"),"dau":5503,"orderPaidCount": 2879,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7208,"orderTotalCount": 2879,"orderUserCount": 2029,"storeViewRate": 0.5115,"storeViewUv": 2815,"totalPayableAveragePrice": 11.29})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.58,"createTime": ISODate("2023-12-03T05:00:00.000Z"),"dataTime": ISODate("2023-12-03T05:00:00.000Z"),"dau":26178,"orderPaidCount": 6400,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5451,"orderTotalCount": 6400,"orderUserCount": 5116,"storeViewRate": 0.3585,"storeViewUv": 9386,"totalPayableAveragePrice": 6.76})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 4.07,"createTime": ISODate("2023-12-03T05:00:00.000Z"),"dataTime": ISODate("2023-12-03T05:00:00.000Z"),"dau":40605,"orderPaidCount": 9771,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5638,"orderTotalCount": 9771,"orderUserCount": 7811,"storeViewRate": 0.3412,"storeViewUv": 13854,"totalPayableAveragePrice": 6.1})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 7.95,"createTime": ISODate("2023-12-04T05:00:00.000Z"),"dataTime": ISODate("2023-12-04T05:00:00.000Z"),"dau":5945,"orderPaidCount": 2742,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7185,"orderTotalCount": 2742,"orderUserCount": 1993,"storeViewRate": 0.4666,"storeViewUv": 2774,"totalPayableAveragePrice": 10.62})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.06,"createTime": ISODate("2023-12-04T05:00:00.000Z"),"dataTime": ISODate("2023-12-04T05:00:00.000Z"),"dau":28259,"orderPaidCount": 8012,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5964,"orderTotalCount": 8012,"orderUserCount": 6512,"storeViewRate": 0.3864,"storeViewUv": 10919,"totalPayableAveragePrice": 6.38})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.8,"createTime": ISODate("2023-12-04T05:00:00.000Z"),"dataTime": ISODate("2023-12-04T05:00:00.000Z"),"dau":43157,"orderPaidCount": 10579,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5837,"orderTotalCount": 10579,"orderUserCount": 8504,"storeViewRate": 0.3376,"storeViewUv": 14570,"totalPayableAveragePrice": 6.03})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.52,"createTime": ISODate("2023-12-05T05:00:00.000Z"),"dataTime": ISODate("2023-12-05T05:00:00.000Z"),"dau":5324,"orderPaidCount": 2615,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7101,"orderTotalCount": 2615,"orderUserCount": 1906,"storeViewRate": 0.5041,"storeViewUv": 2684,"totalPayableAveragePrice": 11.01})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.01,"createTime": ISODate("2023-12-05T05:00:00.000Z"),"dataTime": ISODate("2023-12-05T05:00:00.000Z"),"dau":27335,"orderPaidCount": 7891,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5968,"orderTotalCount": 7891,"orderUserCount": 6433,"storeViewRate": 0.3943,"storeViewUv": 10779,"totalPayableAveragePrice": 6.29})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.8,"createTime": ISODate("2023-12-05T05:00:00.000Z"),"dataTime": ISODate("2023-12-05T05:00:00.000Z"),"dau":41318,"orderPaidCount": 10561,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5787,"orderTotalCount": 10561,"orderUserCount": 8486,"storeViewRate": 0.3549,"storeViewUv": 14664,"totalPayableAveragePrice": 6.0})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "zh-CN","actualPayAveragePrice": 8.3,"createTime": ISODate("2023-12-06T05:00:00.000Z"),"dataTime": ISODate("2023-12-06T05:00:00.000Z"),"dau":5542,"orderPaidCount": 2787,"orderPaidRate": 1.0,"orderStoreUserRate": 0.7216,"orderTotalCount": 2787,"orderUserCount": 2027,"storeViewRate": 0.5069,"storeViewUv": 2809,"totalPayableAveragePrice": 10.86})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "en-US","actualPayAveragePrice": 4.11,"createTime": ISODate("2023-12-06T05:00:00.000Z"),"dataTime": ISODate("2023-12-06T05:00:00.000Z"),"dau":27611,"orderPaidCount": 8097,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5923,"orderTotalCount": 8097,"orderUserCount": 6674,"storeViewRate": 0.4081,"storeViewUv": 11267,"totalPayableAveragePrice": 6.45})
db.getCollection("report_yumnow_store_order_conversion_rate").insert({"language": "km-KH","actualPayAveragePrice": 3.91,"createTime": ISODate("2023-12-06T05:00:00.000Z"),"dataTime": ISODate("2023-12-06T05:00:00.000Z"),"dau":40818,"orderPaidCount": 10684,"orderPaidRate": 1.0,"orderStoreUserRate": 0.5886,"orderTotalCount": 10684,"orderUserCount": 8617,"storeViewRate": 0.3586,"storeViewUv": 14639,"totalPayableAveragePrice": 6.12})
db.getCollection('report_yumnow_store_order_conversion_rate').createIndex({'dataTime':1,'language':1},{background:true});
