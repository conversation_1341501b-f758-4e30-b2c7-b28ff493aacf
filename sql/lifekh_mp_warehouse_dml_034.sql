db.getCollection("tag_classification").insert( {
    createTime: new Date(),
    firstTagClassificationNo: "1419569629125082021",
    firstClassificationName: "用户属性",
    secondTagClassificationNo: "1587011380725121024",
    secondaryClassificationName: "新用户",
    secondaryClassificationNameEn: "new user",
    secondaryClassificationNameKm: "new user",
    tagClassify: "public"
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587013884615933952",
    ruleName: "首单-外卖",
    ruleType: "first_order_biz",
    ruleValue: "YumNow",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316840787968",
    ruleName: "首单-电商",
    ruleType: "first_order_biz",
    ruleValue: "TinhNow",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982272",
    ruleName: "首单-话费充值",
    ruleType: "first_order_biz",
    ruleValue: "PhoneTopUp",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982274",
    ruleName: "首单-OTA",
    ruleType: "first_order_biz",
    ruleValue: "OTA",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982276",
    ruleName: "首单-团购",
    ruleType: "first_order_biz",
    ruleValue: "GroupBuy",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982278",
    ruleName: "首单-游戏频道",
    ruleType: "first_order_biz",
    ruleValue: "GameChannel",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );


db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982280",
    ruleName: "首单-酒店频道",
    ruleType: "first_order_biz",
    ruleValue: "HotelChannel",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587017316844982282",
    ruleName: "首单-账单缴费",
    ruleType: "first_order_biz",
    ruleValue: "BillPayment",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );

db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1587334544672886792",
    ruleName: "首单-外部合作",
    ruleType: "first_order_biz",
    ruleValue: "ExternalCooperation",
    timeliness: true,
    classification: 10,
    generalRules: [ ],
    specialRules: []
} );



db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982310",
    tagName: {
        "en-US": "First order-YumNow",
        "zh-CN": "首单-外卖",
        "km-KH": "First order-YumNow"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587013884615933952",
            ruleName: "首单-外卖",
            ruleType: "first_order_biz",
            ruleValue: "YumNow"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982312",
    tagName: {
        "en-US": "First order-TinhNow",
        "zh-CN": "首单-电商",
        "km-KH": "First order-TinhNow"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316840787968",
            ruleName: "首单-电商",
            ruleType: "first_order_biz",
            ruleValue: "TinhNow"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982314",
    tagName: {
        "en-US": "First order-PhoneTopUp",
        "zh-CN": "首单-话费充值",
        "km-KH": "First order-PhoneTopUp"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982272",
            ruleName: "首单-话费充值",
            ruleType: "first_order_biz",
            ruleValue: "PhoneTopUp"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982316",
    tagName: {
        "en-US": "First order-OTA",
        "zh-CN": "首单-OTA",
        "km-KH": "First order-OTA"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982274",
            ruleName: "首单-OTA",
            ruleType: "first_order_biz",
            ruleValue: "OTA"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982318",
    tagName: {
        "en-US": "First order-GroupBuy",
        "zh-CN": "首单-团购",
        "km-KH": "First order-GroupBuy"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982276",
            ruleName: "首单-团购",
            ruleType: "first_order_biz",
            ruleValue: "GroupBuy"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316844982320",
    tagName: {
        "en-US": "First order-GameChannel",
        "zh-CN": "首单-游戏频道",
        "km-KH": "First order-GameChannel"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982278",
            ruleName: "首单-游戏频道",
            ruleType: "first_order_biz",
            ruleValue: "GameChannel"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316849176577",
    tagName: {
        "en-US": "First order-HotelChannel",
        "zh-CN": "首单-酒店频道",
        "km-KH": "First order-HotelChannel"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982280",
            ruleName: "首单-酒店频道",
            ruleType: "first_order_biz",
            ruleValue: "HotelChannel"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587017316849176579",
    tagName: {
        "en-US": "First order-BillPayment",
        "zh-CN": "首单-账单缴费",
        "km-KH": "First order-BillPayment"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587017316844982282",
            ruleName: "首单-账单缴费",
            ruleType: "first_order_biz",
            ruleValue: "BillPayment"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );

db.getCollection("tag_info").insert( {
    createTime: new Date(),
    tagNo: "1587334544672886798",
    tagName: {
        "en-US": "First order-ExternalCooperation",
        "zh-CN": "首单-外部合作",
        "km-KH": "First order-ExternalCooperation"
    },
    tagType: "basic",
    tagClassify: "public",
    tagStatus: "open",
    rule: [
        {
            ruleNo: "1587334544672886792",
            ruleName: "首单-外部合作",
            ruleType: "first_order_biz",
            ruleValue: "ExternalCooperation"
        }
    ],
    firstClassification: "用户属性",
    secondaryClassification: "新用户",
    firstClassificationNo: "1419569629125082021",
    secondaryClassificationEn: "new user",
    secondaryClassificationKm: "new user",
    secondaryClassificationNo: "1587011380725121024",
    execStatus: "effective",
    execType: "system",
    tagScope: "everyone",
    tagCatalogue: "system",
    execTime: new Date(),
    totalUser: NumberInt("0")
} );
