db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-05-13T09:45:51.837Z"),
eventGroup: "other",
eventName: "砸金蛋活动页面浏览数PV",
eventNo: "LuckyEggsPV",
remark: "砸金蛋活动页面浏览数PV",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-05-13T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-05-13T09:45:51.837Z"),
eventGroup: "other",
eventName: "砸金蛋领奖品按钮点击次数",
eventNo: "HitEgg_ReceivePrize",
remark: "砸金蛋领奖品点击按钮次数",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-05-13T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-05-13T09:45:51.837Z"),
eventGroup: "other",
eventName: "砸金蛋分享好友领取按钮点击次数",
eventNo: "LuckyEggs_ShareFriends_click",
remark: "砸金蛋分享好友领取按钮点击次数",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-05-13T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-05-13T09:45:51.837Z"),
eventGroup: "other",
eventName: "砸金蛋马上使用按钮点击次数",
eventNo: "LuckyEggs_UseImmediately_click",
remark: "砸金蛋马上使用按钮点击次数",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-05-13T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-05-13T09:45:51.837Z"),
eventGroup: "other",
eventName: "砸金蛋进入下载页面浏览数",
eventNo: "LuckyEggs_DownloadPV",
remark: "砸金蛋进入下载页面浏览数",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-05-13T09:45:51.837Z")
} );