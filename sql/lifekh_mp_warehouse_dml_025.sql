db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-04-20T09:45:51.837Z"),
eventGroup: "login",
eventName: "活跃用户",
eventNo: "active_user",
remark: "活跃用户",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-04-20T09:45:51.837Z")
} );


db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-04-26T09:45:51.837Z"),
eventGroup: "other",
eventName: "领券活动页面的下载页PV",
eventNo: "sms_marketing_downloadPagePV",
remark: "领券活动页面的下载页PV",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-04-26T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-04-26T09:45:51.837Z"),
eventGroup: "other",
eventName: "领券活动页面的立即领取福利",
eventNo: "sms_marketing_welfareClick",
remark: "领券活动页面的立即领取福利",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-04-26T09:45:51.837Z")
} );

db.getCollection("event_type").insert( {
bussinessLine: "all",
createBy: "system",
createTime: ISODate("2022-04-26T09:45:51.837Z"),
eventGroup: "other",
eventName: "领券活动页面的PV统计数",
eventNo: "sms_marketing_sendCouponsPagePV",
remark: "领券活动页面的PV统计数",
status: 10,
updateBy: "system",
updateTime: ISODate("2022-04-26T09:45:51.837Z")
} );
