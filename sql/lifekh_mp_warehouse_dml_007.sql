db.getCollection("tag_rule").insert( {
    _class: "com.lifekh.data.warehouse.bo.TagRuleBO",
    createTime: ISODate("2021-11-02T11:28:42.292Z"),
    updateTime: ISODate("2021-11-02T11:28:42.292Z"),
    ruleNo: "2021110221499719680",
    ruleName: "新用户",
    ruleType: "add_delete_tag",
    ruleValue: "add_delete_tag",
    timeliness: true,
    classification: 11,
    generalRules: [ ],
    specialRules: [
    ]
} );


db.getCollection("tag_info").insert( {
    createTime: ISODate("2021-11-02T03:46:28.056Z"),
    updateTime: ISODate("2021-11-02T03:00:21.632Z"),
    createBy: "gxf",
    updateBy: "gxf",
    tagNo: "2021110211551498496",
    tagName: {
        "zh-CN": "外卖新用户",
        "en-US": "yumNow new User",
        "km-KH": "yumNow new User"
    },
    tagType: "rule",
    tagClassify: "public",
    firstClassification: "用户属性",
    secondaryClassification: "默认",
    tagStatus: "open",
    tagDescription: "外卖新用户",
    timeliness: false,
    totalUser: NumberLong("0"),
    rule: [
        {
            ruleNo: "2021110221499719680",
            ruleName: "新用户",
            timeliness: true,
            classification: "11",
            ruleType: "add_delete_tag",
            generalRules: [
            ],
            specialRules: [
            ]
        }
    ],
    _class: "com.lifekh.data.warehouse.bo.TagInfoV2BO"
});

db.getCollection("event_type").insert( {
    createTime: ISODate("2021-11-11T09:45:51.841Z"),
    updateTime: ISODate("2021-11-11T09:45:51.841Z"),
    eventGroup: "other",
    eventNo: "@DEBUG",
    eventName: "APP调试",
    bussinessLine: "all",
    status: 10,
    remark: "APP调试",
    createBy: "wgl",
    updateBy: "wgl"
} );

db.tag_user.updateMany({}, {$addToSet:{tagNo:"2021110211551498496"}})

db.AGGREGATE_ORDER.distinct('USER_ID',{BUSINESS_LINE:'YumNow',AGGREGATE_ORDER_FINAL_STATE:11}).forEach(function(u){
    var res = db.tag_user.updateOne( {operatorNo:u},{$pull:{tagNo:"2021110211551498496"}});
})