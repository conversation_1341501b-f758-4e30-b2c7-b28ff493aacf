db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1676156249073788284",
    ruleName: "打开APP登录成功",
    ruleType: "user_online",
    ruleValue: "user_online",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "user_online",
            symbol: "more than"
        },
        {
            ruleFiled: "user_online",
            symbol: "less than"
        },
        {
            ruleFiled: "user_online",
            symbol: "equal"
        },
        {
            ruleFiled: "user_online",
            symbol: "interval"
        }
    ],
    classification: 11
} );

db.getCollection("tag_rule").insert( {
    createTime: new Date(),
    updateTime: new Date(),
    ruleNo: "1676155934389956241",
    ruleName: "没有打开过APP",
    ruleType: "user_offline",
    ruleValue: "user_offline",
    timeliness: false,
    generalRules: [
    ],
    specialRules: [
        {
            ruleFiled: "days",
            symbol: "more than"
        },
        {
            ruleFiled: "user_offline",
            symbol: "more than"
        },
        {
            ruleFiled: "user_offline",
            symbol: "less than"
        },
        {
            ruleFiled: "user_offline",
            symbol: "equal"
        },
        {
            ruleFiled: "user_offline",
            symbol: "interval"
        }
    ],
    classification: 11
} );