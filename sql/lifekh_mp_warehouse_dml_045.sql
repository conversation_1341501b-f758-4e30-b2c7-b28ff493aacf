db.event_type.insert({
		"eventGroup": "other",
		"eventNo": "store_list_pv",
		"eventName": "分类列表页 pv",
		"remark": "",
		"table": "collect_buried_point_takeaway_store_list_pv",
		"status": 10,
		"bussinessLine": "all",
		"createBy": "system",
		"createTime": {
			"$date": "2023-11-06T09:34:52.970Z"
		},
		"updateBy": "system",
		"updateTime": {
			"$date": "2023-11-06T09:34:52.970Z"
		}
});
db.event_type.insert({
		"eventGroup": "other",
		"eventNo": "topic_page_pv",
		"eventName": "专题页 pv",
		"remark": "",
		"table": "collect_buried_point_takeaway_topic_page_pv",
		"status": 10,
		"bussinessLine": "all",
		"createBy": "system",
		"createTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		},
		"updateBy": "system",
		"updateTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		}
});
db.event_type.insert({
		"eventGroup": "other",
		"eventNo": "pay_success_pv",
		"eventName": "支付成功页 pv",
		"remark": "",
		"table": "collect_buried_point_takeaway_pay_success_pv",
		"status": 10,
		"bussinessLine": "all",
		"createBy": "system",
		"createTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		},
		"updateBy": "system",
		"updateTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		}
});
db.event_type.insert({
		"eventGroup": "other",
		"eventNo": "takeawayKKDExposure",
		"eventName": "金刚区曝光",
		"remark": "",
		"table": "collect_buried_point_takeaway_view_v2",
		"status": 10,
		"bussinessLine": "all",
		"createBy": "system",
		"createTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		},
		"updateBy": "system",
		"updateTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		}
});
db.event_type.insert({
		"eventGroup": "other",
		"eventNo": "takeawayBannerExposure",
		"eventName": "banner曝光",
		"remark": "",
		"table": "collect_buried_point_takeaway_view_v2",
		"status": 10,
		"bussinessLine": "all",
		"createBy": "system",
		"createTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		},
		"updateBy": "system",
		"updateTime": {
			"$date": "2025-03-17T09:34:52.970Z"
		}
});
