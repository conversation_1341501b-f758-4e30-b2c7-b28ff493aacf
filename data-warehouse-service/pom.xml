<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>user-data-warehouse</artifactId>
        <groupId>com.lifekh</groupId>
        <version>*******-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>data-warehouse-service</artifactId>

    <properties>
        <!--跳过deploy-->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>framework-container-springmvc</artifactId>
            <groupId>com.outstanding</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-collection</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-container-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-elastic-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>basic-common-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>chaos-usercenter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kh-super.takeaway</groupId>
            <artifactId>product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kh-super.takeaway</groupId>
            <artifactId>merchant-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lifekh</groupId>
            <artifactId>data-warehouse-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>shop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-plugin-money</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>app-config-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>discovery-review-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>marketing-api</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>user-datawarehouse-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>