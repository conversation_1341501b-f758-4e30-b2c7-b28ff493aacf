package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserBehaviorInfoServiceTest {
    @Autowired
    private UserBehaviorInfoService userBehaviorInfoService;
    @Test
    public void add(){
//        UserBehaviorInfoBO bo = new UserBehaviorInfoBO();
//        bo.setAppVersion("2.17.0");
//        bo.setCreateTime(new Date());
//        bo.setDelState(DelStateEnum.NORMAL);
//        bo.setDeviceId("D928A9F264B36F8F9B1649EB7C4BFEC42C56AD54");
//        bo.setDeviceType("ANDROID");
//        bo.setEvent("LOGIN");
//        bo.setId(30132L);
//        bo.setLanguage("zh-CN");
//        bo.setLatitude(new BigDecimal(23.119962));
//        bo.setLoginName("8613531906744");
//        bo.setLongitude(new BigDecimal(113.401156));
//        bo.setMessage("{riskToken=9ca17ae2e6fbcda170e2e6ee99f67df3ae8bb4c261a2b48ba2c44b978f9fbbb6628eaaa484f94ea8f5a588ea2af0feaec3b92a95b48199b744b78bbba8f34b978b9bb6d85aa1aea385f172b69cb7d9c9549c8aee9e, biz=SMS_LOGIN, requestTm=202107191634235203, agreementNo=AG1139809824121798655, smsCode=904285, mobile=8613531906744, deviceId=D928A9F264B36F8F9B1649EB7C4BFEC42C56AD54, deviceInfo={\"abi\":\"arm64-v8a\",\"apiLevel\":\"29\",\"appChannel\":\"Portal\",\"appVersion\":\"2.17.0\",\"batteryLevel\":\"57%\",\"batteryState\":\"DISCHARGING\",\"brand\":\"HONOR\",\"cpuCores\":\"8\",\"density\":\"3.0\",\"densityDpi\":\"480\",\"deviceId\":\"D928A9F264B36F8F9B1649EB7C4BFEC42C56AD54\",\"glesVersion\":\"3.2\",\"language\":\"zh\",\"latitude\":\"23.119962\",\"longitude\":\"113.401156\",\"manufacturer\":\"HUAWEI\",\"maxMemory\":\"512\",\"memory\":\"3637\",\"model\":\"BKL-AL00\",\"networkBrand\":\"46003\",\"networkType\":\"network_wify\",\"osVersion\":\"10\",\"region\":\"CN\",\"supportedAbis\":\"arm64-v8a,armeabi-v7a,armeabi\",\"totalDisk\":\"56612618240\",\"uid\":\"\",\"uname\":\"\",\"wxh\":\"1080x2160\"}}    ");
//        bo.setOperatorNo("1412726200166146048");
//        bo.setUpdateTime(new Date());
//        bo.setVersion(0L);
//        userBehaviorInfoService.add(bo);

    }

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ZoneTagService zoneTagService;

    @Test
    public void test2() {

        //db.ogg_user_behavior_info.aggregate([{$match:{longitude : { "$ne":null } }},{$group :{_id :'$operatorNo','operatorNo': {'$last': '$operatorNo'},'longitude': {'$last': '$longitude'},'latitude': {'$last': '$latitude'}}}])
        Integer pageNum = 1;
        Integer pageSize = 5;
//        Pageable pageable = PageRequest.of(0, 4);

//        Sort sort = new Sort(Sort.Direction.DESC,"msgEn");
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("longitude").ne(null).and("operatorNo").is("1336569147477356544")),//条件
                 Aggregation.sort(Sort.Direction.DESC, "createTime"),
                 Aggregation.group("operatorNo")
                         .first("operatorNo").as("operatorNo")
                         .first("latitude").as("latitude")
                         .first("longitude").as("longitude"),//分组字段
                Aggregation.skip((pageNum - 1L) * pageSize),//过滤
                Aggregation.limit(pageSize)//页数
        );
        AggregationResults<UserBehaviorInfoBO> outputType = mongoTemplate.aggregate(agg, "ogg_user_behavior_info", UserBehaviorInfoBO.class);
        List<UserBehaviorInfoBO> list=outputType.getMappedResults();
    }

    @Test
    public void testExists() {
        Query query = new Query();
        query.addCriteria(Criteria.where("userInfoBo.operatorNo").is("1422092198263009280").and("eventBo.event").is("REFRESH_TOKEN").and("createTime").gte(DateUtil.dateCalculation(new Date(),  Calendar.DATE, 0)));
        boolean exists = mongoTemplate.exists(query, UserBehaviorInfoBO.class);
        log.info("查询结果：{}", exists);
    }

}
