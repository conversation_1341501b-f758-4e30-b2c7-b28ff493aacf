package com.lifekh.data.warehouse.service;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.executor.ShardingContexts;
import com.google.common.collect.Maps;
import com.lifekh.data.warehouse.report.TakeawayFlowConversionReportJob;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TakeawayFlowConversionReportJobTest extends GoServiceTest {

    @Autowired
    private TakeawayFlowConversionReportJob takeawayFlowConversionReportJob;

    @Test
    public void testTakeawayFlowConversionReportJob() {
        takeawayFlowConversionReportJob.execute(new ShardingContext(new ShardingContexts("jobName", "taskId", 2, "jobParameter", Maps.newHashMap() , 1), 2));
        log.info("外卖首页流量转化统计测试结束");
    }
}
