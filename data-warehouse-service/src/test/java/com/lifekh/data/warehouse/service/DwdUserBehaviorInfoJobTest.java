package com.lifekh.data.warehouse.service;

import cn.hutool.json.JSONUtil;
import com.chaos.common.enums.AppIdEnum;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.executor.ShardingContexts;
import com.github.pagehelper.PageHelper;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.DwdUserBehaviorInfoDO;
import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.UserLevelInfoBO;
import com.lifekh.data.warehouse.bo.behavior.LocationBO;
import com.lifekh.data.warehouse.bo.behavior.UserInfoBO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorInfoBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.DwdUserBehaviorDTO;
import com.lifekh.data.warehouse.enums.LoginMethodDataEnum;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorBO;
import com.lifekh.data.warehouse.oracle.bo.OracleUserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.oracle.bo.OracleZoneBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleZoneDAO;
import com.lifekh.data.warehouse.report.DwdUserBehaviorInfoJob;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DwdUserBehaviorInfoJobTest extends GoServiceTest {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    /**
     * 用户端Android，用户端iOS
     */
    private static List<String> APP_NO_LIST = Arrays.asList("10","11");

    @Autowired
    private LocationManage locationManage;

    @Autowired
    private OracleZoneDAO oracleZoneDAO;

    @Test
    public void testDistinct() {

        Criteria userCriteria = Criteria.where("createTime")
                .gte(DateUtil.getTodayStartDate())
                .and("eventBo.event").is("active_user");

        Integer count =  mongoTemplate.findDistinct(Query.query(userCriteria),"userInfoBo.operatorNo", "collect_behavior_v2", CollectSpmBO.class).size();

        log.info("查询总数:{}", count);
    }

    @Test
    public void test222() {
        Integer pageNum=1, pageSize=10;
        do {
            PageHelper.startPage(pageNum++, pageSize);
            //查询活跃的用户
            List<OracleOperatorBO> list = oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime(DateUtil.getFirstDateOfWeek(new Date()), 2, 0);
            log.info("查询oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime,size:{}", JSONUtil.toJsonStr(list));


            if(CollectionUtils.isEmpty(list)) {
                break;
            }

            //查询列表为空则退出遍历
        } while (true);

        pageNum=1;
        pageSize=10;
        do {
            PageHelper.startPage(pageNum++, pageSize);
            //查询活跃的用户
            List<OracleOperatorBO> list = oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime(DateUtil.getFirstDateOfWeek(new Date()), 2, 1);
            log.info("查询oracleUserOperatorLoginInfoDAO.queryUserByOnlineTime,size:{}", JSONUtil.toJsonStr(list));

            if(CollectionUtils.isEmpty(list)) {
                break;
            }
            //查询列表为空则退出遍历
        } while (true);
    }

    @Test
    public void getProvince() {
//        String longitude = "11.5515518188477";
//        String latitude = "104.919380187988";
        //latitude = 纬度
        String latitude = "11.544199";
        //longitude = 经度
        String longitude = "104.900993";
        LocationDTO locationResponse = new LocationDTO();
        //根据经纬度找出对应的省份名称
        LocationDTO locationDTO = locationManage.getNameByPosition(longitude, latitude);
        if (Objects.nonNull(locationDTO)) {
            OracleZoneBO province = oracleZoneDAO.queryZone(locationDTO.getProvinceName().toLowerCase(), 11);
            if(province != null) {
                locationResponse.setProvinceName(province.getMsgEn());
                OracleZoneBO district = oracleZoneDAO.queryZoneByParent(locationDTO.getDistinctName().toLowerCase(), province.getCode());
                locationResponse.setDistinctName(Optional.ofNullable(district).orElse(new OracleZoneBO()).getMsgEn());
            }
        }
        locationResponse.setLatitude(latitude);
        locationResponse.setLongitude(longitude);
        log.info("结果:{}", locationDTO);
    }

    @Test
    public void test3() {
        AggregationResults<DwdUserBehaviorDTO> results = null;
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).is("1487754504243986432");
        criteria.and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_ACTIVE_USER);
//        criteria.and(ClickReportConstant.CREATE_TIME).gte(DateUtil.getYesterdayStartDate());
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project(Fields.from(Fields.field("coordinates", "locationBo.coordinates"),
                        Fields.field(ClickReportConstant.SIMPLE_LANGUAGE, ClickReportConstant.LANGUAGE),
                        Fields.field(ClickReportConstant.PROVINCE_NAME, ClickReportConstant.PROVINCE_NAME_EN),
                        Fields.field(ClickReportConstant.AREA_NAME, ClickReportConstant.AREA_NAME_EN),
                        Fields.field(ClickReportConstant.CREATE_TIME, ClickReportConstant.CREATE_TIME))),
                Aggregation.sort(Sort.by(Sort.Direction.DESC,ClickReportConstant.CREATE_TIME)),
                Aggregation.limit(1)
        );
        try {
            results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
        } catch (Exception e) {
        }
        log.info("结果：{}", results);

    }

    @Test
    public void test2() {
        String operatorNo = "1538805894043754496";
        AggregationResults<DwdUserBehaviorDTO> results = null;
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).is(operatorNo);
        criteria.and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_ACTIVE_USER);
        criteria.and(ClickReportConstant.CREATE_TIME).gte(DateUtil.getYesterdayStartDate());
//        criteria.and("_id").is("62b048730c8ba70001b7d60c");

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project(Fields.from(Fields.field("coordinates", "locationBo.coordinates"),
                        Fields.field(ClickReportConstant.SIMPLE_LANGUAGE, ClickReportConstant.LANGUAGE),
                        Fields.field(ClickReportConstant.CREATE_TIME, ClickReportConstant.CREATE_TIME))),
                Aggregation.sort(Sort.by(Sort.Direction.DESC,ClickReportConstant.CREATE_TIME)),
                Aggregation.limit(1)
        );
        try {
            results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
        } catch (Exception e) {
            try {
                results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
            } catch (Exception ex) {
                log.error("查询用户活跃数据异常,op:{}",operatorNo, e);
            }
        }
        log.info("查询结果：{}", results);
    }

    @Test
    public void test1() {
        //查询用户的经纬度
        Query locationQuery = new Query();
        locationQuery.addCriteria(Criteria.where(ClickReportConstant.USERINFOBO_OPERATOR_NO).is("1282953142081392640")
                .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_ACTIVE_USER)).with(Sort.by(Sort.Direction.DESC,ClickReportConstant.CREATE_TIME));
        UserBehaviorInfoBO userBehaviorInfoBo = mongoTemplate.findOne(locationQuery, UserBehaviorInfoBO.class, MongoDbCollectonName.COLLECT_BEHAVIOR);

        log.info("查询结果:{}", userBehaviorInfoBo);
    }

    @Test
    public void testJob() {
        int shardingTotalCount = 2;
        int shardingItem = 0;
        int pageSize = 6;

        //分页查询前一天登录过的用户
        //Integer pageSize = 1000;
        Date startTime = DateUtil.getYesterdayStartDate();
        Date endTime = DateUtil.getYesterdayEndTime();

        Criteria userCriteria = Criteria.where("createTime").gte(startTime).lt(endTime)
                .and(ClickReportConstant.EVENT).is("active_user");
        Query query = Query.query(userCriteria);

        //查询总数，生产job分片数较多，需要手动计算每个分片的区间
        int count =  mongoTemplate.findDistinct(query,ClickReportConstant.USERINFOBO_OPERATOR_NO,
                MongoDbCollectonName.COLLECT_BEHAVIOR, CollectSpmBO.class).size();
        //先根据每页大小算出总页数
        int pages = Math.floorDiv(count, pageSize);
        //如果当前总页数有余，则向上取整
        if(Math.floorMod(count, pageSize) > 0) {
            pages++;
        }

        //当总页数小于总分片数量时，需要修改总分片数为总页数
        if(pages < shardingTotalCount) {
            if(pages <= shardingItem) {
                //当前分片数大于页数时，直接返回
                log.info("分片数大于页数, 总分片数:{}, 总页数:{}, 当前分片:{}", shardingTotalCount, pages, shardingItem);
                return;
            }
            shardingTotalCount = pages;
        }

        //根据总页数算出每个分片占多少页
        int loopPageSize = Math.floorDiv(pages, shardingTotalCount);

        //根据分配数和页数算出起始位置
        long startIndex = (long) loopPageSize * shardingItem * pageSize;

        //如果当前分片是最后一个，且页数有余，则增加一页
        if(Math.floorMod(pages, shardingTotalCount) > 0
                && shardingTotalCount == (shardingItem + 1)) {
            loopPageSize++;
        }

        //遍历页数
        for (int pageIndex=0; pageIndex < loopPageSize; pageIndex++) {
            //查询活跃的用户
            List<String> list = getActiveUserList(query, startIndex, pageSize, pageIndex);

            //查询列表为空则退出遍历
            if(CollectionUtils.isEmpty(list)) {
                break;
            }
            //统计时间
            long startTimeLog = System.currentTimeMillis();
            for(String op : list) {
                try {
                    //如果为空则继续遍历下一个
                    if(StringUtils.isBlank(op)) continue;

                    //查询鉴权表
                    OracleUserOperatorLoginInfoBO bo = getOracleUserOperatorLoginInfoBo(startTime, op);

                    //查询dwd用户信息
                    DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO = queryDwdUserInfo(op);

                    //查询用户的经纬度
                    activeUserLocation(bo, dwdUserBehaviorInfoDO);

                    //查询用户基本信息
                    queryUserBasicInfo(bo, dwdUserBehaviorInfoDO);

                    dwdUserBehaviorInfoDO.setOperatorNo(bo.getOperatorNo());
                    dwdUserBehaviorInfoDO.setOnlineTime(isTodayDate(bo.getOnlineTime()) ? dwdUserBehaviorInfoDO.getOnlineTime() : bo.getOnlineTime());
                    dwdUserBehaviorInfoDO.setLastOnlineTime(Optional.ofNullable(bo.getLastOnlineTime()).orElse(dwdUserBehaviorInfoDO.getOnlineTime()));
                    dwdUserBehaviorInfoDO.setAppNo(Optional.ofNullable(bo.getAppNo()).orElse(0));
                    dwdUserBehaviorInfoDO.setAppVersion(APP_NO_LIST.contains(bo.getAppNo()) ? bo.getAppVersion() : null);
                    dwdUserBehaviorInfoDO.setDeviceId(bo.getDeviceId());
                    dwdUserBehaviorInfoDO.setUdDeviceId(bo.getUdDeviceId());
                    dwdUserBehaviorInfoDO.setPhoneModel(APP_NO_LIST.contains(bo.getAppNo()) ? bo.getPhoneModel() : null);
                    dwdUserBehaviorInfoDO.setSeq(bo.getId());
                    log.info("活跃用户信息:{}", dwdUserBehaviorInfoDO);

                    if(dwdUserBehaviorInfoDO.getId() == null) {
                        mongoTemplate.insert(dwdUserBehaviorInfoDO);
                    } else {
                        Query dwdUserInfoQuery = new Query();
                        dwdUserInfoQuery.addCriteria(Criteria.where(DwdUserBehaviorInfoDO.OPERATOR_NO).is(bo.getOperatorNo()));

                        Update update = new Update();
                        update.set(DwdUserBehaviorInfoDO.MOBILE, dwdUserBehaviorInfoDO.getMobile());
                        update.set(DwdUserBehaviorInfoDO.GENDER, dwdUserBehaviorInfoDO.getGender());
                        update.set(DwdUserBehaviorInfoDO.BIRTHDAY, dwdUserBehaviorInfoDO.getBirthday());
                        update.set(DwdUserBehaviorInfoDO.LATITUDE, dwdUserBehaviorInfoDO.getLatitude());
                        update.set(DwdUserBehaviorInfoDO.LONGITUDE,dwdUserBehaviorInfoDO.getLongitude());
                        update.set(DwdUserBehaviorInfoDO.PROVINCE, dwdUserBehaviorInfoDO.getProvince());
                        update.set(DwdUserBehaviorInfoDO.DISTRICT, dwdUserBehaviorInfoDO.getDistrict());
                        update.set(DwdUserBehaviorInfoDO.ONLINE_TIME, dwdUserBehaviorInfoDO.getOnlineTime());
                        update.set(DwdUserBehaviorInfoDO.LANGUAGE, dwdUserBehaviorInfoDO.getLanguage());
                        update.set(DwdUserBehaviorInfoDO.APP_NO, dwdUserBehaviorInfoDO.getAppNo());
                        update.set(DwdUserBehaviorInfoDO.APP_VERSION, dwdUserBehaviorInfoDO.getAppVersion());
                        update.set(DwdUserBehaviorInfoDO.DEVICE_ID, dwdUserBehaviorInfoDO.getDeviceId());
                        update.set(DwdUserBehaviorInfoDO.UD_DEVICE_ID, dwdUserBehaviorInfoDO.getUdDeviceId());
                        update.set(DwdUserBehaviorInfoDO.PHONE_MODEL, dwdUserBehaviorInfoDO.getPhoneModel());
                        update.set(DwdUserBehaviorInfoDO.SEQ, dwdUserBehaviorInfoDO.getSeq());
                        update.set(DwdUserBehaviorInfoDO.OP_LEVEL, dwdUserBehaviorInfoDO.getOpLevel());
                        update.set(DwdUserBehaviorInfoDO.LEVEL_NAEM, dwdUserBehaviorInfoDO.getLevelName());
                        update.set(DwdUserBehaviorInfoDO.AGE, dwdUserBehaviorInfoDO.getAge());
                        update.set(DwdUserBehaviorInfoDO.OPERATOR_STATUS, dwdUserBehaviorInfoDO.getOperatorStatus());
                        update.set(DwdUserBehaviorInfoDO.LOGIN_METHOD, dwdUserBehaviorInfoDO.getLoginMethod());
                        update.set(DwdUserBehaviorInfoDO.REG_LATITUDE, dwdUserBehaviorInfoDO.getRegLatitude());
                        update.set(DwdUserBehaviorInfoDO.REG_LONGITUDE, dwdUserBehaviorInfoDO.getRegLongitude());
                        update.set(DwdUserBehaviorInfoDO.REG_PROVINCE, dwdUserBehaviorInfoDO.getRegProvince());
                        update.set(DwdUserBehaviorInfoDO.REG_DISTRICT, dwdUserBehaviorInfoDO.getRegDistrict());
                        update.set(DwdUserBehaviorInfoDO.LAST_ONLINE_TIME, dwdUserBehaviorInfoDO.getLastOnlineTime());
                        mongoTemplate.upsert(dwdUserInfoQuery, update, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
                    }
                } catch (Exception e) {
                    log.error("统计昨日用户行为数据异常, operatorNo:{}", op, e);
                }
            }
            log.info("打印时间:{},size:{}", (System.currentTimeMillis() - startTimeLog), list.size());
        }
        log.info("结束统计昨日用户行为数据,{}");
    }

    /**
     * 判断是否是当前日期
     *
     * @param date
     * @return
     */
    private boolean isTodayDate(Date date) {
        return date.getTime() >= DateUtil.getTodayStartDate().getTime();
    }

    private OracleUserOperatorLoginInfoBO getOracleUserOperatorLoginInfoBo(Date startTime, String op) {
        return oracleUserOperatorLoginInfoDAO.queryUserByOperatorNoAndTime(startTime, op);
    }

    private List<String> getActiveUserList(Query query, long startIndex, Integer pageSize, Integer pageNum) {
        return mongoTemplate.findDistinct(query,
                ClickReportConstant.USERINFOBO_OPERATOR_NO,
                MongoDbCollectonName.COLLECT_BEHAVIOR,
                String.class).stream().sorted().skip(startIndex + ((long) pageSize * pageNum)).limit(pageSize).collect(Collectors.toList());
    }

    private DwdUserBehaviorInfoDO queryDwdUserInfo(String operatorNo) {
        DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO = null;
        Query dwdUserInfoQuery = new Query();
        dwdUserInfoQuery.addCriteria(Criteria.where(DwdUserBehaviorInfoDO.OPERATOR_NO).is(operatorNo));
        try {
            dwdUserBehaviorInfoDO = mongoTemplate.findOne(dwdUserInfoQuery, DwdUserBehaviorInfoDO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
        } catch (Exception e) {
            try {
                dwdUserBehaviorInfoDO = mongoTemplate.findOne(dwdUserInfoQuery, DwdUserBehaviorInfoDO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
            } catch (Exception ex) {
                log.error("查询用户行为数据异常,op:{}", operatorNo, ex);
            }
        }

        if(Objects.isNull(dwdUserBehaviorInfoDO)) {
            dwdUserBehaviorInfoDO = new DwdUserBehaviorInfoDO();
        }
        return dwdUserBehaviorInfoDO;
    }

    private void queryUserBasicInfo(OracleUserOperatorLoginInfoBO bo, DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO) {
        //查询用户的性别，出生日期
        OracleUserOperatorLoginInfoBO userInfo = oracleUserOperatorLoginInfoDAO.queryUserByOperatorNo(bo.getOperatorNo());

        if(userInfo != null) {
            dwdUserBehaviorInfoDO.setRegisterTime(userInfo.getRegisterTime());
            dwdUserBehaviorInfoDO.setGender(userInfo.getGender());
            dwdUserBehaviorInfoDO.setBirthday(userInfo.getBirthday());
            dwdUserBehaviorInfoDO.setAge(getAge(userInfo.getBirthday()));
            dwdUserBehaviorInfoDO.setOperatorStatus(userInfo.getOperatorStatus());
            dwdUserBehaviorInfoDO.setMobile(userInfo.getLoginName());

            //如果登录号为空则查询历史表
            if(StringUtils.isEmpty(userInfo.getLoginName())) {
                String loginName = oracleUserOperatorLoginInfoDAO.queryLoginhistoryByOperatorNo(bo.getOperatorNo());
                dwdUserBehaviorInfoDO.setMobile(Optional.ofNullable(loginName).orElse(""));
            }

            if(userInfo.getRegLatitude() != null && userInfo.getRegLongitude() != null) {
                LocationDTO locationDTO = getProvince(userInfo.getRegLongitude().toString(), userInfo.getRegLatitude().toString());
                dwdUserBehaviorInfoDO.setRegProvince(locationDTO.getProvinceName());
                dwdUserBehaviorInfoDO.setRegDistrict(locationDTO.getDistinctName());
            }
            dwdUserBehaviorInfoDO.setRegLatitude(userInfo.getRegLatitude());
            dwdUserBehaviorInfoDO.setRegLongitude(userInfo.getRegLongitude());
        }

        //查询用户等级
        UserLevelInfoBO levelInfoBo = oracleUserOperatorLoginInfoDAO.queryUserLevelByOperatorNo(bo.getOperatorNo());
        dwdUserBehaviorInfoDO.setOpLevel(Optional.ofNullable(levelInfoBo).orElse(new UserLevelInfoBO(1, "大众会员")).getOpLevel());
        dwdUserBehaviorInfoDO.setLevelName(Optional.ofNullable(levelInfoBo).orElse(new UserLevelInfoBO(1, "大众会员")).getLevelName());

        //登录方式
        try {
            String loginMethod = oracleUserOperatorLoginInfoDAO.queryLoginMethod(bo.getOperatorNo());
            if(!org.apache.commons.lang3.StringUtils.isBlank(loginMethod)) {
                dwdUserBehaviorInfoDO.setLoginMethod(LoginMethodDataEnum.getMessageByCode(loginMethod));
            }
        } catch (Exception e) {
            log.error("查询登录方式异常, operatorNo:{}", bo.getOperatorNo(), e);
        }
    }

    /**
     * 查询用户的经纬度
     *
     * @param bo
     * @param dwdUserBehaviorInfoDO
     */
    private void activeUserLocation(OracleUserOperatorLoginInfoBO bo, DwdUserBehaviorInfoDO dwdUserBehaviorInfoDO) {
        AggregationResults<DwdUserBehaviorDTO> results = null;
        Criteria criteria = new Criteria();
        criteria.and(ClickReportConstant.USERINFOBO_OPERATOR_NO).is(bo.getOperatorNo());
        criteria.and(ClickReportConstant.EVENT).is(EventConstant.EVENT_NO_ACTIVE_USER);
        criteria.and(ClickReportConstant.CREATE_TIME).gte(DateUtil.getYesterdayStartDate()).lt(DateUtil.getYesterdayEndTime());
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project(Fields.from(Fields.field("coordinates", "locationBo.coordinates"),
                        Fields.field(ClickReportConstant.SIMPLE_LANGUAGE, ClickReportConstant.LANGUAGE),
                        Fields.field(ClickReportConstant.PROVINCE_NAME, ClickReportConstant.PROVINCE_NAME_EN),
                        Fields.field(ClickReportConstant.AREA_NAME, ClickReportConstant.AREA_NAME_EN),
                        Fields.field(ClickReportConstant.CREATE_TIME, ClickReportConstant.CREATE_TIME))),
                Aggregation.sort(Sort.by(Sort.Direction.DESC,ClickReportConstant.CREATE_TIME)),
                Aggregation.limit(1)
        );
        try {
            results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
        } catch (Exception e) {
            try {
                results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.COLLECT_BEHAVIOR,  DwdUserBehaviorDTO.class);
            } catch (Exception ex) {
                log.error("查询用户活跃数据异常,op:{}",bo.getOperatorNo(), e);
            }
        }

        if(Objects.nonNull(results) && !CollectionUtils.isEmpty(results.getMappedResults())) {
            DwdUserBehaviorDTO dwdUserBehaviorDTO = results.getMappedResults().get(0);

            //活跃地区
            if(Objects.nonNull(dwdUserBehaviorDTO.getCoordinates())
                    && dwdUserBehaviorDTO.getCoordinates().length == 2) {
                //纬度
                dwdUserBehaviorInfoDO.setLatitude(new BigDecimal(dwdUserBehaviorDTO.getCoordinates()[1]).setScale(8, BigDecimal.ROUND_HALF_UP).floatValue());
                //经度
                dwdUserBehaviorInfoDO.setLongitude(new BigDecimal(dwdUserBehaviorDTO.getCoordinates()[0]).setScale(8, BigDecimal.ROUND_HALF_UP).floatValue());
//                LocationDTO locationDTO = getProvince(dwdUserBehaviorDTO.getCoordinates()[0].toString(),
//                        dwdUserBehaviorDTO.getCoordinates()[1].toString());
                dwdUserBehaviorInfoDO.setProvince(dwdUserBehaviorDTO.getProvinceName());
                dwdUserBehaviorInfoDO.setDistrict(dwdUserBehaviorDTO.getAreaName());
            }

            //语言
            dwdUserBehaviorInfoDO.setOnlineTime(Optional.of(dwdUserBehaviorDTO).orElse(new DwdUserBehaviorDTO()).getCreateTime());
            dwdUserBehaviorInfoDO.setLanguage(Optional.of(dwdUserBehaviorDTO).orElse(new DwdUserBehaviorDTO()).getLanguage());
        }
    }

    private LocationDTO getProvince(String longitude, String latitude) {
        LocationDTO locationResponse = new LocationDTO();
        //根据经纬度找出对应的省份名称
        LocationDTO locationDTO = locationManage.getNameByPosition(longitude, latitude);
        if (Objects.nonNull(locationDTO)) {
            OracleZoneBO province = oracleZoneDAO.queryZone(locationDTO.getProvinceName().toLowerCase(), 11);
            if(province != null) {
                locationResponse.setProvinceName(province.getMsgEn());
                OracleZoneBO district = oracleZoneDAO.queryZoneByParent(locationDTO.getDistinctName().toLowerCase(), province.getCode());
                locationResponse.setDistinctName(Optional.ofNullable(district).orElse(new OracleZoneBO()).getMsgEn());
            }
        }
        locationResponse.setLatitude(latitude);
        locationResponse.setLongitude(longitude);
        return locationResponse;
    }

    /**
     * 计算年龄
     */
    private static int getAge(Date birthDay) {
        int age = 0;

        if(birthDay == null) {
            return age;
        }

        try {
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) {
                return age;
            }
            int yearNow = cal.get(Calendar.YEAR);
            int monthNow = cal.get(Calendar.MONTH);
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
            cal.setTime(birthDay);

            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

            age = yearNow - yearBirth;

            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) {
                        age--;
                    }
                }else{
                    age--;
                }
            }
        } catch (Exception e) {
            log.error("计算年龄异常,birthDay:{}", birthDay, e);
        }
        return age;
    }
}
