package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.report.service.PageViewReportService;
import com.lifekh.data.warehouse.report.service.UserViewReportService;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class UserViewReportServiceTest extends GoServiceTest {

    @Autowired
    private UserViewReportService userViewReportService;

    @Test
    public void countTheUVofTheCurrentPage() {
        userViewReportService.countTheUVofTheCurrentPage();
    }

    @Test
    public void testCurrentOnlineUser() {
        userViewReportService.currentOnlineUser();
    }
}
