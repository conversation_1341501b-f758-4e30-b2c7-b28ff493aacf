package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.ChannelReportStrategy;
import com.lifekh.data.warehouse.report.service.LocationReportStrategy;
import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;


@Slf4j
public class LocationReportStrategyTest extends GoServiceTest {

    @Autowired
    private LocationReportStrategy locationReportStrategy;

    @Autowired
    private TargetReportService targetReportService;

    @Test
    public void testDay() {
        int dayOffset = 1;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date dataTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);

//        Date weekStartTime = DateUtil.getFirstDateOfWeek(yesterday);
//        Date weekEndTime = DateUtil.getLastDateOfWeek(yesterday);
        Date weekStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date weekEndTime = DateUtil.getEndTimeOfDate(yesterday);
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(weekStartTime);
        reportBasicReqDTO.setDataTime(dataTime);
        reportBasicReqDTO.setEndTime(weekEndTime);
        locationReportStrategy.statisticsByDay(reportBasicReqDTO);


        //targetReportService.staticTargetData();
    }
}
