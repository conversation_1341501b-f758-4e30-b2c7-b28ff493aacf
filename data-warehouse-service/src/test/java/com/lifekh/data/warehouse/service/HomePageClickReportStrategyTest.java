package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.HomePageClickReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

public class HomePageClickReportStrategyTest extends GoServiceTest {

    @Autowired
    private HomePageClickReportStrategy homePageClickReportStrategy;

    @Test
    public void test1() {
        int dayOffset = 1;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date yesterdayEndTime = DateUtil.getEndTimeOfDate(yesterday);
        homePageClickReportStrategy.statisticsByDay(new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday));
    }
}
