package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserLabelServiceTest {
    @Autowired
    private UserLabelService userLabelService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Test
    public void add(){
        UserLabelBO bo = new UserLabelBO();
        bo.setAppId("SuperApp");
        bo.setClientIp("**************");
        bo.setCreateTime(new Date());
        bo.setDeviceId("5576D5A345253BE07DF85C1071BE30AC80457E13");
        bo.setDeviceType("ANDROID");
        bo.setId(new Long("8135"));
        bo.setInvitationAccount(null);
        bo.setInvitationCode(null);
        bo.setInvitationCodeSelf(null);
        bo.setInvitationSource(null);
        bo.setLanguage("zh-CN");
        bo.setLoginCount(5);
        bo.setLoginName("*************");
        bo.setOperatorNo("1413325721820360704");
        bo.setRegisterChannel(null);
        bo.setUpdateTime(new Date());
        bo.setVersion(0L);
        userLabelService.add(bo);
    }
}
