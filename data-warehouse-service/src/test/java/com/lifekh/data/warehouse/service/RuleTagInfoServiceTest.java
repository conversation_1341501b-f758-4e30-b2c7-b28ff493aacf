package com.lifekh.data.warehouse.service;

import com.chaos.common.enums.BusinessLineEnum;
import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.enums.*;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.RuleTagInfoDetailRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RuleTagInfoServiceTest {
    @Autowired
    private RuleTagInfoService ruleTagInfoService;
    @Autowired
    private TagInfoService tagInfoService;
    @Test
    public void add(){
        RuleTagInfoAddReqDTO ruleTagInfoAddReqDTO = new RuleTagInfoAddReqDTO();
        Map<String,String> map = new HashMap<>();
        map.put(LanguageEnum.ZH_CN.getCode(), "活跃用户");
        map.put(LanguageEnum.EN_US.getCode(), "active user");
        map.put(LanguageEnum.KM_KH.getCode(), "active user");
        ruleTagInfoAddReqDTO.setTagName(map);
        ruleTagInfoAddReqDTO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        ruleTagInfoAddReqDTO.setTagType(TagTypeEnum.RULE_TAG.getCode());
        ruleTagInfoAddReqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        ruleTagInfoAddReqDTO.setTagDescription("订单规则标签");
        RuleTagRuleReqDTO ruleTagRuleReqDTO = new RuleTagRuleReqDTO();
        ruleTagRuleReqDTO.setBusinessLine(BusinessLineEnum.YUMNOW.getCode());
        ruleTagRuleReqDTO.setRuleType(RuleTypeEnum.RULE_TAG.getCode());
        ruleTagRuleReqDTO.setDays(30L);
        ruleTagRuleReqDTO.setMin(1L);
        ruleTagRuleReqDTO.setMax(5L);
        ruleTagRuleReqDTO.setBehavior(BehaviorEnum.ORDER_SUCCESS.getCode());
        ruleTagRuleReqDTO.setSymbol(SymbolEnum.INTERVAL.getCode());
        ruleTagInfoAddReqDTO.setRule(Arrays.asList(ruleTagRuleReqDTO));
        ruleTagInfoService.add(ruleTagInfoAddReqDTO);
    }
    @Test
    public void edit(){
        RuleTagInfoEditReqDTO ruleTagInfoEditReqDTO = new RuleTagInfoEditReqDTO();
        Map<String,String> map = new HashMap<>();
        map.put(LanguageEnum.ZH_CN.getCode(), "普通用户");
        map.put(LanguageEnum.EN_US.getCode(), "customer user");
        map.put(LanguageEnum.KM_KH.getCode(), "tagRuleBO user");
        ruleTagInfoEditReqDTO.setTagName(map);
        ruleTagInfoEditReqDTO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        ruleTagInfoEditReqDTO.setTagType(TagTypeEnum.RULE_TAG.getCode());
        ruleTagInfoEditReqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        ruleTagInfoEditReqDTO.setTagDescription("订单规则标签1");
        ruleTagInfoEditReqDTO.setTagNo("1425396068934045697");
        RuleTagRuleReqDTO ruleTagRuleReqDTO = new RuleTagRuleReqDTO();
        ruleTagRuleReqDTO.setBusinessLine(BusinessLineEnum.TINHNOW.getCode());
        ruleTagRuleReqDTO.setRuleType(RuleTypeEnum.RULE_TAG.getCode());
        ruleTagRuleReqDTO.setDays(31L);
        ruleTagRuleReqDTO.setMin(2L);
        ruleTagRuleReqDTO.setMax(6L);
        ruleTagRuleReqDTO.setSymbol(SymbolEnum.INTERVAL.getCode());
        ruleTagRuleReqDTO.setBehavior(BehaviorEnum.ORDER_SUCCESS.getCode());
        ruleTagRuleReqDTO.setRuleNo("1425333311962394624");
        ruleTagInfoEditReqDTO.setRule(Arrays.asList(ruleTagRuleReqDTO));
        ruleTagInfoService.edit(ruleTagInfoEditReqDTO);
    }
    @Test
    public void list(){
        RuleTagInfoListReqDTO ruleTagInfoListReqDTO = new RuleTagInfoListReqDTO();
        ruleTagInfoListReqDTO.setTagClassify(null);
        ruleTagInfoListReqDTO.setTagName(null);
        ruleTagInfoListReqDTO.setTagStatus(null);
        PageInfoDTO<RuleTagInfoDetailRespDTO> pageInfoDTO = ruleTagInfoService.list(ruleTagInfoListReqDTO);
        System.out.println("结果："+pageInfoDTO.getList().size());
    }
    @Test
    public void detail(){
        RuleTagInfoDetailReqDTO reqDTO = new RuleTagInfoDetailReqDTO();
        reqDTO.setTagNo("1425396068934045697");
        RuleTagInfoDetailRespDTO respDTO = ruleTagInfoService.detail(reqDTO);
        System.out.println("结果："+respDTO);
    }
    @Test
    public void updateStatus(){
        TagInfoUpdateStatusReqDTO reqDTO = new TagInfoUpdateStatusReqDTO();
        reqDTO.setTagNo("1425333312427962369");
        reqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        tagInfoService.updateStatus(reqDTO);
    }
}
