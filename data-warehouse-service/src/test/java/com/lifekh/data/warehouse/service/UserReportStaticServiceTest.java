package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.collection.CollectHomePageViewBO;
import com.lifekh.data.warehouse.report.service.UserReportStaticService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserReportStaticServiceTest {

    @Autowired
    private UserReportStaticService userReportStaticService;

    @Test
    public void testUserLoginMethodReport() {
        userReportStaticService.userLoginMethodReport();
    }

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void test01() {
        //获取当前小时所在的时间
        Date currentHour = DateUtil.getCurrentHour();
        CollectHomePageViewBO bo = mongoTemplate.findOne(new Query(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).gte(currentHour)
                .and(CollectHomePageViewBO.DEVICE_ID).is("29D64B00873694ABD0549CE7AE2A196E69C7F85A")), CollectHomePageViewBO.class, MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
        if(bo == null) {
            mongoTemplate.insert(new CollectHomePageViewBO(null, new Date(),
                    "1331857886484918272", "29D64B00873694ABD0549CE7AE2A196E69C7F85A"), MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
        }
    }
}
