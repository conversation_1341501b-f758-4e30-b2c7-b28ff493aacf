package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.OpenScreenStaticReqDTO;
import com.lifekh.data.warehouse.api.req.PopStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.OpenScreenStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.PopStaticRespDTO;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class PopAdsStaticServiceTest extends GoServiceTest {

    @Autowired
    private PopAdsStaticService popAdsStaticService;

    @Test
    public void testQueryStaticByPopNo() {
        PopStaticReqDTO reqDTO = new PopStaticReqDTO();
        reqDTO.setPopNo("1543894185839718400");
        PopStaticRespDTO respDTO = popAdsStaticService.queryStaticByPopNo(reqDTO);
        log.info("respDTO:{}", respDTO);
    }
}
