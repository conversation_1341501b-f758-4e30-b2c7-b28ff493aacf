package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.InviteRegisterActivityNoReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

public class InviteRegisterActivityNoReportStrategyTest extends GoServiceTest{

    @Autowired
    private InviteRegisterActivityNoReportStrategy inviteRegisterActivityNoReportStrategy;

    @Test
    public void test1() {
        int dayOffset = 1;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date dataTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);

//        Date weekStartTime = DateUtil.getFirstDateOfWeek(yesterday);
//        Date weekEndTime = DateUtil.getLastDateOfWeek(yesterday);
        Date weekStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date weekEndTime = DateUtil.getEndTimeOfDate(yesterday);
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(weekStartTime);
        reportBasicReqDTO.setDataTime(dataTime);
        reportBasicReqDTO.setEndTime(weekEndTime);
        inviteRegisterActivityNoReportStrategy.statisticsByDay(reportBasicReqDTO);
    }
}
