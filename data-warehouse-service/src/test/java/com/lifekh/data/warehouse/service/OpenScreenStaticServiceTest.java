package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.OpenScreenStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.OpenScreenStaticRespDTO;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OpenScreenStaticServiceTest extends GoServiceTest {

    @Autowired
    private OpenScreenStaticService openScreenStaticService;

    @Test
    public void testQueryStaticByPopNo() {
        OpenScreenStaticReqDTO reqDTO = new OpenScreenStaticReqDTO();
        reqDTO.setAdsNo("1543781992129789952");
        OpenScreenStaticRespDTO respDTO = openScreenStaticService.queryStaticByPopNo(reqDTO);

        log.info("respDTO:{}", respDTO);
    }
}
