package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.SmsReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class SmsReportStrategyTest extends GoServiceTest {

    @Autowired
    private SmsReportStrategy smsReportStrategy;

    @Test
    public void report() {
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(DateUtil.getFirstDayDateOfMonth(new Date()));
        reportBasicReqDTO.setEndTime(new Date());
        reportBasicReqDTO.setDataTime(new Date());
        smsReportStrategy.statisticsByDay(reportBasicReqDTO);
    }

    @Test
    public void testReportChannelRate() {
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(DateUtil.getYesterdayStartDate());
        reportBasicReqDTO.setEndTime(DateUtil.getYesterdayEndTime());
        reportBasicReqDTO.setDataTime(DateUtil.getYesterdayEndTime());
        smsReportStrategy.reportChannelRate(reportBasicReqDTO);
    }
}
