package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.TagUserInfoRespDTO;
import com.lifekh.data.warehouse.bo.TagUserTempBO;
import com.mongodb.bulk.BulkWriteResult;
import com.lifekh.data.warehouse.bo.TagUserBO;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TagUserServiceTest {
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Test
    public void add(){
        TagUserAddReqDTO tagUserAddReqDTO = new TagUserAddReqDTO();
        tagUserAddReqDTO.setMobile("8550888158158");
        tagUserAddReqDTO.setNickname("zhang");
        tagUserAddReqDTO.setOperatorNo("1379710201691406336");
//        tagUserAddReqDTO.setTags(Arrays.asList("TAG"));
        tagUserService.add(tagUserAddReqDTO);
    }

    @Test
    public void selectJoinTagAndRule() {
        TagUserDTO tagUserDTO = tagUserService.selectUserLeftJoinTagInfo("1336569147477356544");
        System.out.println(tagUserDTO);
    }
    @Test
    public void list(){
        TagUserListReqDTO tagUserListReqDTO = new TagUserListReqDTO();
//        tagUserListReqDTO.setMobile("855088815815");
//        tagUserListReqDTO.setOperatorNo("137971020169140633");
//        tagUserListReqDTO.setNickname("aaaa");
        PageInfoDTO<TagUserDTO> page = tagUserService.list(tagUserListReqDTO);
        System.out.println(page.getTotal());
    }

    @Test
    public void query() {
        TagUserQueryReqDTO reqDTO = new TagUserQueryReqDTO();
        reqDTO.setOperatorNo("1422373022554943488");
        TagUserRespDTO respDTO = tagUserService.queryByOperatorNo(reqDTO);
        log.info("查询结果:{}", respDTO);
    }
    @Test
    public void tagUserImport(){
        List<TagUserTempBO> tagUserTempBOS = new ArrayList<>();
        for(int i=0;i<5;i++){
            TagUserTempBO tagUserTempBO = new TagUserTempBO();
            tagUserTempBO.setOperatorNo(String.valueOf(i));
            tagUserTempBO.setTagNo(String.valueOf(i));
            tagUserTempBO.setCreateTime(new Date());
            tagUserTempBOS.add(tagUserTempBO);
        }
//        mongoTemplate.save(tagUserTempBOS);
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, TagUserTempBO.class);
        operations.insert(tagUserTempBOS);
        BulkWriteResult result = operations.execute();
    }

    @Test
    public void testQueryConfig() throws InterruptedException {
        TimeUnit.SECONDS.sleep(60);
        long start = System.currentTimeMillis();
        //查询mongodb
        TagUserBO tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is("1422373022554943488")),TagUserBO.class, TagUserBO.TABLE_NAME);
        log.info("第一次查询使用时间：{}", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        //查询mongodb
        tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is("1422373022554943488")),TagUserBO.class, TagUserBO.TABLE_NAME);
        log.info("第二次查询使用时间：{}", System.currentTimeMillis() - start);


        start = System.currentTimeMillis();
        //查询mongodb
        tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is("1422092198263009280")),TagUserBO.class, TagUserBO.TABLE_NAME);
        log.info("第三次查询使用时间：{}", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        //查询mongodb
        tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is("1422473305300008960")),TagUserBO.class, TagUserBO.TABLE_NAME);
        log.info("第四次查询使用时间：{}", System.currentTimeMillis() - start);
    }

    @Test
    public void testQueryCount() {
        List<String> tagNos = new ArrayList<>();
        tagNos.add("1419569629508108289");
        tagNos.add("1419569629831069697");
        Long total = tagUserService.queryUserTotalByTag(tagNos);
        log.info("查询总数为：{}", total);
    }

    @Test
    public void testQueryTagUserInfo() {
        TagUserQueryByTagsReqDTO reqDTO = new TagUserQueryByTagsReqDTO();
        List<String> l = new ArrayList<>();
        l.add("1419569629860429825");
        reqDTO.setTagNos(l);
        reqDTO.setPageNum(50001);
        PageInfoDTO<TagUserInfoRespDTO> list = tagUserService.queryTagUserInfo(reqDTO);
        log.info("查询结果:{}", list);
    }

    @Test
    public void testTagUser(){
        TagUserListReqDTO reqDTO = new TagUserListReqDTO();
        reqDTO.setPageNum(1);
        reqDTO.setPageSize(500);
        //reqDTO.setTagNo("1419569630149836802");
        System.out.println("开始时间"+new Date().getTime());
        PageInfoDTO<TagUserDTO> tagUserDTOPageInfoDTO = tagUserService.list(reqDTO);
        System.out.println("结束时间"+new Date().getTime());
        System.out.println("222");
    }

    @Test
    public void testGetuserInfoByGroupNo(){
        UserTagGroupsReqDTO userTagGroupsReqDTO = new UserTagGroupsReqDTO();
        List<String> groupNos = new ArrayList<>();
        groupNos.add("1447400661202202624");
        groupNos.add("1447409531161112576");
        groupNos.add("1447439766082514944");
        userTagGroupsReqDTO.setGroupNos(groupNos);
        tagUserService.queryTagUserInfoByTagGroup(userTagGroupsReqDTO);
    }

    @Test
    public void testGetuserInfo(){
        List<String> tagNos = new ArrayList<>();
        //["1419569630149836803","1431103132612165632"]
        tagNos.add("1419569630149836803");
        tagNos.add("1426014249909018624");
        Query query = new Query(Criteria.where("tagNo").all(tagNos));
        List<TagUserInfoRespDTO> tagUserList = mongoTemplate.find(query, TagUserInfoRespDTO.class, TagUserBO.TABLE_NAME);
        System.out.println("22");
    }

    @Test
    public void testUserAndGroup(){
        OperatorNoAndGroupNoReqDTO operatorNoAndGroupNoReqDTO = new OperatorNoAndGroupNoReqDTO();
        operatorNoAndGroupNoReqDTO.setOperatorNo("1352518161645809664");
        operatorNoAndGroupNoReqDTO.setGroupNo("1447409531161112576");
        tagUserService.checkUserOfGroup(operatorNoAndGroupNoReqDTO);
    }
}
