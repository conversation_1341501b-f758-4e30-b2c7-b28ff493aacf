package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.oracle.bo.OracleUserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.report.RebackAndSilentUserReportJob;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class RebackAndSilentUserReportJobTest extends GoServiceTest {

    @Autowired
    private RebackAndSilentUserReportJob job;

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Test
    public void test(){
        job.execute(null);
    }

    @Test
    public void TestQueryUserByOperatorNo() {
        OracleUserOperatorLoginInfoBO bo = oracleUserOperatorLoginInfoDAO.queryUserByOperatorNoAndTime(new Date(), "xxx");
        log.info("查询结果：{}", bo);
    }
}
