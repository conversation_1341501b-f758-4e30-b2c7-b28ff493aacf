package com.lifekh.data.warehouse.service;

import com.chaos.common.enums.AppIdEnum;
import com.lifekh.data.warehouse.api.UserDataStaticFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.req.ActiveUserStaticReqDTO;
import com.lifekh.data.warehouse.api.req.ReportStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserReportStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserStickyRespDTO;
import com.lifekh.data.warehouse.api.resp.NewUserReportStaticRespDTO;
import com.lifekh.data.warehouse.bo.collection.RemainUserAggregationBO;
import com.lifekh.data.warehouse.report.service.MerchantAdsPublishStaticService;
import com.lifekh.data.warehouse.report.service.MerchantAdsPublishStaticServiceImpl;
import com.lifekh.data.warehouse.report.service.UserReportStaticService;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/9/27 17:44
 * @Version 1.0
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class CollecationTest {
    @Autowired
    UserReportStaticService userReportStaticService;

    @Autowired
    UserDataStaticFacade userDataStaticFacade;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void testNewUserReport(){
        ReportStaticReqDTO reportStaticReqDTO = new ReportStaticReqDTO();
        reportStaticReqDTO.setStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,-10));
        //reportStaticReqDTO.setEndStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        userReportStaticService.newUserReportStatic();
    }

    @Test
    public void testActiveUserReport(){
        userReportStaticService.activeUserReportStatic();
    }


    @Test
    public void testNewUserReportSearch(){
        ReportStaticReqDTO reportStaticReqDTO = new ReportStaticReqDTO();
        reportStaticReqDTO.setStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,-10));
       // reportStaticReqDTO.setEndStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        userDataStaticFacade.getNewUserReportStatic(reportStaticReqDTO);
    }


    @Test
    public void testActiveUserReportSearch(){
        ActiveUserStaticReqDTO reportStaticReqDTO = new ActiveUserStaticReqDTO();
        reportStaticReqDTO.setStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,-7));
        reportStaticReqDTO.setEndDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        reportStaticReqDTO.setStaticType(12);
        List<ActiveUserReportStaticRespDTO> activeUserReportStaticRespDTOS = userDataStaticFacade.getActiveUserReportStatic(reportStaticReqDTO);
        System.out.println();
    }

    @Test
    public void testSticky(){

        int t = DateUtil.getOneDatePlus(new Date());
        ReportStaticReqDTO reportStaticReqDTO = new ReportStaticReqDTO();
        reportStaticReqDTO.setStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,-50));
        reportStaticReqDTO.setEndDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        List<ActiveUserStickyRespDTO> activeUserStickyRespDTOS = userDataStaticFacade.getActiveUserStickyStatic(reportStaticReqDTO);
        System.out.println();
    }

    @Test
    public void testMongoDb(){
        //A表查询条件
        Criteria criteria = new Criteria();
        criteria.and("CREATE_TIME").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1))
                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        criteria.and("APP_ID").is(AppIdEnum.SUPER_APP.getCode());
        //结果筛选条件，result 必须与lookup的as的字符串相同
        Criteria resultCriteria = new Criteria();
        resultCriteria.and("result.UPDATE_TIME").gte(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1))
                .lt(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup(MongoDbCollectonName.SUP_MOBILE_TOKEN,"OPERATOR_NO","OPERATOR_NO","result"),
                Aggregation.match(resultCriteria),
                Aggregation.count().as("count")
        );
        AggregationResults<RemainUserAggregationBO> aggregate = mongoTemplate.aggregate(
                aggregationCount,MongoDbCollectonName.USER_OPERATOR_INFO,RemainUserAggregationBO.class
        );
        RemainUserAggregationBO remainUserAggregationBO = aggregate.getUniqueMappedResult();
        System.out.println(remainUserAggregationBO.getCount());
    }

    @Test
    public void testUserMain(){
        Date tt = DateUtil.getFirstDayDateOfMonth(new Date());
        userReportStaticService.userRemainReportStatic();
    }

    @Test
    public void testBossUserMain(){
        ReportStaticReqDTO reportStaticReqDTO = new ReportStaticReqDTO();
        reportStaticReqDTO.setStartDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,-5));
        reportStaticReqDTO.setEndDate(DateUtil.dateCalculation(new Date(), Calendar.DATE,0));
        userDataStaticFacade.getUserRemainStatic(reportStaticReqDTO);
    }

    @Test
    public void mongoIndex(){
        String collectName = MongoDbCollectonName.COLLECT_BURIED_POINT_CLICK + "_" + DateUtil.getLocalYearAndMonth();
        IndexOperations indexs  = mongoTemplate.indexOps(collectName);
        List<IndexInfo> indexInfos = indexs.getIndexInfo();
        if (Objects.isNull(indexInfos) || indexInfos.size() == 0) {
            Index index = new Index();
            index.on("deviceInfoBo.deviceId",Sort.Direction.ASC);
            index.on("currentPage",Sort.Direction.ASC);
            index.on("userInfoBo.operatorNo",Sort.Direction.ASC);
            index.on("createTime",Sort.Direction.DESC);
            indexs.ensureIndex(index);
        }
        System.out.println(333);
    }

    @Autowired
    MerchantAdsPublishStaticServiceImpl merchantAdsPublishStaticService;

    @Test
    public void staticAdsPublish(){
        Date startDate = DateUtil.getYesterdayStartDate();
        Date endDate = DateUtil.getYesterdayEndTime();
        //merchantAdsPublishStaticService.countUserClickNum(startDate,endDate,"1450720768466436092");
        //merchantAdsPublishStaticService.countClickNum(startDate,endDate,"1450720768466436092");
       // merchantAdsPublishStaticService.countPageViewNum(startDate,endDate);
        //merchantAdsPublishStaticService.countPageViewUserNum(startDate,endDate);
        merchantAdsPublishStaticService.staticMerchantAdsPublish();
    }
}
