package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.LanguageReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

@Slf4j
public class LanguageReportStrategyTest extends GoServiceTest {

    @Autowired
    private LanguageReportStrategy languageReportStrategy;

    @Test
    public void testAggregateQueryOracle() {
        int dayOffset = 130;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date yesterdayStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date yesterdayEndTime = DateUtil.getEndTimeOfDate(new Date());
        languageReportStrategy.aggregateQueryOracle(new ReportBasicReqDTO(yesterdayStartTime, yesterdayEndTime, yesterday));
    }

    @Test
    public void testStatisticsBusinessActiveUser() {
        languageReportStrategy.statisticsBusinessActiveUser();
    }
}
