package com.lifekh.data.warehouse.service;

import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.TagInfoFacade;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.req.ClassificationTagReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoFirstClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoRespDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.enums.TagClassifyEnum;
import com.lifekh.data.warehouse.api.enums.TagStatusEnum;
import com.lifekh.data.warehouse.api.enums.TagTypeEnum;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.TagDetailQueryRespDTO;
import com.lifekh.data.warehouse.api.resp.TagInfoHasUserRespDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.bo.TagRuleBO;
import com.lifekh.data.warehouse.dao.TagRuleDAO;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TagInfoServiceTest {

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private TagRuleDAO tagRuleDAO;
    @Autowired
    private TagInfoFacade tagInfoFacade;



    @Test
    public void testSaveTag() {
        TagAddReqDTO tagInfoBO = new TagAddReqDTO();

        Map<String, String> map = new HashMap<>();
        map.put("beta", "内测");
        tagInfoBO.setTagName(map);
        tagInfoBO.setTagType(TagTypeEnum.BASIC_TAG.getCode());
        tagInfoBO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        tagInfoBO.setTagStatus(TagStatusEnum.OPEN.getCode());
        tagInfoBO.setTagDescription("内测");
//        tagInfoBO.setRuleNos(Arrays.asList("1417688767101005824"));
//        tagInfoService.saveTag(tagInfoBO);
    }

    @Test
    public void test() {
        TagInfoBO tagInfoBO = tagInfoService.findFirstByCondition("zone", "855120000", 1);
        log.info("tagInfoBO:{}", tagInfoBO);
    }

    @Test
    public void testSaveZoneTag() {

        List<TagRuleBO> list =  tagRuleDAO.findAll();

        list.forEach(bo -> {

            TagAddReqDTO tagInfoBO = new TagAddReqDTO();

            Map<String, String> map = new HashMap<>();
            map.put("zh-CN", bo.getRuleName());
            tagInfoBO.setTagName(map);
            tagInfoBO.setTagType(TagTypeEnum.BASIC_TAG.getCode());
            tagInfoBO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
            tagInfoBO.setTagStatus(TagStatusEnum.OPEN.getCode());
            tagInfoBO.setTagDescription(bo.getRuleName());
//            tagInfoBO.setRuleNos(Arrays.asList(bo.getRuleNo()));
//            tagInfoService.saveTag(tagInfoBO);
        });

    }

    @Test
    public void testFindList() {
        TagListQueryReqDTO reqDTO = new TagListQueryReqDTO();
//        reqDTO.setTagNo("TAG1");
//        reqDTO.setTagName("zhang");
        PageInfoDTO<TagInfoDTO> response =  tagInfoService.findList(reqDTO);
        log.info("结果：{}", response.getList());
    }

    @Test
    public void detail(){
        TagDetailQueryReqDTO tagDetailQueryReqDTO = new TagDetailQueryReqDTO();
        tagDetailQueryReqDTO.setTagNo("TAG1");
        TagDetailQueryRespDTO tagDetailQueryRespDTO = tagInfoService.detail(tagDetailQueryReqDTO);
        log.info("结果：{}",tagDetailQueryRespDTO);
    }
    @Test
    public void editTag(){
        TagEditReqDTO tagEditReqDTO = new TagEditReqDTO();
        tagEditReqDTO.setId("60f12f82a907d50460772004");
        Map<String, String> map = new HashMap<>();
        map.put("km-kh", "柬文");
        map.put("zh-cn", "zhongwen");
        tagEditReqDTO.setTagName(map);
        tagEditReqDTO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        tagEditReqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        tagInfoService.editTag(tagEditReqDTO);
    }

    @Test
    public void deleteTag(){
        TagDeleteReqDTO tagDeleteReqDTO = new TagDeleteReqDTO();
        tagDeleteReqDTO.setId("60f12f82a907d50460772004");
        tagInfoService.deleteTag(tagDeleteReqDTO);
    }
    @Test
    public void test1(){
        TagInfoBO tagInfoBO = tagInfoService.findFirstByCondition(RuleTypeEnum.LANGUAGE_TAG.getCode(),"zh-CN",1);
        System.out.println(tagInfoBO);
    }
    @Test
    public void list(){
        TagListQueryReqDTO tagListQueryReqDTO = new TagListQueryReqDTO();
        tagListQueryReqDTO.setLanguage(LanguageEnum.ZH_CN.getCode());
        tagListQueryReqDTO.setTagName("语言");
        tagListQueryReqDTO.setTagNo("1419569630149836801");
        tagListQueryReqDTO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        tagListQueryReqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        tagListQueryReqDTO.setTagType(TagTypeEnum.BASIC_TAG.getCode());
        PageInfoDTO<TagInfoDTO> pageInfoDTO = tagInfoService.findList(tagListQueryReqDTO);
        System.out.println(pageInfoDTO.getTotal());
    }
    @Test
    public void listForHasUser(){
        TagInfoHasUserReqDTO reqDTO = new TagInfoHasUserReqDTO();
        PageInfoDTO<TagInfoHasUserRespDTO> pageInfoDTO = tagInfoFacade.listForHasUser(reqDTO);
        List<TagInfoHasUserRespDTO> list = pageInfoDTO.getList();
        list.forEach(System.out::println);
    }

    @Test
    public void testQueryTagByTagNos() {
        List<String> tagNos = new ArrayList<>();
        tagNos.add("1419569630149836801");
        tagNos.add("1419569629885595649");
        tagNos.add("1419569629860429825");
        tagNos.add("1419569629600382977");
        tagNos.add("1419569629805903873");
        List<TagInfoRespDTO> list = tagInfoService.queryTagByTagNos(tagNos);
        log.info("查询结果:{}", list);
    }
    @Test
    public void add(){
        TagAddReqDTO tagAddReqDTO = new TagAddReqDTO();
        tagAddReqDTO.setRuleNo("1419936837561659396");
        tagAddReqDTO.setTagClassify(TagClassifyEnum.PUBLIC.getCode());
        tagAddReqDTO.setTagDescription("普通用户");
        Map<String, String> map = new HashMap<>();
        map.put(LanguageEnum.ZH_CN.getCode(),"普通用户");
        map.put(LanguageEnum.EN_US.getCode(),"普通用户");
        map.put(LanguageEnum.KM_KH.getCode(),"普通用户");
        tagAddReqDTO.setTagName(map);
        tagAddReqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        tagAddReqDTO.setTagType(TagTypeEnum.BASIC_TAG.getCode());
        tagInfoFacade.add(tagAddReqDTO);

    }

    @Test
    public void edit(){
        TagEditReqDTO tagEditReqDTO = new TagEditReqDTO();
        tagEditReqDTO.setTagStatus(TagStatusEnum.CLOSE.getCode());
        tagEditReqDTO.setTagClassify(TagClassifyEnum.YUMNOW.getCode());
        Map<String, String> map = new HashMap<>();
        map.put(LanguageEnum.ZH_CN.getCode(),"普通用户1");
        map.put(LanguageEnum.EN_US.getCode(),"普通用户1");
        map.put(LanguageEnum.KM_KH.getCode(),"普通用户1");
        tagEditReqDTO.setTagName(map);
        tagEditReqDTO.setTagNo("1425368754801709056");
        tagEditReqDTO.setRuleNo("1419936837561659396");
        tagEditReqDTO.setTagDescription("普通用户1");
        tagEditReqDTO.setTagType(TagTypeEnum.BASIC_TAG.getCode());
        tagInfoFacade.edit(tagEditReqDTO);
    }
    @Test
    public void updateStatus(){
        TagInfoUpdateStatusReqDTO reqDTO = new TagInfoUpdateStatusReqDTO();
        reqDTO.setTagStatus(TagStatusEnum.CLOSE.getCode());
        reqDTO.setTagNo("1425368754801709056");
        tagInfoFacade.updateStatus(reqDTO);
    }

    @Test
    public void testQueryClassificationTagByTagName(){
        ClassificationTagReqDTO classificationTagReqDTO = new ClassificationTagReqDTO();
        List<TagInfoFirstClassificationRespDTO> tagInfoFirstClassificationRespDTOS = tagInfoService.queryClassificationTagByTagName(classificationTagReqDTO);
        System.out.println("222");
    }
}
