package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.EventPageClassifyEnum;
import com.lifekh.data.warehouse.bo.ClickReportDayBO;
import com.lifekh.data.warehouse.bo.ReportHomePageViewBO;
import com.lifekh.data.warehouse.bo.collection.EventPageBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.dto.DeviceDTO;
import com.lifekh.data.warehouse.report.service.PageViewReportService;
import com.lifekh.data.warehouse.report.service.UserViewReportService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static java.util.stream.Collectors.toList;

@Slf4j
public class PageViewReportServiceTest extends GoServiceTest {

    @Autowired
    private PageViewReportService pageViewReportService;

    @Autowired
    private UserViewReportService userViewReportService;

    @Test
    public void testCountThePVofTheCurrentPage() {
        pageViewReportService.countThePVofTheCurrentPage();
        //userViewReportService.countTheUVofTheCurrentPage();
    }

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void testNewDeviceConversionRate() {
        pageViewReportService.newDeviceConversionRate();
    }
}
