package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.oracle.bo.OracleAggregateOrderBO;
import com.lifekh.data.warehouse.oracle.dao.OracleAggregateOrderDAO;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import lombok.extern.slf4j.Slf4j;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DeleteNewUserTagConsumer 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class DeleteNewUserTagConsumerTest {

    @InjectMocks
    private DeleteNewUserTagConsumer deleteNewUserTagConsumer;

    @Mock
    private OracleAggregateOrderDAO oracleAggregateOrderDAO;

    @Mock
    private TagInfoService tagInfoService;

    @Mock
    private TagUserService tagUserService;

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    private static final String TEST_ORDER_NO = "TEST_ORDER_123456";
    private static final String TEST_USER_ID = "TEST_USER_789";
    private static final String TEST_BUSINESS_LINE = "YUMNOW";
    private static final String TEST_TAG_NO = "TAG_123456789";



    /**
     * 测试添加首单标签 - 正常流程
     */
    @Test
    public void testAddFirstOrderTag_Success() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();
        TagInfoBO mockTagInfo = createMockTagInfo();
        TagUserRespDTO mockTagUser = null; // 用户没有标签

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(mockTagInfo);
        when(tagUserService.queryByOperatorNo(any(TagUserQueryReqDTO.class))).thenReturn(mockTagUser);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService).findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1);
        verify(tagUserService).queryByOperatorNo(any(TagUserQueryReqDTO.class));

        // 验证发送MQ消息
        ArgumentCaptor<UpdateUserTagMQReqDTO> mqCaptor = ArgumentCaptor.forClass(UpdateUserTagMQReqDTO.class);
        verify(rocketMQTemplate).syncSend(eq(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC), mqCaptor.capture());

        UpdateUserTagMQReqDTO capturedMQ = mqCaptor.getValue();
        assertNotNull(capturedMQ);
        assertNotNull(capturedMQ.getList());
        assertEquals(1, capturedMQ.getList().size());

        UpdateUserTagDTO updateUserTagDTO = capturedMQ.getList().get(0);
        assertEquals(OptTypeEnum.ADD.getCode(), updateUserTagDTO.getOptType());
        assertEquals(TEST_USER_ID, updateUserTagDTO.getOperatorNo());
        assertEquals(TEST_TAG_NO, updateUserTagDTO.getTagNo());
    }

    /**
     * 测试添加首单标签 - 订单为空
     */
    @Test
    public void testAddFirstOrderTag_OrderIsNull() {
        // 设置Mock行为 - 返回null订单
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(null);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证只调用了查询订单，其他方法不应该被调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService, never()).findFirstByCondition(any(), any(), any());
        verify(tagUserService, never()).queryByOperatorNo(any());
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 测试添加首单标签 - 业务线为空
     */
    @Test
    public void testAddFirstOrderTag_BusinessLineIsBlank() {
        // 准备测试数据 - 业务线为空
        OracleAggregateOrderBO mockOrder = createMockOrder();
        mockOrder.setBusinessLine(""); // 设置为空字符串

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService, never()).findFirstByCondition(any(), any(), any());
        verify(tagUserService, never()).queryByOperatorNo(any());
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 测试添加首单标签 - 标签信息为空
     */
    @Test
    public void testAddFirstOrderTag_TagInfoIsNull() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();

        // 设置Mock行为 - 标签信息返回null
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(null);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService).findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1);
        verify(tagUserService, never()).queryByOperatorNo(any());
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 测试添加首单标签 - 用户已有该标签
     */
    @Test
    public void testAddFirstOrderTag_UserAlreadyHasTag() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();
        TagInfoBO mockTagInfo = createMockTagInfo();
        TagUserRespDTO mockTagUser = createMockTagUserWithTag(); // 用户已有标签

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(mockTagInfo);
        when(tagUserService.queryByOperatorNo(any(TagUserQueryReqDTO.class))).thenReturn(mockTagUser);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService).findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1);
        verify(tagUserService).queryByOperatorNo(any(TagUserQueryReqDTO.class));

        // 验证不发送MQ消息（因为用户已有标签）
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 测试添加首单标签 - 用户标签列表为空但用户存在
     */
    @Test
    public void testAddFirstOrderTag_UserExistsButNoTags() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();
        TagInfoBO mockTagInfo = createMockTagInfo();
        TagUserRespDTO mockTagUser = createMockTagUserWithoutTag(); // 用户存在但没有标签

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(mockTagInfo);
        when(tagUserService.queryByOperatorNo(any(TagUserQueryReqDTO.class))).thenReturn(mockTagUser);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService).findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1);
        verify(tagUserService).queryByOperatorNo(any(TagUserQueryReqDTO.class));

        // 验证发送MQ消息
        ArgumentCaptor<UpdateUserTagMQReqDTO> mqCaptor = ArgumentCaptor.forClass(UpdateUserTagMQReqDTO.class);
        verify(rocketMQTemplate).syncSend(eq(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC), mqCaptor.capture());

        UpdateUserTagMQReqDTO capturedMQ = mqCaptor.getValue();
        assertNotNull(capturedMQ);
        assertNotNull(capturedMQ.getList());
        assertEquals(1, capturedMQ.getList().size());

        UpdateUserTagDTO updateUserTagDTO = capturedMQ.getList().get(0);
        assertEquals(OptTypeEnum.ADD.getCode(), updateUserTagDTO.getOptType());
        assertEquals(TEST_USER_ID, updateUserTagDTO.getOperatorNo());
        assertEquals(TEST_TAG_NO, updateUserTagDTO.getTagNo());
    }

    /**
     * 测试添加首单标签 - 异常处理
     */
    @Test
    public void testAddFirstOrderTag_ExceptionHandling() {
        // 设置Mock行为 - 抛出异常
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO))
                .thenThrow(new RuntimeException("Database connection error"));

        // 执行测试方法 - 应该不抛出异常（因为有try-catch）
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用了查询方法
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        // 其他方法不应该被调用
        verify(tagInfoService, never()).findFirstByCondition(any(), any(), any());
        verify(tagUserService, never()).queryByOperatorNo(any());
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 创建模拟订单对象
     */
    private OracleAggregateOrderBO createMockOrder() {
        OracleAggregateOrderBO order = new OracleAggregateOrderBO();
        order.setUserId(TEST_USER_ID);
        order.setBusinessLine(TEST_BUSINESS_LINE);
        order.setAggregateOrderNo(TEST_ORDER_NO);
        return order;
    }

    /**
     * 创建模拟标签信息对象
     */
    private TagInfoBO createMockTagInfo() {
        TagInfoBO tagInfo = new TagInfoBO();
        tagInfo.setTagNo(TEST_TAG_NO);
        Map<String, String> tagNameMap = new HashMap<>();
        tagNameMap.put("zh-CN", "首单业务线标签");
        tagInfo.setTagName(tagNameMap);
        return tagInfo;
    }

    /**
     * 创建模拟用户标签对象（已有标签）
     */
    private TagUserRespDTO createMockTagUserWithTag() {
        TagUserRespDTO tagUser = new TagUserRespDTO();
        tagUser.setOperatorNo(TEST_USER_ID);
        tagUser.setTagNo(Arrays.asList(TEST_TAG_NO, "OTHER_TAG"));
        return tagUser;
    }

    /**
     * 创建模拟用户标签对象（没有标签）
     */
    private TagUserRespDTO createMockTagUserWithoutTag() {
        TagUserRespDTO tagUser = new TagUserRespDTO();
        tagUser.setOperatorNo(TEST_USER_ID);
        tagUser.setTagNo(null); // 没有标签
        return tagUser;
    }

    /**
     * 测试添加首单标签 - 用户有其他标签但没有目标标签
     */
    @Test
    public void testAddFirstOrderTag_UserHasOtherTagsButNotTarget() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();
        TagInfoBO mockTagInfo = createMockTagInfo();
        TagUserRespDTO mockTagUser = createMockTagUserWithOtherTags(); // 用户有其他标签但没有目标标签

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(mockTagInfo);
        when(tagUserService.queryByOperatorNo(any(TagUserQueryReqDTO.class))).thenReturn(mockTagUser);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService).findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1);
        verify(tagUserService).queryByOperatorNo(any(TagUserQueryReqDTO.class));

        // 验证发送MQ消息（因为用户没有目标标签）
        ArgumentCaptor<UpdateUserTagMQReqDTO> mqCaptor = ArgumentCaptor.forClass(UpdateUserTagMQReqDTO.class);
        verify(rocketMQTemplate).syncSend(eq(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC), mqCaptor.capture());

        UpdateUserTagMQReqDTO capturedMQ = mqCaptor.getValue();
        assertNotNull(capturedMQ);
        assertNotNull(capturedMQ.getList());
        assertEquals(1, capturedMQ.getList().size());

        UpdateUserTagDTO updateUserTagDTO = capturedMQ.getList().get(0);
        assertEquals(OptTypeEnum.ADD.getCode(), updateUserTagDTO.getOptType());
        assertEquals(TEST_USER_ID, updateUserTagDTO.getOperatorNo());
        assertEquals(TEST_TAG_NO, updateUserTagDTO.getTagNo());
    }

    /**
     * 测试添加首单标签 - 业务线为null
     */
    @Test
    public void testAddFirstOrderTag_BusinessLineIsNull() {
        // 准备测试数据 - 业务线为null
        OracleAggregateOrderBO mockOrder = createMockOrder();
        mockOrder.setBusinessLine(null); // 设置为null

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证调用
        verify(oracleAggregateOrderDAO).queryUserFirstOrderByOrderNo(TEST_ORDER_NO);
        verify(tagInfoService, never()).findFirstByCondition(any(), any(), any());
        verify(tagUserService, never()).queryByOperatorNo(any());
        verify(rocketMQTemplate, never()).syncSend(any(), any());
    }

    /**
     * 测试验证TagUserQueryReqDTO的参数设置
     */
    @Test
    public void testAddFirstOrderTag_VerifyTagUserQueryReqDTO() {
        // 准备测试数据
        OracleAggregateOrderBO mockOrder = createMockOrder();
        TagInfoBO mockTagInfo = createMockTagInfo();
        TagUserRespDTO mockTagUser = null;

        // 设置Mock行为
        when(oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(TEST_ORDER_NO)).thenReturn(mockOrder);
        when(tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), TEST_BUSINESS_LINE, 1))
                .thenReturn(mockTagInfo);
        when(tagUserService.queryByOperatorNo(any(TagUserQueryReqDTO.class))).thenReturn(mockTagUser);

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(deleteNewUserTagConsumer, "addFirstOrderTag", TEST_ORDER_NO);

        // 验证TagUserQueryReqDTO的参数
        ArgumentCaptor<TagUserQueryReqDTO> queryCaptor = ArgumentCaptor.forClass(TagUserQueryReqDTO.class);
        verify(tagUserService).queryByOperatorNo(queryCaptor.capture());

        TagUserQueryReqDTO capturedQuery = queryCaptor.getValue();
        assertNotNull(capturedQuery);
        assertEquals(TEST_USER_ID, capturedQuery.getOperatorNo());
    }

    /**
     * 创建模拟用户标签对象（有其他标签但没有目标标签）
     */
    private TagUserRespDTO createMockTagUserWithOtherTags() {
        TagUserRespDTO tagUser = new TagUserRespDTO();
        tagUser.setOperatorNo(TEST_USER_ID);
        tagUser.setTagNo(Arrays.asList("OTHER_TAG_1", "OTHER_TAG_2")); // 有其他标签但没有目标标签
        return tagUser;
    }
}
