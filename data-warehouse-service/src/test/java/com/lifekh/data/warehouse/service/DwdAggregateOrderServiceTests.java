package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.report.service.DwdAggregateOrderService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DwdAggregateOrderServiceTests extends GoServiceTest {

    @Autowired
    private DwdAggregateOrderService dwdAggregateOrderService;

    @Test
    public void testStaticOrderReport() {
        dwdAggregateOrderService.staticOrderReport();
    }

    @Test
    public void testStaticYumNowStoreOrderRateReport() {
        dwdAggregateOrderService.staticYumNowStoreOrderRateReport(ReportDateDTO.yesterday());
    }
}
