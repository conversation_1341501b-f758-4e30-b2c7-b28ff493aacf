package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.oracle.bo.OracleZoneBO;
import com.lifekh.data.warehouse.oracle.dao.OracleZoneDAO;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OracleZoneDAOTest extends GoServiceTest {

    @Autowired
    private OracleZoneDAO oracleZoneDAO;

    @Test
    public void test1(){
        OracleZoneBO zone = oracleZoneDAO.queryZone("金边", 11);

        OracleZoneBO zoneBO2 = oracleZoneDAO.queryZoneByParent("桑", zone.getCode());
        log.info("查询结果：{},{}", zone, zoneBO2);
    }
}
