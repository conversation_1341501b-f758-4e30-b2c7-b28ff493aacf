package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.SearchHotWordReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SearchHotWordReportStrategyTest {

    @Autowired
    private SearchHotWordReportStrategy searchHotWordReportStrategy;

    @Test
    public void testReportChannelRate() {
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(DateUtil.getYesterdayStartDate());
        reportBasicReqDTO.setEndTime(DateUtil.getYesterdayEndTime());
        reportBasicReqDTO.setDataTime(DateUtil.getYesterdayEndTime());
        searchHotWordReportStrategy.statisticsByDay(reportBasicReqDTO);
    }
}
