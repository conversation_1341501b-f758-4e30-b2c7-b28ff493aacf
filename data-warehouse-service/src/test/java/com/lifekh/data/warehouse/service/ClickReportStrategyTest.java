package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.ClickReportStrategy;
import com.lifekh.data.warehouse.report.service.ClickTwoLayerReportStrategy;
import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

public class ClickReportStrategyTest extends GoServiceTest {

    @Autowired
    private ClickReportStrategy clickReportStrategy;

    @Autowired
    private ClickTwoLayerReportStrategy clickTwoLayerReportStrategy;

    @Test
    public void testStatistics0() {
        clickReportStrategy.statistics(0,0);
        clickTwoLayerReportStrategy.statistics(0,0);
    }

    @Test
    public void testStatistics() {
        clickReportStrategy.statistics(0,0);
    }

    @Test
    public void testStatistics2() {
        clickTwoLayerReportStrategy.statistics(0,0);
    }

    @Test
    public void homePageSearchClickStatistics() {
        
    }

    @Test
    public void test() {
//        System.out.println("xxxxx" + DateUtil.getFirstDateOfWeekFormat(new Date()));

//        int monthOffset = DateUtil.getOneDatePlus(new Date());
        Date date = DateUtil.dateCalculation(new Date(), Calendar.DATE,-1);
        Date monthStartTime = DateUtil.getLastDateOfWeek(date);

        Date startDate = DateUtil.getStartTimeOfDate(date);
        Date endDate = DateUtil.getEndTimeOfDate(date);
        System.out.println("dddddd"+ monthStartTime);
    }

    @Test
    public void testDay() {
        int dayOffset = 1;
        Date yesterday = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -dayOffset);
        Date dataTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -1);

//        Date weekStartTime = DateUtil.getFirstDateOfWeek(yesterday);
//        Date weekEndTime = DateUtil.getLastDateOfWeek(yesterday);
        Date weekStartTime = DateUtil.getStartTimeOfDate(yesterday);
        Date weekEndTime = DateUtil.getEndTimeOfDate(yesterday);
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(weekStartTime);
        reportBasicReqDTO.setDataTime(weekStartTime);
        reportBasicReqDTO.setEndTime(weekEndTime);
        clickReportStrategy.statisticsByDay(reportBasicReqDTO);
    }

    @Autowired
    private TargetReportService targetReportService;
    @Test
    public void test2() {
        targetReportService.staticTargetData();
    }
}
