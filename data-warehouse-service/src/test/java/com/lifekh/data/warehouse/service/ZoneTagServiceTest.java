package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.TagUserAddReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.outstanding.framework.junit.GoServiceTest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

public class ZoneTagServiceTest extends GoServiceTest {
    @Autowired
    private ZoneTagService zoneTagService;


    private void insertZoneTag(List<UpdateUserTagDTO> updateList, String operatorNo, String tagNo) {
        //新增用户标签
        TagUserAddReqDTO reqDTO = new TagUserAddReqDTO();
        reqDTO.setOperatorNo(operatorNo);
//        tagUserService.add(reqDTO);

        //构造新增队列
        updateList.add(new UpdateUserTagDTO(operatorNo, tagNo, OptTypeEnum.ADD.getCode(),null,null));
    }

    /**
     * 构造更新标签请求参数
     *
     * @param updateList   用户标签更新请求参数
     * @param operatorNo   操作员编号
     * @param tagUserDTO   用户已贴标签对象
     * @param tagNo         新标签编号
     */
    private void updateZoneTag(List<UpdateUserTagDTO> updateList, String operatorNo, TagUserDTO tagUserDTO, String tagNo) {
        //用户未添加过标签
        if(CollectionUtils.isEmpty(tagUserDTO.getTagInfo())) {
            updateList.add(new UpdateUserTagDTO(operatorNo, tagNo, OptTypeEnum.ADD.getCode(),null,null));
            return;
        }

        //遍历用户是否已经添加过地区标签
        for(TagInfoDTO tagInfoDTO : tagUserDTO.getTagInfo()) {
            if(Objects.nonNull(tagInfoDTO)) {
                for(TagRuleDTO ruleDTO : tagInfoDTO.getRule()) {
                    //用户已添加过地区标签
                    if(Objects.nonNull(ruleDTO) && RuleTypeEnum.ZONE_TAG.getCode().equals(ruleDTO.getRuleType())) {
                        //如果新标签编号和旧标签编号相同则不更新
                        if(tagInfoDTO.getTagNo().equals(tagNo)) {
                            return;
                        }

                        //删除旧标签
                        updateList.add(new UpdateUserTagDTO(operatorNo, tagInfoDTO.getTagNo(), OptTypeEnum.DEL.getCode(),null,null));

                        //新增新标签
                        updateList.add(new UpdateUserTagDTO(operatorNo, tagNo, OptTypeEnum.ADD.getCode(),null,null));
                        return;
                    }
                }
            }
        }

        //用户未添加过地区标签
        updateList.add(new UpdateUserTagDTO(operatorNo, tagNo, OptTypeEnum.ADD.getCode(),null,null));
    }

}
