package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.RuleDataQueryReqDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.TagRuleAddReqDTO;
import com.lifekh.data.warehouse.dao.TagRuleDAO;
import com.outstanding.framework.base.sequence.SequenceNo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TagRuleServiceTest {
    @Autowired
    private TagRuleService tagRuleService;
    @Autowired
    private SequenceNo sequenceNo;
    @Autowired
    private TagRuleDAO tagRuleDAO;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Test
    public void add(){
        TagRuleAddReqDTO reqDTO = new TagRuleAddReqDTO();
        reqDTO.setRuleName(RuleTypeEnum.LANGUAGE_TAG.getCode());
        reqDTO.setRuleNo(sequenceNo.nextSeq());
        reqDTO.setRuleType(RuleTypeEnum.LANGUAGE_TAG.getCode());
        reqDTO.setRuleValue(RuleTypeEnum.LANGUAGE_TAG.getCode());
        tagRuleService.add(reqDTO);

    }
    @Test
    public void detail(){
        Criteria criteria = new Criteria();
        criteria.and("ruleValue").is("beta");
        criteria.and("ruleType").is("beta");
        //联合查询总条数，分页用
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup("tag_info","ruleNo","ruleNos","tagInfo")

        );
        AggregationResults<TagRuleDTO> aggregate = mongoTemplate.aggregate(
                aggregationCount ,"tag_rule",TagRuleDTO.class//A表，是查询的主表
        );
        List<TagRuleDTO> tagRuleBOS =  aggregate.getMappedResults();
        System.out.println(111);
    }
    @Test
    public void selectTagUserJoinTagInfo(){
        Criteria criteria = new Criteria();
        criteria.and("operatorNo").is("1413325721820360704");
        //联合查询总条数，分页用
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup("tag_info","tagNo","tagNo","tagInfo")
//                Aggregation.unwind("tagNo")
        );
        AggregationResults<TagUserDTO> aggregate = mongoTemplate.aggregate(
                aggregationCount ,"tag_user",TagUserDTO.class
        );
        List<TagUserDTO> tagRuleBOS =  aggregate.getMappedResults();
        tagRuleBOS.forEach(tagUserDTO -> {
            List<TagInfoDTO> list = tagUserDTO.getTagInfo();
            TagInfoDTO tagInfoDTO = list.get(0);
            System.out.println(111);
        });
        System.out.println(111);
    }

    @Test
    public void selectTagRule(){
        Criteria criteria = new Criteria();
        criteria.and("operatorNo").is("1413325721820360704");
        //联合查询总条数，分页用
        Aggregation aggregationCount = Aggregation.newAggregation(
//                Aggregation.match(criteria),
                Aggregation.group("ruleValue")
                .first("_id").as("id")
        );
        AggregationResults<TagUserDTO> aggregate = mongoTemplate.aggregate(
                aggregationCount ,"tag_rule",TagUserDTO.class
        );
        List<TagUserDTO> tagRuleBOS =  aggregate.getMappedResults();
        tagRuleBOS.forEach(tagUserDTO -> {
            List<TagInfoDTO> list = tagUserDTO.getTagInfo();
            TagInfoDTO tagInfoDTO = list.get(0);
            System.out.println(111);
        });
        System.out.println(111);
    }

    @Test
    public void testSearchTagRuleData(){
        RuleDataQueryReqDTO ruleDataQueryReqDTO = new RuleDataQueryReqDTO();
        //ruleDataQueryReqDTO.setRuleName("Mondul");
        tagRuleService.queryUserAttributes(ruleDataQueryReqDTO);
        System.out.println("333");
    }

    @Test
    public void testQueryUserBehaviorRule(){
        tagRuleService.queryUserBehaviorRule();
    }
}
