package com.lifekh.data.warehouse.service;

import cn.hutool.core.date.DateUtil;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryClickReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryClickRespDTO;
import com.lifekh.data.warehouse.service.discovery.DiscoveryService;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/9/27 17:44
 * @Version 1.0
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DiscoveryTest {

    @Autowired
    DiscoveryService discoveryService;


    @Test
    public void testStrategy(){
        Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());
        Date endTime = DateUtil.beginOfDay(new Date());
        //discoveryService.moveDiscovryDetailsClick();
        discoveryService.moveDiscovryGoodsClick(startTime, endTime);
        //discoveryService.staticDiscoveryContentDetail();
    }

    @Test
    public void testGoodsStrategy(){
        Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());
        Date endTime = DateUtil.beginOfDay(new Date());
        discoveryService.moveDiscovryGoodsClick(startTime, endTime);
    }

    @Test
    public void testSearchDiscoveryClickStrategy(){
        SearchDiscoveryClickReqDTO searchDiscoveryClickReqDTO = new SearchDiscoveryClickReqDTO();
        searchDiscoveryClickReqDTO.setPageNum(1);
        searchDiscoveryClickReqDTO.setPageSize(8);
        searchDiscoveryClickReqDTO.setContentTitle("苹果");
        PageInfoDTO<SearchDiscoveryClickRespDTO> searchDiscoveryClickRespDTOPageInfoDTO = discoveryService.searchDiscoveryClickDetail(searchDiscoveryClickReqDTO);
        System.out.println();
    }

    @Test
    public void tetet(){
        Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());
        Date endTime = DateUtil.beginOfDay(new Date());
        discoveryService.staticDiscoveryContentDetail(startTime, endTime);
    }
}
