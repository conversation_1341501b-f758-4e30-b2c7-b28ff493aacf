package com.lifekh.data.warehouse.service;


import com.github.pagehelper.PageHelper;
import com.lifekh.data.warehouse.api.req.UpdateUserOperatorInfoReqDTO;
import com.lifekh.data.warehouse.config.route.DataBaseContextHolder;
import com.lifekh.data.warehouse.config.route.DataSourceType;
import com.lifekh.data.warehouse.oracle.bo.OracleAggregateOrderBO;
import com.lifekh.data.warehouse.oracle.bo.OracleUserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.oracle.dao.OracleAggregateOrderDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.utils.GoPageHelper;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserOperatorInfoServiceTest {
    @Autowired
    private UserOperatorInfoService userOperatorInfoService;
    @Test
    public void updateUserOperatorInfo(){
        UpdateUserOperatorInfoReqDTO reqDTO = new UpdateUserOperatorInfoReqDTO();
        reqDTO.setOperatorNo("1443146205217472512");
        reqDTO.setLanguage("zh-CN");
        userOperatorInfoService.updateUserOperatorInfo(reqDTO);
    }

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Test
    public void queryAll() {
//        DataBaseContextHolder.setDataSourceType(DataSourceType.LIFEKH_MP_CUSTOMER);
        PageHelper.startPage(1, 10);
        List<OracleUserOperatorLoginInfoBO> list =  oracleUserOperatorLoginInfoDAO.query();
        PageInfoDTO<OracleUserOperatorLoginInfoBO> result = GoPageHelper.createPageInfoDTO(list);
        log.info("查询结果1：{}", result);

        PageHelper.startPage(1, 10);
//        DataBaseContextHolder.setDataSourceType(DataSourceType.LIFEKH_MP_SHOP);
        List<OracleAggregateOrderBO> list2 = oracleAggregateOrderDAO.query();
        PageInfoDTO<OracleAggregateOrderBO> result2 = GoPageHelper.createPageInfoDTO(list2);
        log.info("查询结果2：{}", result2);
    }

    @Test
    public void queryLoginMethod() {
        String loginMethod = oracleUserOperatorLoginInfoDAO.queryLoginMethod("1371290672601260032");
        String loginMethod2= oracleUserOperatorLoginInfoDAO.queryLoginMethod("1371290672601260032");
        log.info("loginMethod:{}， loginMethod2:{}", loginMethod, loginMethod2);
    }

    @Test
    public void queryActiveUser() {
        Long total = oracleUserOperatorLoginInfoDAO.queryActiveUserYesterday();
        log.info("昨天活跃用户数为：{}", total);
    }

    @Autowired
    private OracleAggregateOrderDAO oracleAggregateOrderDAO;

    @Test
    public void queryAggregateOrder() {
        PageHelper.startPage(1, 10);
//        DataBaseContextHolder.setDataSourceType(DataSourceType.LIFEKH_MP_SHOP);
        List<OracleAggregateOrderBO> list = oracleAggregateOrderDAO.query();
        PageInfoDTO<OracleAggregateOrderBO> result = GoPageHelper.createPageInfoDTO(list);
        log.info("查询结果：{}", result);
    }
}
