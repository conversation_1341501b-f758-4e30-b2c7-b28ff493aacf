package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.outstanding.framework.junit.GoServiceTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

public class TargetReportServiceTest extends GoServiceTest {

    @Autowired
    private TargetReportService targetReportService;

    @Test
    public void test1() {
//        targetReportService.staticRemainUserCount(Arrays.asList(1, 7, 30));

//        targetReportService.staticOrderUserCount(Arrays.asList(1, 7, 30));

        targetReportService.staticOverviewOrderConversionRateUV(Arrays.asList(1));
    }
}
