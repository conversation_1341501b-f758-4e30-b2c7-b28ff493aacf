package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.dto.ReportBasicReqDTO;
import com.lifekh.data.warehouse.report.service.TakeawayBannerFlowConversionReportStrategy;
import com.lifekh.data.warehouse.report.service.TakeawayEotFlowConversionReportStrategy;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.junit.GoServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

@Slf4j
public class TakeawayBannerFlowConversionReportStrategyTest extends GoServiceTest {

    @Autowired
    private TakeawayBannerFlowConversionReportStrategy takeawayBannerFlowConversionReportStrategy;

    @Test
    public void testStatisticsByDay() {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        log.info("testStatisticsByDay start...");

        Date beginTime = DateUtil.getSearchStaticTime(new Date(), Calendar.DATE, -90);
        ReportBasicReqDTO reportBasicReqDTO = new ReportBasicReqDTO();
        reportBasicReqDTO.setBeginTime(DateUtil.getStartTimeOfDate(beginTime));
        reportBasicReqDTO.setEndTime(DateUtil.getEndTimeOfDate(new Date()));
        takeawayBannerFlowConversionReportStrategy.statisticsByDay(reportBasicReqDTO);
        log.info("testStatisticsByDay end");

        // 记录结束时间并计算执行时间
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        log.info("testStatisticsByDay execution time: {} ms", executionTime);
    }
}
