package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.bo.ogg.AppVersionBetaUserBO;
import com.outstanding.framework.core.DelStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AppVersionBetaUserServiceTest {

    @Autowired
    private AppVersionBetaUserService appVersionBetaUserService;
    @Test
    public void add(){
        AppVersionBetaUserBO bo = new AppVersionBetaUserBO();
        bo.setCreateTime(new Date());
        bo.setDelState(DelStateEnum.NORMAL.getCode());
        bo.setDepartment("zhongtai");
        bo.setDeviceType("IOS");
        bo.setLoginName("8550888158158");
        bo.setNickname("yao");
        bo.setOperatorNo("1379710201691406336");
        bo.setRealName("yao");
        bo.setStatus("enable");
        bo.setUpdateTime(new Date());
        bo.setVersion(0L);
//        bo.setId(new Long("112"));
        appVersionBetaUserService.add(bo);
    }
}
