package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.EventTypeFacade;
import com.lifekh.data.warehouse.api.collect.req.CollectSpmReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.enums.collect.CollectionEventEnum;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.api.req.EventTypeUpdateReqDTO;
import com.lifekh.data.warehouse.bo.behavior.ApplicationInfoBO;
import com.lifekh.data.warehouse.service.collection.DataCollectionService;
import com.lifekh.data.warehouse.strategy.collection.EventGroupStrategyContext;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:53
 * @Version 1.0
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EventTest {

    @Autowired
    EventGroupStrategyContext eventGroupStrategyContext;

    @Autowired
    DataCollectionService dataCollectionService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private EventTypeFacade eventTypeFacade;

    @Test
    public void testStrategy(){
        StandardDataCollectionReqDTO standardDataCollectionReqDTO = new StandardDataCollectionReqDTO();
        eventGroupStrategyContext.excuteSaveUserEventData(standardDataCollectionReqDTO, EventGroupEnum.LOGIN);
    }

    @Test
    public void testUpdate(){
        EventTypeUpdateReqDTO reqDTO = new EventTypeUpdateReqDTO();
        reqDTO.setEventNo("@login");
        reqDTO.setEventGroup("login");
        reqDTO.setEventName("登录");
        reqDTO.setTable("collect_behavior_v3");
        eventTypeFacade.update(reqDTO);
    }

    @Test
    public void testMq(){
        StandardDataCollectionReqDTO standardDataCollectionReqDTO = new StandardDataCollectionReqDTO();
        standardDataCollectionReqDTO.setIp("**************");
        standardDataCollectionReqDTO.setAppId("SuperApp");
        standardDataCollectionReqDTO.setAppNo("11");
        standardDataCollectionReqDTO.setChannel("AppStore");
        standardDataCollectionReqDTO.setAppVersion("2.19.1");
        standardDataCollectionReqDTO.setDeviceId("4DFEE33B-DBA0-4ADC-8453-47F46480350B");
        standardDataCollectionReqDTO.setDeviceType("IOS");
        standardDataCollectionReqDTO.setEventGroup(EventGroupEnum.LOGIN.getCode());
        standardDataCollectionReqDTO.setEvent(CollectionEventEnum.OPEN_APP.getCode());
        standardDataCollectionReqDTO.setEventName(CollectionEventEnum.OPEN_APP.getMessage());
        standardDataCollectionReqDTO.setLanguage("en-US");
        standardDataCollectionReqDTO.setLongitude("104.890628");
        standardDataCollectionReqDTO.setLatitude("11.566199");
        dataCollectionService.saveStandardCollectionData(standardDataCollectionReqDTO);
    }

    @Test
    public void  testMQ(){
        ApplicationInfoBO applicationInfoBO = new ApplicationInfoBO();
        applicationInfoBO.setAppVersion("3232");
        applicationInfoBO.setChannel("appStore");
        applicationInfoBO.setAppNo("12");
        mongoTemplate.insert(applicationInfoBO,"login@testCollection");
    }

    @Test
    public void testOher(){
        StandardDataCollectionReqDTO standardDataCollectionReqDTO = new StandardDataCollectionReqDTO();
        standardDataCollectionReqDTO.setIp("**************");
        standardDataCollectionReqDTO.setAppId("SuperApp");
        standardDataCollectionReqDTO.setAppNo("10");
        standardDataCollectionReqDTO.setChannel("AppStore");
        standardDataCollectionReqDTO.setAppVersion("2.19.1");
        standardDataCollectionReqDTO.setDeviceId("4DFEE33B-DBA0-4ADC-8453-47F46480350B");
        standardDataCollectionReqDTO.setDeviceType("IOS");
        standardDataCollectionReqDTO.setEventGroup(EventGroupEnum.VIEW_PAGE.getCode());
        standardDataCollectionReqDTO.setEvent(CollectionEventEnum.VIEW_PAGE.getCode());
        standardDataCollectionReqDTO.setEventName(CollectionEventEnum.VIEW_PAGE.getMessage());
        standardDataCollectionReqDTO.setLanguage("en-US");
        standardDataCollectionReqDTO.setLongitude("104.890628");
        standardDataCollectionReqDTO.setLatitude("11.566199");
        CollectSpmReqDTO collectSpmReqDTO = new CollectSpmReqDTO();
        collectSpmReqDTO.setCurrentPage("外卖首页");
        collectSpmReqDTO.setCurrentArea("金刚区");
        collectSpmReqDTO.setParentPage("WOWNOW 首页");
        collectSpmReqDTO.setStayTime(1000L);
        standardDataCollectionReqDTO.setSpm(collectSpmReqDTO);
        Map<String, Object> ext = new HashMap<>();
        ext.put("businessLine","YumNow");
        standardDataCollectionReqDTO.setExt(ext);
        dataCollectionService.saveStandardCollectionData(standardDataCollectionReqDTO);
    }

    @Test
    public void testSave(){
        String event = "{\"ext\":{\"associatedId\":\"1528975611727331328\",\"channel\":\"blank\",\"url\":\"http://localhost:8081/marketing/found-page/index/detail?id=1528975611727331328\"},\"deviceType\":\"Android\",\"spm\":{},\"recordTime\":1653469523660,\"appId\":\"SuperAPP\",\"businessName\":\"发现页文章浏览数\",\"appNo\":21,\"language\":\"zh-CN\",\"sessionid\":\"20220525093720435931\",\"event\":\"discovry_details_click\",\"eventGroup\":\"other\",\"longitude\":\"104.934182\",\"latitude\":\"11.512677\"}";

        StandardDataCollectionReqDTO reqDTO = FastJsonUtil.jsonToObject(event, StandardDataCollectionReqDTO.class);
        dataCollectionService.saveOtherCollectionData(reqDTO);
        System.out.println("=====");
    }
}
