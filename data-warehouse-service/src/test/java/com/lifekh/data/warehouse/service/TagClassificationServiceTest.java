package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.TagInfoFacade;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationEditReqDTO;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagClassificationRespDTO;
import com.lifekh.data.warehouse.dao.TagRuleDAO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TagClassificationServiceTest {

    @Autowired
    private TagClassificationService tagClassificationService;

    @Test
    public void testQueryTag() {
        TagClassificationQueryReqDTO tagClassificationQueryReqDTO = new TagClassificationQueryReqDTO();
        tagClassificationQueryReqDTO.setTagClassificationName("用户交易");
        List<TagClassificationRespDTO> respDTOS = tagClassificationService.queryTagClassification(tagClassificationQueryReqDTO);
        log.info("查询结果为:{}",respDTOS);
    }

    @Test
    public void testEditOrAddTagClassification() {
        TagClassificationEditReqDTO tagClassificationQueryReqDTO = new TagClassificationEditReqDTO();
        tagClassificationQueryReqDTO.setFirstTagClassificationNo("1419569629125082021");
        tagClassificationQueryReqDTO.setSecondTagClassificationNo("2021090649125082022");
        tagClassificationQueryReqDTO.setSecondaryClassificationName("语言");
        tagClassificationService.editOrAddTagClassification(tagClassificationQueryReqDTO);
    }
}
