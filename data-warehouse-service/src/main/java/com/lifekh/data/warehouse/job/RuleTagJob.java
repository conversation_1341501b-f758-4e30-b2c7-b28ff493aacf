package com.lifekh.data.warehouse.job;

import cn.hutool.core.collection.ListUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.TagExecStatusEnum;
import com.lifekh.data.warehouse.api.enums.TagStatusEnum;
import com.lifekh.data.warehouse.api.enums.TagTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.bo.TagInfoV2BO;
import com.lifekh.data.warehouse.dao.TagInfoV2DAO;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.strategy.GenerateTagStrategyContent;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;

/**
 * 用户规则标签
 */
@Slf4j
@ElasticJobConf(name = "rule-tag-job", cron = "0 0 3 * * ?", description = "定时处理规则标签", shardingTotalCount = 1)
public class RuleTagJob extends AbstractSimpleJob {

    @Autowired
    private TagInfoV2DAO tagInfoV2DAO;
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private GenerateTagStrategyContent generateTagStrategyContent;

    @Value("${data.warehouse.newUserTagNo:2021110211551498496}")
    private String newUserTagNo;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始执行规则标签任务");
        try {
            List<TagInfoV2BO> tagInfoBOS = tagInfoV2DAO.findByTagTypeAndTagStatus(TagTypeEnum.RULE_TAG.getCode(), TagStatusEnum.OPEN.getCode());
            //遍历标签
            for (TagInfoV2BO tagInfoV2BO : tagInfoBOS) {
                if (newUserTagNo.equals(tagInfoV2BO.getTagNo())) {
                    //新用户标签目前无规则设置，不自动贴标签
                    continue;
                }
                try {
                    log.info("开始处理规则标签，tagNo:{}", tagInfoV2BO.getTagNo());

                    List<String> addOptNos = new ArrayList<>();
                    List<RuleTagRuleReqV2DTO> tagRuleBOS = tagInfoV2BO.getRule();
                    //查询行为和属性标签
                    for (RuleTagRuleReqV2DTO rule : tagRuleBOS) {
                        //查询出符合该规则的用户
                        List<String> matchOptNos = generateTagStrategyContent.calculateTag(rule);
                        if (CollectionUtils.isNotEmpty(matchOptNos)) {
                            //首条规则匹配用户全量添加
                            if (addOptNos.isEmpty()) {
                                addOptNos.addAll(matchOptNos);
                            } else {
                                //非首条规则用户取交集
                                addOptNos.retainAll(matchOptNos);
                            }
                        } else {
                            //匹配用户为空说明没有用户符合该规则
                            addOptNos = new ArrayList<>();
                            break;
                        }
                    }

                    List<UpdateUserTagDTO> updateUserTags = new ArrayList<>();

                    log.info("匹配标签用户完成，开始判断需要更新标签的用户，本次匹配用户的数量: {}", addOptNos.size());

                    //查询拥有当前标签的用户
                    List<String> oldOptNos = tagUserService.findOperatorNoByTagNo(tagInfoV2BO.getTagNo());
                    log.info("查询当前标签的用户，tagNo: {}，用户数: {}", tagInfoV2BO.getTagNo(), oldOptNos.size());

                    //转成set，提高判断效率
                    Set<String> oldOptNoSet = new HashSet<>(oldOptNos);
                    Set<String> addOptNoSet = new HashSet<>(addOptNos);

                    //如果当前标签不包含该用户，则贴标签
                    for (String optNo : addOptNos) {
                        if (!oldOptNoSet.contains(optNo)) {
                            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                            updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
                            updateUserTagDTO.setOperatorNo(optNo);
                            updateUserTagDTO.setTagNo(tagInfoV2BO.getTagNo());
                            updateUserTags.add(updateUserTagDTO);
                        }
                    }

                    //如果当前标签用户不在本次匹配中，则移除标签
                    for (String optNo : oldOptNos) {
                        if (!addOptNoSet.contains(optNo)) {
                            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                            updateUserTagDTO.setOptType(OptTypeEnum.DEL.getCode());
                            updateUserTagDTO.setOperatorNo(optNo);
                            updateUserTagDTO.setTagNo(tagInfoV2BO.getTagNo());
                            updateUserTags.add(updateUserTagDTO);
                        }
                    }

                    log.info("开始发送更新标签消息，tagNo：{}，已有用户数量：{}，更新数量：{}", tagInfoV2BO.getTagNo(), oldOptNos.size(), updateUserTags.size());
                    //发送至MQ
                    if (CollectionUtils.isNotEmpty(updateUserTags)) {
                        //拆分list，避免mq单条消息过大
                        List<List<UpdateUserTagDTO>> splitReqs = ListUtil.split(updateUserTags, 50);
                        splitReqs.forEach(req ->
                                rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(new ArrayList<>(req)))
                        );
                    }

                    //更新执行状态
                    Update update = Update.update(TagInfoV2BO.EXEC_STATUS, TagExecStatusEnum.EFFECTIVE.getCode()).set(TagInfoV2BO.EXEC_TIME, new Date());
                    mongoTemplate.updateFirst(Query.query(Criteria.where(TagInfoV2BO.TAG_NO).is(tagInfoV2BO.getTagNo())), update, TagInfoV2BO.TABLE_NAME);

                    log.info("结束处理规则标签，tagNo:{}", tagInfoV2BO.getTagNo());
                } catch (Exception e) {
                    log.error("单个规则标签处理异常, tagNo:{}", tagInfoV2BO.getTagNo(), e);
                }
            }
        } catch (Exception e) {
            log.error("规则标签任务执行异常", e);
        }
        log.info("结束执行规则标签任务");
    }
}