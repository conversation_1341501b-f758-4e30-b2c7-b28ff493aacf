package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.ShortLinkReportFacade;
import com.lifekh.data.warehouse.api.req.ShortLinkReportQueryReqDTO;
import com.lifekh.data.warehouse.api.resp.ShortLinkReportRespDTO;
import com.lifekh.data.warehouse.report.service.ShortLinkReportService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ShortLinkReportFacadeImpl implements ShortLinkReportFacade {

    @Autowired
    private ShortLinkReportService shortLinkReportService;

    @Override
    public PageInfoDTO<ShortLinkReportRespDTO> queryReport(ShortLinkReportQueryReqDTO reqDTO) throws PendingException {
        return shortLinkReportService.queryReport(reqDTO);
    }
}
