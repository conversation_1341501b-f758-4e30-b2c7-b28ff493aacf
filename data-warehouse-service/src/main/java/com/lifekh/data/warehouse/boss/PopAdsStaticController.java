package com.lifekh.data.warehouse.boss;

import com.lifekh.data.warehouse.api.req.PopStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.PopStaticRespDTO;
import com.lifekh.data.warehouse.service.PopAdsStaticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 弹窗点击统计
 *
 * @module data-warehouse-collection
 */
@RequestMapping("/warehouse/boss/pop")
@RestController
@Slf4j
public class PopAdsStaticController {

    @Autowired
    private PopAdsStaticService popAdsStaticService;

    /**
     * 弹窗点击统计
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/total.do")
    @ResponseBody
    public PopStaticRespDTO queryStaticByPopNo(@RequestBody PopStaticReqDTO reqDTO) {
        return popAdsStaticService.queryStaticByPopNo(reqDTO);
    }
}
