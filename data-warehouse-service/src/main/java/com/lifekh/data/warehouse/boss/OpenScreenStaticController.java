package com.lifekh.data.warehouse.boss;

import com.lifekh.data.warehouse.api.req.OpenScreenStaticReqDTO;
import com.lifekh.data.warehouse.api.req.PopStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.OpenScreenStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.PopStaticRespDTO;
import com.lifekh.data.warehouse.service.OpenScreenStaticService;
import com.lifekh.data.warehouse.service.PopAdsStaticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 启动页点击统计
 *
 * @module data-warehouse-collection
 */
@RequestMapping("/warehouse/boss/open")
@RestController
@Slf4j
public class OpenScreenStaticController {

    @Autowired
    private OpenScreenStaticService openScreenStaticService;

    /**
     * 启动页点击统计
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/total.do")
    @ResponseBody
    public OpenScreenStaticRespDTO queryStaticByPopNo(@RequestBody OpenScreenStaticReqDTO reqDTO) {
        return openScreenStaticService.queryStaticByPopNo(reqDTO);
    }
}
