package com.lifekh.data.warehouse.job;

import cn.hutool.core.collection.ListUtil;
import com.chaos.common.enums.AppIdEnum;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorInfoBO;
import com.lifekh.data.warehouse.service.AggregateOrderService;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户首单业务线标签同步
 */
@Slf4j
@ElasticJobConf(name = "first-order-tag-job", cron = "0 10 4 24 11 ?", description = "用户首单业务线标签同步任务", shardingTotalCount = 1)
public class FirstOrderTagJob extends AbstractSimpleJob {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private AggregateOrderService aggregateOrderService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始执行首单业务线标签同步");
        List<TagInfoBO> tagInfos = tagInfoService.findByRuleType(RuleTypeEnum.FIRST_ORDER_BIZ.getCode());
        if (tagInfos.isEmpty()) {
            return;
        }
        //转map
        Map<String, TagInfoBO> tagMap = new HashMap<>();
        for (TagInfoBO tag : tagInfos) {
            if (CollectionUtils.isNotEmpty(tag.getRule())) {
                tagMap.put(tag.getRule().get(0).getRuleValue(), tag);
            }
        }

        int size = 1000;
        for (int page = 0; ; page++) {
            String operatorNo = null;
            try {
                log.info("分页查询操作员, page: {}", page);
                Query query = Query.query(Criteria.where("APP_ID").is(AppIdEnum.SUPER_APP.getCode()))
                        .with(Sort.by(Sort.Direction.ASC, "CREATE_TIME"))
                        .with(PageRequest.of(page, size));
                List<UserOperatorInfoBO> operators = mongoTemplate.find(query, UserOperatorInfoBO.class);
                if (operators.isEmpty()) {
                    break;
                }
                List<UpdateUserTagDTO> updateUserTags = new ArrayList<>();
                for (UserOperatorInfoBO o : operators) {
                    operatorNo = o.getOperatorNo();
                    AggregateOrderBO order = aggregateOrderService.findFirstOrderByUserId(o.getOperatorNo());
                    if (order != null && StringUtils.isNotBlank(order.getBusinessLine())) {
                        //获取标签
                        TagInfoBO tagInfo = tagMap.get(order.getBusinessLine());
                        if (tagInfo != null) {
                            //查询当前用户有无此标签
                            TagUserQueryReqDTO tagUserQueryReqDTO = new TagUserQueryReqDTO();
                            tagUserQueryReqDTO.setOperatorNo(o.getOperatorNo());
                            TagUserRespDTO tagUser = tagUserService.queryByOperatorNo(tagUserQueryReqDTO);
                            //没有标签就新增
                            if (tagUser == null || CollectionUtils.isEmpty(tagUser.getTagNo()) || !tagUser.getTagNo().contains(tagInfo.getTagNo())) {
                                UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                                updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
                                updateUserTagDTO.setOperatorNo(o.getOperatorNo());
                                updateUserTagDTO.setTagNo(tagInfo.getTagNo());
                                updateUserTags.add(updateUserTagDTO);
                            }
                        }
                    }
                }

                if (!updateUserTags.isEmpty()) {
                    //拆分list
                    List<List<UpdateUserTagDTO>> splitReqs = ListUtil.split(updateUserTags, 50);
                    splitReqs.forEach(req ->
                            rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(new ArrayList<>(req)))
                    );
                }
            } catch (Exception e) {
                log.error("首单业务线标签同步出现异常, page:{}, operatorNo:{}", page, operatorNo, e);
            }
        }
        log.info("结束执行首单业务线标签同步");
    }
}