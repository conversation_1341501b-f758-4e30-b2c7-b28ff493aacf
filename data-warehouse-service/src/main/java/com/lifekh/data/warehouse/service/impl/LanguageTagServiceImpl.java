package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import com.lifekh.data.warehouse.service.*;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class LanguageTagServiceImpl implements LanguageTagService {
    @Autowired
    private UserLabelService userLabelService;
    @Autowired
    private TagRuleService tagRuleService;
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Value("${tag.allDataSwitch:true}")
    private Boolean allDataSwitch;
    @Override
    public void generateTag(ScheduledTagUsereqDTO scheduledTagUsereqDTO) throws PendingException {
        if (allDataSwitch) {
            int size = 500;
            for(int page=0; ; page++) {
                Pageable pageable = PageRequest.of(page, size);
                log.info("批量生成语言标签");
                List<UserLabelBO> list = userLabelService.findHistoryData(scheduledTagUsereqDTO,pageable);
                if(CollectionUtils.isEmpty(list)) {
                    break;
                }
                log.info("批量更新语言标签：{}",list.size());
                updateUserTagList(list);
            }
        }

    }

    private void updateUserTagList(List<UserLabelBO> list) {
        //批量更新标签列表
        List<UpdateUserTagDTO> updateList = new ArrayList<>();
        //对用户标签数据进行遍历贴标签
        list.forEach(bo -> {
            updateUserTag(bo,updateList);
        });
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        log.info("批量发送更新语言标签数据：{},{}",updateList.size(),updateList.get(0).toString());
        //批量发送
        try {
            rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateList));
        } catch (Exception e) {
            log.info("发送更新用户标签异常", e);
        }

    }
    @Override
    public void updateUserTagOne(UserLabelBO bo){
        List<UpdateUserTagDTO> updateList = new ArrayList<>();
        updateUserTag(bo,updateList);
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        //发送mq
        rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateList));
    }

    public void updateUserTag(UserLabelBO bo,List<UpdateUserTagDTO> updateList) {
        List<UpdateUserTagDTO> newUpdateList = new ArrayList<>();
        if (StringUtils.isNotEmpty(bo.getLanguage())) {
            //根据语言查询标签规则
            TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.LANGUAGE_TAG.getCode(), bo.getLanguage(), 1);
            if (tagInfo == null) {
                return;
            }
            //根据标签规则编号查询标签表
            TagUserDTO tagUserDTO = tagUserService.selectUserLeftJoinTagInfo(bo.getOperatorNo());
            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
            updateUserTagDTO.setOperatorNo(bo.getOperatorNo());
            updateUserTagDTO.setTagNo(tagInfo.getTagNo());
            updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
            boolean exist = true;
            if (tagUserDTO != null ) {
                for(TagInfoDTO tagInfoDTO : tagUserDTO.getTagInfo()) {
                    if(Objects.nonNull(tagInfoDTO)) {
                        for(TagRuleDTO ruleDTO : tagInfoDTO.getRule()) {
                            //用户已添加过地区标签
                            if(Objects.nonNull(ruleDTO) && RuleTypeEnum.LANGUAGE_TAG.getCode().equals(ruleDTO.getRuleType())) {
                                //如果新标签编号和旧标签编号相同则不更新
                                if(tagInfoDTO.getTagNo().equals(tagInfo.getTagNo())) {
                                    exist = false;
                                    return;
                                }
                                //删除旧标签
                                newUpdateList.add(new UpdateUserTagDTO(bo.getOperatorNo(), tagInfoDTO.getTagNo(),
                                        OptTypeEnum.DEL.getCode(), null, null));
                                //添加新标签
                                newUpdateList.add(updateUserTagDTO);
                                updateList.addAll(newUpdateList);
                                return;
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(newUpdateList) && exist) {
                newUpdateList.add(updateUserTagDTO);
            }
        }
        updateList.addAll(newUpdateList);
    }
}
