package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.req.RuleDataQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserAttributesQueryRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRuleRespDTO;
import com.lifekh.data.warehouse.api.req.TagRuleAddReqDTO;
import com.lifekh.data.warehouse.bo.TagRuleBO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagRuleService {

    void add(TagRuleAddReqDTO reqDTO) throws PendingException;

    TagRuleDTO selectByRuleValue(String ruleValue, String ruleType) throws PendingException;


    TagRuleBO selectByRuleNo(String ruleNo) throws PendingException;

    List<UserAttributesQueryRespDTO> queryUserAttributes(RuleDataQueryReqDTO ruleDataQueryReqDTO);

    List<UserBehaviorRuleRespDTO> queryUserBehaviorRule();

}
