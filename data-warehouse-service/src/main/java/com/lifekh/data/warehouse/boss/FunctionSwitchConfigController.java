package com.lifekh.data.warehouse.boss;


import com.lifekh.data.warehouse.api.EventTypeFacade;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.EventTypeRespDTO;
import com.lifekh.data.warehouse.manage.FunctionSwitchManager;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/warehouse/boss/switch")
@RestController
@Slf4j
public class FunctionSwitchConfigController {

    @Autowired
    private FunctionSwitchManager functionSwitchManager;

    /**
     * 新增配置
     */
    @RequestMapping(value = "/add.do", method = RequestMethod.POST)
    @ResponseBody
    public void add(@RequestBody FunctionSwitchAddReqDTO reqDTO,
                    @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        functionSwitchManager.add(reqDTO);
    }

    /**
     * 关闭配置
     */
    @RequestMapping(value = "/disable.do", method = RequestMethod.POST)
    @ResponseBody
    public void disable(@RequestBody FunSwitchDisableReqDTO reqDTO,
                        @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        functionSwitchManager.disable(reqDTO);
    }

    /**
     * 启用配置
     */
    @RequestMapping(value = "/enable.do", method = RequestMethod.POST)
    @ResponseBody
    public void enable(@RequestBody FunSwitchEnableReqDTO reqDTO,
                       @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        functionSwitchManager.enable(reqDTO);
    }
}
