package com.lifekh.data.warehouse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.PopStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.PopStaticRespDTO;
import com.lifekh.data.warehouse.service.PopAdsStaticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("popAdsStaticService")
public class PopAdsStaticServiceImpl implements PopAdsStaticService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public PopStaticRespDTO queryStaticByPopNo(PopStaticReqDTO reqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("popNo").is(reqDTO.getPopNo());
        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group("popNo")
                .first("popNo").as("popNo")
                .sum("count").as("total"));
        AggregationResults<PopStaticRespDTO> aggregateTotalResults = mongoTemplate.aggregate(Aggregation.newAggregation(querList), MongoDbCollectonName.POP_ADS_STATIC, PopStaticRespDTO.class);
        List<PopStaticRespDTO> totalList = aggregateTotalResults.getMappedResults();
        PopStaticRespDTO respDTO = new PopStaticRespDTO(0L);
        if (CollectionUtil.isNotEmpty(totalList)) {
            respDTO.setTotal(totalList.get(0) != null ? totalList.get(0).getTotal() : 0);
        }
        return respDTO;
    }
}
