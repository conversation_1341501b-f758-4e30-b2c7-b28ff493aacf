package com.lifekh.data.warehouse.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 设备池数据同步
 */
@Slf4j
@ElasticJobConf(name = "device-pool-sync-job", cron = "30 5/10 * * * ?", description = "设备池数据同步", shardingTotalCount = 1)
public class DevicePoolSyncJob extends AbstractSimpleJob {

    @Autowired
    private DevicePoolService devicePoolService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始同步设备数据");

        //设备相关埋点事件
        devicePoolService.handleDeviceEvent();

        log.info("结束同步设备数据");
    }

}