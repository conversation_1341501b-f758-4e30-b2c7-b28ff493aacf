package com.lifekh.data.warehouse.service.collection.impl;

import com.lifekh.data.warehouse.api.collect.req.CollectSpmReqDTO;
import com.lifekh.data.warehouse.api.collect.req.OtherDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.bo.ZoneBO;
import com.lifekh.data.warehouse.bo.behavior.*;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.dao.UserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.service.collection.OtherDataCollectionService;
import com.lifekh.data.warehouse.service.discovery.DiscoveryService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.Decimal128;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/10/20 15:25
 * @Version 1.0
 **/
@Service
@Slf4j
public class OtherDataCollectionServiceImpl implements OtherDataCollectionService {

    @Autowired
    private UserOperatorLoginInfoDAO userOperatorLoginInfoDAO;

    @Autowired
    private LocationManage locationManage;

    @Autowired
    private ZoneDAO zoneDAO;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DiscoveryService discoveryService;

    @Override
    public void saveOtherDataCollection(OtherDataCollectionReqDTO messageDTO) throws PendingException {
        CollectSpmBO collectSpmBO = new CollectSpmBO();
        //spm数据
        if (Objects.nonNull(messageDTO.getSpm())){
            CollectSpmReqDTO collectSpmReqDTO = messageDTO.getSpm();
            collectSpmBO.setAppNo(messageDTO.getAppNo());
            collectSpmBO.setParentPage(collectSpmReqDTO.getParentPage());
            collectSpmBO.setCurrentPage(collectSpmReqDTO.getCurrentPage());
            collectSpmBO.setCurrentArea(collectSpmReqDTO.getCurrentArea());
            collectSpmBO.setNode(collectSpmReqDTO.getNode());
            collectSpmBO.setStayTime(collectSpmReqDTO.getStayTime());
            collectSpmBO.setChildPage(collectSpmReqDTO.getChildPage());
        }
        //用户映射数据
        UserInfoBO userInfoBo = new UserInfoBO();
        userInfoBo.setLanguage(messageDTO.getLanguage());
        userInfoBo.setAppId(messageDTO.getAppId());
        userInfoBo.setOperatorNo(messageDTO.getOperatorNo());
        userInfoBo.setLoginName(messageDTO.getLoginName());
        collectSpmBO.setUserInfoBo(userInfoBo);
        //应用映射数据
        ApplicationInfoBO applicationInfoBo = new ApplicationInfoBO();
        applicationInfoBo.setAppNo(messageDTO.getAppNo());
        applicationInfoBo.setChannel(messageDTO.getChannel());
        applicationInfoBo.setAppVersion(messageDTO.getAppVersion());
        collectSpmBO.setApplicationInfoBo(applicationInfoBo);
        //设备映射数据
        DeviceInfoBO deviceInfoBo = new DeviceInfoBO();
        deviceInfoBo.setDeviceId(messageDTO.getDeviceId());
        deviceInfoBo.setDeviceType(messageDTO.getDeviceType());
        deviceInfoBo.setAppVersion(messageDTO.getAppVersion());
        collectSpmBO.setDeviceInfoBo(deviceInfoBo);
        //事件映射数据
        EventBO eventBo = new EventBO();
        eventBo.setEvent(messageDTO.getEvent());
        eventBo.setEventName(messageDTO.getEventName());
        collectSpmBO.setEventBo(eventBo);
        //网络映射数据
        NetworkBO networkBo = new NetworkBO();
        networkBo.setIp(messageDTO.getIp());
        collectSpmBO.setNetworkBo(networkBo);
        //获取位置信息
        LocationBO locationBo = new LocationBO();
        if (StringUtils.isNotEmpty(messageDTO.getLongitude()) && StringUtils.isNotEmpty(messageDTO.getLatitude())) {
            locationBo = searchLocationInfo(messageDTO.getLongitude(),messageDTO.getLatitude(),locationManage);
        }
        collectSpmBO.setLocationBo(locationBo);
        //业务信息
        collectSpmBO.setExt(messageDTO.getExt());
        collectSpmBO.setCreateTime(new Date());
        collectSpmBO.setBusinessName(messageDTO.getBusinessName());
        collectSpmBO.setRecordTime(messageDTO.getRecordTime());
        collectSpmBO.setSessionId(messageDTO.getSessionId());
        collectSpmBO.setBusinessLine(messageDTO.getBusinessLine());
        mongoTemplate.insert(collectSpmBO, MongoDbCollectonName.COLLECT_BURIED_POINT_OTHER);

        //另存发现页事件
        discoveryService.saveDiscovryEvent(collectSpmBO);
    }

    private LocationBO searchLocationInfo(String longitude, String latitude, LocationManage locationManage) throws PendingException {
        LocationBO locationBo = new LocationBO();
        Decimal128 longitudeD = Decimal128.parse(longitude);
        Decimal128 latitudeD = Decimal128.parse(latitude);
        //根据经纬度查询区域
        if(Objects.nonNull(longitudeD) && Objects.nonNull(latitudeD)) {
            locationBo.setCoordinates(new Double[]{Double.parseDouble(longitude) , Double.parseDouble(latitude)});
            //根据经纬度找出对应的省份名称
            LocationDTO locationDTO = locationManage.getNameByPosition(longitudeD.toString(), latitudeD.toString());
            if (Objects.nonNull(locationDTO)) {
                //找出省份对应的编码
                locationBo.setProvinceName(locationDTO.getProvinceName());
                List<ZoneBO> zoneList = zoneDAO.findByMsgEn(locationDTO.getProvinceName());
                locationBo.setProvinceNo(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getCode() : null);
                locationBo.setProvinceNameKm(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getMsgCb() : null);
                locationBo.setProvinceNameZh(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getMsgZh() : null);
                locationBo.setAreaName(locationDTO.getDistinctName());
            }
        }
        return locationBo;
    }

}
