package com.lifekh.data.warehouse.mq;

import com.chaos.common.enums.BusinessLineEnum;
import com.chaos.message.api.dto.ReceiveMessageDTO;
import com.chaos.shop.api.AggregateFacadeService;
import com.chaos.shop.api.dto.order.mq.MQConstant;
import com.chaos.shop.api.dto.order.mq.dto.SaveOrderSuccessMsgReqDTO;
import com.chaos.shop.api.dto.order.req.GetAggregateOrderListReqDTO;
import com.chaos.shop.api.dto.order.rsp.AggregateOrderListRspDTO;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.oracle.bo.OracleAggregateOrderBO;
import com.lifekh.data.warehouse.oracle.dao.OracleAggregateOrderDAO;
import com.lifekh.data.warehouse.report.service.DwdAggregateOrderService;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 更新用户标签消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQConstant.CREATE_ORDER_SUCCESS_TOPIC, consumerGroup = "usercenter-core-service", consumeThreadMax = 1)
public class DeleteNewUserTagConsumer implements RocketMQListener<ReceiveMessageDTO> {

    @Autowired
    AggregateFacadeService aggregateFacadeService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${data.warehouse.newUserTagNo:2021110211551498496}")
    private String newUserTagNo;

    @Autowired
    TagUserService tagUserService;

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private OracleAggregateOrderDAO oracleAggregateOrderDAO;

    @Autowired
    private DwdAggregateOrderService dwdAggregateOrderService;

    @Override
    public void onMessage(ReceiveMessageDTO receiveMessageDTO) {
        SaveOrderSuccessMsgReqDTO saveOrderSuccessMsgReqDTO = FastJsonUtil.jsonToObject(
                receiveMessageDTO.getMessageBody(), SaveOrderSuccessMsgReqDTO.class);
        if (BusinessLineEnum.YUMNOW.getCode().equals(saveOrderSuccessMsgReqDTO.getBusinessLine())){
            GetAggregateOrderListReqDTO getAggregateOrderListReqDTO = new GetAggregateOrderListReqDTO();
            getAggregateOrderListReqDTO.setAggregateOrderNo(saveOrderSuccessMsgReqDTO.getAggregateOrderNo());
            getAggregateOrderListReqDTO.setOrderType("10");
            PageInfoDTO<AggregateOrderListRspDTO> aggregateOrderListRspDTOPageInfoDTO = aggregateFacadeService.getAggregateOrderList(getAggregateOrderListReqDTO);
            if (Objects.nonNull(aggregateOrderListRspDTOPageInfoDTO) && Objects.nonNull(aggregateOrderListRspDTOPageInfoDTO.getList()) && aggregateOrderListRspDTOPageInfoDTO.getList().size() >0){
                //查询该操作员标签
                TagUserQueryReqDTO tagUserQueryReqDTO = new TagUserQueryReqDTO();
                tagUserQueryReqDTO.setOperatorNo(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                TagUserRespDTO tagUserRespDTO = tagUserService.queryByOperatorNo(tagUserQueryReqDTO);
                if (Objects.nonNull(tagUserRespDTO) && Objects.nonNull(tagUserRespDTO.getTagNo()) && tagUserRespDTO.getTagNo().contains(newUserTagNo)){
                    log.info("外卖下单事件：{}，messageId：{}，删除操作员：{}的新用户角色",
                            receiveMessageDTO.getMessageBody(),receiveMessageDTO.getMessageId(),aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                    //如果还包含新用户标签，发送删除新用户标签mq
                    List<UpdateUserTagDTO> updateUserTagDTOS = new ArrayList<>();
                    UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                    updateUserTagDTO.setOptType(OptTypeEnum.DEL.getCode());
                    updateUserTagDTO.setOperatorNo(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                    updateUserTagDTO.setTagNo(newUserTagNo);
                    updateUserTagDTOS.add(updateUserTagDTO);
                    rocketMQTemplate.syncSend(com.lifekh.data.warehouse.api.constants.Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateUserTagDTOS));
                }
            }
        }

        this.addFirstOrderTag(saveOrderSuccessMsgReqDTO.getAggregateOrderNo());

        dwdAggregateOrderService.saveDwdAggregateOrder(saveOrderSuccessMsgReqDTO.getAggregateOrderNo());
    }

    /**
     * 添加用户首单业务线标签
     *
     * @param orderNo
     */
    private void addFirstOrderTag(String orderNo) {
        try {
            //用户首单
            OracleAggregateOrderBO order = oracleAggregateOrderDAO.queryUserFirstOrderByOrderNo(orderNo);
            if (order != null && StringUtils.isNotBlank(order.getBusinessLine())) {
                //查询标签信息
                TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.FIRST_ORDER_BIZ.getCode(), order.getBusinessLine(), 1);
                if (tagInfo != null) {
                    //查询当前用户有无此标签
                    TagUserQueryReqDTO tagUserQueryReqDTO = new TagUserQueryReqDTO();
                    tagUserQueryReqDTO.setOperatorNo(order.getUserId());
                    TagUserRespDTO tagUser = tagUserService.queryByOperatorNo(tagUserQueryReqDTO);
                    //没有标签就新增
                    if (tagUser == null || CollectionUtils.isEmpty(tagUser.getTagNo()) || !tagUser.getTagNo().contains(tagInfo.getTagNo())) {
                        List<UpdateUserTagDTO> updateUserTagDTOS = new ArrayList<>();
                        UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                        updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
                        updateUserTagDTO.setOperatorNo(order.getUserId());
                        updateUserTagDTO.setTagNo(tagInfo.getTagNo());
                        updateUserTagDTOS.add(updateUserTagDTO);
                        rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateUserTagDTOS));
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加用户首单业务线标签出现异常, orderNo: {}", orderNo, e);
        }
    }

}
