package com.lifekh.data.warehouse.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.strategy.GenerateTagStrategyContent;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 用户语言标签
 */
@Slf4j
@ElasticJobConf(name = "tag-user-job", cron = "0 20 2 * * ? ", description = "定时生成用户标签",shardingTotalCount = 1)
public class ZoneTagJob extends AbstractSimpleJob {
    @Autowired
    private GenerateTagStrategyContent generateTagStrategyContent;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始生成用户标签");
        //作业总数
        int totalCount = shardingContext.getShardingTotalCount();
        int item = shardingContext.getShardingItem();
        ScheduledTagUsereqDTO scheduledStationLetterReqDTO = new ScheduledTagUsereqDTO();
        scheduledStationLetterReqDTO.setItem(item);
        scheduledStationLetterReqDTO.setTotalCount(totalCount);
        generateTagStrategyContent.executeGenerateTag(scheduledStationLetterReqDTO,RuleTypeEnum.BETA_TAG);
        generateTagStrategyContent.executeGenerateTag(scheduledStationLetterReqDTO,RuleTypeEnum.GENERAL_USER);

        generateTagStrategyContent.executeGenerateTag(scheduledStationLetterReqDTO,RuleTypeEnum.LANGUAGE_TAG);
        log.info("结束生成用户标签");
    }
}