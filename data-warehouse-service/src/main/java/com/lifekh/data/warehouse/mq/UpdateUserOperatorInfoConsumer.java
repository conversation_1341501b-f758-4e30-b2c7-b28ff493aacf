package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.req.UpdateUserOperatorInfoReqDTO;
import com.lifekh.data.warehouse.service.UserOperatorInfoService;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.USER_MODIFY_OPERATOR_INFO_TOPIC, consumerGroup = "usercenter-core-service", consumeThreadMax = 1)
public class UpdateUserOperatorInfoConsumer implements RocketMQListener<String> {

    @Autowired
    private UserOperatorInfoService userOperatorInfoService;

    @Override
    public void onMessage(String message) {
        log.info("修改用户信息开始:{}", message);
        UpdateUserOperatorInfoReqDTO reqDTO = FastJsonUtil.jsonToObject(message, UpdateUserOperatorInfoReqDTO.class);
        userOperatorInfoService.updateUserOperatorInfo(reqDTO);
        log.info("修改用户信息开始:{}", message);
    }
}
