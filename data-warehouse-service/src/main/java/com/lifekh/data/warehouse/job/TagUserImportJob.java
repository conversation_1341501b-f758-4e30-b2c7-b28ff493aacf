package com.lifekh.data.warehouse.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.strategy.GenerateTagStrategyContent;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 用户语言标签
 */
@Slf4j
@ElasticJobConf(name = "tag-user-import-job", cron = "0 0 0/1 * * ?", description = "定时处理批量导入用户标签数据",shardingTotalCount = 1)
public class TagUserImportJob extends AbstractSimpleJob {
    @Autowired
    private TagUserService tagUserService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始处理批量导入的用户标签数据");
        try {
            tagUserService.updateUserTagForImport();
        } catch (Exception e) {
            log.error("处理批量导入的用户标签数据异常", e);
        }
        log.info("结束处理批量导入的用户标签数据");
    }
}