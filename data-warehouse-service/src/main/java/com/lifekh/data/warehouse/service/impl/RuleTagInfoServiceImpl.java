package com.lifekh.data.warehouse.service.impl;

import com.chaos.common.enums.LanguageEnum;
import com.chaos.usercenter.api.constants.RoleConstant;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagNoRespDTO;
import com.lifekh.data.warehouse.api.enums.*;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.RuleTagInfoDetailRespDTO;
import com.lifekh.data.warehouse.api.resp.TagDetailQueryRespDTO;
import com.lifekh.data.warehouse.bo.TagClassificationBO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.bo.TagInfoV2BO;
import com.lifekh.data.warehouse.bo.TagRuleBO;
import com.lifekh.data.warehouse.dao.TagInfoDAO;
import com.lifekh.data.warehouse.service.*;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.base.sequence.SequenceNo;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class RuleTagInfoServiceImpl implements RuleTagInfoService {
    @Autowired
    private TagRuleService tagRuleService;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private SequenceNo sequenceRuleNo;
    @Autowired
    private SequenceNo sequenceTagNo;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private TagInfoDAO tagInfoDAO;
    @Autowired
    private TagClassificationService tagClassificationService;

    @Value("${data.warehouse.newUserTagNo:2021110211551498496}")
    private String newUserTagNo;

    @Override
    public void add(RuleTagInfoAddReqDTO reqDTO) throws PendingException{
        reqDTO.getRule().forEach(ruleTagRuleReqDTO -> {
            Map<String,Object> map = new HashMap<>();
            map.put("days", ruleTagRuleReqDTO.getDays());
            map.put("min", ruleTagRuleReqDTO.getMin());
            map.put("max", ruleTagRuleReqDTO.getMax());
            map.put("behavior", ruleTagRuleReqDTO.getBehavior());
            map.put("symbol", ruleTagRuleReqDTO.getSymbol());
            String ruleValue = FastJsonUtil.objectToJson(map);
            String ruleNo = sequenceRuleNo.nextSeq();
            //新增标签
            TagInfoBO bo = new TagInfoBO();
            BeanCopierHelper.copyProperties(reqDTO, bo);
            bo.setCreateTime(new Date());
            bo.setUpdateTime(new Date());
            bo.setTagNo(sequenceTagNo.nextSeq());
            TagRuleBO tagRuleBO = new TagRuleBO();
            tagRuleBO.setCreateTime(new Date());
            tagRuleBO.setUpdateTime(new Date());
            tagRuleBO.setRuleName(ruleTagRuleReqDTO.getRuleType());
            tagRuleBO.setRuleNo(ruleNo);
            tagRuleBO.setRuleType(ruleTagRuleReqDTO.getRuleType());
            tagRuleBO.setRuleValue(ruleValue);
            tagRuleBO.setBusinessLine(ruleTagRuleReqDTO.getBusinessLine());
            bo.setRule(Arrays.asList(tagRuleBO));
            tagInfoService.saveTag(bo);
        });
    }

    @Override
    public void edit(RuleTagInfoEditReqDTO reqDTO) throws PendingException {
        if (newUserTagNo.equals(reqDTO.getTagNo())){
            //新用户标签不允许编辑
            return;
        }
        if (CollectionUtils.isEmpty(reqDTO.getRule())) {
        }
        reqDTO.getRule().forEach(ruleTagRuleReqDTO -> {
            //查询标签是否有用户，如果有则不能修改标签规则，只修改标签归属、标签名称、说明内容
            TagUserListReqDTO tagUserListReqDTO = new TagUserListReqDTO();
            tagUserListReqDTO.setTagNo(reqDTO.getTagNo());
            List<TagUserDTO> tagUserDTOS = tagUserService.list(tagUserListReqDTO).getList();
            Query query = new Query();
            query.addCriteria(Criteria.where("tagNo").is(reqDTO.getTagNo()));
            Update update = new Update();
            update.set("tagClassify", reqDTO.getTagClassify());
            update.set("tagName",reqDTO.getTagName());
            update.set("tagDescription", reqDTO.getTagDescription());
            if (CollectionUtils.isEmpty(tagUserDTOS)) {
                Map<String,Object> map = new HashMap<>();
                map.put("days", ruleTagRuleReqDTO.getDays());
                map.put("min", ruleTagRuleReqDTO.getMin());
                map.put("max", ruleTagRuleReqDTO.getMax());
                map.put("behavior", ruleTagRuleReqDTO.getBehavior());
                map.put("symbol", ruleTagRuleReqDTO.getSymbol());
                String ruleValue = FastJsonUtil.objectToJson(map);
                TagRuleBO tagRuleBO = new TagRuleBO();
                tagRuleBO.setCreateTime(new Date());
                tagRuleBO.setUpdateTime(new Date());
                tagRuleBO.setRuleName(ruleTagRuleReqDTO.getRuleType());
                tagRuleBO.setRuleNo(ruleTagRuleReqDTO.getRuleNo());
                tagRuleBO.setRuleType(ruleTagRuleReqDTO.getRuleType());
                tagRuleBO.setRuleValue(ruleValue);
                tagRuleBO.setBusinessLine(ruleTagRuleReqDTO.getBusinessLine());
                update.set("rule", Arrays.asList(tagRuleBO));
            }
            //修改规则标签
            mongoTemplate.updateMulti(query,update,TagInfoBO.class);
        });
    }

    @Override
    public PageInfoDTO<RuleTagInfoDetailRespDTO> list(RuleTagInfoListReqDTO reqDTO) throws PendingException {
        Criteria criteria = new Criteria();
        //数据权限
        Criteria scopeCri = new Criteria();
        if (!RoleConstant.ADMIN_ROLE.equals(reqDTO.getRoleNo())) {
            List<Criteria> criterias = new ArrayList<>();
            Criteria ownCri = Criteria.where("tagScope").is(TagScopeEnum.OWN.getCode()).and("createBy").is(reqDTO.getLoginName());
            criterias.add(ownCri);

            List<String> businessLines = new ArrayList<>();
            businessLines.add(TagClassifyEnum.PUBLIC.getCode());
            if (StringUtils.isNotBlank(reqDTO.getBusinessLine())) {
                businessLines.addAll(Arrays.asList(reqDTO.getBusinessLine().split(",")));
            }
            Criteria everyCri = Criteria.where("tagScope").is(TagScopeEnum.EVERYONE.getCode()).and("tagClassify").in(businessLines);
            criterias.add(everyCri);
            scopeCri.orOperator(criterias);
        }

        if (StringUtils.isNotEmpty(reqDTO.getTagNo())) {
            criteria.and("tagNo").is(reqDTO.getTagNo());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagType())) {
            criteria.and("tagType").is(reqDTO.getTagType());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagCatalogue())) {
            criteria.and("tagCatalogue").is(reqDTO.getTagCatalogue());
        }

        Criteria nameCri = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getTagName())) {
            List<Criteria> criteriaList = new ArrayList<>();
            for (LanguageEnum languageEnum : LanguageEnum.values()) {
                criteriaList.add(Criteria.where("tagName."+languageEnum.getCode()).regex(reqDTO.getTagName()));
            }
            nameCri.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagClassify())) {
            criteria.and("tagClassify").is(reqDTO.getTagClassify());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagStatus())) {
            criteria.and("tagStatus").is(reqDTO.getTagStatus());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagScope())) {
            criteria.and("tagScope").is(reqDTO.getTagScope());
        }
        if (StringUtils.isNotEmpty(reqDTO.getExecStatus())) {
            criteria.and("execStatus").is(reqDTO.getExecStatus());
        }
        if (StringUtils.isNotEmpty(reqDTO.getCreateBy())) {
            criteria.and("createBy").regex(reqDTO.getCreateBy());
        }
        if (StringUtils.isNotEmpty(reqDTO.getUpdateBy())) {
            criteria.and("updateBy").regex(reqDTO.getUpdateBy());
        }
        if (reqDTO.getCreateStartTime() != null && reqDTO.getCreateEndTime() != null) {
            criteria.and("createTime").gte(reqDTO.getCreateStartTime()).lte(reqDTO.getCreateEndTime());
        }

        criteria.andOperator(scopeCri, nameCri);

        Query query = new Query(criteria);

        long total = mongoTemplate.count(query, TagInfoBO.class);

        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());

        List<RuleTagInfoDetailRespDTO> tagInfoDTOS = mongoTemplate.find(query,RuleTagInfoDetailRespDTO.class,"tag_info");

        PageInfoDTO<RuleTagInfoDetailRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(tagInfoDTOS.size());
        target.setTotal(total);
        target.setList(tagInfoDTOS);
        return target;
    }

    @Override
    public RuleTagInfoDetailRespDTO detail(RuleTagInfoDetailReqDTO reqDTO) throws PendingException {
        RuleTagInfoDetailRespDTO ruleTagInfoDetailRespDTO = new RuleTagInfoDetailRespDTO();
        TagDetailQueryRespDTO tagDetailQueryRespDTO = tagInfoDAO.findByTagNo(reqDTO.getTagNo());
        BeanCopierHelper.copyProperties(tagDetailQueryRespDTO,ruleTagInfoDetailRespDTO);
        return ruleTagInfoDetailRespDTO;
    }

    @Override
    public TagNoRespDTO addV2(RuleTagInfoAddReqV2DTO reqDTO) throws PendingException {
        this.validTagName(null, reqDTO.getTagClassify(), reqDTO.getTagName());

        if (StringUtils.isBlank(reqDTO.getSecondaryClassificationNo())) {
            String secondaryClassificationNo = tagClassificationService.addTagClassification(reqDTO.getTagClassify(), reqDTO.getFirstClassificationNo(),
                    reqDTO.getSecondaryClassification(), reqDTO.getSecondaryClassificationEn(), reqDTO.getSecondaryClassificationKm());
            reqDTO.setSecondaryClassificationNo(secondaryClassificationNo);
        }
        //默认贴标签人数为0
        reqDTO.setTotalUser(0L);
        if (StringUtils.isEmpty(reqDTO.getTagStatus())) {
            reqDTO.setTagStatus(TagStatusEnum.OPEN.getCode());
        }

        TagInfoV2BO tagInfoBO = new TagInfoV2BO();
        BeanCopierHelper.copyProperties(reqDTO,tagInfoBO);
        tagInfoBO.setTagNo(sequenceTagNo.nextSeq());
        tagInfoBO.setExecStatus(TagExecStatusEnum.WAIT.getCode());
        tagInfoBO.setTagCatalogue(TagCatalogueEnum.CUSTOMIZE.getCode());
        tagInfoBO.setCreateTime(new Date());
        tagInfoBO.setUpdateTime(new Date());
        tagInfoBO.setUpdateBy(reqDTO.getCreateBy());
        this.handleRule(reqDTO.getRule(), tagInfoBO);
        this.addFirstClassName(tagInfoBO);

        mongoTemplate.save(tagInfoBO,TagInfoV2BO.TABLE_NAME);

        return new TagNoRespDTO().setTagNo(tagInfoBO.getTagNo());
    }

    @Override
    public void editV2(RuleTagInfoEditReqV2DTO reqDTO) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("tagNo").is(reqDTO.getTagNo()));
        TagInfoV2BO tagInfoV2BO = mongoTemplate.findOne(query, TagInfoV2BO.class);
        if (TagCatalogueEnum.SYSTEM.getCode().equals(tagInfoV2BO.getTagCatalogue())) {
            throw new PendingException(WarehouseErrorCodeEnum.W2007);
        }

        this.validTagName(reqDTO.getTagNo(), reqDTO.getTagClassify(), reqDTO.getTagName());

        if (StringUtils.isBlank(reqDTO.getSecondaryClassificationNo())) {
            String secondaryClassificationNo = tagClassificationService.addTagClassification(reqDTO.getTagClassify(), reqDTO.getFirstClassificationNo(),
                    reqDTO.getSecondaryClassification(), reqDTO.getSecondaryClassificationEn(), reqDTO.getSecondaryClassificationKm());
            reqDTO.setSecondaryClassificationNo(secondaryClassificationNo);
        }

        BeanCopierHelper.copyProperties(reqDTO,tagInfoV2BO);
        tagInfoV2BO.setUpdateTime(new Date());
        this.handleRule(reqDTO.getRule(), tagInfoV2BO);
        this.addFirstClassName(tagInfoV2BO);

        //创建人为空时，赋值修改人
        if (StringUtils.isBlank(tagInfoV2BO.getCreateBy())) {
            tagInfoV2BO.setCreateBy(tagInfoV2BO.getUpdateBy());
        }
        mongoTemplate.save(tagInfoV2BO,TagInfoV2BO.TABLE_NAME);
    }

    private void handleRule(List<RuleTagRuleReqV2DTO> rules, TagInfoV2BO tagInfo) {
        if (CollectionUtils.isEmpty(rules)) {
            //自定义规则
            TagRuleDTO tagRuleDTO = tagRuleService.selectByRuleValue(RuleTypeEnum.CUSTOMIZE.getCode(),RuleTypeEnum.CUSTOMIZE.getCode());
            RuleTagRuleReqV2DTO rule = new RuleTagRuleReqV2DTO();
            rule.setRuleNo(tagRuleDTO.getRuleNo());
            rule.setRuleName(tagRuleDTO.getRuleName());
            rule.setRuleType(tagRuleDTO.getRuleType());
            rule.setRuleValue(tagRuleDTO.getRuleValue());
            tagInfo.setRule(Collections.singletonList(rule));
            tagInfo.setExecType(TagExecTypeEnum.MANUAL.getCode());
            tagInfo.setTagType(TagTypeEnum.BASIC_TAG.getCode());
        } else {
            tagInfo.setExecType(TagExecTypeEnum.SYSTEM.getCode());
            tagInfo.setTagType(TagTypeEnum.RULE_TAG.getCode());
            tagInfo.setRule(rules);
        }
    }

    private void addFirstClassName(TagInfoV2BO tagInfo) {
        //一级分类
        TagClassificationBO firstClass = mongoTemplate.findOne(Query.query(Criteria.where(TagClassificationBO.FIRST_TAG_CLASSIFICATION_NO).is(tagInfo.getFirstClassificationNo())),
                TagClassificationBO.class, TagClassificationBO.TABLE_NAME);
        if (firstClass != null) {
            tagInfo.setFirstClassification(firstClass.getFirstClassificationName());
            tagInfo.setFirstClassificationEn(firstClass.getFirstClassificationNameEn());
            tagInfo.setFirstClassificationKm(firstClass.getFirstClassificationNameKm());
        }
    }

    /**
     * 校验标签名称是否重复(同一业务线内)
     *
     * @param tagNo
     * @param tagClassify
     * @param tagNames
     */
    private void validTagName(String tagNo, String tagClassify, Map<String, String> tagNames) {
        String tagNameZh = tagNames.get(LanguageEnum.ZH_CN.getCode());
        String tagNameEn = tagNames.get(LanguageEnum.EN_US.getCode());
        String tagNameKm = tagNames.get(LanguageEnum.KM_KH.getCode());
        Criteria criteria = Criteria.where(TagInfoV2BO.TAG_CLASSIFY).is(tagClassify);
        if (StringUtils.isNotBlank(tagNo)) {
            criteria.and(TagInfoV2BO.TAG_NO).ne(tagNo);
        }

        long nameCount = mongoTemplate.count(Query.query(Criteria.where(TagInfoV2BO.TAG_NAME_ZH).is(tagNameZh).andOperator(criteria)), TagInfoV2BO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2001);
        }
        nameCount = mongoTemplate.count(Query.query(Criteria.where(TagInfoV2BO.TAG_NAME_EN).is(tagNameEn).andOperator(criteria)), TagInfoV2BO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2002);
        }
        nameCount = mongoTemplate.count(Query.query(Criteria.where(TagInfoV2BO.TAG_NAME_KM).is(tagNameKm).andOperator(criteria)), TagInfoV2BO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2003);
        }
    }

}
