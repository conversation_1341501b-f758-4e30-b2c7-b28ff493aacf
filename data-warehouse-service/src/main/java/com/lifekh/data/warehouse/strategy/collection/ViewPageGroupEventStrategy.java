package com.lifekh.data.warehouse.strategy.collection;

import com.lifekh.data.warehouse.api.collect.req.CollectSpmReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.collection.EventPageBO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.report.service.PageViewReportService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:01
 * @Version 1.0
 **/
@Component
@Slf4j
public class ViewPageGroupEventStrategy extends AbstractGenerateEventStrategy {

    @Autowired
    private PageViewReportService pageViewReportService;

    public ViewPageGroupEventStrategy(MongoTemplate mongoTemplate, ZoneDAO zoneDAO, LocationManage locationManage) {
        super(mongoTemplate, zoneDAO, locationManage);
    }

    @Override
    public EventGroupEnum getEventGroup() {
        return EventGroupEnum.VIEW_PAGE;
    }

    @Override
    public void convertSpm(CollectSpmBO collectSpmBO, StandardDataCollectionReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getSpm())) {
            CollectSpmReqDTO spmReqDTO = reqDTO.getSpm();
            collectSpmBO.setAppNo(reqDTO.getAppNo());
            collectSpmBO.setParentPage(spmReqDTO.getParentPage());
            collectSpmBO.setCurrentPage(spmReqDTO.getCurrentPage());
            collectSpmBO.setCurrentArea(spmReqDTO.getCurrentArea());
            collectSpmBO.setNode(spmReqDTO.getNode());
            collectSpmBO.setChildPage(spmReqDTO.getChildPage());
            collectSpmBO.setStayTime(spmReqDTO.getStayTime());

            if (StringUtils.isNotBlank(collectSpmBO.getParentPage())) {
                EventPageBO eventPage = getByPageCode(collectSpmBO.getParentPage());
                collectSpmBO.setParentPageName(eventPage == null ? null : eventPage.getPageName());
            }

            if (StringUtils.isNotBlank(collectSpmBO.getCurrentPage())) {
                EventPageBO eventPage = getByPageCode(collectSpmBO.getCurrentPage());
                if (eventPage != null) {
                    collectSpmBO.setCurrentPageName(eventPage.getPageName());
                    collectSpmBO.setBusinessLine(StringUtils.isBlank(collectSpmBO.getBusinessLine()) ? eventPage.getBusinessLine() : collectSpmBO.getBusinessLine());
                }
            }
        }
    }

    @Override
    protected void doAfter(CollectSpmBO collectSpmBO) throws PendingException {
        pageViewReportService.saveViewEventRecord(collectSpmBO);
    }

    private EventPageBO getByPageCode(String pageCode) {
        EventPageBO eventPage = null;
        try {
            eventPage = mongoTemplate.findOne(Query.query(Criteria.where(EventPageBO.PAGE_CODE).is(pageCode)), EventPageBO.class, MongoDbCollectonName.EVENT_PAGE);
        } catch (Exception e) {
            log.error("事件页面名称查询异常, pageCode: {}", pageCode, e);
        }
        return eventPage;
    }
}