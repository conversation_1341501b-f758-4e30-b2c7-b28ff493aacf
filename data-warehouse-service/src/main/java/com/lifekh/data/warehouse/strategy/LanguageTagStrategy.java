package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.service.LanguageTagService;
import com.lifekh.data.warehouse.service.UserLabelService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class LanguageTagStrategy extends AbstractGenerateTagStrategy{
    @Autowired
    private LanguageTagService languageTagService;
    @Autowired
    private UserLabelService userLabelService;
    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.LANGUAGE_TAG;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledTagUsereqDTO) throws PendingException {
        languageTagService.generateTag(scheduledTagUsereqDTO);
    }


    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        List<String> matchNos = new ArrayList<>();

        RuleDTO ruleDTO = rule.getSpecialRules().get(0);
        if (!"language".equals(ruleDTO.getRuleFiled())) {
            return matchNos;
        }
        int size = 5000;
        for (int page = 0; ; page++) {
            List<String> optNos = userLabelService.findOperatorNoByPage(page, size, ruleDTO.getMin());
            if (optNos.isEmpty()) {
                break;
            }
            matchNos.addAll(optNos);
        }
        return matchNos;
    }
}
