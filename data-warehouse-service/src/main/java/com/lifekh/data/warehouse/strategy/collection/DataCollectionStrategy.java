package com.lifekh.data.warehouse.strategy.collection;

import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.outstanding.framework.core.PendingException;


public interface DataCollectionStrategy {

    EventGroupEnum getEventGroup();

    //保存埋点消息
    void saveUserEventData(StandardDataCollectionReqDTO messageDTO) throws PendingException;

    void convertSpm(CollectSpmBO collectSpmBO, StandardDataCollectionReqDTO reqDTO);
}
