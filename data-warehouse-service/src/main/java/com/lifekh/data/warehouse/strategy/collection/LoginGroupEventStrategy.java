package com.lifekh.data.warehouse.strategy.collection;

import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.lifekh.data.warehouse.service.UserBehaviorInfoService;
import com.lifekh.data.warehouse.service.ZoneTagService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:01
 * @Version 1.0
 **/
@Component
@Slf4j
public class LoginGroupEventStrategy extends AbstractGenerateEventStrategy {

    public LoginGroupEventStrategy(MongoTemplate mongoTemplate, ZoneDAO zoneDAO, LocationManage locationManage) {
        super(mongoTemplate, zoneDAO, locationManage);
    }

    @Autowired
    private ZoneTagService zoneTagService;

    @Autowired
    private DevicePoolService devicePoolService;

    @Autowired
    private UserBehaviorInfoService userBehaviorInfoService;

    @Override
    public EventGroupEnum getEventGroup() {
        return EventGroupEnum.LOGIN;
    }

    @Override
    public void doAfter(CollectSpmBO collectSpmBO) throws PendingException {
        //更新地区标签
        if (collectSpmBO.getLocationBo() != null && collectSpmBO.getLocationBo().getCoordinates() != null) {
            UserBehaviorInfoBO behaviorBo2 = new UserBehaviorInfoBO();
            behaviorBo2.setLocationBo(collectSpmBO.getLocationBo());
            behaviorBo2.setUserInfoBo(collectSpmBO.getUserInfoBo());
            zoneTagService.updateUserTagOne(behaviorBo2);
        }

        //另存设备事件
        devicePoolService.saveDeviceEvent(collectSpmBO);

        //发送用户登录mq
        userBehaviorInfoService.sendUserLoginTaskMq(collectSpmBO);
    }
}