package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.DevicePoolFacade;
import com.lifekh.data.warehouse.api.req.DeviceIdReqDTO;
import com.lifekh.data.warehouse.api.resp.DeviceRespDTO;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DevicePoolFacadeImpl implements DevicePoolFacade {

    @Autowired
    private DevicePoolService devicePoolService;

    @Override
    public DeviceRespDTO getDeviceInfo(DeviceIdReqDTO reqDTO) throws PendingException {
        return devicePoolService.getDeviceInfo(reqDTO);
    }
}
