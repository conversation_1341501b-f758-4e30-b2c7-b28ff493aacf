package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.req.RuleDataQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserAttributesDetailRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserAttributesQueryRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRuleRespDTO;
import com.lifekh.data.warehouse.api.enums.BehaviorEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.TagRuleAddReqDTO;
import com.lifekh.data.warehouse.bo.TagRuleBO;
import com.lifekh.data.warehouse.bo.TagRuleV2BO;
import com.lifekh.data.warehouse.dao.TagRuleDAO;
import com.lifekh.data.warehouse.service.TagRuleService;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TagRuleServiceImpl implements TagRuleService {
    @Autowired
    private TagRuleDAO tagRuleDAO;
    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public void add(TagRuleAddReqDTO reqDTO) throws PendingException {
        TagRuleBO tagRuleBO = new TagRuleBO();
        BeanCopierHelper.copyProperties(reqDTO,tagRuleBO);
        tagRuleBO.setCreateTime(new Date());
        tagRuleBO.setUpdateTime(new Date());
        tagRuleDAO.save(tagRuleBO);
    }

    @Override
    public TagRuleDTO selectByRuleValue(String ruleValue,String ruleType) throws PendingException {
        Criteria criteria = new Criteria();
        criteria.and("ruleValue").is(ruleValue);
        criteria.and("ruleType").is(ruleType);
        //联合查询总条数，分页用
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup("tag_info","ruleNo","ruleNos","tagInfo")
        );
        AggregationResults<TagRuleDTO> aggregate = mongoTemplate.aggregate(
                aggregationCount ,"tag_rule",TagRuleDTO.class//A表，是查询的主表
        );
        List<TagRuleDTO> tagRuleBOS =  aggregate.getMappedResults();
        if (CollectionUtils.isNotEmpty(tagRuleBOS)) {
            return tagRuleBOS.get(0);
        }
        return null;
    }

    @Override
    public TagRuleBO selectByRuleNo(String ruleNo) throws PendingException {
        return tagRuleDAO.findByRuleNo(ruleNo);
    }

    @Override
    public List<UserAttributesQueryRespDTO> queryUserAttributes(RuleDataQueryReqDTO ruleDataQueryReqDTO) {
        List<UserAttributesQueryRespDTO> userAttributesQueryRespDTOS = new ArrayList<>();
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("ruleType").is(RuleTypeEnum.ZONE_TAG.getCode()));
        criteriaList.add(Criteria.where("ruleType").is(RuleTypeEnum.LANGUAGE_TAG.getCode()));
        criteriaList.add(Criteria.where("ruleType").is(RuleTypeEnum.BETA_TAG.getCode()));
        criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        if (StringUtils.isNotEmpty(ruleDataQueryReqDTO.getRuleName())) {
            criteria.and("ruleName").regex(ruleDataQueryReqDTO.getRuleName());
        }
        Query query = new Query(criteria);
        List<TagRuleV2BO> tagRuleV2BOS = mongoTemplate.find(query, TagRuleV2BO.class,"tag_rule");
        Map<String, UserAttributesQueryRespDTO> ruleDataQueryRespDTOMap = new HashMap<>();
        for (TagRuleV2BO tagRuleBO:tagRuleV2BOS){
            UserAttributesQueryRespDTO userAttributesQueryRespDTO = ruleDataQueryRespDTOMap.get(tagRuleBO.getRuleType());
            if (Objects.isNull(userAttributesQueryRespDTO)){//还未记录
                userAttributesQueryRespDTO = new UserAttributesQueryRespDTO();
                userAttributesQueryRespDTO.setRuleTypeName(RuleTypeEnum.getByCode(tagRuleBO.getRuleType()).getMessage());
                List<UserAttributesDetailRespDTO> userAttributesDetailRespDTOS = new ArrayList<>();
                UserAttributesDetailRespDTO userAttributesDetailRespDTO = new UserAttributesDetailRespDTO();
                userAttributesDetailRespDTO.setRuleName(tagRuleBO.getRuleName());
                userAttributesDetailRespDTO.setRuleNo(tagRuleBO.getRuleNo());
                userAttributesDetailRespDTO.setSpecialRules(tagRuleBO.getSpecialRules());
                userAttributesDetailRespDTOS.add(userAttributesDetailRespDTO);
                userAttributesQueryRespDTO.setRuleDetailList(userAttributesDetailRespDTOS);
                userAttributesQueryRespDTO.setRuleType(tagRuleBO.getRuleType());
                ruleDataQueryRespDTOMap.put(tagRuleBO.getRuleType(), userAttributesQueryRespDTO);
                userAttributesQueryRespDTOS.add(userAttributesQueryRespDTO);
            }else {//已记录，则在原基础上新增
                UserAttributesDetailRespDTO userAttributesDetailRespDTO = new UserAttributesDetailRespDTO();
                userAttributesDetailRespDTO.setRuleName(tagRuleBO.getRuleName());
                userAttributesDetailRespDTO.setRuleNo(tagRuleBO.getRuleNo());
                userAttributesDetailRespDTO.setSpecialRules(tagRuleBO.getSpecialRules());
                userAttributesQueryRespDTO.getRuleDetailList().add(userAttributesDetailRespDTO);
            }
        }
        return userAttributesQueryRespDTOS;
    }

    @Override
    public List<UserBehaviorRuleRespDTO> queryUserBehaviorRule() {
        List<UserBehaviorRuleRespDTO> userBehaviorRuleRespDTOS = new ArrayList<>();
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();
        for (BehaviorEnum e : BehaviorEnum.values()) {
            criteriaList.add(Criteria.where("ruleType").is(e.getCode()));
        }
        criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        Query query = new Query(criteria);
        List<TagRuleV2BO> tagRuleBOS = mongoTemplate.find(query, TagRuleV2BO.class,"tag_rule");
        for (TagRuleV2BO tagRuleBO:tagRuleBOS){
            UserBehaviorRuleRespDTO userBehaviorRuleRespDTO = new UserBehaviorRuleRespDTO();
            userBehaviorRuleRespDTO.setRuleNo(tagRuleBO.getRuleNo());
            userBehaviorRuleRespDTO.setRuleName(tagRuleBO.getRuleName());
            userBehaviorRuleRespDTO.setRuleType(tagRuleBO.getRuleType());
            userBehaviorRuleRespDTO.setGeneralRules(tagRuleBO.getGeneralRules());
            userBehaviorRuleRespDTO.setSpecialRules(tagRuleBO.getSpecialRules());
            userBehaviorRuleRespDTOS.add(userBehaviorRuleRespDTO);
        }
        return userBehaviorRuleRespDTOS;
    }

}
