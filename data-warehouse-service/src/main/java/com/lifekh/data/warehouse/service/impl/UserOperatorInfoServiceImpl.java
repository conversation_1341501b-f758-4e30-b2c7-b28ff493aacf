package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.req.UpdateUserOperatorInfoReqDTO;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.service.UserOperatorInfoService;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserOperatorInfoServiceImpl implements UserOperatorInfoService {
    @Autowired
    private TagUserService tagUserService;
    @Override
    public void updateUserOperatorInfo(UpdateUserOperatorInfoReqDTO reqDTO) throws PendingException {
        tagUserService.updateNickname(reqDTO.getOperatorNo(),reqDTO.getNickname(),reqDTO.getLanguage());
    }
}
