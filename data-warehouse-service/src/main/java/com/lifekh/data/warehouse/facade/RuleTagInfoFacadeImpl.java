package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.RuleTagInfoFacade;
import com.lifekh.data.warehouse.api.dto.req.RuleDataQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagNoRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserAttributesQueryRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRuleRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.RuleTagInfoDetailRespDTO;
import com.lifekh.data.warehouse.service.RuleTagInfoService;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagRuleService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RuleTagInfoFacadeImpl implements RuleTagInfoFacade {

    @Autowired
    private RuleTagInfoService ruleTagInfoService;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    TagRuleService tagRuleService;

    @Override
    public void add(RuleTagInfoAddReqDTO reqDTO) throws PendingException {
        ruleTagInfoService.add(reqDTO);
    }

    @Override
    public TagNoRespDTO addV2(RuleTagInfoAddReqV2DTO reqDTO) throws PendingException {
        return ruleTagInfoService.addV2(reqDTO);
    }

    @Override
    public void edit(RuleTagInfoEditReqDTO reqDTO) throws PendingException {
        ruleTagInfoService.edit(reqDTO);
    }

    @Override
    public void editV2(RuleTagInfoEditReqV2DTO reqDTO) throws PendingException {
        ruleTagInfoService.editV2(reqDTO);
    }

    @Override
    public PageInfoDTO<RuleTagInfoDetailRespDTO> list(RuleTagInfoListReqDTO reqDTO) throws PendingException {
        return ruleTagInfoService.list(reqDTO);
    }

    @Override
    public RuleTagInfoDetailRespDTO detail(RuleTagInfoDetailReqDTO reqDTO) throws PendingException {
        return ruleTagInfoService.detail(reqDTO);
    }

    @Override
    public void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException {
        tagInfoService.updateStatus(reqDTO);
    }

    @Override
    public List<UserAttributesQueryRespDTO> queryUserAttributes(RuleDataQueryReqDTO ruleDataQueryReqDTO) {
        return tagRuleService.queryUserAttributes(ruleDataQueryReqDTO);
    }

    @Override
    public List<UserBehaviorRuleRespDTO> queryUserBehaviorRule() {
        return tagRuleService.queryUserBehaviorRule();
    }
}
