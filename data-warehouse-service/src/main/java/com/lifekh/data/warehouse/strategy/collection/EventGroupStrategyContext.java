package com.lifekh.data.warehouse.strategy.collection;

import com.google.common.collect.Maps;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:33
 * @Version 1.0
 **/
@Component
@Slf4j
public class EventGroupStrategyContext {
    private Map<EventGroupEnum, DataCollectionStrategy> eventGroupStrategyMap = Maps.newEnumMap(EventGroupEnum.class);

    @Autowired
    public EventGroupStrategyContext(List<DataCollectionStrategy> strategyList){
        eventGroupStrategyMap.clear();
        strategyList.forEach(e -> eventGroupStrategyMap.put(e.getEventGroup(), e));
    }

    public void excuteSaveUserEventData(StandardDataCollectionReqDTO messageDTO, EventGroupEnum eventGroupEnum)  throws PendingException {
        DataCollectionStrategy dataCollectionStrategy = eventGroupStrategyMap.get(eventGroupEnum);
        dataCollectionStrategy.saveUserEventData(messageDTO);
    }
}
