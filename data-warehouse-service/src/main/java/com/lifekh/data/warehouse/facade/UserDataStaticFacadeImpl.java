package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.UserDataStaticFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.StaticTypeEnum;
import com.lifekh.data.warehouse.api.req.ActiveUserStaticReqDTO;
import com.lifekh.data.warehouse.api.req.ReportStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserReportStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.ActiveUserStickyRespDTO;
import com.lifekh.data.warehouse.api.resp.NewUserReportStaticRespDTO;
import com.lifekh.data.warehouse.api.resp.UserRemainStaticRespDTO;
import com.lifekh.data.warehouse.bo.ActiveUserStaticBO;
import com.lifekh.data.warehouse.bo.NewUserStaticBO;
import com.lifekh.data.warehouse.bo.UserRemainStaticBO;
import com.lifekh.data.warehouse.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/11/5 10:13
 * @Version 1.0
 **/
@Service
public class UserDataStaticFacadeImpl implements UserDataStaticFacade {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<NewUserReportStaticRespDTO> getNewUserReportStatic(ReportStaticReqDTO reportStaticReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("createTime").gte(reportStaticReqDTO.getStartDate())
                .lt(DateUtil.dateAddOneDay(reportStaticReqDTO.getEndDate(),1));
        Query query = new Query(criteria);
        List<NewUserStaticBO> newUserStaticBOS = mongoTemplate.find(query,NewUserStaticBO.class, MongoDbCollectonName.NEW_USER_STATIC);
        Map<String,NewUserStaticBO> userReportStaticRespDTOMap = new HashMap<>();
        for (NewUserStaticBO newUserStaticBO:newUserStaticBOS){
            userReportStaticRespDTOMap.put(newUserStaticBO.getStaticDate(),newUserStaticBO);
        }
        //补足空白日期返回
        List<NewUserReportStaticRespDTO> newUserStaticResultList = new ArrayList<>();
        Date startTime = reportStaticReqDTO.getStartDate();
        Date endTime = reportStaticReqDTO.getEndDate();
        int i = 0;
        while (true){
            if (startTime.getTime() > endTime.getTime()){
                break;
            }
            String startDateString = DateUtil.dataToString(startTime);
            if (userReportStaticRespDTOMap.keySet().contains(startDateString)){
                NewUserStaticBO newUserStaticBO = userReportStaticRespDTOMap.get(startDateString);
                NewUserReportStaticRespDTO newUserReportStaticRespDTO = new NewUserReportStaticRespDTO();
                newUserReportStaticRespDTO.setDate(newUserStaticBO.getStaticDate());
                newUserReportStaticRespDTO.setNum(newUserStaticBO.getNewUserNum());
                newUserStaticResultList.add(newUserReportStaticRespDTO);
            }else {
                NewUserReportStaticRespDTO newUserReportStaticRespDTO = new NewUserReportStaticRespDTO();
                newUserReportStaticRespDTO.setDate(startDateString);
                newUserReportStaticRespDTO.setNum(0L);
                newUserStaticResultList.add(newUserReportStaticRespDTO);
            }
            startTime = DateUtil.dateAddOneDay(startTime,1);
            i++;
            if (i > 100000){
                //防止无限循环的可能
                break;
            }
        }
        return newUserStaticResultList;
    }

    @Override
    public List<ActiveUserReportStaticRespDTO> getActiveUserReportStatic(ActiveUserStaticReqDTO reportStaticReqDTO) {
        if (StaticTypeEnum.DAY.getCode().equals(reportStaticReqDTO.getStaticType())){//按天统计
            return this.getActiveUserReportStaticByDay(reportStaticReqDTO);
        }
        if (StaticTypeEnum.WEEK.getCode().equals(reportStaticReqDTO.getStaticType())){//按周统计
            return this.getActiveUserReportStaticByWeek(reportStaticReqDTO);
        }
        if (StaticTypeEnum.MONTH.getCode().equals(reportStaticReqDTO.getStaticType())){//按月统计
            return this.getActiveUserReportStaticByMonth(reportStaticReqDTO);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ActiveUserStickyRespDTO> getActiveUserStickyStatic(ReportStaticReqDTO reportStaticReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("searchStaticTime").gte(reportStaticReqDTO.getStartDate())
                .lt(reportStaticReqDTO.getEndDate());
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "searchStaticTime"));
        List<ActiveUserStaticBO> activeUserStaticBOS = mongoTemplate.find(query,ActiveUserStaticBO.class, MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
        //取本月当前的月活跃数
        Long localMonthActive = 0L;
        if (DateUtil.getOneDatePlus(new Date()) != 1){//判断今日是否1号，1号所在月还未有统计记录，避免拿上个月的月活
            String yesterday = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1));
            Criteria dayCriteria = new Criteria();
            dayCriteria.and("staticDate").is(yesterday);
            Query dayQuery = new Query(dayCriteria);
            ActiveUserStaticBO yesterdayActiveStatic = mongoTemplate.findOne(dayQuery,ActiveUserStaticBO.class,MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
            if (Objects.nonNull(yesterdayActiveStatic)){
                localMonthActive = yesterdayActiveStatic.getLocalMonthActiveNum();
            }
        }
        //取本月开始时间
        Date localMonthStartTime = DateUtil.getFirstDayDateOfMonth(new Date());
        Map<String,ActiveUserStaticBO> activeUserStickyRespDTOMap = new HashMap<>();
        for (ActiveUserStaticBO activeUserStaticBO:activeUserStaticBOS){
            if (activeUserStaticBO.getSearchStaticTime().getTime() > localMonthStartTime.getTime()){
                activeUserStaticBO.setLocalMonthActiveNum(localMonthActive);
            }
            activeUserStickyRespDTOMap.put(activeUserStaticBO.getStaticDate(),activeUserStaticBO);
        }
        //计算粘性度，并补足空白数据
        List<ActiveUserStickyRespDTO> activeUserStickyRespDTOS = new ArrayList<>();
        Date startTime = reportStaticReqDTO.getStartDate();
        Date endTime = reportStaticReqDTO.getEndDate();
        BigDecimal multyplyNum = BigDecimal.valueOf(100.0);
        int i = 0;
        while (true){
            if (startTime.getTime() > endTime.getTime()){
                break;
            }
            String startDateString = DateUtil.dataToString(startTime);
            ActiveUserStickyRespDTO activeUserStickyRespDTO = new ActiveUserStickyRespDTO();
            activeUserStickyRespDTO.setDate(startDateString);
            if (activeUserStickyRespDTOMap.keySet().contains(startDateString)){
                ActiveUserStaticBO activeUserStaticBO = activeUserStickyRespDTOMap.get(startDateString);
                if (activeUserStaticBO.getActiveUserNum() == 0 || activeUserStaticBO.getLocalMonthActiveNum() ==0){
                    activeUserStickyRespDTO.setUserStickyRate(0.0);
                }else {
                    BigDecimal a = new BigDecimal(activeUserStaticBO.getActiveUserNum());
                    BigDecimal b = new BigDecimal(activeUserStaticBO.getLocalMonthActiveNum());
                    Double UserStickyRate = a.divide(b,3,BigDecimal.ROUND_HALF_UP).multiply(multyplyNum).doubleValue();
                    activeUserStickyRespDTO.setUserStickyRate(UserStickyRate);
                }
            }else {
                activeUserStickyRespDTO.setUserStickyRate(0.0);
            }
            activeUserStickyRespDTOS.add(activeUserStickyRespDTO);
            startTime = DateUtil.dateAddOneDay(startTime,1);
            i++;
            if (i > 100000){
                //防止无限循环的可能
                break;
            }
        }
        return activeUserStickyRespDTOS;
    }

    @Override
    public List<UserRemainStaticRespDTO> getUserRemainStatic(ReportStaticReqDTO reportStaticReqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("searchStaticTime").gte(reportStaticReqDTO.getStartDate())
                .lt(reportStaticReqDTO.getEndDate());
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "searchStaticTime"));
        List<UserRemainStaticBO> userRemainStaticBOS = mongoTemplate.find(query,UserRemainStaticBO.class,MongoDbCollectonName.USER_REMAIN_STATIC);
        Map<String,UserRemainStaticBO> userRemainStaticBOMap = new HashMap<>();
        for (UserRemainStaticBO userRemainStaticBO:userRemainStaticBOS){
            userRemainStaticBOMap.put(userRemainStaticBO.getStaticDate(),userRemainStaticBO);
        }
        List<UserRemainStaticRespDTO> userRemainStaticRespDTOS = new ArrayList<>();
        Date startTime = reportStaticReqDTO.getStartDate();
        Date endTime = reportStaticReqDTO.getEndDate();
        int i = 0;
        while (true){
            if (startTime.getTime() > endTime.getTime()){
                break;
            }
            String startDateString = DateUtil.dataToString(startTime);
            UserRemainStaticRespDTO userRemainStaticRespDTO = new UserRemainStaticRespDTO();
            userRemainStaticRespDTO.setDate(startDateString);
            if ( ! userRemainStaticBOMap.keySet().contains(startDateString)){
                //查询当日新增人数
                Criteria staticCriteria = new Criteria();
                staticCriteria.and("staticDate").is(startDateString);
                Query newUserNumQuery = new Query(staticCriteria);
                NewUserStaticBO newUserStaticBO = mongoTemplate.findOne(newUserNumQuery,NewUserStaticBO.class,MongoDbCollectonName.NEW_USER_STATIC);
                userRemainStaticRespDTO.setNewUserNum(newUserStaticBO == null ? 0 : newUserStaticBO.getNewUserNum().intValue());
            }else {
                UserRemainStaticBO userRemainStatic = userRemainStaticBOMap.get(startDateString);
                this.dealUserRemainData(userRemainStaticRespDTO,userRemainStatic);
            }
            userRemainStaticRespDTOS.add(userRemainStaticRespDTO);
            startTime = DateUtil.dateAddOneDay(startTime,1);
            i++;
            if (i > 100000){
                //防止无限循环的可能
                break;
            }
        }
        return userRemainStaticRespDTOS;
    }

    private List<ActiveUserReportStaticRespDTO> getActiveUserReportStaticByDay(ActiveUserStaticReqDTO reportStaticReqDTO){
        Criteria criteria = new Criteria();
        criteria.and("searchStaticTime").gte(reportStaticReqDTO.getStartDate())
                .lt(reportStaticReqDTO.getEndDate());
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "searchStaticTime"));
        List<ActiveUserStaticBO> activeUserStaticBOS = mongoTemplate.find(query,ActiveUserStaticBO.class, MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
        Map<String,ActiveUserStaticBO> userReportStaticRespDTOMap = new HashMap<>();
        for (ActiveUserStaticBO activeUserStaticBO:activeUserStaticBOS){
            userReportStaticRespDTOMap.put(activeUserStaticBO.getStaticDate(),activeUserStaticBO);
        }
        //补足空白日期返回
        List<ActiveUserReportStaticRespDTO> activeUserReportStaticRespDTOS = new ArrayList<>();
        Date startTime = reportStaticReqDTO.getStartDate();
        Date endTime = reportStaticReqDTO.getEndDate();
        int i = 0;
        while (true){
            if (startTime.getTime() > endTime.getTime()){
                break;
            }
            String startDateString = DateUtil.dateAddToString(startTime,1);
            if (userReportStaticRespDTOMap.keySet().contains(startDateString)){
                ActiveUserStaticBO activeUserStaticBO = userReportStaticRespDTOMap.get(startDateString);
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(activeUserStaticBO.getStaticDate());
                activeUserReportStaticRespDTO.setActiveUserNum(activeUserStaticBO.getActiveUserNum());
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }else {
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(startDateString);
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }
            startTime = DateUtil.dateAddOneDay(startTime,1);
            i++;
            if (i > 100000){
                //防止无限循环的可能
                break;
            }
        }
        return activeUserReportStaticRespDTOS;
    }

    private List<ActiveUserReportStaticRespDTO> getActiveUserReportStaticByWeek(ActiveUserStaticReqDTO reportStaticReqDTO){
        Criteria criteria = new Criteria();
        //结束时间+7天，保证查到当前周的统计数据
        Date afterSevenDay = DateUtil.dateCalculation(reportStaticReqDTO.getEndDate(), Calendar.DATE,7);
        criteria.and("searchStaticTime").gte(reportStaticReqDTO.getStartDate())
                .lt(afterSevenDay);
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "searchStaticTime"));
        List<ActiveUserStaticBO> activeUserStaticBOS = mongoTemplate.find(query,ActiveUserStaticBO.class, MongoDbCollectonName.ACTIVE_USER_STATIC_WEEK);
        Map<String,ActiveUserStaticBO> activeUserStaticBOMap = new HashMap<>();
        for (ActiveUserStaticBO activeUserStaticBO:activeUserStaticBOS){
            activeUserStaticBOMap.put(activeUserStaticBO.getStaticDate(),activeUserStaticBO);
        }
        //补足空白数据
        //获取开始时间所在周的开始时间
        String startOfWeek = DateUtil.getFirstOfWeek(reportStaticReqDTO.getStartDate());
        //获取结束时间所在周的结束时间
        String endOfWeek = DateUtil.getLastOfWeek(reportStaticReqDTO.getEndDate());
        //取开始和结束时间所包含的整个自然周的日期
        List<List<String>> weekList = DateUtil.getNatureWeeks(startOfWeek,endOfWeek);
        List<ActiveUserReportStaticRespDTO> activeUserReportStaticRespDTOS = new ArrayList<>();
        for (List<String> week:weekList){
            String staticDate = week.get(0) +  "/" + week.get(week.size() - 1);
            if (activeUserStaticBOMap.keySet().contains(staticDate)){
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(staticDate);
                activeUserReportStaticRespDTO.setActiveUserNum(activeUserStaticBOMap.get(staticDate).getActiveUserNum());
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }else {
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(staticDate);
                activeUserReportStaticRespDTO.setActiveUserNum(0L);
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }
        }
        //获取当前周的开始时间
        Date localWeekStartDate = DateUtil.getFirstDateOfWeek(new Date());
        if (reportStaticReqDTO.getEndDate().getTime() > localWeekStartDate.getTime()){
            //获取当前日距当前周周一的日活统计数据作为该周的返回数据
            //判断今日是否周一，周一的数据还未统计，不能拿上周的作为周活跃
            if (DateUtil.getMondayPlus() != 0) {
                String yesterday = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1));
                Criteria dayCriteria = new Criteria();
                dayCriteria.and("staticDate").is(yesterday);
                Query dayQuery = new Query(dayCriteria);
                ActiveUserStaticBO yesterdayActiveStatic = mongoTemplate.findOne(dayQuery,ActiveUserStaticBO.class,MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
                if (Objects.nonNull(yesterdayActiveStatic)){
                    activeUserReportStaticRespDTOS.get(activeUserReportStaticRespDTOS.size() - 1).setActiveUserNum(yesterdayActiveStatic.getLocalWeekActiveNum());
                }
            }
        }
        return activeUserReportStaticRespDTOS;
    }

    private List<ActiveUserReportStaticRespDTO> getActiveUserReportStaticByMonth(ActiveUserStaticReqDTO reportStaticReqDTO){
        Criteria criteria = new Criteria();
        //开始时间所在月的起始时间
        Date monthStart = DateUtil.getFirstDayDateOfMonth(reportStaticReqDTO.getStartDate());
        //结束日期所在月的结束时间
        Date monthEnd = DateUtil.getLastDayOfMonth(reportStaticReqDTO.getEndDate());
        criteria.and("searchStaticTime").gte(monthStart)
                .lt(monthEnd);
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "searchStaticTime"));
        List<ActiveUserStaticBO> activeUserStaticBOS = mongoTemplate.find(query,ActiveUserStaticBO.class, MongoDbCollectonName.ACTIVE_USER_STATIC_MONTH);
        Map<String,ActiveUserStaticBO> activeUserStaticBOMap = new HashMap<>();
        for (ActiveUserStaticBO activeUserStaticBO:activeUserStaticBOS){
            activeUserStaticBOMap.put(activeUserStaticBO.getStaticDate(),activeUserStaticBO);
        }
        //补足空白数据
        //取开始和结束时间所包含的整个自然月的起止日期
        List<List<String>> monthList = DateUtil.getMonthBetween(monthStart,monthEnd);
        List<ActiveUserReportStaticRespDTO> activeUserReportStaticRespDTOS = new ArrayList<>();
        for (List<String> momth:monthList){
            String staticDate = momth.get(0) +  "/" + momth.get(momth.size() - 1);
            if (activeUserStaticBOMap.keySet().contains(staticDate)){
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(staticDate);
                activeUserReportStaticRespDTO.setActiveUserNum(activeUserStaticBOMap.get(staticDate).getActiveUserNum());
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }else {
                ActiveUserReportStaticRespDTO activeUserReportStaticRespDTO = new ActiveUserReportStaticRespDTO();
                activeUserReportStaticRespDTO.setDate(staticDate);
                activeUserReportStaticRespDTO.setActiveUserNum(0L);
                activeUserReportStaticRespDTOS.add(activeUserReportStaticRespDTO);
            }
        }
        if (DateUtil.isSameMonth(monthEnd, new Date())){
            String yesterday = DateUtil.dataToString(DateUtil.dateCalculation(new Date(), Calendar.DATE,-1));
            Criteria dayCriteria = new Criteria();
            dayCriteria.and("staticDate").is(yesterday);
            Query dayQuery = new Query(dayCriteria);
            ActiveUserStaticBO yesterdayActiveStatic = mongoTemplate.findOne(dayQuery,ActiveUserStaticBO.class,MongoDbCollectonName.ACTIVE_USER_STATIC_DAY);
            if (Objects.nonNull(yesterdayActiveStatic)){
                activeUserReportStaticRespDTOS.get(activeUserReportStaticRespDTOS.size() - 1).setActiveUserNum(yesterdayActiveStatic.getLocalMonthActiveNum());
            }
        }
        return activeUserReportStaticRespDTOS;
    }

    private UserRemainStaticRespDTO dealUserRemainData(UserRemainStaticRespDTO userRemainStaticRespDTO,UserRemainStaticBO userRemainStatic){
        userRemainStaticRespDTO.setNewUserNum(userRemainStatic.getNewUserNum());
        //1日留存
        userRemainStaticRespDTO.setOneDayRemainNum(userRemainStatic.getOneDayRemainNum());
        userRemainStaticRespDTO.setOneDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getOneDayRemainNum()));
        //2日留存
        userRemainStaticRespDTO.setTwoDayRemainNum(userRemainStatic.getTwoDayRemainNum());
        userRemainStaticRespDTO.setTwoDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getTwoDayRemainNum()));
        //3日留存
        userRemainStaticRespDTO.setThreeDayRemainNum(userRemainStatic.getThreeDayRemainNum());
        userRemainStaticRespDTO.setThreeDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getThreeDayRemainNum()));
        //4日留存
        userRemainStaticRespDTO.setFourDayRemainNum(userRemainStatic.getFourDayRemainNum());
        userRemainStaticRespDTO.setFourDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getFourDayRemainNum()));
        //5日留存
        userRemainStaticRespDTO.setFiveDayRemainNum(userRemainStatic.getFiveDayRemainNum());
        userRemainStaticRespDTO.setFiveDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getFiveDayRemainNum()));
        //6日留存
        userRemainStaticRespDTO.setSixDayRemainNum(userRemainStatic.getSixDayRemainNum());
        userRemainStaticRespDTO.setSixDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getSixDayRemainNum()));
        //7日留存
        userRemainStaticRespDTO.setSevenDayRemainNum(userRemainStatic.getSevenDayRemainNum());
        userRemainStaticRespDTO.setSevenDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getSevenDayRemainNum()));
        //15日留存
        userRemainStaticRespDTO.setFifteenDayRemainNum(userRemainStatic.getFifteenDayRemainNum());
        userRemainStaticRespDTO.setFifteenDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getFifteenDayRemainNum()));
        //30日留存
        userRemainStaticRespDTO.setThirtyDayRemainNum(userRemainStatic.getThirtyDayRemainNum());
        userRemainStaticRespDTO.setThirtyDayRemainRate(this.calculateRate(userRemainStatic.getNewUserNum(),userRemainStatic.getThirtyDayRemainNum()));
        return userRemainStaticRespDTO;
    }

    private Double calculateRate(Integer newUserNum,Integer remainNum){
        if (newUserNum == null || newUserNum == 0 || remainNum == null || remainNum == 0){
            return 0.0;
        }
        BigDecimal multyplyNum = BigDecimal.valueOf(100.0);
        BigDecimal a = new BigDecimal(remainNum);
        BigDecimal b = new BigDecimal(newUserNum);
        return a.divide(b,4,BigDecimal.ROUND_HALF_UP).multiply(multyplyNum).doubleValue();
    }

}
