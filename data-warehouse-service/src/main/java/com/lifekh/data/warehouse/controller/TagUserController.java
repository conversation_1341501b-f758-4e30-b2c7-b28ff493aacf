package com.lifekh.data.warehouse.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.lifekh.data.warehouse.api.TagUserFacade;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.GreenBlueTagRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RequestMapping("/warehouse/tag")
@RestController
@Slf4j
public class TagUserController {

    @Autowired
    private TagUserFacade tagUserFacade;

    private static List<String> greenBlueTag = Arrays.asList("1507252740798099456", "1507252658413580288");

    @RequestMapping(value = "/query.do", method = RequestMethod.POST)
    @ResponseBody
    public TagUserRespDTO queryTagByOpNo(@RequestBody TagUserQueryReqDTO reqDTO,
                                                     @RequestHeader(value = "operatorNo",required = false) String operatorNo) throws PendingException {
        if(StringUtils.isBlank(reqDTO.getOperatorNo())) {
            if(StringUtils.isBlank(operatorNo)) {
                return new TagUserRespDTO();
            }
            reqDTO.setOperatorNo(operatorNo);
        }
        return tagUserFacade.queryByOperatorNo(reqDTO);
    }

    @RequestMapping(value = "/blueAndGreen/query.do", method = RequestMethod.POST)
    @ResponseBody
    public GreenBlueTagRespDTO blueAndGreenQuery(@RequestBody TagUserQueryReqDTO reqDTO,
                                              @RequestHeader(value = "operatorNo",required = false) String operatorNo) throws PendingException {
//        if(StringUtils.isBlank(reqDTO.getOperatorNo())) {
//            if(StringUtils.isBlank(operatorNo)) {
                return new GreenBlueTagRespDTO(new ArrayList<>());
//            }
//            reqDTO.setOperatorNo(operatorNo);
//        }

//        TagUserRespDTO respDTO = tagUserFacade.queryByOperatorNo(reqDTO);
//
//        if(CollectionUtils.isNotEmpty(respDTO.getTagNo())) {
//            List<String> tags = new ArrayList<>();
//            respDTO.getTagNo().forEach(tag -> {
//                if(greenBlueTag.contains(tag)) {
//                    tags.add(tag);
//                }});
//            return new GreenBlueTagRespDTO(tags);
//        } else {
//            return new GreenBlueTagRespDTO(new ArrayList<>());
//        }
    }
}
