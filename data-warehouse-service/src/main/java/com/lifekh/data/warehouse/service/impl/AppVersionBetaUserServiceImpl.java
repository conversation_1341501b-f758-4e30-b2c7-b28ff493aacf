package com.lifekh.data.warehouse.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.AppVersionBetaUserBO;
import com.lifekh.data.warehouse.dao.AppVersionBetaUserDAO;
import com.lifekh.data.warehouse.service.AppVersionBetaUserService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.DelStateEnum;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AppVersionBetaUserServiceImpl implements AppVersionBetaUserService {
    @Autowired
    private AppVersionBetaUserDAO appVersionBetaUserDAO;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public void add(AppVersionBetaUserBO bo) throws PendingException {
        appVersionBetaUserDAO.save(bo);
    }

    @Override
    public List<AppVersionBetaUserBO> findYesterdayData(ScheduledTagUsereqDTO scheduledTagUsereqDTO, Pageable pageable) throws PendingException {
        Criteria criteria = new Criteria();
        criteria.and("operatorNo").ne(null);
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("createTime").gt(DateUtil.dateCalculation(new Date(), Calendar.DATE, -1)));
        criteriaList.add(Criteria.where("updateTime").gt(DateUtil.dateCalculation(new Date(), Calendar.DATE, -1)));
        criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.DESC, "createTime"),
                Aggregation.group("operatorNo")
                        .first("operatorNo").as("OPERATOR_NO")
                        .first("status").as("STATUS")
                        .first("delState").as("DEL_STATE"),
                Aggregation.skip((pageable.getPageNumber()) * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        );
        AggregationResults<AppVersionBetaUserBO> outputType = mongoTemplate.aggregate(agg, AppVersionBetaUserBO.class, AppVersionBetaUserBO.class);
        return outputType.getMappedResults();
    }

    @Override
    public List<AppVersionBetaUserBO> findHistoryData(ScheduledTagUsereqDTO scheduledTagUsereqDTO, Pageable pageable) throws PendingException {
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("operatorNo").ne(null)),
                Aggregation.sort(Sort.Direction.DESC, "createTime"),
                Aggregation.group("operatorNo")
                        .first("operatorNo").as("OPERATOR_NO")
                        .first("status").as("STATUS")
                        .first("delState").as("DEL_STATE"),
                Aggregation.skip((pageable.getPageNumber()) * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        );
        AggregationResults<AppVersionBetaUserBO> outputType = mongoTemplate.aggregate(agg, AppVersionBetaUserBO.class, AppVersionBetaUserBO.class);
        return outputType.getMappedResults();
    }

    @Override
    public List<String> findByOperatorNos(List<String> operatorNos) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("operatorNo").in(operatorNos).and("STATUS").is("ENABLE").and("DEL_STATE").is(DelStateEnum.NORMAL.getCode()));
        List<AppVersionBetaUserBO> bos = mongoTemplate.find(query,AppVersionBetaUserBO.class);
        if (CollectionUtils.isEmpty(bos)) {
            return null;
        }
        return bos.stream().map(AppVersionBetaUserBO::getOperatorNo).collect(Collectors.toList());
    }

    @Override
    public List<String> findAllBetaOperatorNo() throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("STATUS").is("ENABLE").and("DEL_STATE").is(DelStateEnum.NORMAL.getCode()));
        List<AppVersionBetaUserBO> bos = mongoTemplate.find(query, AppVersionBetaUserBO.class);
        return bos.stream().map(AppVersionBetaUserBO::getOperatorNo).collect(Collectors.toList());
    }
}
