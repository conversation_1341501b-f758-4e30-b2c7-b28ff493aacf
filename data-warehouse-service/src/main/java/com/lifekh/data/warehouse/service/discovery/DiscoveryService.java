package com.lifekh.data.warehouse.service.discovery;

import com.lifekh.data.warehouse.api.req.discovery.DiscoveryBehaviorCountReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryGoodsClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryShareClickReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryBehaviorCountRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryShareClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryGoodsClickRespDTO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.outstanding.framework.core.PageInfoDTO;

import java.util.Date;

public interface DiscoveryService {
    /**
     * 迁移发现页浏览数
     * @throws
     */
    void moveDiscovryDetailsClick(Date startTime, Date endTime);

    /**
     * 迁移好物点击数(其中包含：加购数，好物下单数)
     *
     * @throws
     */
    void moveDiscovryGoodsClick(Date startTime, Date endTime);

    /**
     * 浏览数查询
     * @throws
     */
    PageInfoDTO<SearchDiscoveryClickRespDTO> searchDiscoveryClickDetail(SearchDiscoveryClickReqDTO searchDiscoveryClickReqDTO);

    /**
     * 好物点击数查询接口
     * @throws
     */
    PageInfoDTO<SearchDiscoveryGoodsClickRespDTO> searchDiscoveryGoodsClick(SearchDiscoveryGoodsClickReqDTO searchDiscoveryGoodsClickReqDTO);

    /**
     * 分享记录查询
     *
     * @param reqDTO
     * @return
     */
    PageInfoDTO<DiscoveryShareClickRespDTO> searchDiscoveryShareClick(SearchDiscoveryShareClickReqDTO reqDTO);

    void staticDiscoveryContentDetail(Date startTime, Date endTime);

    /**
     * 发现页事件另存
     *
     * @param collectSpmBO
     */
    void saveDiscovryEvent(CollectSpmBO collectSpmBO);

    /**
     * 删除点击数据
     *
     * @param startTime
     * @param endTime
     */
    void deleteDiscoryClickData(Date startTime, Date endTime);

    /**
     * 统计内容分享数
     *
     * @param collectSpmBO
     */
    void staticContentShareCount(CollectSpmBO collectSpmBO);

    /**
     * 发送发现页任务mq
     *
     * @param collectSpmBO
     */
    void sendDiscoveryTaskMq(CollectSpmBO collectSpmBO);

    /**
     * 统计发现频道行为数量
     *
     * @param reqDTO
     * @return
     */
    DiscoveryBehaviorCountRespDTO countDiscoveryBehavior(DiscoveryBehaviorCountReqDTO reqDTO);

    /**
     * 统计报表
     */
    void statisticsDiscoveryReport();
}
