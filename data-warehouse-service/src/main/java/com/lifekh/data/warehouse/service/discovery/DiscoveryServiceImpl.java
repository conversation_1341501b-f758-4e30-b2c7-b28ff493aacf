package com.lifekh.data.warehouse.service.discovery;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.chaos.discovery.review.api.ContentInfoFacade;
import com.chaos.discovery.review.api.dto.content.req.ContentNoReqDTO;
import com.chaos.discovery.review.api.dto.content.req.ContentShareCountIncrReqDTO;
import com.chaos.discovery.review.api.dto.content.req.ContentStaticListReqDTO;
import com.chaos.discovery.review.api.dto.content.req.ContentStatisticsReqDTO;
import com.chaos.discovery.review.api.dto.content.resp.ContentStaticInfoDetailRespDTO;
import com.chaos.discovery.review.api.dto.content.resp.ContentStatisticsRespDTO;
import com.chaos.discovery.review.api.enums.ContentStatusEnum;
import com.chaos.marketing.api.dto.task.mq.BrowseDiscoveryMQDTO;
import com.chaos.marketing.api.dto.task.mq.SharePageMQDTO;
import com.chaos.message.api.MessageFacade;
import com.chaos.message.api.dto.SendMessageDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.discovery.DiscoveryBehaviorCountReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryGoodsClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryShareClickReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryBehaviorCountRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryShareClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryGoodsClickRespDTO;
import com.lifekh.data.warehouse.bo.ReportDiscoveryBO;
import com.lifekh.data.warehouse.bo.TagUserBO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.discovery.DiscoveryDetailBO;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dto.ReportDateDTO;
import com.lifekh.data.warehouse.enums.ReportDiscoveryDataTypeEnum;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/2/15 17:24
 * @Version 1.0
 **/
@Service
@Slf4j
public class DiscoveryServiceImpl implements DiscoveryService{

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ContentInfoFacade contentInfoFacade;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private MessageFacade messageFacade;

    //用户端埋点传的是：contentId
    private final String appContentNo = "associatedId";

    private final String contentNo = "contentNo";

    private final String orderNo = "orderNo";

    private final String orderNoKey = "discovery:orderNoKey:";

    private final List<String> orderBusinessNames = Arrays.asList("外卖_下单_discover","团购_下单_discover","电商_下单_discover","话费充值_下单_discover","外卖_购物车_加购_discover","   ","发现页好物点击");

    @Override
    public void moveDiscovryDetailsClick(Date startTime, Date endTime) {
        Criteria criteria = new Criteria();
        criteria.and("createTime").gte(startTime).lt(endTime);
        criteria.and("eventBo.event").is(EventConstant.EVENT_DISCOVRY_DETAILS_CLICK);
        int size = 350;
        //暂时用map缓存发现页内容具体数据，此处有内存溢出风险。
        Map<String, ContentStaticInfoDetailRespDTO> contenDetailMap = new HashMap<>();
        for (int page=0; ;page ++){
            Query query = new Query(criteria);
            query.with(PageRequest.of(page,size));
            List<DiscoveryDetailBO> discoveryDetailBOS = mongoTemplate.find(query, DiscoveryDetailBO.class,MongoDbCollectonName.COLLECT_BURIED_POINT_DISCOVRY);
            if (Objects.isNull(discoveryDetailBOS) || discoveryDetailBOS.isEmpty()){
                break;
            }
            for (DiscoveryDetailBO discoveryDetailBO:discoveryDetailBOS){
                String contentNo = (String)discoveryDetailBO.getExt().get(appContentNo);
                if (StringUtils.isEmpty(contentNo)){
                    continue;
                }
                if (contenDetailMap.size() > 200){
                    //清空一次，避免内存溢出
                    contenDetailMap.clear();
                }
                ContentStaticInfoDetailRespDTO contentStaticInfoDetailRespDTO = contenDetailMap.get(contentNo);
                if (Objects.isNull(contentStaticInfoDetailRespDTO)){
                    ContentNoReqDTO contentNoReqDTO = new ContentNoReqDTO();
                    contentNoReqDTO.setContentNo(contentNo);
                    contentStaticInfoDetailRespDTO = contentInfoFacade.getContentStaticInfo(contentNoReqDTO);
                }
                if (Objects.isNull(contentStaticInfoDetailRespDTO)) {
                    log.info("查询内容信息为空，contentNo：{}", contentNo);
                    continue;
                }
                DiscoveryDetailBO discoveryDetailBO1 = this.buildDiscoveryDetailBO(discoveryDetailBO,contentStaticInfoDetailRespDTO);
                if (Objects.nonNull(discoveryDetailBO1.getUserInfoBo())
                        && StringUtils.isEmpty(discoveryDetailBO1.getUserInfoBo().getLoginName())
                        && StringUtils.isNotEmpty(discoveryDetailBO1.getUserInfoBo().getOperatorNo())) {
                    //只传操作员编号，未传loginName情况下，查询获取loginName
                    TagUserBO tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is(discoveryDetailBO1.getUserInfoBo().getOperatorNo())),TagUserBO.class, TagUserBO.TABLE_NAME);
                    discoveryDetailBO1.getUserInfoBo().setLoginName(tagUserBO.getMobile());
                }
                mongoTemplate.save(discoveryDetailBO1,MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);
                contenDetailMap.put(contentNo,contentStaticInfoDetailRespDTO);
            }
        }
    }


    @Override
    public void moveDiscovryGoodsClick(Date startTime, Date endTime) {
        //事件表映射
        Map<String, String> eventTableMap = new HashMap<>();
        eventTableMap.put(EventConstant.EVENT_DISCOVRY_GOODS_CLICK, MongoDbCollectonName.COLLECT_BURIED_POINT_DISCOVRY);
        eventTableMap.put(EventConstant.EVENT_ADD_SHOPCART, MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER);
        eventTableMap.put(EventConstant.EVENT_ORDER_SUBMIT, MongoDbCollectonName.COLLECT_BURIED_POINT_ORDER);

        eventTableMap.forEach((event, table) -> {
            Criteria criteria = new Criteria();
            criteria.and("createTime").gte(startTime).lt(endTime)
                    .and("eventBo.event").is(event);

            int size = 350;
            //暂时用map缓存发现页内容具体数据，此处有内存溢出风险。
            Map<String, ContentStaticInfoDetailRespDTO> contenDetailMap = new HashMap<>();
            for (int page=0; ;page ++){
                Query query = new Query(criteria);
                query.with(PageRequest.of(page,size));
                List<DiscoveryDetailBO> discoveryDetailBOS = mongoTemplate.find(query, DiscoveryDetailBO.class, table);
                if (Objects.isNull(discoveryDetailBOS) || discoveryDetailBOS.isEmpty()){
                    break;
                }
                for (DiscoveryDetailBO discoveryDetailBO:discoveryDetailBOS){
                    String contentNo = (String)discoveryDetailBO.getExt().get(appContentNo);
                    if (StringUtils.isEmpty(contentNo)
                            || StringUtils.isBlank(discoveryDetailBO.getBusinessName())
                            || !orderBusinessNames.contains(discoveryDetailBO.getBusinessName())){
                        continue;
                    }

                    if (contenDetailMap.size() > 200){
                        //清空一次，避免内存溢出
                        contenDetailMap.clear();
                    }
                    if (EventConstant.EVENT_ORDER_SUBMIT.equals(discoveryDetailBO.getEventBo().getEvent())){
                        try {
                            //下单事件，需要过滤因网络重试产生的重复订单号事件
                            Object orderNoObject = discoveryDetailBO.getExt().get("orderNo");
                            if (Objects.nonNull(orderNoObject)) {
                                String orderNo = orderNoObject.toString();
                                String key = this.orderNoKey + EventConstant.EVENT_ORDER_SUBMIT + ":" + orderNo;
                                String reOrderNo = redisTemplate.opsForValue().get(key);
                                if (StringUtils.isNotEmpty(reOrderNo)) {
                                    continue;
                                } else {
                                    redisTemplate.opsForValue().set(key, orderNo, 10, TimeUnit.MINUTES);
                                }
                            }
                        } catch (Exception e) {
                            log.error("重复订单号缓存查询异常", e);
                        }
                    }
                    ContentStaticInfoDetailRespDTO contentStaticInfoDetailRespDTO = contenDetailMap.get(contentNo);
                    if (Objects.isNull(contentStaticInfoDetailRespDTO)){
                        ContentNoReqDTO contentNoReqDTO = new ContentNoReqDTO();
                        contentNoReqDTO.setContentNo(contentNo);
                        contentStaticInfoDetailRespDTO = contentInfoFacade.getContentStaticInfo(contentNoReqDTO);
                    }
                    if (Objects.isNull(contentStaticInfoDetailRespDTO)){
                        log.info("查询内容信息为空，contentNo：{}", contentNo);
                        continue;
                    }
                    DiscoveryDetailBO discoveryDetailBO1 = this.buildDiscoveryDetailBO(discoveryDetailBO,contentStaticInfoDetailRespDTO);
                    if (Objects.nonNull(discoveryDetailBO1.getUserInfoBo())
                            && StringUtils.isEmpty(discoveryDetailBO1.getUserInfoBo().getLoginName())
                            && StringUtils.isNotEmpty(discoveryDetailBO1.getUserInfoBo().getOperatorNo())) {
                        //只传操作员编号，未传loginName情况下，查询获取loginName
                        TagUserBO tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is(discoveryDetailBO1.getUserInfoBo().getOperatorNo())),TagUserBO.class, TagUserBO.TABLE_NAME);
                        discoveryDetailBO1.getUserInfoBo().setLoginName(tagUserBO.getMobile());
                    }
                    mongoTemplate.save(discoveryDetailBO1,MongoDbCollectonName.GOODS_EVENT);
                    contenDetailMap.put(contentNo,contentStaticInfoDetailRespDTO);
                }
            }
        });
    }

    @Override
    public PageInfoDTO<SearchDiscoveryClickRespDTO> searchDiscoveryClickDetail(SearchDiscoveryClickReqDTO searchDiscoveryClickReqDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentNo())) {
            criteria.and("contentNo").is(searchDiscoveryClickReqDTO.getContentNo());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentBusinessLine())) {
            criteria.and("contentBusinessLine").is(searchDiscoveryClickReqDTO.getContentBusinessLine());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentCategory())) {
            criteria.and("contentCategoryList").is(searchDiscoveryClickReqDTO.getContentCategory());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentType())) {
            criteria.and("contentType").is(searchDiscoveryClickReqDTO.getContentType());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentTitle())) {
            criteria.and("contentTitle").regex("^.*" + searchDiscoveryClickReqDTO.getContentTitle() + ".*$");
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getLoginName())) {
            criteria.and("userInfoBo.loginName").regex("^.*" + searchDiscoveryClickReqDTO.getLoginName() + ".*$");
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getContentLanguage())) {
            criteria.and("contentLanguage").regex("^.*" + searchDiscoveryClickReqDTO.getContentLanguage() + ".*$");
        }
        if (Objects.nonNull(searchDiscoveryClickReqDTO.getStartTime()) && Objects.nonNull(searchDiscoveryClickReqDTO.getEndTime())){
            criteria.and("recordTime").gte(searchDiscoveryClickReqDTO.getStartTime())
                    .lt(searchDiscoveryClickReqDTO.getEndTime());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryClickReqDTO.getCreateBy())) {
            criteria.and("createBy").regex("^.*" + searchDiscoveryClickReqDTO.getCreateBy() + ".*$");
        }
        if (Objects.nonNull(searchDiscoveryClickReqDTO.getContentCreateStartTime()) && Objects.nonNull(searchDiscoveryClickReqDTO.getContentCreateEndTime())){
            criteria.and("contentCreateTime").gte(searchDiscoveryClickReqDTO.getContentCreateStartTime())
                    .lt(searchDiscoveryClickReqDTO.getContentCreateEndTime());
        }
        if (searchDiscoveryClickReqDTO.getHasGoods() != null) {
            if (searchDiscoveryClickReqDTO.getHasGoods()) {
                criteria.and("goodsLink").nin("", null);
            } else {
                criteria.and("goodsLink").in("", null);
            }
        }

        Query query = new Query(criteria);

        long total = mongoTemplate.count(query,MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);

        query.with(Sort.by(Sort.Direction.DESC,"recordTime"));
        query.skip((searchDiscoveryClickReqDTO.getPageNum() - 1) * searchDiscoveryClickReqDTO.getPageSize());
        query.limit(searchDiscoveryClickReqDTO.getPageSize());
        List<DiscoveryDetailBO> discoveryDetailBOS =mongoTemplate.find(query,DiscoveryDetailBO.class,MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);

        PageInfoDTO<SearchDiscoveryClickRespDTO> target = new PageInfoDTO<>();
        List<SearchDiscoveryClickRespDTO> searchDiscoveryClickRespDTOList = new ArrayList<>();
        for (DiscoveryDetailBO discoveryDetailBO:discoveryDetailBOS){
            SearchDiscoveryClickRespDTO searchDiscoveryClickRespDTO = new SearchDiscoveryClickRespDTO();
            searchDiscoveryClickRespDTO.setContentNo(discoveryDetailBO.getContentNo());
            searchDiscoveryClickRespDTO.setContentBusinessLine(discoveryDetailBO.getContentBusinessLine());
            searchDiscoveryClickRespDTO.setContentCategory(discoveryDetailBO.getContentCategory());
            searchDiscoveryClickRespDTO.setContentCategoryList(discoveryDetailBO.getContentCategoryList());
            searchDiscoveryClickRespDTO.setContentType(discoveryDetailBO.getContentType());
            searchDiscoveryClickRespDTO.setContentTitle(discoveryDetailBO.getContentTitle());
            searchDiscoveryClickRespDTO.setLoginName(discoveryDetailBO.getUserInfoBo().getLoginName());
            searchDiscoveryClickRespDTO.setDeviceId(discoveryDetailBO.getDeviceInfoBo().getDeviceId());
            searchDiscoveryClickRespDTO.setContentLanguage(discoveryDetailBO.getContentLanguage());
            searchDiscoveryClickRespDTO.setRecordTime(discoveryDetailBO.getRecordTime());
            searchDiscoveryClickRespDTO.setCreateBy(discoveryDetailBO.getCreateBy());
            searchDiscoveryClickRespDTO.setContentCreateTime(discoveryDetailBO.getContentCreateTime());
            searchDiscoveryClickRespDTOList.add(searchDiscoveryClickRespDTO);
        }
        target.setPageSize(searchDiscoveryClickReqDTO.getPageSize());
        target.setPageNum(searchDiscoveryClickReqDTO.getPageNum());
        target.setSize(discoveryDetailBOS.size());
        target.setTotal(total);
        target.setPages(((int)total + searchDiscoveryClickReqDTO.getPageSize() - 1)/searchDiscoveryClickReqDTO.getPageSize());
        target.setList(searchDiscoveryClickRespDTOList);
        return target;
    }

    @Override
    public PageInfoDTO<SearchDiscoveryGoodsClickRespDTO> searchDiscoveryGoodsClick(SearchDiscoveryGoodsClickReqDTO searchDiscoveryGoodsClickReqDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentNo())) {
            criteria.and("contentNo").is(searchDiscoveryGoodsClickReqDTO.getContentNo());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentBusinessLine())) {
            criteria.and("contentBusinessLine").is(searchDiscoveryGoodsClickReqDTO.getContentBusinessLine());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentCategory())) {
            criteria.and("contentCategoryList").is(searchDiscoveryGoodsClickReqDTO.getContentCategory());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentType())) {
            criteria.and("contentType").is(searchDiscoveryGoodsClickReqDTO.getContentType());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentTitle())) {
            criteria.and("contentTitle").regex("^.*" + searchDiscoveryGoodsClickReqDTO.getContentTitle() + ".*$");
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getLoginName())) {
            criteria.and("userInfoBo.loginName").regex("^.*" + searchDiscoveryGoodsClickReqDTO.getLoginName() + ".*$");
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getContentLanguage())) {
            criteria.and("contentLanguage").regex("^.*" + searchDiscoveryGoodsClickReqDTO.getContentLanguage() + ".*$");
        }
        if (Objects.nonNull(searchDiscoveryGoodsClickReqDTO.getStartTime()) && Objects.nonNull(searchDiscoveryGoodsClickReqDTO.getEndTime())){
            criteria.and("recordTime").gte(searchDiscoveryGoodsClickReqDTO.getStartTime())
                    .lt(searchDiscoveryGoodsClickReqDTO.getEndTime());
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getCreateBy())) {
            criteria.and("createBy").regex("^.*" + searchDiscoveryGoodsClickReqDTO.getCreateBy() + ".*$");
        }
        if (StringUtils.isNotEmpty(searchDiscoveryGoodsClickReqDTO.getEventType())) {
            criteria.and("eventBo.event").is(searchDiscoveryGoodsClickReqDTO.getEventType());
        }
        if (Objects.nonNull(searchDiscoveryGoodsClickReqDTO.getContentCreateStartTime()) && Objects.nonNull(searchDiscoveryGoodsClickReqDTO.getContentCreateEndTime())){
            criteria.and("contentCreateTime").gte(searchDiscoveryGoodsClickReqDTO.getContentCreateStartTime())
                    .lt(searchDiscoveryGoodsClickReqDTO.getContentCreateEndTime());
        }
        Query query = new Query(criteria);

        long total = mongoTemplate.count(query,MongoDbCollectonName.GOODS_EVENT);

        query.with(Sort.by(Sort.Direction.DESC,"recordTime"));
        query.skip((searchDiscoveryGoodsClickReqDTO.getPageNum() - 1) * searchDiscoveryGoodsClickReqDTO.getPageSize());
        query.limit(searchDiscoveryGoodsClickReqDTO.getPageSize());
        List<DiscoveryDetailBO> discoveryDetailBOS =mongoTemplate.find(query,DiscoveryDetailBO.class,MongoDbCollectonName.GOODS_EVENT);

        PageInfoDTO<SearchDiscoveryGoodsClickRespDTO> target = new PageInfoDTO<>();
        List<SearchDiscoveryGoodsClickRespDTO> searchDiscoveryGoodsClickRespDTOList = new ArrayList<>();
        for (DiscoveryDetailBO discoveryDetailBO:discoveryDetailBOS){
            SearchDiscoveryGoodsClickRespDTO searchDiscoveryGoodsClick = new SearchDiscoveryGoodsClickRespDTO();
            searchDiscoveryGoodsClick.setContentNo(discoveryDetailBO.getContentNo());
            searchDiscoveryGoodsClick.setContentBusinessLine(discoveryDetailBO.getContentBusinessLine());
            searchDiscoveryGoodsClick.setContentCategory(discoveryDetailBO.getContentCategory());
            searchDiscoveryGoodsClick.setContentCategoryList(discoveryDetailBO.getContentCategoryList());
            searchDiscoveryGoodsClick.setContentType(discoveryDetailBO.getContentType());
            searchDiscoveryGoodsClick.setContentTitle(discoveryDetailBO.getContentTitle());
            searchDiscoveryGoodsClick.setGoodsName(discoveryDetailBO.getGoodsName());
            searchDiscoveryGoodsClick.setGoodsType(discoveryDetailBO.getGoodsType());
            searchDiscoveryGoodsClick.setGoodsLink(discoveryDetailBO.getGoodsLink());
            searchDiscoveryGoodsClick.setEventType(discoveryDetailBO.getEventBo().getEvent());
            searchDiscoveryGoodsClick.setOrderNo((String)discoveryDetailBO.getExt().get(orderNo));
            searchDiscoveryGoodsClick.setLoginName(discoveryDetailBO.getUserInfoBo().getLoginName());
            searchDiscoveryGoodsClick.setDeviceId(discoveryDetailBO.getDeviceInfoBo().getDeviceId());
            searchDiscoveryGoodsClick.setContentLanguage(discoveryDetailBO.getContentLanguage());
            searchDiscoveryGoodsClick.setRecordTime(discoveryDetailBO.getRecordTime());
            searchDiscoveryGoodsClick.setCreateBy(discoveryDetailBO.getCreateBy());
            searchDiscoveryGoodsClick.setContentCreateTime(discoveryDetailBO.getContentCreateTime());
            searchDiscoveryGoodsClickRespDTOList.add(searchDiscoveryGoodsClick);
        }
        target.setPageSize(searchDiscoveryGoodsClickReqDTO.getPageSize());
        target.setPageNum(searchDiscoveryGoodsClickReqDTO.getPageNum());
        target.setSize(discoveryDetailBOS.size());
        target.setTotal(total);
        target.setPages(((int)total + searchDiscoveryGoodsClickReqDTO.getPageSize() - 1)/searchDiscoveryGoodsClickReqDTO.getPageSize());
        target.setList(searchDiscoveryGoodsClickRespDTOList);
        return target;
    }

    @Override
    public PageInfoDTO<DiscoveryShareClickRespDTO> searchDiscoveryShareClick(SearchDiscoveryShareClickReqDTO reqDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getContentNo())) {
            criteria.and("contentNo").is(reqDTO.getContentNo());
        }
        if (StringUtils.isNotEmpty(reqDTO.getContentBusinessLine())) {
            criteria.and("contentBusinessLine").is(reqDTO.getContentBusinessLine());
        }
        if (StringUtils.isNotEmpty(reqDTO.getContentCategory())) {
            criteria.and("contentCategoryList").is(reqDTO.getContentCategory());
        }
        if (StringUtils.isNotEmpty(reqDTO.getContentType())) {
            criteria.and("contentType").is(reqDTO.getContentType());
        }
        if (StringUtils.isNotEmpty(reqDTO.getContentTitle())) {
            criteria.and("contentTitle").regex("^.*" + reqDTO.getContentTitle() + ".*$");
        }
        if (StringUtils.isNotEmpty(reqDTO.getLoginName())) {
            criteria.and("userInfoBo.loginName").regex("^.*" + reqDTO.getLoginName() + ".*$");
        }
        if (StringUtils.isNotEmpty(reqDTO.getContentLanguage())) {
            criteria.and("contentLanguage").regex("^.*" + reqDTO.getContentLanguage() + ".*$");
        }
        if (Objects.nonNull(reqDTO.getStartTime()) && Objects.nonNull(reqDTO.getEndTime())) {
            criteria.and("recordTime").gte(reqDTO.getStartTime()).lt(reqDTO.getEndTime());
        }
        if (StringUtils.isNotEmpty(reqDTO.getCreateBy())) {
            criteria.and("createBy").regex("^.*" + reqDTO.getCreateBy() + ".*$");
        }
        if (Objects.nonNull(reqDTO.getContentCreateStartTime()) && Objects.nonNull(reqDTO.getContentCreateEndTime())){
            criteria.and("contentCreateTime").gte(reqDTO.getContentCreateStartTime())
                    .lt(reqDTO.getContentCreateEndTime());
        }

        Query query = new Query(criteria);
        long total = mongoTemplate.count(query, MongoDbCollectonName.DISCOVERY_SHARE_CLICK);

        query.with(Sort.by(Sort.Direction.DESC, "recordTime"));
        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());
        List<DiscoveryDetailBO> shareDataList = mongoTemplate.find(query, DiscoveryDetailBO.class, MongoDbCollectonName.DISCOVERY_SHARE_CLICK);

        List<DiscoveryShareClickRespDTO> respList = new ArrayList<>();
        for (DiscoveryDetailBO shareData : shareDataList) {
            DiscoveryShareClickRespDTO respDTO = new DiscoveryShareClickRespDTO()
                    .setContentNo(shareData.getContentNo())
                    .setContentBusinessLine(shareData.getContentBusinessLine())
                    .setContentCategoryList(shareData.getContentCategoryList())
                    .setContentType(shareData.getContentType())
                    .setContentTitle(shareData.getContentTitle())
                    .setGoodsName(shareData.getGoodsName())
                    .setGoodsType(shareData.getGoodsType())
                    .setGoodsLink(shareData.getGoodsLink())
                    .setLoginName(shareData.getUserInfoBo().getLoginName())
                    .setDeviceId(shareData.getDeviceInfoBo().getDeviceId())
                    .setContentLanguage(shareData.getContentLanguage())
                    .setRecordTime(shareData.getRecordTime())
                    .setCreateBy(shareData.getCreateBy())
                    .setContentCreateTime(shareData.getContentCreateTime());
            respList.add(respDTO);
        }
        PageInfoDTO<DiscoveryShareClickRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(respList.size());
        target.setTotal(total);
        target.setPages(((int) total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        target.setList(respList);
        return target;
    }

    @Override
    public void staticDiscoveryContentDetail(Date startTime, Date endTime) {
        int size = 100;
        //先获取未结束的内容
        for (int page = 1 ; ; page ++){
            ContentStaticListReqDTO  contentStaticListReqDTO = new ContentStaticListReqDTO();
            contentStaticListReqDTO.setContentStatus(ContentStatusEnum.ENABLE.getCode());
            contentStaticListReqDTO.setPageNum(page);
            contentStaticListReqDTO.setPageSize(size);
            List<ContentStatisticsRespDTO> contentStatisticsRespDTOS =contentInfoFacade.getContentStatisticsList(contentStaticListReqDTO);
            if (Objects.isNull(contentStatisticsRespDTOS) || contentStatisticsRespDTOS.isEmpty()){
                break;
            }
            for (ContentStatisticsRespDTO contentStatisticsRespDTO:contentStatisticsRespDTOS){
                this.countAndUpdateStatic(contentStatisticsRespDTO, startTime, endTime);
            }
        }
        //取昨天结束和下架的内容
        for (int page = 1 ; ; page ++){
            ContentStaticListReqDTO  contentStaticListReqDTO = new ContentStaticListReqDTO();
            contentStaticListReqDTO.setContentStatus(ContentStatusEnum.DISABLE.getCode());
            contentStaticListReqDTO.setStartTime(startTime);
            contentStaticListReqDTO.setEndTime(endTime);
            contentStaticListReqDTO.setPageNum(page);
            contentStaticListReqDTO.setPageSize(size);
            List<ContentStatisticsRespDTO> contentStatisticsRespDTOS =contentInfoFacade.getContentStatisticsList(contentStaticListReqDTO);
            if (Objects.isNull(contentStatisticsRespDTOS) || contentStatisticsRespDTOS.isEmpty()){
                break;
            }
            for (ContentStatisticsRespDTO contentStatisticsRespDTO:contentStatisticsRespDTOS){
                this.countAndUpdateStatic(contentStatisticsRespDTO, startTime, endTime);
            }
        }
    }

    private void countAndUpdateStatic(ContentStatisticsRespDTO contentStatisticsRespDTOS, Date startTime, Date endTime){
        ContentStatisticsReqDTO contentStatisticsReqDTO = new ContentStatisticsReqDTO();
        contentStatisticsReqDTO.setContentNo(contentStatisticsRespDTOS.getContentNo());
        //统计浏览数
        Criteria viewsCriteria = new Criteria();
        viewsCriteria.and("createTime").gte(startTime).lt(endTime);
        viewsCriteria.and(contentNo).is(contentStatisticsRespDTOS.getContentNo());
        Query query = new Query(viewsCriteria);
        long views = mongoTemplate.count(query,MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);
        long updateViews = views + (Objects.isNull(contentStatisticsRespDTOS.getViews()) ? 0 : contentStatisticsRespDTOS.getViews());
        contentStatisticsReqDTO.setViews(updateViews);
        //统计好物点击数
        Criteria clickOfDetailsCriteria = new Criteria();
        clickOfDetailsCriteria.and("createTime").gte(startTime).lt(endTime);
        clickOfDetailsCriteria.and(contentNo).is(contentStatisticsRespDTOS.getContentNo());
        clickOfDetailsCriteria.and("eventBo.event").is(EventConstant.EVENT_DISCOVRY_GOODS_CLICK);
        Query clickOfDetailsQuery = new Query(clickOfDetailsCriteria);
        long clickOfDetails = mongoTemplate.count(clickOfDetailsQuery,MongoDbCollectonName.GOODS_EVENT);
        long updateClickOfDetails = clickOfDetails + (Objects.isNull(contentStatisticsRespDTOS.getClickOfDetails()) ? 0 : contentStatisticsRespDTOS.getClickOfDetails());
        contentStatisticsReqDTO.setClickOfDetails(updateClickOfDetails);
        //统计加购数
        Criteria addToCartsCriteria = new Criteria();
        addToCartsCriteria.and("createTime").gte(startTime).lt(endTime);
        addToCartsCriteria.and(contentNo).is(contentStatisticsRespDTOS.getContentNo());
        addToCartsCriteria.and("eventBo.event").is(EventConstant.EVENT_ADD_SHOPCART);
        Query addToCartsQuery = new Query(addToCartsCriteria);
        long addToCarts = mongoTemplate.count(addToCartsQuery,MongoDbCollectonName.GOODS_EVENT);
        long updateAddToCarts = addToCarts + (Objects.isNull(contentStatisticsRespDTOS.getAddToCarts()) ? 0 : contentStatisticsRespDTOS.getAddToCarts());
        contentStatisticsReqDTO.setAddToCarts(updateAddToCarts);
        //统计下单数
        Criteria ordersCriteria = new Criteria();
        ordersCriteria.and("createTime").gte(startTime).lt(endTime);
        ordersCriteria.and(contentNo).is(contentStatisticsRespDTOS.getContentNo());
        ordersCriteria.and("eventBo.event").is(EventConstant.EVENT_ORDER_SUBMIT);
        Query ordersQuery = new Query(ordersCriteria);
        long orders = mongoTemplate.count(ordersQuery,MongoDbCollectonName.GOODS_EVENT);
        long updateOrders = orders + (Objects.isNull(contentStatisticsRespDTOS.getOrders()) ? 0 : contentStatisticsRespDTOS.getOrders());
        contentStatisticsReqDTO.setOrders(updateOrders);
        contentInfoFacade.updateContentStatic(contentStatisticsReqDTO);
    }

    private DiscoveryDetailBO buildDiscoveryDetailBO(DiscoveryDetailBO discoveryDetailBO,ContentStaticInfoDetailRespDTO contentStaticInfo){
        discoveryDetailBO.setContentNo(contentStaticInfo.getContentNo());
        discoveryDetailBO.setPublishTime(contentStaticInfo.getPublishTime());
        discoveryDetailBO.setContentTitle(contentStaticInfo.getContentTitle());
        discoveryDetailBO.setContentDescribe(contentStaticInfo.getContentDescribe());
        discoveryDetailBO.setGoodsType(contentStaticInfo.getGoodsType());
        discoveryDetailBO.setGoodsName(contentStaticInfo.getGoodsName());
        discoveryDetailBO.setGoodsImages(contentStaticInfo.getGoodsImages());
        discoveryDetailBO.setGoodsLink(contentStaticInfo.getGoodsLink());
        discoveryDetailBO.setContentLanguage(contentStaticInfo.getContentLanguage());
        discoveryDetailBO.setContentBusinessLine(contentStaticInfo.getBusinessLine());
        discoveryDetailBO.setContentCategory(contentStaticInfo.getContentCategory());
        discoveryDetailBO.setContentCategoryList(contentStaticInfo.getContentCategoryList());
        discoveryDetailBO.setContentType(contentStaticInfo.getContentType());
        discoveryDetailBO.setCreateBy(contentStaticInfo.getCreateBy());
        discoveryDetailBO.setContentCreateTime(contentStaticInfo.getCreateTime());
        return discoveryDetailBO;
    }

    @Override
    public void saveDiscovryEvent(CollectSpmBO collectSpmBO) {
        try {
            //发现页埋点事件
            List<String> discovryEvents = Arrays.asList(
                    EventConstant.EVENT_DISCOVRY_DETAILS_CLICK,
                    EventConstant.EVENT_DISCOVRY_GOODS_CLICK,
                    EventConstant.EVENT_ADD_SHOPCART,
                    EventConstant.EVENT_ORDER_SUBMIT);

            if (discovryEvents.contains(collectSpmBO.getEventBo().getEvent())) {
                collectSpmBO.setId(null);
                mongoTemplate.insert(collectSpmBO, MongoDbCollectonName.DISCOVRY_EVENT_RECORD);
            }
        } catch (Exception e) {
            log.error("另存发现页事件出现异常", e);
        }
    }

    @Override
    public void deleteDiscoryClickData(Date startTime, Date endTime) {
        log.info("开始删除发现页点击数据");
        try {
            mongoTemplate.remove(Query.query(Criteria.where("recordTime").gte(startTime).lt(endTime)), MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);
            mongoTemplate.remove(Query.query(Criteria.where("recordTime").gte(startTime).lt(endTime)), MongoDbCollectonName.GOODS_EVENT);
        } catch (Exception e) {
            log.error("删除发现页点击数据出现异常", e);
        }
        log.info("结束删除发现页点击数据");
    }

    @Override
    public void staticContentShareCount(CollectSpmBO collectSpmBO) {
        try {
            if (EventConstant.EVENT_DISCOVER_PAGE_CLICK_SHARE.equals(collectSpmBO.getEventBo().getEvent())) {
                Map<String, Object> ext = collectSpmBO.getExt();
                if (ext != null) {
                    //内容编号
                    Object extId = ext.get("id");
                    if (extId != null) {
                        String contentNo = extId.toString();
                        ContentNoReqDTO contentNoReqDTO = new ContentNoReqDTO();
                        contentNoReqDTO.setContentNo(contentNo);
                        ContentStaticInfoDetailRespDTO contentDetail = contentInfoFacade.getContentStaticInfo(contentNoReqDTO);
                        if (contentDetail != null) {
                            //保存分享记录
                            DiscoveryDetailBO discoveryDetail = new DiscoveryDetailBO();
                            BeanCopierHelper.copyProperties(collectSpmBO, discoveryDetail);
                            this.buildDiscoveryDetailBO(discoveryDetail, contentDetail);
                            mongoTemplate.insert(discoveryDetail, MongoDbCollectonName.DISCOVERY_SHARE_CLICK);

                            //递增分享数
                            ContentShareCountIncrReqDTO reqDTO = new ContentShareCountIncrReqDTO();
                            reqDTO.setContentNo(contentNo);
                            reqDTO.setIncrCount(1L);
                            contentInfoFacade.incrContentShareCount(reqDTO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("统计发现页分享数出现异常, 埋点ID: {}", collectSpmBO.getId(), e);
        }
    }

    @Override
    public void sendDiscoveryTaskMq(CollectSpmBO collectSpmBO) {
        try {
            String operatorNo = collectSpmBO.getUserInfoBo().getOperatorNo();
            if (StringUtils.isNotBlank(operatorNo)) {
                if (EventConstant.EVENT_DISCOVER_PAGE_CLICK_SHARE.equals(collectSpmBO.getEventBo().getEvent())) {
                    //发现频道分享
                    String pageUrl = null;
                    Map<String, Object> ext = collectSpmBO.getExt();
                    if (ext != null) {
                        pageUrl = MapUtil.getStr(ext, "url");
                    }
                    SharePageMQDTO sharePageMQDTO = new SharePageMQDTO();
                    sharePageMQDTO.setOperatorNo(operatorNo);
                    sharePageMQDTO.setPageChannel("DISCOVERY");
                    sharePageMQDTO.setPageUrl(pageUrl);

                    SendMessageDTO sendMessageDTO = new SendMessageDTO();
                    sendMessageDTO.setMessageBody(FastJsonUtil.objectToJson(sharePageMQDTO));
                    sendMessageDTO.setConsumerQueue(SharePageMQDTO.topic);
                    sendMessageDTO.setBusinessUnique(IdUtil.simpleUUID());
                    messageFacade.saveAndSendMessage(sendMessageDTO);
                }
            }
        } catch (Exception e) {
            log.error("发送发现频道任务mq异常，eventId: {}", collectSpmBO.getId(), e);
        }
    }

    @Override
    public DiscoveryBehaviorCountRespDTO countDiscoveryBehavior(DiscoveryBehaviorCountReqDTO reqDTO) {
        Long viewCount = 0L;
        Long shareCount = 0L;
        Date now = new Date();

        //按天查
        for (int i = 1; i <= reqDTO.getDays(); i++) {
            Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -i));
            Date endTime = DateUtil.endOfDay(startTime);

            Criteria criteria = Criteria.where("recordTime").gte(startTime).lte(endTime)
                    .and("contentLanguage").is(reqDTO.getLanguage())
                    .and("contentCategoryList").is(reqDTO.getCategoryNo());

            viewCount += mongoTemplate.count(Query.query(criteria), MongoDbCollectonName.DISCOVERY_DETAIL_CLICK);
            shareCount += mongoTemplate.count(Query.query(criteria), MongoDbCollectonName.DISCOVERY_SHARE_CLICK);
        }

        return new DiscoveryBehaviorCountRespDTO()
                .setLanguage(reqDTO.getLanguage())
                .setCategoryNo(reqDTO.getCategoryNo())
                .setViewCount(viewCount.intValue())
                .setShareCount(shareCount.intValue());
    }

    @Override
    public void statisticsDiscoveryReport() {
        Arrays.asList(1, 7, 30).forEach(days -> {
            ReportDateDTO dateDTO = ReportDateDTO.recent(days);

            //统计浏览数
            try {
                Criteria criteria = Criteria.where(ClickReportConstant.RECORD_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime());
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group("contentNo", "contentTitle", "contentLanguage", "contentBusinessLine")
                                .first("contentNo").as(ReportDiscoveryBO.CONTENT_NO)
                                .first("contentTitle").as(ReportDiscoveryBO.CONTENT_TITLE)
                                .first("contentLanguage").as(ReportDiscoveryBO.CONTENT_LANGUAGE)
                                .first("contentBusinessLine").as(ReportDiscoveryBO.CONTENT_BUSINESS_LINE)
                                .count().as(ReportDiscoveryBO.COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportDiscoveryBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DISCOVERY_DETAIL_CLICK, ReportDiscoveryBO.class).getMappedResults();
                reports.forEach(report -> {
                    report.setDataTime(dateDTO.getDataTime())
                            .setCreateTime(new Date())
                            .setDataType(ReportDiscoveryDataTypeEnum.VIEW_COUNT.getCode())
                            .setDataTypeName(ReportDiscoveryDataTypeEnum.VIEW_COUNT.getMessage())
                            .setDays(days);
                    mongoTemplate.save(report);
                });
            } catch (Exception e) {
                log.error("统计发现频道浏览数出现异常, days: {}", days, e);
            }

            //统计好物点击数
            try {
                Criteria criteria = Criteria.where(ClickReportConstant.RECORD_TIME).gte(dateDTO.getStartTime()).lte(dateDTO.getEndTime())
                        .and(ClickReportConstant.EVENT).is(EventConstant.EVENT_DISCOVRY_GOODS_CLICK);
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group("contentNo", "contentTitle", "contentLanguage", "contentBusinessLine")
                                .first("contentNo").as(ReportDiscoveryBO.CONTENT_NO)
                                .first("contentTitle").as(ReportDiscoveryBO.CONTENT_TITLE)
                                .first("contentLanguage").as(ReportDiscoveryBO.CONTENT_LANGUAGE)
                                .first("contentBusinessLine").as(ReportDiscoveryBO.CONTENT_BUSINESS_LINE)
                                .count().as(ReportDiscoveryBO.COUNT))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
                List<ReportDiscoveryBO> reports = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.GOODS_EVENT, ReportDiscoveryBO.class).getMappedResults();
                reports.forEach(report -> {
                    report.setDataTime(dateDTO.getDataTime())
                            .setCreateTime(new Date())
                            .setDataType(ReportDiscoveryDataTypeEnum.GOODS_CLICK_COUNT.getCode())
                            .setDataTypeName(ReportDiscoveryDataTypeEnum.GOODS_CLICK_COUNT.getMessage())
                            .setDays(days);
                    mongoTemplate.save(report);
                });
            } catch (Exception e) {
                log.error("统计发现频道好物点击数出现异常, days: {}", days, e);
            }
        });
    }

}
