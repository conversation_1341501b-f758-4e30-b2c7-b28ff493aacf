package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.collect.req.OtherDataCollectionListReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.service.collection.DataCollectionService;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户数据采集
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.DATA_WAREHOUSE_COLLECTION_OTHER_MQ_TOPIC, consumerGroup = "${spring.application.name}", consumeThreadMax = 1)
public class DataCollectionOtherMqConsumer implements RocketMQListener<OtherDataCollectionListReqDTO> {

    @Autowired
    private DataCollectionService dataCollectionService;

    @Override
    public void onMessage(OtherDataCollectionListReqDTO message) {
        log.info("开始用户其它事件数据采集:{}", message);
        try {
            for (StandardDataCollectionReqDTO standardDataCollectionReqDTO : message.getOtherCollectionDatas()) {
                if (standardDataCollectionReqDTO == null) {
                    continue;
                }
                dataCollectionService.saveStandardCollectionData(standardDataCollectionReqDTO);
            }

            //首页流量埋点临时表
            dataCollectionService.collectHomePageData(message.getOtherCollectionDatas());
        } catch (Exception e) {
            log.error("用户数据采集消费异常", e);
        }
        log.info("结束用户其它事件数据采集:{}", message);
    }
}
