package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.DiscoveryContentFacade;
import com.lifekh.data.warehouse.api.req.discovery.DiscoveryBehaviorCountReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryGoodsClickReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.SearchDiscoveryShareClickReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryBehaviorCountRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.DiscoveryShareClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryClickRespDTO;
import com.lifekh.data.warehouse.api.resp.discovery.SearchDiscoveryGoodsClickRespDTO;
import com.lifekh.data.warehouse.service.discovery.DiscoveryService;
import com.outstanding.framework.core.PageInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/2/18 9:31
 * @Version 1.0
 **/
@Service("discoveryContentFacade")
public class DiscoveryContentFacadeImpl implements DiscoveryContentFacade {

    @Autowired
    DiscoveryService discoveryService;

    @Override
    public PageInfoDTO<SearchDiscoveryClickRespDTO> searchDiscoveryClickDetail(SearchDiscoveryClickReqDTO searchDiscoveryClickReqDTO) {
        return discoveryService.searchDiscoveryClickDetail(searchDiscoveryClickReqDTO);
    }

    @Override
    public PageInfoDTO<SearchDiscoveryGoodsClickRespDTO> searchDiscoveryGoodsClick(SearchDiscoveryGoodsClickReqDTO searchDiscoveryGoodsClickReqDTO) {
        return discoveryService.searchDiscoveryGoodsClick(searchDiscoveryGoodsClickReqDTO);
    }

    @Override
    public PageInfoDTO<DiscoveryShareClickRespDTO> searchDiscoveryShareClick(SearchDiscoveryShareClickReqDTO reqDTO) {
        return discoveryService.searchDiscoveryShareClick(reqDTO);
    }

    @Override
    public DiscoveryBehaviorCountRespDTO countDiscoveryBehavior(DiscoveryBehaviorCountReqDTO reqDTO) {
        return discoveryService.countDiscoveryBehavior(reqDTO);
    }
}
