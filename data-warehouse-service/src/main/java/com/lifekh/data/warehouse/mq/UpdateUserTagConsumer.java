package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.service.TagUserService;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 更新用户标签消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, consumerGroup = "${spring.application.name}", consumeThreadMax = 2)
public class UpdateUserTagConsumer implements RocketMQListener<UpdateUserTagMQReqDTO> {

    @Autowired
    private TagUserService tagUserService;
    @Override
    public void onMessage(UpdateUserTagMQReqDTO message) {
        log.info("开始消费:{}", message);
        try {
            tagUserService.updateUserTag(message.getList());
        } catch (Exception e) {
            log.error("消费异常", e);
        }
        log.info("结束消费:{}", message);
    }
}
