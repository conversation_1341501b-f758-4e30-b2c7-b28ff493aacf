package com.lifekh.data.warehouse.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.chaos.common.enums.AppIdEnum;
import com.chaos.marketing.api.dto.task.mq.OpenAndLoginWownowMQDTO;
import com.chaos.message.api.MessageFacade;
import com.chaos.message.api.dto.SendMessageDTO;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.UserBehaviorInfoMessageDTO;
import com.lifekh.data.warehouse.api.enums.EventEnum;
import com.lifekh.data.warehouse.api.req.UpdateUserOperatorInfoReqDTO;
import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.ZoneBO;
import com.lifekh.data.warehouse.bo.behavior.*;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dao.UserBehaviorInfoDAO;
import com.lifekh.data.warehouse.dao.UserOperatorInfoDAO;
import com.lifekh.data.warehouse.dao.UserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.service.UserBehaviorInfoService;
import com.lifekh.data.warehouse.service.ZoneTagService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.Decimal128;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserBehaviorInfoServiceImpl implements UserBehaviorInfoService {
    @Autowired
    private UserBehaviorInfoDAO userBehaviorInfoDAO;

    @Autowired
    private UserOperatorInfoDAO userOperatorInfoDAO;

    @Autowired
    private UserOperatorLoginInfoDAO userOperatorLoginInfoDAO;

    @Autowired
    private LocationManage locationManage;

    @Autowired
    private ZoneDAO zoneDAO;

    @Autowired
    private ZoneTagService zoneTagService;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private MessageFacade messageFacade;

    @Override
    public void add(UserBehaviorInfoBO bo) throws PendingException {
        userBehaviorInfoDAO.save(bo);
    }

    @Override
    public void collectBehaviorInfoFromGatewayBuriedPoint(String message) throws PendingException {
        //获取事件
        UserBehaviorInfoMessageDTO messageDTO = JSONObject.parseObject(message, UserBehaviorInfoMessageDTO.class);

        if(Objects.isNull(messageDTO) || Objects.isNull(messageDTO.getUserBehaviorEventDto())) {
            log.warn("无法解析埋点数据");
            return;
        }

        UserBehaviorInfoBO behaviorBo = new UserBehaviorInfoBO();
        //用户映射数据
        UserInfoBO userInfoBo = new UserInfoBO();
        BeanCopierHelper.copyProperties(messageDTO.getUserBehaviorEventDto(), userInfoBo);
        behaviorBo.setUserInfoBo(userInfoBo);

        //非用户数据，不做记录
        if(!AppIdEnum.SUPER_APP.getCode().equals(userInfoBo.getAppId())) {
            return;
        }

        if(StringUtils.isBlank(userInfoBo.getOperatorNo())) {
            String loginName = StringUtils.isNotBlank(userInfoBo.getLoginName()) ? userInfoBo.getLoginName() : (String) messageDTO.getRequestBody().get("loginName");
            loginName = StringUtils.isNotBlank(loginName)?loginName:(String) messageDTO.getRequestBody().get("mobile");
            userInfoBo.setLoginName(loginName);
            try {
                //注册时无法获取操作员编号，先停1秒，提高获取的概率
                TimeUnit.SECONDS.sleep(1L);

                //查询操作员编号
                List<UserOperatorLoginInfoBO> loginBos = userOperatorLoginInfoDAO.findByLoginNameAndAppId(loginName, userInfoBo.getAppId());
                if(CollectionUtils.isNotEmpty(loginBos)) {
                    userInfoBo.setOperatorNo(loginBos.get(0).getOperatorNo());
                }
            } catch (Exception e) {
                log.warn("操作员编号获取失败, loginName:{}, exception:{}", loginName, e.getMessage());
            }
        }

        //应用映射数据
        ApplicationInfoBO applicationInfoBo = new ApplicationInfoBO();
        BeanCopierHelper.copyProperties(messageDTO.getUserBehaviorEventDto(), applicationInfoBo);
        behaviorBo.setApplicationInfoBo(applicationInfoBo);

        //设备映射数据
        DeviceInfoBO deviceInfoBo = new DeviceInfoBO();
        BeanCopierHelper.copyProperties(messageDTO.getUserBehaviorEventDto(), deviceInfoBo);
        behaviorBo.setDeviceInfoBo(deviceInfoBo);

        //事件映射数据
        EventBO eventBo = new EventBO();
        eventBo.setEvent(messageDTO.getUserBehaviorEventDto().getEvent());
        eventBo.setEventName(EventEnum.getMessageByCode(eventBo.getEvent()));
        behaviorBo.setEventBo(eventBo);

        //网络映射数据
        NetworkBO networkBo = new NetworkBO();
        networkBo.setIp(messageDTO.getUserBehaviorEventDto().getIp());
        behaviorBo.setNetworkBo(networkBo);

        //事件类型为空则不记录
        if(StringUtils.isBlank(messageDTO.getUserBehaviorEventDto().getEvent())) {
            return;
        }
        UserBehaviorInfoBO oggBo = new UserBehaviorInfoBO();
        //获取设备信息、用户信息和经纬度
        LocationBO locationBo = new LocationBO();
        Decimal128 longitude = null, latitude = null;
        if(messageDTO.getRequestBody() != null) {
            try {
                JSONObject deviceObject = JSONObject.parseObject((String) messageDTO.getRequestBody().get("deviceInfo"));
                if(Objects.nonNull(deviceObject)) {
                    if (deviceObject.getBigDecimal("longitude") != null && deviceObject.getBigDecimal("latitude") != null) {
                        locationBo.setCoordinates(new Double[]{deviceObject.getBigDecimal("longitude").doubleValue(),
                                deviceObject.getBigDecimal("latitude").doubleValue()});
                        longitude = Decimal128.parse(String.valueOf(deviceObject.getBigDecimal("longitude")));
                        latitude = Decimal128.parse(String.valueOf(deviceObject.getBigDecimal("latitude")));
                    }
                }
            } catch (Exception e) {
                try {
                    String deviceInfo = URLDecoder.decode((String) messageDTO.getRequestBody().get("deviceInfo"),"UTF-8");
                    Map<String,Object> deviceInfoMap = (Map<String, Object>) JSONObject.parse(deviceInfo);
                    if(Objects.nonNull(deviceInfoMap)) {
                        if (deviceInfoMap.get("longitude") != null && deviceInfoMap.get("latitude") != null) {
                            locationBo.setCoordinates(new Double[]{Double.parseDouble((String) deviceInfoMap.get("longitude")) ,
                                    Double.parseDouble((String) deviceInfoMap.get("latitude"))});
                            longitude = Decimal128.parse(String.valueOf(deviceInfoMap.get("longitude")));
                            latitude = Decimal128.parse(String.valueOf(deviceInfoMap.get("latitude")));
                        }
                    }
                } catch (UnsupportedEncodingException e1) {
                    log.warn("经纬度解析异常:" ,e);
                }
            }

            if(messageDTO.getRequestBody().toString().length() >= 3500) {
                behaviorBo.setRequestBody(messageDTO.getRequestBody().toString().substring(0, 3499));
            } else {
                behaviorBo.setRequestBody(messageDTO.getRequestBody().toString());
            }

            //根据经纬度查询区域
            if(longitude != null && latitude != null) {
                //根据经纬度找出对应的省份名称
                LocationDTO locationDTO = locationManage.getNameByPosition(longitude.toString(), latitude.toString());
                if (Objects.nonNull(locationDTO)) {
                    //找出省份对应的编码
                    locationBo.setProvinceName(locationDTO.getProvinceName());
                    List<ZoneBO> zoneList = zoneDAO.findByMsgEn(locationDTO.getProvinceName());
                    locationBo.setProvinceNo(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getCode() : null);
                    locationBo.setProvinceNameKm(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getMsgCb() : null);
                    locationBo.setProvinceNameZh(CollectionUtils.isNotEmpty(zoneList)? zoneList.get(0).getMsgZh() : null);
                    locationBo.setAreaName(locationDTO.getDistinctName());
                }
            }
            behaviorBo.setCreateTime(new Date());
            behaviorBo.setLocationBo(locationBo);

            //查询当天是否已记录REFRESH_TOKEN
            boolean exists = false;
            if(EventEnum.REFRESH_TOKEN.getCode().equalsIgnoreCase(eventBo.getEvent())) {
                Query query = new Query();
                query.addCriteria(Criteria.where("userInfoBo.operatorNo").is(userInfoBo.getOperatorNo()).and("eventBo.event").is(eventBo.getEvent()).and("createTime").gte(DateUtil.dateCalculation(new Date(),  Calendar.DATE, 0)));
                exists = mongoTemplate.exists(query, UserBehaviorInfoBO.class);
            }

            //入库
            if(!exists) {
                userBehaviorInfoDAO.save(behaviorBo);
            }
            log.info("已完成用户数据采集 operatorNo:{}, even:{}", behaviorBo.getUserInfoBo().getOperatorNo(), behaviorBo.getEventBo().getEvent());
        }

        //更新地区标签
//        oggBo.setOperatorNo(userInfoBo.getOperatorNo());
        if (longitude != null && latitude != null ) {
            zoneTagService.updateUserTagOne(behaviorBo);
        }

        //修改用户信息mq
        if(StringUtils.isNotBlank(behaviorBo.getUserInfoBo().getOperatorNo()) && StringUtils.isNotBlank(behaviorBo.getUserInfoBo().getLanguage())) {
            UpdateUserOperatorInfoReqDTO reqDTO = new UpdateUserOperatorInfoReqDTO();
            reqDTO.setOperatorNo(behaviorBo.getUserInfoBo().getOperatorNo());
            reqDTO.setLanguage(behaviorBo.getUserInfoBo().getLanguage());
            rocketMQTemplate.syncSend(Topic.USER_MODIFY_OPERATOR_INFO_TOPIC, JSONObject.toJSONString(reqDTO));
        }
    }

    @Override
    public void sendUserLoginTaskMq(CollectSpmBO collectSpmBO) {
        try {
            if (EventConstant.LOGIN_TASK_EVENT.contains(collectSpmBO.getEventBo().getEvent())
                    && StringUtils.isNotBlank(collectSpmBO.getUserInfoBo().getOperatorNo())) {
                OpenAndLoginWownowMQDTO mqDTO = new OpenAndLoginWownowMQDTO();
                mqDTO.setOperatorNo(collectSpmBO.getUserInfoBo().getOperatorNo());

                SendMessageDTO sendMessageDTO = new SendMessageDTO();
                sendMessageDTO.setMessageBody(FastJsonUtil.objectToJson(mqDTO));
                sendMessageDTO.setConsumerQueue(OpenAndLoginWownowMQDTO.topic);
                sendMessageDTO.setBusinessUnique(IdUtil.simpleUUID());
                messageFacade.saveAndSendMessage(sendMessageDTO);
            }
        } catch (Exception e) {
            log.error("用户登录任务mq发送异常, eventId: {}, eventNo: {}, operatorNo: {}",
                    collectSpmBO.getId(), collectSpmBO.getEventBo().getEvent(), collectSpmBO.getUserInfoBo().getOperatorNo(), e);
        }
    }
}
