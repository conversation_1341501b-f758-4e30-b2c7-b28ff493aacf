package com.lifekh.data.warehouse.controller;

import com.lifekh.data.warehouse.api.req.CmsMerchantAdsPublishReqDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticDetailRespDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticRespDTO;
import com.lifekh.data.warehouse.report.service.MerchantAdsPublishStaticService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 广告统计结果查询
 * @module data-warehouse-collection
 */
@RequestMapping("/warehouse/static")
@RestController
@Slf4j
public class AdsPublishStaticController {

    @Autowired
    MerchantAdsPublishStaticService merchantAdsPublishStaticService;

    /**
     * 查询投放广告统计列表
     * @throws
     */
    @RequestMapping(value = "/seachMerchantNodePlanStaticList.do", method = RequestMethod.POST)
    @ResponseBody
    public PageInfoDTO<MerchantAdsPublishStaticRespDTO> seachMerchantNodePlanStaticList(@RequestBody CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) throws PendingException {
        return merchantAdsPublishStaticService.seachMerchantNodePlanStaticList(cmsMerchantAdsPublishReqDTO);
    }

    /**
     * 查询投放广告统计详情
     * @throws
     */
    @RequestMapping(value = "/seachMerchantNodePlanStaticDetail.do", method = RequestMethod.POST)
    @ResponseBody
    public MerchantAdsPublishStaticDetailRespDTO seachMerchantNodePlanStaticDetail(@RequestBody CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) throws PendingException {
        return merchantAdsPublishStaticService.seachMerchantNodePlanStaticDetail(cmsMerchantAdsPublishReqDTO);
    }
}