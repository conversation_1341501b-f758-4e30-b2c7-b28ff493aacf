package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.AdsLogFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.discovery.AdsViewLogSaveReqDTO;
import com.lifekh.data.warehouse.api.req.discovery.AdsViewLogSearchReqDTO;
import com.lifekh.data.warehouse.api.resp.discovery.AdsViewLogRespDTO;
import com.lifekh.data.warehouse.bo.discovery.AdsViewLogBO;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class AdsLogFacadeImpl implements AdsLogFacade {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void saveViewLog(AdsViewLogSaveReqDTO reqDTO) throws PendingException {
        AdsViewLogBO viewLog = new AdsViewLogBO();
        BeanCopierHelper.copyProperties(reqDTO, viewLog);
        viewLog.setCreateTime(new Date());
        mongoTemplate.insert(viewLog, MongoDbCollectonName.ADS_VIEW_LOG);
    }

    @Override
    public PageInfoDTO<AdsViewLogRespDTO> searchViewLog(AdsViewLogSearchReqDTO reqDTO) throws PendingException {
        List<AdsViewLogRespDTO> datas = new ArrayList<>();

        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(reqDTO.getAdsNo())) {
            criteria.and(AdsViewLogBO.ADS_NO).is(reqDTO.getAdsNo());
        }
        if (StringUtils.isNotBlank(reqDTO.getAdsSubject())) {
            criteria.and(AdsViewLogBO.ADS_SUBJECT).is(reqDTO.getAdsSubject());
        }
        if (StringUtils.isNotBlank(reqDTO.getAdsType())) {
            criteria.and(AdsViewLogBO.ADS_TYPE).is(reqDTO.getAdsType());
        }
        if (StringUtils.isNotBlank(reqDTO.getAdsName())) {
            criteria.and(AdsViewLogBO.ADS_NAME).regex("^.*" + reqDTO.getAdsName() + ".*$");
        }
        if (StringUtils.isNotBlank(reqDTO.getAdsTitle())) {
            criteria.and(AdsViewLogBO.ADS_TITLE).regex("^.*" + reqDTO.getAdsTitle() + ".*$");
        }
        if (StringUtils.isNotBlank(reqDTO.getUseScene())) {
            criteria.and(AdsViewLogBO.USE_SCENE).is(reqDTO.getUseScene());
        }
        if (StringUtils.isNotBlank(reqDTO.getLanguage())) {
            criteria.and(AdsViewLogBO.LANGUAGE).is(reqDTO.getLanguage());
        }
        if (StringUtils.isNotBlank(reqDTO.getOperatorNo())) {
            criteria.and(AdsViewLogBO.OPERATOR_NO).is(reqDTO.getOperatorNo());
        }
        if (StringUtils.isNotBlank(reqDTO.getCreateBy())) {
            criteria.and(AdsViewLogBO.CREATE_BY).regex("^.*" + reqDTO.getCreateBy() + ".*$");
        }
        if (StringUtils.isNotBlank(reqDTO.getLoginName())) {
            criteria.and(AdsViewLogBO.LOGIN_NAME).regex("^.*" + reqDTO.getLoginName() + ".*$");
        }
        if (reqDTO.getStartTime() != null || reqDTO.getEndTime() != null) {
            Criteria timeCriteria = Criteria.where(AdsViewLogBO.RECORD_TIME);
            if (reqDTO.getStartTime() != null) {
                timeCriteria.gte(reqDTO.getStartTime());
            }
            if (reqDTO.getEndTime() != null) {
                timeCriteria.lte(reqDTO.getEndTime());
            }
            criteria.andOperator(timeCriteria);
        }

        Query query = Query.query(criteria);
        long total = mongoTemplate.count(query, MongoDbCollectonName.ADS_VIEW_LOG);
        if (total > 0) {
            query = query.with(Sort.by(Sort.Direction.DESC, AdsViewLogBO.RECORD_TIME))
                    .skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize())
                    .limit(reqDTO.getPageSize());
            datas = mongoTemplate.find(query, AdsViewLogRespDTO.class, MongoDbCollectonName.ADS_VIEW_LOG);
        }
        PageInfoDTO<AdsViewLogRespDTO> pageRes = new PageInfoDTO<>();
        pageRes.setPageSize(reqDTO.getPageSize());
        pageRes.setPageNum(reqDTO.getPageNum());
        pageRes.setSize(datas.size());
        pageRes.setTotal(total);
        pageRes.setPages(((int) total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        pageRes.setList(datas);
        return pageRes;
    }
}
