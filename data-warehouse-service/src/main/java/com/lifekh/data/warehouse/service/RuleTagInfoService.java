package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.dto.resp.TagNoRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.RuleTagInfoDetailRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

public interface RuleTagInfoService {
    /**
     * 新增规则标签
     * @param reqDTO
     */
    void add(RuleTagInfoAddReqDTO reqDTO) throws PendingException;

    /**
     * 编辑规则标签
     * @param reqDTO
     * @throws PendingException
     */
    void edit(RuleTagInfoEditReqDTO reqDTO) throws PendingException;

    /**
     * 规则标签列表
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<RuleTagInfoDetailRespDTO> list(RuleTagInfoListReqDTO reqDTO) throws PendingException;

    /**
     * 详情
     * @return
     * @throws PendingException
     */
    RuleTagInfoDetailRespDTO detail(RuleTagInfoDetailReqDTO reqDTO) throws PendingException;


    /**
     * 新增规则标签V2
     * @param reqDTO
     * @throws PendingException
     */
    TagNoRespDTO addV2(RuleTagInfoAddReqV2DTO reqDTO) throws PendingException;

    /**
     * 编辑标签规则V2
     * @param reqDTO
     * @throws PendingException
     */
    void editV2(RuleTagInfoEditReqV2DTO reqDTO) throws PendingException;
}
