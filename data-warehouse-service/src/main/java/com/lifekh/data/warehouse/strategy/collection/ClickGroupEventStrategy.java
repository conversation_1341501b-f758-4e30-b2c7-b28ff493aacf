package com.lifekh.data.warehouse.strategy.collection;

import com.lifekh.data.warehouse.api.collect.req.CollectSpmReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:01
 * @Version 1.0
 **/
@Component
@Slf4j
public class ClickGroupEventStrategy extends AbstractGenerateEventStrategy {

    public ClickGroupEventStrategy(MongoTemplate mongoTemplate, ZoneDAO zoneDAO, LocationManage locationManage) {
        super(mongoTemplate, zoneDAO, locationManage);
    }

    private final static String separation_char = "@";

    @Override
    public EventGroupEnum getEventGroup() {
        return EventGroupEnum.CLICK;
    }

    @Override
    public void convertSpm(CollectSpmBO collectSpmBO, StandardDataCollectionReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getSpm())) {
            CollectSpmReqDTO spmReqDTO = reqDTO.getSpm();
            collectSpmBO.setChildPage(spmReqDTO.getChildPage());
            collectSpmBO.setAppNo(reqDTO.getAppNo());
            collectSpmBO.setParentPage(spmReqDTO.getParentPage());
            collectSpmBO.setCurrentPage(spmReqDTO.getCurrentPage());
            if (StringUtils.isNotEmpty(spmReqDTO.getCurrentArea())) {
                String[] areaAndLocation = spmReqDTO.getCurrentArea().split(separation_char);
                collectSpmBO.setCurrentArea(areaAndLocation[0]);
                if (areaAndLocation.length == 2) {
                    collectSpmBO.setCurrentAreaLocation(Integer.parseInt(areaAndLocation[1]));
                }
            }
            if (StringUtils.isNotEmpty(spmReqDTO.getNode())) {
                String[] nodeAndLocation = spmReqDTO.getNode().split(separation_char);
                collectSpmBO.setNode(nodeAndLocation[0]);
                if (nodeAndLocation.length == 2) {
                    collectSpmBO.setCurrentNodeLocation(Integer.parseInt(nodeAndLocation[1]));
                }
            }
            collectSpmBO.setStayTime(spmReqDTO.getStayTime());
        }
    }

    @Override
    public void doAfter(CollectSpmBO collectSpmBO) throws PendingException {

    }
}