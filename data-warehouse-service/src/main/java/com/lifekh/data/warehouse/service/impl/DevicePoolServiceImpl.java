package com.lifekh.data.warehouse.service.impl;

import cn.hutool.core.date.DateUtil;
import com.chaos.common.enums.AppIdEnum;
import com.chaos.common.enums.LanguageEnum;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.req.UpdateUserDeviceInfoReqDTO;
import com.chaos.usercenter.api.dto.req.UserRegisterReqDTO;
import com.chaos.usercenter.api.enums.DeviceTypeEnum;
import com.chaos.usercenter.api.enums.UserLabelNewDeviceEnum;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.DeviceUseStatusEnum;
import com.lifekh.data.warehouse.api.req.DeviceIdReqDTO;
import com.lifekh.data.warehouse.api.resp.DeviceRespDTO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.bo.device.*;
import com.lifekh.data.warehouse.constant.ClickReportConstant;
import com.lifekh.data.warehouse.constant.EventConstant;
import com.lifekh.data.warehouse.dao.DevicePoolDAO;
import com.lifekh.data.warehouse.dao.UserRegisterDeviceDAO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Date: 2022/4/8
 * @Description: 设备池服务实现层
 */
@Slf4j
@Service
public class DevicePoolServiceImpl implements DevicePoolService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DevicePoolDAO devicePoolDAO;
    @Autowired
    private UserOperatorFacade userOperatorFacade;
    @Autowired
    private UserRegisterDeviceDAO userRegisterDeviceDAO;
    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;


    @Override
    public boolean saveDevice(String deviceId, String deviceType, DeviceUseStatusEnum useStatus, Date recordTime, Date lastActiveTime,
                              String language, String province, String district, Double[] coordinates) {
        boolean isNew = false;
        //非ios和安卓设备不处理
        if (!DeviceTypeEnum.isIosOrAndroid(deviceType)) {
            return isNew;
        }

        //经纬度
        Double longitude = null;
        Double latitude = null;
        if (coordinates != null && coordinates.length == 2) {
            longitude = coordinates[0];
            latitude = coordinates[1];
        }

        DevicePoolBO device = null;
        //判断是否已存在该设备
        List<DevicePoolBO> devices = devicePoolDAO.findByDeviceId(deviceId);

        if (devices.isEmpty()) {
            //新增
            device = new DevicePoolBO()
                    .setDeviceId(deviceId)
                    .setDeviceType(StringUtils.upperCase(deviceType))
                    .setUseStatus(useStatus.getCode())
                    .setUseTime(useStatus == DeviceUseStatusEnum.USED ? recordTime : null)
                    .setRecordTime(recordTime)
                    .setLastActiveTime(lastActiveTime)
                    .setLanguage(Optional.ofNullable(language).orElse(LanguageEnum.EN_US.getCode()))
                    .setLongitude(longitude)
                    .setLatitude(latitude)
                    .setProvince(province)
                    .setDistrict(district)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setVersion(0);
            devicePoolDAO.insert(device);
            isNew = true;
        } else {
            //修改
            device = devices.get(0);
            if (DeviceUseStatusEnum.USED == useStatus && DeviceUseStatusEnum.UNUSED.getCode().equals(device.getUseStatus())) {
                device.setUseStatus(useStatus.getCode())
                        .setUseTime(recordTime);
            }

            //最后活跃时间
            if (lastActiveTime != null && device.getLastActiveTime() != null) {
                lastActiveTime = device.getLastActiveTime().before(lastActiveTime) ? lastActiveTime : device.getLastActiveTime();
            }
            device.setLanguage(Optional.ofNullable(language).orElse(LanguageEnum.EN_US.getCode()))
                    .setLongitude(longitude)
                    .setLatitude(latitude)
                    .setProvince(province)
                    .setDistrict(district)
                    .setLastActiveTime(lastActiveTime)
                    .setUpdateTime(new Date())
                    .setVersion(device.getVersion() + 1);
            devicePoolDAO.save(device);
        }
        return isNew;
    }

    @Override
    public void syncDeviceFromMobileToken(Date startTime, Date endTime) {
        //查询指定范围时间的设备ID
        Query query = Query.query(Criteria.where("PROJECT_NAME").is(AppIdEnum.SUPER_APP.getCode())
                .andOperator(Criteria.where("CREATE_TIME").gte(startTime), Criteria.where("CREATE_TIME").lte(endTime)));
        query.fields().include("DEVICE_ID").include("CREATE_TIME").include("APP_NO");
        List<MobileTokenDeviceBO> devices = mongoTemplate.find(query, MobileTokenDeviceBO.class, MongoDbCollectonName.SUP_MOBILE_TOKEN);

        //map去重
        Map<String, Date> deviceMap = new HashMap<>();
        Map<String, DeviceTypeEnum> typeMap = new HashMap<>();
        devices.forEach(d -> {
            if (d.getAppNo() != null && (d.getAppNo() == 10 || d.getAppNo() == 11)) {
                if (d.getAppNo() == 10)
                    typeMap.put(d.getDeviceId(), DeviceTypeEnum.ANDROID);
                else if (d.getAppNo() == 11)
                    typeMap.put(d.getDeviceId(), DeviceTypeEnum.IOS);

                deviceMap.put(d.getDeviceId(), d.getCreateTime());
            }
        });

        //保存
        deviceMap.forEach((k, v) -> {
            DeviceTypeEnum deviceType = typeMap.get(k);
            this.saveDevice(k, deviceType != null ? deviceType.getCode() : null, DeviceUseStatusEnum.USED, v, v, null, null, null, null);
        });
    }

    @Override
    public void saveUserRegisterMsg(UserRegisterReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getDeviceId()) || StringUtils.isBlank(reqDTO.getOperatorNo())) {
            return;
        }
        //先保存到临时表，后定时任务处理
        UserRegisterDeviceBO rb = new UserRegisterDeviceBO()
                .setOperatorNo(reqDTO.getOperatorNo())
                .setDeviceId(reqDTO.getDeviceId())
                .setDeviceType(reqDTO.getDeviceType())
                .setRecordTime(reqDTO.getRegisterTime())
                .setHandleCount(0)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
        mongoTemplate.insert(rb);
    }

    @Override
    public void handleUserRegisterDeviceMsg() {
        try {
            int page = 0;
            int size = 500;
            Sort sort = Sort.by(Sort.Direction.ASC, "recordTime");
            Pageable pageable = PageRequest.of(page, size, sort);
            for (; ; ) {
                Page<UserRegisterDeviceBO> pageRes = userRegisterDeviceDAO.findAll(pageable);
                List<UserRegisterDeviceBO> results = pageRes.getContent();
                if (results.isEmpty()) {
                    break;
                }

                results.forEach(r -> {
                    try {
                        boolean finished = this.doUpdateUserDeviceInfo(r);
                        //超过指定天数进行删除
                        //if (finished || DateUtil.betweenDay(r.getRecordTime(), now, true) > 15) {
                        mongoTemplate.remove(r);
//                    } else {
//                        r.setHandleCount(r.getHandleCount() + 1)
//                                .setUpdateTime(now);
//                        mongoTemplate.save(r);
//                    }
                    } catch (Exception e) {
                        log.error("更新用户设备信息出现异常,operatorNo:{}", r.getOperatorNo(), e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("处理用户注册设备消息出现异常", e);
        }
    }


    /**
     * 更新用户设备信息
     *
     * @param r
     */
    private boolean doUpdateUserDeviceInfo(UserRegisterDeviceBO r) {
        boolean finished = true;
        //非ios和安卓设备不处理
        if (!DeviceTypeEnum.isIosOrAndroid(r.getDeviceType())) {
            return finished;
        }

        UserLabelNewDeviceEnum newDevice = UserLabelNewDeviceEnum.OLD;

        //判断是否新设备，首次处理才判断，避免重复判断
        if (r.getHandleCount() == 0) {
            List<DevicePoolBO> devices = devicePoolDAO.findByDeviceId(r.getDeviceId());
            if (devices.isEmpty()) {
                //设备池中不存在该设备，则保存
                this.saveDevice(r.getDeviceId(), r.getDeviceType(), DeviceUseStatusEnum.USED, r.getRecordTime(), r.getRecordTime(), null, null, null, null);
                newDevice = UserLabelNewDeviceEnum.NEW;
            } else {
                DevicePoolBO device = devices.get(0);
                if (DeviceUseStatusEnum.UNUSED.getCode().equals(device.getUseStatus())) {
                    newDevice = UserLabelNewDeviceEnum.NEW;
                    device.setUseStatus(DeviceUseStatusEnum.USED.getCode())
                            .setUseTime(r.getRecordTime())
                            .setUpdateTime(new Date())
                            .setVersion(device.getVersion() + 1);
                    devicePoolDAO.save(device);
                }
            }
        } else {
            //置空，本次操作不更新
            newDevice = null;
        }

        if (newDevice != null) {
            //更新用户设备信息
            UpdateUserDeviceInfoReqDTO updateDTO = new UpdateUserDeviceInfoReqDTO()
                    .setOperatorNo(r.getOperatorNo())
                    .setNewDevice(newDevice.getCode());
            userOperatorFacade.updateUserDeviceInfo(updateDTO);
        }
        return finished;
    }

    @Override
    public void statisticsDeviceData() {
        log.info("开始统计每日设备数据");
        try {
            Date yesterday = DateUtil.yesterday();
            Date startTime = DateUtil.beginOfDay(yesterday);
            Date endTime = DateUtil.endOfDay(yesterday);
            Date dataTime = DateUtil.beginOfHour(DateUtil.offsetHour(endTime, -12)); //中午12点
            this.statisticsNewDevice(startTime, endTime, dataTime);
            this.statisticsActiveDevice(startTime, endTime, dataTime);
        } catch (Exception e) {
            log.error("统计每日设备数据出现异常", e);
        }
        log.info("结束统计每日设备数据");
    }

    /**
     * 统计每日新增设备数
     *
     * @param startTime
     * @param endTime
     */
    private void statisticsNewDevice(Date startTime, Date endTime, Date dataTime) {
        Criteria criteria = new Criteria();
        criteria.and("recordTime").gte(startTime).lte(endTime);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.count().as("deviceCount")
        );
        AggregationResults<DeviceNewStaticDay> results = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DEVICE_POOL, DeviceNewStaticDay.class);
        List<DeviceNewStaticDay> datas = results.getMappedResults();

        int deviceCount = 0;
        if (CollectionUtils.isNotEmpty(datas)) {
            deviceCount = datas.get(0).getDeviceCount();
        }
        DeviceNewStaticDay deviceNewStaticDay = new DeviceNewStaticDay()
                .setDataDate(DateUtil.formatDate(startTime))
                .setDataTime(dataTime)
                .setDeviceCount(deviceCount)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setVersion(0);
        mongoTemplate.save(deviceNewStaticDay);
    }

    /**
     * 统计每日活跃设备数
     *
     * @param startTime
     * @param endTime
     */
    private void statisticsActiveDevice(Date startTime, Date endTime, Date dataTime) {
        Long activeCount = oracleUserOperatorLoginInfoDAO.queryActiveDeviceByTime(startTime, endTime);
        Long udActiveCount = oracleUserOperatorLoginInfoDAO.queryActiveUdDeviceByTime(startTime, endTime);

        DeviceActiveStaticDay deviceActiveStaticDay = new DeviceActiveStaticDay()
                .setDataDate(DateUtil.formatDate(startTime))
                .setDataTime(dataTime)
                .setActiveCount(activeCount.intValue())
                .setUdActiveCount(udActiveCount.intValue())
                .setCreateTime(new Date());
        mongoTemplate.save(deviceActiveStaticDay);
    }

    @Override
    public void saveDeviceEvent(CollectSpmBO collectSpm) {
        try {
            List<String> events = Arrays.asList(
                    EventConstant.EVENT_NO_FIRST_OPEN,
                    EventConstant.EVENT_NO_SESSION_START,
                    EventConstant.EVENT_NO_SESSION_END);
            if (events.contains(collectSpm.getEventBo().getEvent())
                    && AppIdEnum.SUPER_APP.getCode().equals(collectSpm.getUserInfoBo().getAppId())
                    && DeviceTypeEnum.isIosOrAndroid(collectSpm.getDeviceInfoBo().getDeviceType())) {
                collectSpm.setId(null);
                mongoTemplate.insert(collectSpm, MongoDbCollectonName.DEVICE_EVENT_RECORD);
            }
        } catch (Exception e) {
            log.error("另存设备事件异常", e);
        }
    }

    @Override
    public void handleDeviceEvent() {
        log.info("开始处理设备埋点事件");
        Date now = new Date();
        String nowStr = DateUtil.formatDateTime(now);
        Date startTime = DateUtil.offsetHour(now, -1);
        Date todayStartTime = DateUtil.beginOfDay(now);
        int currPage = 0;
        try {
            Criteria criteria = Criteria.where(ClickReportConstant.CREATE_TIME).gte(startTime).lte(now);
            int size = 500;
            for (int page = 0; ; page++) {
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(criteria),
                        Aggregation.group(ClickReportConstant.DEVICEINFOBO_DEVICE_ID)
                                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_ID).as(DevicePoolBO.DEVICE_ID)
                                .first(ClickReportConstant.DEVICEINFOBO_DEVICE_TYPE).as(DevicePoolBO.DEVICE_TYPE)
                                .last(ClickReportConstant.LANGUAGE).as(DevicePoolBO.LANGUAGE)
                                .last(ClickReportConstant.PROVINCE_NAME_EN).as(DevicePoolBO.PROVINCE)
                                .last(ClickReportConstant.AREA_NAME_EN).as(DevicePoolBO.DISTRICT)
                                .last(ClickReportConstant.LOCATIONBO_COORDINATES).as(DevicePoolBO.COORDINATES)
                                .min(ClickReportConstant.RECORD_TIME).as(DevicePoolBO.RECORD_TIME)
                                .max(ClickReportConstant.CREATE_TIME).as(DevicePoolBO.LAST_ACTIVE_TIME),
                        Aggregation.skip(page * size),
                        Aggregation.limit(size))
                        .withOptions(AggregationOptions.builder().allowDiskUse(true).build());

                log.info("分页聚合查询设备数据开始, page: {}, now: {}", page, nowStr);
                List<DevicePoolBO> datas = mongoTemplate.aggregate(aggregation, MongoDbCollectonName.DEVICE_EVENT_RECORD, DevicePoolBO.class).getMappedResults();
                log.info("分页聚合查询设备数据结束, page: {}, size: {}", page, datas.size());
                if (datas.isEmpty()) {
                    break;
                }

                Map<String, Integer> deviceNewCountMap = new HashMap<>();
                for (DevicePoolBO d : datas) {
                    boolean isNew = this.saveDevice(d.getDeviceId(), d.getDeviceType(), DeviceUseStatusEnum.UNUSED, d.getRecordTime(), d.getLastActiveTime(),
                            d.getLanguage(), d.getProvince(), d.getDistrict(), d.getCoordinates());

                    //如有今天之前新增的设备，更新设备新增数统计
                    if (isNew && todayStartTime.after(d.getRecordTime())) {
                        String recordDate = DateUtil.formatDate(d.getRecordTime());
                        deviceNewCountMap.merge(recordDate, 1, (a, b) -> a + b);
                    }
                }

                //更新历史每日设备新增数
                deviceNewCountMap.forEach((date, count) -> {
                    DeviceNewStaticDay staticDay = mongoTemplate.findOne(Query.query(Criteria.where(DeviceNewStaticDay.DATA_DATE).is(date)), DeviceNewStaticDay.class);
                    if (staticDay != null) {
                        staticDay.setDeviceCount(staticDay.getDeviceCount() + count)
                                .setVersion(staticDay.getVersion() == null ? 1 : staticDay.getVersion() + 1)
                                .setUpdateTime(new Date());
                        mongoTemplate.save(staticDay);
                    }
                });

                currPage = page;
            }
        } catch (Exception e) {
            log.error("处理设备埋点事件出现异常, page: {}, now: {}", currPage, nowStr, e);
        }

        try {
            //删除记录
            mongoTemplate.remove(Query.query(Criteria.where(ClickReportConstant.CREATE_TIME).lte(now)), MongoDbCollectonName.DEVICE_EVENT_RECORD);
        } catch (Exception e) {
            log.error("删除设备埋点事件出现异常, now: {}", nowStr, e);
        }
        log.info("处理设备埋点事件结束");
    }


    @Override
    public DeviceRespDTO getDeviceInfo(DeviceIdReqDTO reqDTO) throws PendingException {
        DeviceRespDTO respDTO = null;
        List<DevicePoolBO> devices = devicePoolDAO.findByDeviceId(reqDTO.getDeviceId());
        if (!devices.isEmpty()) {
            DevicePoolBO device = devices.get(0);
            respDTO = new DeviceRespDTO();
            respDTO.setDeviceId(device.getDeviceId());
            respDTO.setDeviceType(device.getDeviceType());
            respDTO.setUsed(DeviceUseStatusEnum.USED.getCode().equals(device.getUseStatus()));
        }
        return respDTO;
    }
}
