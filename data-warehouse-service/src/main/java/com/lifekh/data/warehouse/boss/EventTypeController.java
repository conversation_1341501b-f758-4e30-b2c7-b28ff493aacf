package com.lifekh.data.warehouse.boss;


import com.lifekh.data.warehouse.api.EventTypeFacade;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.EventTypeRespDTO;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/warehouse/boss/event")
@RestController
@Slf4j
public class EventTypeController {

    @Autowired
    private EventTypeFacade eventTypeFacade;

    /**
     * 事件列表查询
     */
    @RequestMapping(value = "/list.do", method = RequestMethod.POST)
    @ResponseBody
    public PageInfoDTO<EventTypeRespDTO> queryList(@RequestBody EventTypeQueryReqDTO reqDTO) {
        return eventTypeFacade.queryList(reqDTO);
    }

    /**
     * 新增事件
     */
    @RequestMapping(value = "/add.do", method = RequestMethod.POST)
    @ResponseBody
    public void add(@RequestBody EventTypeAddReqDTO reqDTO,
                    @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setCreateBy(loginName);
        eventTypeFacade.add(reqDTO);
    }

    /**
     * 关闭事件
     */
    @RequestMapping(value = "/disable.do", method = RequestMethod.POST)
    @ResponseBody
    public void disable(@RequestBody EventTypeDisableReqDTO reqDTO,
                        @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        eventTypeFacade.disable(reqDTO);
    }

    /**
     * 启用事件
     */
    @RequestMapping(value = "/enable.do", method = RequestMethod.POST)
    @ResponseBody
    public void enable(@RequestBody EventTypeEnableReqDTO reqDTO,
                       @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        eventTypeFacade.enable(reqDTO);
    }

    /**
     * 修改事件s
     */
    @RequestMapping(value = "/update.do", method = RequestMethod.POST)
    @ResponseBody
    public void update(@RequestBody EventTypeUpdateReqDTO reqDTO,
                       @RequestHeader(value = "loginName", required = false) String loginName) {
        reqDTO.setUpdateBy(loginName);
        eventTypeFacade.update(reqDTO);
    }
}
