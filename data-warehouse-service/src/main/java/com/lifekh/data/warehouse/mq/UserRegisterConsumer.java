package com.lifekh.data.warehouse.mq;

import com.chaos.usercenter.api.dto.req.UserRegisterReqDTO;
import com.chaos.usercenter.api.mq.Topic;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import com.outstanding.framework.plugin.mq.rocketmq.serializer.SerializerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户注册消息消费
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.USER_REGISTER_TOPIC, serializer= SerializerEnum.JACKSON, consumerGroup = "${spring.application.name}", consumeThreadMax = 1)
public class UserRegisterConsumer implements RocketMQListener<UserRegisterReqDTO> {

    @Autowired
    private DevicePoolService devicePoolService;

    @Override
    public void onMessage(UserRegisterReqDTO message) {
        log.info("开始消费用户注册消息:{}", message);
        try {
            devicePoolService.saveUserRegisterMsg(message);
        } catch (Exception e) {
            log.error("用户注册消息消费异常", e);
        }
        log.info("结束消费用户注册消息:{}", message);
    }
}
