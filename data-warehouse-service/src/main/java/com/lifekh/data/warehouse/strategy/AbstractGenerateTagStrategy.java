package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.enums.SymbolEnum;

public abstract class AbstractGenerateTagStrategy implements GenerateTagStrategy{
    public boolean ruleMatching(Double orderCount, String symbol, Long min, Long max) {
        //下单总金额/下单总次数
        if (SymbolEnum.LESS_THAN.getCode().equals(symbol)) {
            //小于
            if (orderCount<max) {
                return true;
            }
        }else if(SymbolEnum.EQUAL.getCode().equals(symbol)){
            //等于
            if (orderCount.equals(min)) {
                return true;
            }
        } else if (SymbolEnum.MORE_THAN.getCode().equals(symbol)) {
            //大于
            if (orderCount>min) {
                return true;
            }
        } else if (SymbolEnum.INTERVAL.getCode().equals(symbol)) {
            //区间
            if (orderCount>=min&&orderCount<=max) {
                return true;
            }
        } else if (SymbolEnum.LESS_OR_EQUAL.getCode().equals(symbol)) {
            //小于等于
            if (orderCount<=max) {
                return true;
            }
        } else if (SymbolEnum.MORE_OR_EQUAL.getCode().equals(symbol)) {
            //大于等于
            if (orderCount>=min) {
                return true;
            }
        }
        return false;
    }
}
