package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import com.lifekh.data.warehouse.service.AggregateOrderService;
import com.lifekh.data.warehouse.service.BetaTagService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class OrderSuccessTagStrategy extends AbstractGenerateTagStrategy{

    @Autowired
    private BetaTagService betaTagService;
    @Autowired
    private AggregateOrderService aggregateOrderService;
    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.ORDER_SUCCESS;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {
        betaTagService.generateTag(scheduledStationLetterReqDTO);
    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        log.info("开始匹配用户订单数量, ruleNo: {}", rule.getRuleNo());

        List<String> matchNos = new ArrayList<>();
        //参数解析
        //获取业务线
        String businessLine = "";
        RuleDTO generalRule = rule.getGeneralRules().get(0);
        if ("businessLine".equals(generalRule.getRuleFiled())) {
            businessLine = generalRule.getMin();
        }
        //特殊规则
        int days = 0;
        Long min = null;
        Long max = null;
        String symbol = "";
        List<RuleDTO> specialRules = rule.getSpecialRules();
        for (RuleDTO specialRule : specialRules) {
            if ("days".equals(specialRule.getRuleFiled())) {
                days = Integer.valueOf(specialRule.getMin());
            }
            if ("order success".equals(specialRule.getRuleFiled())) {
                min = StringUtils.isEmpty(specialRule.getMin()) ? null : Long.valueOf(specialRule.getMin());
                max = StringUtils.isEmpty(specialRule.getMax()) ? null : Long.valueOf(specialRule.getMax());
                symbol = specialRule.getSymbol();
            }
        }

        Map<String, Integer> countMap = new HashMap<>(20000);
        //每次查询的天数
        int splitDays = 5;
        int pageSize = 5000;
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.offsetDay(new Date(), -days));
        Date endTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        while (startTime.before(endTime)) {
            Date queryEndTime = cn.hutool.core.date.DateUtil.offsetDay(startTime, splitDays);
            if (queryEndTime.after(endTime)) {
                queryEndTime = endTime;
            }

            log.info("开始查询用户的订单数量，查询时间：{} 至 {}", cn.hutool.core.date.DateUtil.formatDateTime(startTime), cn.hutool.core.date.DateUtil.formatDateTime(queryEndTime));
            List<Integer> aggregateOrderFinalState = new ArrayList<>();
            aggregateOrderFinalState.add(11);
            //分页查询
            for (int page = 0; ; page++) {
                //根据规则参数查询订单，分页获取用户
                List<AggregateOrderBO> orders = aggregateOrderService.findOrderByPage(page, pageSize, startTime, queryEndTime, aggregateOrderFinalState, businessLine, null);
                if (orders.isEmpty()) {
                    break;
                }
                //数量汇总
                for (AggregateOrderBO order : orders) {
                    Integer orderCount = order.getOrderCount();
                    countMap.merge(order.getUserId(), orderCount, (a, b) -> a + b);
                }
            }
            log.info("结束查询用户的订单数量，当前统计的用户数为: {}", countMap.size());

            startTime = queryEndTime;
        }

        //对每一个操作员进行遍历
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            Double orderCount = Double.valueOf(String.valueOf(entry.getValue()));
            //判断是否匹配
            boolean match = ruleMatching(orderCount, symbol, min, max);
            if (match) {
                matchNos.add(entry.getKey());
            }
        }

        log.info("匹配用户订单数量结束，符合规则的用户数为: {}, ruleNo: {}", matchNos.size(), rule.getRuleNo());

        return matchNos;
    }
}
