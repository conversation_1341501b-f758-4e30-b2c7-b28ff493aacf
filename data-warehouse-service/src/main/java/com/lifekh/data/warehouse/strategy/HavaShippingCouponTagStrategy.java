package com.lifekh.data.warehouse.strategy;

import com.chaos.marketing.api.UserCouponInfoFacade;
import com.chaos.marketing.api.dto.coupon.req.UserCouponLabelReqDTO;
import com.chaos.marketing.api.dto.coupon.resp.UserCouponLabelRespDTO;
import com.chaos.marketing.api.enums.MarketingTypeEnum;
import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.service.BetaTagService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class HavaShippingCouponTagStrategy extends AbstractGenerateTagStrategy{

    @Autowired
    private BetaTagService betaTagService;
    @Resource
    private UserCouponInfoFacade userCouponInfoFacade;
    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.HAVA_SHIPPING_COUPON;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {
        betaTagService.generateTag(scheduledStationLetterReqDTO);
    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        log.info("开始匹配用户拥有运费券数量, ruleNo: {}", rule.getRuleNo());

        List<String> matchNos = new ArrayList<>();
        //参数解析
        //获取业务线
        String businessLine = "";
        RuleDTO generalRule = rule.getGeneralRules().get(0);
        if ("businessLine".equals(generalRule.getRuleFiled())) {
            businessLine = generalRule.getMin();
        }
        //特殊规则
        int days = 0;
        Long min = null;
        Long max = null;
        String symbol = "";
        List<RuleDTO> specialRules = rule.getSpecialRules();
        for (RuleDTO specialRule : specialRules) {
            if ("days".equals(specialRule.getRuleFiled())) {
                days = Integer.valueOf(specialRule.getMin());
            }
            if ("hava_shipping_coupon".equals(specialRule.getRuleFiled())) {
                min = StringUtils.isEmpty(specialRule.getMin()) ? null : Long.valueOf(specialRule.getMin());
                max = StringUtils.isEmpty(specialRule.getMax()) ? null : Long.valueOf(specialRule.getMax());
                symbol = specialRule.getSymbol();
            }
        }

        Map<String, Integer> countMap = new HashMap<>(20000);
        //每次查询的天数
        int splitDays = 5;
        int pageSize = 999;
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.offsetDay(new Date(), -days));
        Date endTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        while (startTime.before(endTime)) {
            Date queryEndTime = cn.hutool.core.date.DateUtil.offsetDay(startTime, splitDays);
            if (queryEndTime.after(endTime)) {
                queryEndTime = endTime;
            }

            log.info("开始查询用户拥有运费券数量，查询时间：{} 至 {}", cn.hutool.core.date.DateUtil.formatDateTime(startTime), cn.hutool.core.date.DateUtil.formatDateTime(queryEndTime));
            //分页查询
            for (int page = 0; ; page++) {
                UserCouponLabelReqDTO userCouponLabelReqDTO = new UserCouponLabelReqDTO();
                userCouponLabelReqDTO.setBusinessLine(businessLine);
                userCouponLabelReqDTO.setCouponType(MarketingTypeEnum.SHIPPING_COUPON.getCode());
                userCouponLabelReqDTO.setBeginTime(startTime);
                userCouponLabelReqDTO.setEndTime(queryEndTime);
                userCouponLabelReqDTO.setPageNum(page + 1);
                userCouponLabelReqDTO.setPageSize(pageSize);
                PageInfoDTO<UserCouponLabelRespDTO> userCouponLabelRespDTOPageInfoDTO = userCouponInfoFacade.queryUserCouponLabel(userCouponLabelReqDTO);
                if (null == userCouponLabelRespDTOPageInfoDTO || userCouponLabelRespDTOPageInfoDTO.getList().isEmpty()) {
                    break;
                }
                List<UserCouponLabelRespDTO> userCoupon = userCouponLabelRespDTOPageInfoDTO.getList();
                //数量汇总
                for (UserCouponLabelRespDTO userCouponLabelRespDTO : userCoupon) {
                    countMap.merge(userCouponLabelRespDTO.getUserNo(), 1, (a, b) -> a + b);
                }
            }
            log.info("结束查询用户拥有运费券数量，当前统计的用户数为: {}", countMap.size());

            startTime = queryEndTime;
        }

        //对每一个操作员进行遍历
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            Double couponCount = Double.valueOf(String.valueOf(entry.getValue()));
            //判断是否匹配
            boolean match = ruleMatching(couponCount, symbol, min, max);
            if (match) {
                matchNos.add(entry.getKey());
            }
        }

        log.info("匹配用户拥有运费券数量结束，符合规则的用户数为: {}, ruleNo: {}", matchNos.size(), rule.getRuleNo());

        return matchNos;
    }
}
