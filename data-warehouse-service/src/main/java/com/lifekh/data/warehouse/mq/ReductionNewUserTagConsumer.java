package com.lifekh.data.warehouse.mq;

import com.chaos.common.enums.BusinessLineEnum;
import com.chaos.message.api.dto.ReceiveMessageDTO;
import com.chaos.shop.api.AggregateFacadeService;
import com.chaos.shop.api.dto.order.mq.dto.FinalAggregateOrderMQDTO;
import com.chaos.shop.api.dto.order.req.GetAggregateOrderListReqDTO;
import com.chaos.shop.api.dto.order.rsp.AggregateOrderListRspDTO;
import com.chaos.shop.api.enums.AggregateOrderFinalStateEnum;
import com.chaos.shop.api.enums.AggregateOrderStateEnum;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 还原外卖新用户标签
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = FinalAggregateOrderMQDTO.topic, consumerGroup = "usercenter-core-service", consumeThreadMax = 1)
public class ReductionNewUserTagConsumer implements RocketMQListener<ReceiveMessageDTO> {

    @Autowired
    AggregateFacadeService aggregateFacadeService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${data.warehouse.newUserTagNo:2021110211551498496}")
    private String newUserTagNo;

    @Override
    public void onMessage(ReceiveMessageDTO receiveMessageDTO) {
        String messageBody = receiveMessageDTO.getMessageBody();
        FinalAggregateOrderMQDTO accessRefundMQDTO = FastJsonUtil.jsonToObject(messageBody,FinalAggregateOrderMQDTO.class);
        if (accessRefundMQDTO.getAggregateOrderFinalState() != 11){
            //非完成订单
            GetAggregateOrderListReqDTO getAggregateOrderListReqDTO = new GetAggregateOrderListReqDTO();
            getAggregateOrderListReqDTO.setAggregateOrderNo(accessRefundMQDTO.getAggregateOrderNo());
            getAggregateOrderListReqDTO.setOrderType("10");
            PageInfoDTO<AggregateOrderListRspDTO> aggregateOrderListRspDTOPageInfoDTO = aggregateFacadeService.getAggregateOrderList(getAggregateOrderListReqDTO);
            if(CollectionUtils.isEmpty(aggregateOrderListRspDTOPageInfoDTO.getList())) {
                log.warn("查询订单数据为空, GetAggregateOrderListReqDTO:{}", getAggregateOrderListReqDTO);
                return;
            }
            if (BusinessLineEnum.YUMNOW.getCode().equals(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getBusinessLine().getCode())){
                //只处理外卖订单
                //查询该用户是否有已完成订单
                GetAggregateOrderListReqDTO getAggregateOrderListReqDTO2 = new GetAggregateOrderListReqDTO();
                getAggregateOrderListReqDTO2.setOrderType("10");
                getAggregateOrderListReqDTO2.setPageNum(1);
                getAggregateOrderListReqDTO2.setPageSize(100);
                getAggregateOrderListReqDTO2.setState(AggregateOrderStateEnum.COMPLETE);
                getAggregateOrderListReqDTO2.setUserNo(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                getAggregateOrderListReqDTO2.setBusinessLine(BusinessLineEnum.YUMNOW);
                PageInfoDTO<AggregateOrderListRspDTO> aggregateOrderListRspDTOPageInfoDTO2 = aggregateFacadeService.getAggregateOrderList(getAggregateOrderListReqDTO2);
                boolean isNewYumNowuser = true;
                for (AggregateOrderListRspDTO aggregateOrderListRspDTO:aggregateOrderListRspDTOPageInfoDTO2.getList()){
                    if (Objects.nonNull(aggregateOrderListRspDTO.getAggregateOrderFinalState()) && AggregateOrderFinalStateEnum.COMPLETE.getCode().equals(aggregateOrderListRspDTO.getAggregateOrderFinalState().getCode())){
                        isNewYumNowuser = false;
                        break;
                    }
                }
                for (int i = 2;i<=aggregateOrderListRspDTOPageInfoDTO2.getPages();i++){
                    if (! isNewYumNowuser){
                        break;
                    }
                    GetAggregateOrderListReqDTO getAggregateOrderListReqDTO3 = new GetAggregateOrderListReqDTO();
                    getAggregateOrderListReqDTO3.setOrderType("10");
                    getAggregateOrderListReqDTO3.setPageNum(i);
                    getAggregateOrderListReqDTO3.setPageSize(100);
                    getAggregateOrderListReqDTO3.setState(AggregateOrderStateEnum.COMPLETE);
                    getAggregateOrderListReqDTO3.setUserNo(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                    getAggregateOrderListReqDTO3.setBusinessLine(BusinessLineEnum.YUMNOW);
                    PageInfoDTO<AggregateOrderListRspDTO> aggregateOrderListRspDTOPageInfoDTO3 = aggregateFacadeService.getAggregateOrderList(getAggregateOrderListReqDTO3);
                    for (AggregateOrderListRspDTO aggregateOrderListRspDTO:aggregateOrderListRspDTOPageInfoDTO3.getList()){
                        if (Objects.nonNull(aggregateOrderListRspDTO.getAggregateOrderFinalState()) && AggregateOrderFinalStateEnum.COMPLETE.getCode().equals(aggregateOrderListRspDTO.getAggregateOrderFinalState().getCode())){
                            isNewYumNowuser = false;
                        }
                    }
                }
                if (isNewYumNowuser){
                    //已完成订单为0，恢复新用户标签
                    log.info("外卖订单取消事件：{}，恢复操作员：{} 新用户标签身份",receiveMessageDTO.getMessageBody(),aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                    List<UpdateUserTagDTO> updateUserTagDTOS = new ArrayList<>();
                    UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                    updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
                    updateUserTagDTO.setOperatorNo(aggregateOrderListRspDTOPageInfoDTO.getList().get(0).getOperatorNo());
                    updateUserTagDTO.setTagNo(newUserTagNo);
                    updateUserTagDTOS.add(updateUserTagDTO);
                    rocketMQTemplate.syncSend(com.lifekh.data.warehouse.api.constants.Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateUserTagDTOS));
                }
            }
        }
    }
}
