package com.lifekh.data.warehouse.service;


import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.req.ClassificationTagReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoFirstClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.TagDetailQueryRespDTO;
import com.lifekh.data.warehouse.api.resp.TagInfoHasUserRespDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagInfoService {

    /**
     * 保存标签
     *
     * @param tagInfoBO
     */
    void saveTag(TagInfoBO tagInfoBO);

    /**
     * 分页查询
     * @param reqDTO
     */
    PageInfoDTO<TagInfoDTO> findList(TagListQueryReqDTO reqDTO);

    /**
     * 标签详情
     * @param reqDTO
     * @return
     */
    TagDetailQueryRespDTO detail(TagDetailQueryReqDTO reqDTO);

    /**
     * 编辑标签
     * @param reqDTO
     */
    void editTag(TagEditReqDTO reqDTO);

    /**
     * 删除标签
     * @param reqDTO
     */
    void deleteTag(TagDeleteReqDTO reqDTO);


    TagInfoBO selectByRuleNo(String ruleNo) throws PendingException;

    List<TagInfoBO> findByRuleType(String ruleType);

    TagInfoBO findFirstByCondition(String ruleType, String ruleValue, Integer size) throws PendingException;

    PageInfoDTO<TagInfoHasUserRespDTO> listForHasUser(TagInfoHasUserReqDTO reqDTO) throws PendingException;

    void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException;

    /**
     * 根据标签编号批量查询标签
     *
     * @param tagNos
     * @return
     * @throws PendingException
     */
    List<TagInfoRespDTO> queryTagByTagNos(List<String> tagNos) throws PendingException;

    void add(TagAddReqDTO reqDTO) throws PendingException;

    /**
     * 根据标签名查询分类下的标签内容(分群-标签选择器)
     * @param reqDTO
     */
    List<TagInfoFirstClassificationRespDTO> queryClassificationTagByTagName(ClassificationTagReqDTO reqDTO);
}
