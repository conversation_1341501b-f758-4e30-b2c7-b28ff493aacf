package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ZoneTagStrategy extends AbstractGenerateTagStrategy {
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private TagInfoService tagInfoService;

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.ZONE_TAG;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {
//        zoneTagService.calculateAllUserZoneTag(scheduledStationLetterReqDTO.getTotalCount(), scheduledStationLetterReqDTO.getItem());
    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        List<String> matchNos = new ArrayList<>();
        RuleDTO ruleDTO = rule.getSpecialRules().get(0);
        if (!RuleTypeEnum.ZONE_TAG.getCode().equals(ruleDTO.getRuleFiled())) {
            return matchNos;
        }
        //查询该地区标签
        TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.ZONE_TAG.getCode(), ruleDTO.getMin(), 1);
        if (tagInfo == null) {
            log.info("查询地区标签结果为空, ruleValue:{}", ruleDTO.getMin());
            return matchNos;
        }

        //查询该标签的用户
        matchNos = tagUserService.findOperatorNoByTagNo(tagInfo.getTagNo());
        return matchNos;
    }
}
