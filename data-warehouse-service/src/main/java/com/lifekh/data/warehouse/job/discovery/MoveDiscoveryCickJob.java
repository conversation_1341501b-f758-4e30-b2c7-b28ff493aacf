package com.lifekh.data.warehouse.job.discovery;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.service.discovery.DiscoveryService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 清洗移出发现页点击事件
 * 执行时间：每天凌晨5点30
 */
@Slf4j
@ElasticJobConf(name = "move-discoveryPage-click-job", cron = "0 30 5 * * ?", description = "清洗移出发现页点击事件",shardingTotalCount = 1)
public class MoveDiscoveryCickJob extends AbstractSimpleJob {

    @Autowired
    DiscoveryService discoveryService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始清洗发现点击页事件，任务参数：{}", shardingContext.getJobParameter());
        Date startTime = null;
        Date endTime = null;
        //指定日期统计
        if (StringUtils.isNotBlank(shardingContext.getJobParameter())) {
            try {
                String dateFmt = "yyyyMMdd";
                String dateArr[] = shardingContext.getJobParameter().split("-");
                startTime = DateUtil.beginOfDay(DateUtil.parse(dateArr[0], dateFmt));
                endTime = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.parse(dateArr[1], dateFmt), 1));
            } catch (Exception e) {
                log.error("解析时间参数出现异常", e);
                return;
            }

            //删除已生成的数据
            discoveryService.deleteDiscoryClickData(startTime, endTime);
        } else {
            startTime = DateUtil.beginOfDay(DateUtil.yesterday());
            endTime = DateUtil.beginOfDay(new Date());
        }
        log.info("清洗发现点击页事件时间参数，startTime：{}， endTime：{}", DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));

        discoveryService.moveDiscovryDetailsClick(startTime, endTime);
        discoveryService.moveDiscovryGoodsClick(startTime, endTime);
        log.info("结束清洗发现点击页事件");
        log.info("开始统计内容总点击详情");
        discoveryService.staticDiscoveryContentDetail(startTime, endTime);
        log.info("结束统计内容总点击详情");

        discoveryService.statisticsDiscoveryReport();
        log.info("结束统计发现频道报表");
    }

}