package com.lifekh.data.warehouse.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.lifekh.data.warehouse.report.service.TargetReportService;
import com.lifekh.data.warehouse.service.DevicePoolService;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.elasticjob.AbstractSimpleJob;
import com.outstanding.framework.plugin.elasticjob.annotation.ElasticJobConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * 用户注册设备处理
 */
@Slf4j
@ElasticJobConf(name = "user-register-device-job", cron = "0 30 0 * * ?", description = "定时处理用户注册的设备", shardingTotalCount = 1)
public class UserRegisterDeviceJob extends AbstractSimpleJob {

    @Autowired
    private DevicePoolService devicePoolService;
    @Autowired
    private TargetReportService targetReportService;

    @Override
    protected void doSimpleJob(ShardingContext shardingContext) throws PendingException {
        log.info("开始处理用户注册的设备");

        //用户注册消息
        devicePoolService.handleUserRegisterDeviceMsg();

        //每日设备数据统计
        devicePoolService.statisticsDeviceData();

        //统计设备注册率
        targetReportService.staticDeviceRegisterRate(Collections.singletonList(1));

        //统计首页登录引导点击数
        targetReportService.staticHomeLoginGuideData(Collections.singletonList(1));

        log.info("结束处理用户注册的设备");
    }

}