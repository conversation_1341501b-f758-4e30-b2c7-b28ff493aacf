package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.service.UserBehaviorInfoService;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.USER_BEHAVIOR_INFO_TOPIC, consumerGroup = "${spring.application.name}", consumeThreadMax = 1)
public class UserBehaviorInfoCollectConsumer implements RocketMQListener<String> {

    @Autowired
    private UserBehaviorInfoService userBehaviorInfoService;

    @Override
    public void onMessage(String message) {
        try {
            userBehaviorInfoService.collectBehaviorInfoFromGatewayBuriedPoint(message);
        } catch (Exception e) {
            log.warn("用户行为信息采集异常", e);
        }
    }
}
