package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.TagClassificationFacade;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagClassificationRespDTO;
import com.lifekh.data.warehouse.service.TagClassificationService;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/27 11:51
 * @Version 1.0
 **/
@Service
public class TagClassificationFacadeImpl implements TagClassificationFacade {

    @Autowired
    TagClassificationService tagClassificationService;

    @Override
    public List<TagClassificationRespDTO> queryTagClassification(TagClassificationQueryReqDTO tagClassificationQueryReqDTO) throws PendingException {
        return tagClassificationService.queryTagClassification(tagClassificationQueryReqDTO);
    }

}
