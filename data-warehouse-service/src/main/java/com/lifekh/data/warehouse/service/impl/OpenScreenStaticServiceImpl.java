package com.lifekh.data.warehouse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.OpenScreenStaticReqDTO;
import com.lifekh.data.warehouse.api.resp.OpenScreenStaticRespDTO;
import com.lifekh.data.warehouse.service.OpenScreenStaticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("openScreenStaticService")
public class OpenScreenStaticServiceImpl implements OpenScreenStaticService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public OpenScreenStaticRespDTO queryStaticByPopNo(OpenScreenStaticReqDTO reqDTO) {
        Criteria criteria = new Criteria();
        criteria.and("adNo").is(reqDTO.getAdsNo());
        List<AggregationOperation> querList = new ArrayList<>();
        querList.add(Aggregation.match(criteria));
        querList.add(Aggregation.group("adNo")
                .first("adNo").as("adNo")
                .sum("count").as("total"));
        AggregationResults<OpenScreenStaticRespDTO> aggregateTotalResults = mongoTemplate.aggregate(Aggregation.newAggregation(querList), MongoDbCollectonName.OPEN_SCREEN_STATIC, OpenScreenStaticRespDTO.class);
        List<OpenScreenStaticRespDTO> totalList = aggregateTotalResults.getMappedResults();
        OpenScreenStaticRespDTO respDTO = new OpenScreenStaticRespDTO(0L);
        if (CollectionUtil.isNotEmpty(totalList)) {
            respDTO.setTotal(totalList.get(0) != null ? totalList.get(0).getTotal() : 0);
        }
        return respDTO;
    }
}
