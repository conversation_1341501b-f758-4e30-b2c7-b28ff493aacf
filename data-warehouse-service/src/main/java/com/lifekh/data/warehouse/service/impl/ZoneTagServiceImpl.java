package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.api.enums.*;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.dao.*;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.service.ZoneTagService;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("zoneTagService")
@Slf4j
public class ZoneTagServiceImpl implements ZoneTagService {

    @Autowired
    private ZoneDAO zoneDAO;

    @Autowired
    private LocationManage locationManage;

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private TagUserService tagUserService;

    @Override
    public void updateUserTagOne(UserBehaviorInfoBO oggBo){
        List<UpdateUserTagDTO> updateList = new ArrayList<>();
        updateUserTag(oggBo,updateList);
        //当更新列表不为空是，需要通过MQ更新用户标签
        if (CollectionUtils.isNotEmpty(updateList)) {
            rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateList));
        }
    }

    private void updateUserTag(UserBehaviorInfoBO oggBo, List<UpdateUserTagDTO> updateList) {
        try {
            //根据经纬度找出对应的省份名称
            LocationDTO locationDTO = locationManage.getNameByPosition(Double.toString(oggBo.getLocationBo().getCoordinates()[0]),
                    Double.toString(oggBo.getLocationBo().getCoordinates()[1]));
            if (Objects.nonNull(locationDTO)) {
                //找出省份对应的编码
                List<ZoneBO> zoneList = zoneDAO.findByMsgEn(locationDTO.getProvinceName());
                if (CollectionUtils.isNotEmpty(zoneList)) {
                    ZoneBO zoneBo = zoneList.get(0);
                    //找出编码对应的标签规则
                    TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.ZONE_TAG.getCode(), zoneBo.getCode(), 1);

                    if (Objects.isNull(tagInfo)) {
                        log.warn("没有找到匹配的地区标签规则 ruleType:{}, zoneCode:{}", RuleTypeEnum.ZONE_TAG.getCode(), zoneBo.getCode());
                        return;
                    }

                    //查询用户已贴标签
                    TagUserDTO tagUserDTO = tagUserService.selectUserLeftJoinTagInfo(oggBo.getUserInfoBo().getOperatorNo());

                    if (Objects.isNull(tagUserDTO)) {
                        //新增用户标签
                        updateList.add(new UpdateUserTagDTO(oggBo.getUserInfoBo().getOperatorNo(), tagInfo.getTagNo(), OptTypeEnum.ADD.getCode(), null, null));
                    } else {
                        //修改用户标签
                        updateZoneTag(updateList, oggBo.getUserInfoBo().getOperatorNo(), tagUserDTO, tagInfo.getTagNo());
                    }
                    log.info("已更新用户地区标签 operatorNo:{}, Longitude:{}, Latitude:{}, MsgZh:{}, MsgEn:{}, TagNo:{}",
                            oggBo.getUserInfoBo().getOperatorNo(), Double.toString(oggBo.getLocationBo().getCoordinates()[0]),
                            Double.toString(oggBo.getLocationBo().getCoordinates()[1]), zoneBo.getMsgZh(), zoneBo.getMsgEn(), tagInfo.getTagNo());
                }
            }
        } catch (Exception e) {
            log.error("用户更新地区标签失败, operatorNo:{}, longitude:{}, latitude:{}",
                    oggBo.getUserInfoBo().getOperatorNo(), Double.toString(oggBo.getLocationBo().getCoordinates()[0]),
                    Double.toString(oggBo.getLocationBo().getCoordinates()[1]));
        }
    }

    /**
     * 构造更新用户标签请求参数
     *
     * @param updateList   用户标签更新请求参数
     * @param operatorNo   操作员编号
     * @param tagUserDTO   用户已贴标签对象
     * @param tagNo         新标签编号
     */
    private void updateZoneTag(List<UpdateUserTagDTO> updateList, String operatorNo, TagUserDTO tagUserDTO, String tagNo) {
        List<UpdateUserTagDTO> newUpdateList = new ArrayList<>();
        UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
        updateUserTagDTO.setOperatorNo(operatorNo);
        updateUserTagDTO.setTagNo(tagNo);
        updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
        //用户未添加过标签
        if(CollectionUtils.isEmpty(tagUserDTO.getTagInfo())) {
            newUpdateList.add(updateUserTagDTO);
            updateList.addAll(newUpdateList);
            return;
        }
        boolean exist = true;
        //遍历用户是否已经添加过地区标签
        for(TagInfoDTO tagInfoDTO : tagUserDTO.getTagInfo()) {
            if(Objects.nonNull(tagInfoDTO)) {
                for(TagRuleDTO ruleDTO : tagInfoDTO.getRule()) {
                    //用户已添加过地区标签
                    if(Objects.nonNull(ruleDTO) && RuleTypeEnum.ZONE_TAG.getCode().equals(ruleDTO.getRuleType())) {
                        //如果新标签编号和旧标签编号相同则不更新
                        if(tagInfoDTO.getTagNo().equals(tagNo)) {
                            exist = false;
                            return;
                        }

                        //删除旧标签
                        newUpdateList.add(new UpdateUserTagDTO(operatorNo, tagInfoDTO.getTagNo(), OptTypeEnum.DEL.getCode(),null,null));

                        //新增新标签
                        newUpdateList.add(updateUserTagDTO);
                        updateList.addAll(newUpdateList);
                        return;
                    }
                }
            }
        }
        //用户未添加过地区标签
        if (CollectionUtils.isEmpty(newUpdateList) && exist) {
            newUpdateList.add(updateUserTagDTO);
        }
        updateList.addAll(newUpdateList);
    }
}
