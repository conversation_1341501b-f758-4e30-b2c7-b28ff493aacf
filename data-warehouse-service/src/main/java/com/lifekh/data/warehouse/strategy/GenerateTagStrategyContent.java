package com.lifekh.data.warehouse.strategy;

import com.google.common.collect.Maps;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class GenerateTagStrategyContent {
    private Map<RuleTypeEnum, GenerateTagStrategy> generateTagStrategyMap
            = Maps.newEnumMap(RuleTypeEnum.class);

    @Autowired
    public GenerateTagStrategyContent(List<GenerateTagStrategy> strategyList){
        generateTagStrategyMap.clear();
        strategyList.forEach(e -> generateTagStrategyMap.put(e.getRuleType(), e));
    }

    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledTagUsereqDTO,RuleTypeEnum ruleTypeEnum) throws PendingException{
        GenerateTagStrategy generateTagStrategy = generateTagStrategyMap.get(ruleTypeEnum);
        generateTagStrategy.executeGenerateTag(scheduledTagUsereqDTO);
    }

    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        GenerateTagStrategy generateTagStrategy = generateTagStrategyMap.get(RuleTypeEnum.getByCode(rule.getRuleType()));
        if (generateTagStrategy == null) {
            log.info("获取标签处理策略失败，请检查规则配置，ruleType: {}", rule.getRuleType());
            return new ArrayList<>();
        }
        return generateTagStrategy.calculateTag(rule);
    }
}
