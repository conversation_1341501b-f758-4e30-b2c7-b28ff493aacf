package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.MerchantAdsPublishStaticFacade;
import com.lifekh.data.warehouse.api.req.CmsMerchantAdsPublishReqDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticDetailRespDTO;
import com.lifekh.data.warehouse.api.resp.MerchantAdsPublishStaticRespDTO;
import com.lifekh.data.warehouse.report.service.MerchantAdsPublishStaticService;
import com.outstanding.framework.core.PageInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/1/13 10:05
 * @Version 1.0
 **/
@Service("merchantAdsPublishStaticFacade")
public class MerchantAdsPublishStaticFacadeImpl implements MerchantAdsPublishStaticFacade {

    @Autowired
    MerchantAdsPublishStaticService merchantAdsPublishStaticService;

    @Override
    public PageInfoDTO<MerchantAdsPublishStaticRespDTO> seachMerchantNodePlanStaticList(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) {
        return merchantAdsPublishStaticService.seachMerchantNodePlanStaticList(cmsMerchantAdsPublishReqDTO);
    }

    @Override
    public MerchantAdsPublishStaticDetailRespDTO seachMerchantNodePlanStaticDetail(CmsMerchantAdsPublishReqDTO cmsMerchantAdsPublishReqDTO) {
        return merchantAdsPublishStaticService.seachMerchantNodePlanStaticDetail(cmsMerchantAdsPublishReqDTO);
    }
}
