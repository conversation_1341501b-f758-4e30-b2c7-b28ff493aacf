package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.TagInfoFacade;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.req.ClassificationTagReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoFirstClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.TagInfoHasUserRespDTO;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TagInfoFacadeImpl implements TagInfoFacade {
    @Autowired
    private TagInfoService tagInfoService;

    public PageInfoDTO<TagInfoDTO> list(TagListQueryReqDTO reqDTO) throws PendingException {
        return tagInfoService.findList(reqDTO);
    }

    public PageInfoDTO<TagInfoHasUserRespDTO> listForHasUser(TagInfoHasUserReqDTO reqDTO) throws PendingException{
        return tagInfoService.listForHasUser(reqDTO);
    }

    @Override
    public void add(TagAddReqDTO reqDTO) throws PendingException {
        tagInfoService.add(reqDTO);
    }

    @Override
    public void edit(TagEditReqDTO reqDTO) throws PendingException {
        tagInfoService.editTag(reqDTO);
    }

    @Override
    public void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException {
        tagInfoService.updateStatus(reqDTO);
    }
    @Override
    public List<TagInfoRespDTO> queryTagByTagNos(List<String> tagNos) throws PendingException {
        return tagInfoService.queryTagByTagNos(tagNos);
    }

    @Override
    public List<TagInfoFirstClassificationRespDTO> queryClassificationTagByTagName(ClassificationTagReqDTO reqDTO) {
        return tagInfoService.queryClassificationTagByTagName(reqDTO);
    }
}
