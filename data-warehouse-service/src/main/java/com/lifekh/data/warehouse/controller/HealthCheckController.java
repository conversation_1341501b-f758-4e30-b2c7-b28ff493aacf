package com.lifekh.data.warehouse.controller;

import com.outstanding.framework.plugin.monitor.health.HealthCheckConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 数据仓库的service
 * @module merchant
 */
@RequestMapping("warehouse")
@RestController
public class HealthCheckController {

    @Autowired
    private HealthCheckConfig healthCheckConfig;

    /**
     * 健康检查
     *
     * @return
     */
    @PostMapping("health/info")
    @ResponseBody
    public Map healthInfo(){
        return healthCheckConfig.getHealthInfo();
    }

}
