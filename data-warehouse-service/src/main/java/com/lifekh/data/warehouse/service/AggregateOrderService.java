package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import com.outstanding.framework.core.PendingException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface AggregateOrderService {


    Page<AggregateOrderBO> findList(Pageable pageable);

    List<AggregateOrderBO> findByUpdateTimeAndAggregateOrderFinalState(Date date, Integer aggregateOrderFinalState,String businessLine,Pageable pageable) throws PendingException;

    List<AggregateOrderBO> findByOperatorNosAndUpdateTime(List<String> operatorNos, Date date, Integer aggregateOrderFinalState, String businessLine);

    List<AggregateOrderBO> findOrderByPage(int page, int size, Date startTime, Date endTime, List<Integer> aggregateOrderFinalState, String businessLine, String payType);

    AggregateOrderBO findFirstOrderByUserId(String userId);
}
