package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.AppVersionBetaUserBO;
import com.outstanding.framework.core.PendingException;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AppVersionBetaUserService {

    void add(AppVersionBetaUserBO bo) throws PendingException;

    List<AppVersionBetaUserBO> findYesterdayData(ScheduledTagUsereqDTO scheduledTagUsereqDTO, Pageable pageable) throws PendingException;

    List<AppVersionBetaUserBO> findHistoryData(ScheduledTagUsereqDTO scheduledTagUsereqDTO, Pageable pageable) throws PendingException;

    List<String> findByOperatorNos(List<String> operatorNos) throws PendingException;

    List<String> findAllBetaOperatorNo() throws PendingException;
}
