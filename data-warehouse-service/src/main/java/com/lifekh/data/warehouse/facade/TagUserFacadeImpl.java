package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.TagUserFacade;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.AddZoneTagReqDTO;
import com.lifekh.data.warehouse.api.dto.req.TagUserQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UserTagInfoGetReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserTagInfoRespDTO;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.GroupContainUserRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserGetRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserInfoRespDTO;
import com.lifekh.data.warehouse.service.TagUserService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TagUserFacadeImpl implements TagUserFacade {
    @Autowired
    private TagUserService tagUserService;

    public PageInfoDTO<TagUserDTO> list(TagUserListReqDTO reqDTO) throws PendingException{
        return tagUserService.list(reqDTO);
    }

    @Override
    public TagUserRespDTO queryByOperatorNo(TagUserQueryReqDTO reqDTO) throws PendingException {
        return tagUserService.queryByOperatorNo(reqDTO);
    }

    @Override
    public void tagUserImport(TagUserImportReqDTO reqDTO) throws PendingException {
        tagUserService.tagUserImport(reqDTO);
    }

    @Override
    public void tagUserDelete(TagUserDeleteReqDTO reqDTO) throws PendingException {
        tagUserService.tagUserDelete(reqDTO);
    }

    @Override
    public Long queryUserTotalByTag(List<String> tagNos) throws PendingException {
        return tagUserService.queryUserTotalByTag(tagNos);
    }

    @Override
    public Long countTagUser(TagUserCountReqDTO reqDTO) throws PendingException {
        return tagUserService.countTagUser(reqDTO);
    }

    @Override
    public PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfo(TagUserQueryByTagsReqDTO reqDTO) throws PendingException {
        return tagUserService.queryTagUserInfo(reqDTO);
    }

    @Override
    public PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfoByTagGroup(UserTagGroupsReqDTO reqDTO) throws PendingException {
        return tagUserService.queryTagUserInfoByTagGroup(reqDTO);
    }

    @Override
    public GroupContainUserRespDTO checkUserOfGroup(OperatorNoAndGroupNoReqDTO operatorNoAndGroupNoReqDTO) {
        return tagUserService.checkUserOfGroup(operatorNoAndGroupNoReqDTO);
    }

    @Override
    public void addZoneTag(AddZoneTagReqDTO reqDTO) throws PendingException {
        tagUserService.addZoneTag(reqDTO);
    }

    @Override
    public List<TagUserGetRespDTO> queryUserByTag(TagUserGetReqDTO reqDTO) throws PendingException {
        return tagUserService.queryUserByTag(reqDTO);
    }

    @Override
    public UserTagInfoRespDTO getUserTagInfo(UserTagInfoGetReqDTO reqDTO) throws PendingException {
        return tagUserService.getUserTagInfo(reqDTO);
    }
}
