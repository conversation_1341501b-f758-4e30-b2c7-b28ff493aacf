package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import com.outstanding.framework.core.PendingException;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface UserLabelService {

    void add(UserLabelBO bo) throws PendingException;

    List<UserLabelBO> findYesterdayData(ScheduledTagUsereqDTO scheduledTagUsereqDTO,Pageable pageable) throws PendingException;

    List<UserLabelBO> findHistoryData(ScheduledTagUsereqDTO scheduledTagUsereqDTO,Pageable pageable) throws PendingException;

    List<String> findByOperatorNoAndLanguage(String language, List<String> list) throws PendingException;

    List<String> findOperatorNoByPage(int page, int size, String language) throws PendingException;
}
