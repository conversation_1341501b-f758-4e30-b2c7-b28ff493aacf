package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface GenerateTagStrategy {

    RuleTypeEnum getRuleType();

    void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException;

    List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException;
}
