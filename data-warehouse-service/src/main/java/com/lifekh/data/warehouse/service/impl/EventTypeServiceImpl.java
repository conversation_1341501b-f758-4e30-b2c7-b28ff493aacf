package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.EventTypeRespDTO;
import com.lifekh.data.warehouse.bo.collection.EventTypeBO;
import com.lifekh.data.warehouse.dao.collect.EventTypeDAO;
import com.lifekh.data.warehouse.service.EventTypeService;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service("eventTypeService")
public class EventTypeServiceImpl implements EventTypeService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private EventTypeDAO eventTypeDAO;

    @Override
    public PageInfoDTO<EventTypeRespDTO> queryList(EventTypeQueryReqDTO reqDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getEventGroup())) {
            criteria.and("eventGroup").is(reqDTO.getEventGroup());
        }
        if (StringUtils.isNotEmpty(reqDTO.getEventNo())) {
            criteria.and("eventNo").is(reqDTO.getEventNo());
        }
        if (StringUtils.isNotEmpty(reqDTO.getRemark())) {
            criteria.and("remark").regex("^.*" + reqDTO.getRemark() + ".*$");
        }
        if (StringUtils.isNotEmpty(reqDTO.getTable())) {
            criteria.and("table").is(reqDTO.getTable());
        }
        if (StringUtils.isNotEmpty(reqDTO.getBussinessLine())) {
            criteria.and("bussinessLine").is(reqDTO.getBussinessLine());
        }
        if (StringUtils.isNotEmpty(reqDTO.getEventName())) {
            criteria.and("eventName").regex("^.*" + reqDTO.getEventName() + ".*$");
        }

        Query query = new Query(criteria);

        long total = mongoTemplate.count(query, EventTypeBO.class);

        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());

        List<EventTypeRespDTO> eventTypeRespDTO = mongoTemplate.find(query, EventTypeRespDTO.class, MongoDbCollectonName.EVENT_TYPE);
        PageInfoDTO<EventTypeRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(eventTypeRespDTO.size());
        target.setTotal(total);
        target.setList(eventTypeRespDTO);
        return target;
    }

    @Override
    public void add(EventTypeAddReqDTO reqDTO) {
        EventTypeBO bo = eventTypeDAO.queryByEventNo(reqDTO.getEventNo());
        if (bo != null) {
            log.info("事件编号已存在，无法添加,eventNo:{}", reqDTO.getEventNo());
            return;
        }

        EventTypeBO eventTypeBO = new EventTypeBO();
        BeanCopierHelper.copyProperties(reqDTO, eventTypeBO);
        eventTypeBO.setCreateTime(new Date());
        eventTypeBO.setUpdateTime(new Date());
        eventTypeBO.setUpdateBy(reqDTO.getCreateBy());
        eventTypeBO.setStatus(10);
        eventTypeDAO.save(eventTypeBO);
    }

    @Override
    public void disable(EventTypeDisableReqDTO reqDTO) {
        EventTypeBO bo = eventTypeDAO.queryByEventNo(reqDTO.getEventNo());
        if (bo == null) {
            log.info("事件编号不存在，无法添加,eventNo:{}", reqDTO.getEventNo());
            return;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("eventNo").is(reqDTO.getEventNo()));
        Update update = new Update();
        update.set("status", 11);
        update.set("updateTime", new Date());
        update.set("updateBy", reqDTO.getUpdateBy());
        mongoTemplate.updateFirst(query, update, EventTypeBO.class);
    }

    @Override
    public void enable(EventTypeEnableReqDTO reqDTO) {
        EventTypeBO bo = eventTypeDAO.queryByEventNo(reqDTO.getEventNo());
        if (bo == null) {
            log.info("事件编号不存在，无法添加,eventNo:{}", reqDTO.getEventNo());
            return;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("eventNo").is(reqDTO.getEventNo()));
        Update update = new Update();
        update.set("status", 10);
        update.set("updateTime", new Date());
        update.set("updateBy", reqDTO.getUpdateBy());
        mongoTemplate.updateFirst(query, update, EventTypeBO.class);
    }

    @Override
    public void update(EventTypeUpdateReqDTO reqDTO) {
        EventTypeBO bo = eventTypeDAO.queryByEventNo(reqDTO.getEventNo());
        if (bo == null) {
            log.info("事件编号不存在，无法添加,eventNo:{}", reqDTO.getEventNo());
            return;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("eventNo").is(reqDTO.getEventNo()));
        Update update = new Update();
        update.set("updateTime", new Date());
        update.set("eventGroup", reqDTO.getEventGroup());
        update.set("eventName", reqDTO.getEventName());
        update.set("bussinessLine", reqDTO.getBussinessLine());
        update.set("remark", reqDTO.getRemark());
        update.set("table", reqDTO.getTable());
        update.set("updateBy", reqDTO.getUpdateBy());
        mongoTemplate.updateFirst(query, update, EventTypeBO.class);
    }
}
