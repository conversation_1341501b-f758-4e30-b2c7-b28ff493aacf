package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.bo.ogg.AppVersionBetaUserBO;
import com.lifekh.data.warehouse.service.*;
import com.outstanding.framework.core.DelStateEnum;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class BetaTagServiceImpl implements BetaTagService {
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private TagRuleService tagRuleService;
    @Autowired
    private AppVersionBetaUserService appVersionBetaUserService;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Value("${tag.allDataSwitch:true}")
    private Boolean allDataSwitch;

    @Override
    public void generateTag(ScheduledTagUsereqDTO scheduledTagUsereqDTO) throws PendingException {
        if (allDataSwitch) {
            int size = 500;
            for(int page=0; ; page++) {
                Pageable pageable = PageRequest.of(page, size);
                List<AppVersionBetaUserBO> list = appVersionBetaUserService.findHistoryData(scheduledTagUsereqDTO,pageable);
                if(CollectionUtils.isEmpty(list)) {
                    break;
                }
                updateUserTag(list);
            }
        }else {
            int size = 500;
            for(int page=0; ; page++) {
                Pageable pageable = PageRequest.of(page, size);
                List<AppVersionBetaUserBO> list = appVersionBetaUserService.findYesterdayData(scheduledTagUsereqDTO,pageable);
                if(CollectionUtils.isEmpty(list)) {
                    break;
                }
                updateUserTag(list);
            }
        }
    }

    private void updateUserTag(List<AppVersionBetaUserBO> list) {
        //批量更新标签列表
        List<UpdateUserTagDTO> updateList = new ArrayList<>();
        list.forEach(appVersionBetaUserBO -> {
            List<UpdateUserTagDTO> newUpdateList = new ArrayList<>();
            //根据语言查询标签规则
            TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.BETA_TAG.getCode(), RuleTypeEnum.BETA_TAG.getCode(), 1);
            if (tagInfo == null) {
                return;
            }
            //根据操作员编号查询用户标签
            TagUserDTO tagUserDTO = tagUserService.selectUserLeftJoinTagInfo(appVersionBetaUserBO.getOperatorNo());
            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
            updateUserTagDTO.setOperatorNo(appVersionBetaUserBO.getOperatorNo());
            updateUserTagDTO.setTagNo(tagInfo.getTagNo());
            updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
            boolean exist = true;
            //判断是否存在内测标签
            if (tagUserDTO != null) {
                for(TagInfoDTO tagInfoDTO : tagUserDTO.getTagInfo()) {
                    if(Objects.nonNull(tagInfoDTO)) {
                        for(TagRuleDTO ruleDTO : tagInfoDTO.getRule()) {
                            //用户已添加过地区标签
                            if(Objects.nonNull(ruleDTO) && RuleTypeEnum.BETA_TAG.getCode().equals(ruleDTO.getRuleType())) {
                                //如果新标签编号和旧标签编号相同则不更新
                                if(tagInfoDTO.getTagNo().equals(tagInfo.getTagNo())) {
                                    exist = false;
                                    if (!"ENABLE".equals(appVersionBetaUserBO.getStatus()) || DelStateEnum.DEL.getCode().equals(appVersionBetaUserBO.getDelState())) {
                                        newUpdateList.add(new UpdateUserTagDTO(appVersionBetaUserBO.getOperatorNo(), tagInfo.getTagNo(),
                                                OptTypeEnum.DEL.getCode(), null, null));
                                        updateList.addAll(newUpdateList);
                                    }
                                    return;
                                }
                                newUpdateList.add(new UpdateUserTagDTO(appVersionBetaUserBO.getOperatorNo(), tagInfoDTO.getTagNo(),
                                        OptTypeEnum.DEL.getCode(), null, null));
                                newUpdateList.add(updateUserTagDTO);
                                updateList.addAll(newUpdateList);
                                return;
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(newUpdateList) && "ENABLE".equals(appVersionBetaUserBO.getStatus()) && DelStateEnum.NORMAL.getCode().equals(appVersionBetaUserBO.getDelState())&& exist) {
                newUpdateList.add(updateUserTagDTO);
            }
            updateList.addAll(newUpdateList);
        });
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        //批量发送
        rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateList));
    }
}
