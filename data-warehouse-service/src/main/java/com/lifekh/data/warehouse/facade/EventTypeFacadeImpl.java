package com.lifekh.data.warehouse.facade;

import com.lifekh.data.warehouse.api.EventTypeFacade;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.EventTypeRespDTO;
import com.lifekh.data.warehouse.service.EventTypeService;
import com.outstanding.framework.core.PageInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("eventTypeFacade")
public class EventTypeFacadeImpl implements EventTypeFacade {

    @Autowired
    private EventTypeService eventTypeService;

    @Override
    public PageInfoDTO<EventTypeRespDTO> queryList(EventTypeQueryReqDTO reqDTO) {
        return eventTypeService.queryList(reqDTO);
    }

    public void add(EventTypeAddReqDTO reqDTO) {
        eventTypeService.add(reqDTO);
    }

    public void disable(EventTypeDisableReqDTO reqDTO) {
        eventTypeService.disable(reqDTO);
    }

    public void enable(EventTypeEnableReqDTO reqDTO) {
        eventTypeService.enable(reqDTO);
    }

    public void update(EventTypeUpdateReqDTO reqDTO) {
        eventTypeService.update(reqDTO);
    }
}
