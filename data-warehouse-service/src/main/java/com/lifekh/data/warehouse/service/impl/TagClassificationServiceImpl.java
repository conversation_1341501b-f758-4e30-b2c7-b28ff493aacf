package com.lifekh.data.warehouse.service.impl;

import com.chaos.usercenter.api.constants.RoleConstant;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationEditReqDTO;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagSeconfClassificationRespDTO;
import com.lifekh.data.warehouse.api.enums.TagClassifyEnum;
import com.lifekh.data.warehouse.api.enums.WarehouseErrorCodeEnum;
import com.lifekh.data.warehouse.bo.TagClassificationBO;
import com.lifekh.data.warehouse.dao.TagClassificationDAO;
import com.lifekh.data.warehouse.service.TagClassificationService;
import com.outstanding.framework.base.sequence.SequenceNo;
import com.outstanding.framework.core.PendingException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/8/26 9:31
 * @Version 1.0
 **/
@Service
public class TagClassificationServiceImpl implements TagClassificationService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    TagClassificationDAO tagClassificationDAO;

    @Autowired
    private SequenceNo sequenceNo;

    private  final String notAllowEditFirstClassificationName = "用户属性";

    @Override
    public List<TagClassificationRespDTO> queryTagClassification(TagClassificationQueryReqDTO reqDTO) throws PendingException {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getTagClassificationName())){
            List<Criteria> criteriaList = new ArrayList<>();
            criteriaList.add(Criteria.where("firstClassificationName").regex(reqDTO.getTagClassificationName()));
            criteriaList.add(Criteria.where("secondaryClassificationName").regex(reqDTO.getTagClassificationName()));
            criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }

        //数据权限
        if (!RoleConstant.ADMIN_ROLE.equals(reqDTO.getRoleNo())) {
            List<String> businessLines = new ArrayList<>();
            businessLines.add(TagClassifyEnum.PUBLIC.getCode());
            if (StringUtils.isNotBlank(reqDTO.getBusinessLine())) {
                businessLines.addAll(Arrays.asList(reqDTO.getBusinessLine().split(",")));
            }
            criteria.and("tagClassify").in(businessLines);
        }

        Query query = new Query(criteria);

        List<TagClassificationBO> classificationBOS = mongoTemplate.find(query,TagClassificationBO.class,"tag_classification");
        Map<String,TagClassificationRespDTO> tagClassificationRespMap = new HashMap<>();
        for (TagClassificationBO tagClassificationBO:classificationBOS){
            TagClassificationRespDTO tagClassificationRespDTO = tagClassificationRespMap.get(tagClassificationBO.getFirstTagClassificationNo());
            if (Objects.isNull(tagClassificationRespDTO)){
                TagClassificationRespDTO tagClassificationRespDTO2 = new TagClassificationRespDTO();
                tagClassificationRespDTO2.setFirstTagClassificationName(tagClassificationBO.getFirstClassificationName());
                tagClassificationRespDTO2.setFirstTagClassificationNameEn(tagClassificationBO.getFirstClassificationNameEn());
                tagClassificationRespDTO2.setFirstTagClassificationNameKm(tagClassificationBO.getFirstClassificationNameKm());
                tagClassificationRespDTO2.setFirstTagClassificationNo(tagClassificationBO.getFirstTagClassificationNo());
                List<TagSeconfClassificationRespDTO> tagSeconfClassificationRespDTOList = new ArrayList<>();
                if (StringUtils.isNotEmpty(tagClassificationBO.getSecondTagClassificationNo())){
                    TagSeconfClassificationRespDTO tagSeconfClassificationRespDTO = new TagSeconfClassificationRespDTO();
                    tagSeconfClassificationRespDTO.setSecondTagClassificationNo(tagClassificationBO.getSecondTagClassificationNo());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationName(tagClassificationBO.getSecondaryClassificationName());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationNameEn(tagClassificationBO.getSecondaryClassificationNameEn());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationNameKm(tagClassificationBO.getSecondaryClassificationNameKm());
                    tagSeconfClassificationRespDTOList.add(tagSeconfClassificationRespDTO);
                }
                tagClassificationRespDTO2.setSecondClassificationTags(tagSeconfClassificationRespDTOList);
                tagClassificationRespMap.put(tagClassificationBO.getFirstTagClassificationNo(),tagClassificationRespDTO2);
            }else {
                if (StringUtils.isNotEmpty(tagClassificationBO.getSecondTagClassificationNo())) {
                    TagSeconfClassificationRespDTO tagSeconfClassificationRespDTO = new TagSeconfClassificationRespDTO();
                    tagSeconfClassificationRespDTO.setSecondTagClassificationNo(tagClassificationBO.getSecondTagClassificationNo());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationName(tagClassificationBO.getSecondaryClassificationName());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationNameEn(tagClassificationBO.getSecondaryClassificationNameEn());
                    tagSeconfClassificationRespDTO.setSecondaryTagClassificationNameKm(tagClassificationBO.getSecondaryClassificationNameKm());
                    tagClassificationRespDTO.getSecondClassificationTags().add(tagSeconfClassificationRespDTO);
                }
            }
        }
        //遍历map返回查询结果
        List<TagClassificationRespDTO> tagClassificationRespDTOS = new ArrayList<>();
        for (Map.Entry<String, TagClassificationRespDTO> entry : tagClassificationRespMap.entrySet()) {
            TagClassificationRespDTO tagClassificationRespDTO = entry.getValue();
            if (notAllowEditFirstClassificationName.equals(tagClassificationRespDTO.getFirstTagClassificationName())){
                tagClassificationRespDTO.setIsAllowEdit(11);
            }else {
                tagClassificationRespDTO.setIsAllowEdit(11);
            }
            tagClassificationRespDTO.setSize(tagClassificationRespDTO.getSecondClassificationTags().size());
            tagClassificationRespDTOS.add(tagClassificationRespDTO);
        }
        return tagClassificationRespDTOS;
    }

    @Override
    public void editOrAddTagClassification(TagClassificationEditReqDTO reqDTO) throws PendingException {
        //查询名称是否重复
        Criteria outCriteria = new Criteria();
        outCriteria.and("firstTagClassificationNo").is(reqDTO.getFirstTagClassificationNo());
        outCriteria.and("secondaryClassificationName").is(reqDTO.getSecondaryClassificationName());
        Query outQuery = new Query(outCriteria);
        TagClassificationBO outClassificationBO = mongoTemplate.findOne(outQuery,TagClassificationBO.class,"tag_classification");
        if (Objects.nonNull(outClassificationBO)){
            if (StringUtils.isEmpty(reqDTO.getSecondTagClassificationNo())){
                if (notAllowEditFirstClassificationName.equals(outClassificationBO.getFirstClassificationName())){
                    //用户属性由脚本生成，不允许新增
                    return;
                }
                //新增，标签下名称重复，抛异常
                return;
            }else {
                //编辑时，如果不是和原记录重复，则抛异常
                if (!reqDTO.getSecondTagClassificationNo().equals(outClassificationBO.getSecondTagClassificationNo())){
                    return;
                }
            }
        }
        if (StringUtils.isEmpty(reqDTO.getSecondTagClassificationNo())){
            Criteria criteria = new Criteria();
            //先取原分类信息
            criteria.and("firstTagClassificationNo").is(reqDTO.getFirstTagClassificationNo());
            Query query = new Query(criteria);
            TagClassificationBO classificationBO = mongoTemplate.findOne(query,TagClassificationBO.class,"tag_classification");
            TagClassificationBO tagClassificationBO1 = new TagClassificationBO();
            tagClassificationBO1.setFirstTagClassificationNo(classificationBO.getFirstTagClassificationNo());
            tagClassificationBO1.setFirstClassificationName(classificationBO.getFirstClassificationName());
            tagClassificationBO1.setSecondTagClassificationNo(sequenceNo.nextSeq());
            tagClassificationBO1.setSecondaryClassificationName(reqDTO.getSecondaryClassificationName());
            tagClassificationBO1.setCreateTime(new Date());
            tagClassificationDAO.save(tagClassificationBO1);
        }else {
            Query query = new Query();
            query.addCriteria(Criteria.where("firstTagClassificationNo").is(reqDTO.getFirstTagClassificationNo()));
            query.addCriteria(Criteria.where("secondTagClassificationNo").is(reqDTO.getSecondTagClassificationNo()));
            Update update = new Update();
            update.set("secondaryClassificationName", reqDTO.getSecondaryClassificationName());
            mongoTemplate.updateMulti(query,update, TagClassificationBO.class);
        }
    }

    @Override
    public String addTagClassification(String tagClassify, String firstTagClassificationNo, String secondaryClassification, String secondaryClassificationEn, String secondaryClassificationKm) throws PendingException {
        //校验名称是否存在（同一业务线）
        long nameCount = mongoTemplate.count(Query.query(Criteria.where(TagClassificationBO.TAG_CLASSIFY).is(tagClassify)
                .and(TagClassificationBO.FIRST_TAG_CLASSIFICATION_NO).is(firstTagClassificationNo)
                .and(TagClassificationBO.SECONDARY_CLASSIFICATION_NAME).is(secondaryClassification)), TagClassificationBO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2004);
        }
        nameCount = mongoTemplate.count(Query.query(Criteria.where(TagClassificationBO.TAG_CLASSIFY).is(tagClassify)
                .and(TagClassificationBO.FIRST_TAG_CLASSIFICATION_NO).is(firstTagClassificationNo)
                .and(TagClassificationBO.SECONDARY_CLASSIFICATION_NAME_EN).is(secondaryClassificationEn)), TagClassificationBO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2005);
        }
        nameCount = mongoTemplate.count(Query.query(Criteria.where(TagClassificationBO.TAG_CLASSIFY).is(tagClassify)
                .and(TagClassificationBO.FIRST_TAG_CLASSIFICATION_NO).is(firstTagClassificationNo)
                .and(TagClassificationBO.SECONDARY_CLASSIFICATION_NAME_KM).is(secondaryClassificationKm)), TagClassificationBO.TABLE_NAME);
        if (nameCount > 0) {
            throw new PendingException(WarehouseErrorCodeEnum.W2006);
        }

        //新增
        TagClassificationBO classificationBO = mongoTemplate.findOne(Query.query(Criteria.where(TagClassificationBO.FIRST_TAG_CLASSIFICATION_NO).is(firstTagClassificationNo)),
                TagClassificationBO.class, TagClassificationBO.TABLE_NAME);
        TagClassificationBO classification = new TagClassificationBO();
        classification.setFirstTagClassificationNo(classificationBO.getFirstTagClassificationNo());
        classification.setFirstClassificationName(classificationBO.getFirstClassificationName());
        classification.setFirstClassificationNameEn(classificationBO.getFirstClassificationNameEn());
        classification.setFirstClassificationNameKm(classificationBO.getFirstClassificationNameKm());
        classification.setSecondTagClassificationNo(sequenceNo.nextSeq());
        classification.setSecondaryClassificationName(secondaryClassification);
        classification.setSecondaryClassificationNameEn(secondaryClassificationEn);
        classification.setSecondaryClassificationNameKm(secondaryClassificationKm);
        classification.setTagClassify(tagClassify);
        classification.setCreateTime(new Date());
        tagClassificationDAO.save(classification);

        return classification.getSecondTagClassificationNo();
    }
}
