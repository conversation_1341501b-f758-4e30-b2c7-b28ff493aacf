package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import com.outstanding.framework.core.PendingException;

public interface LanguageTagService {

    void generateTag(ScheduledTagUsereqDTO scheduledTagUsereqDTO) throws PendingException;

    void updateUserTagOne(UserLabelBO bo) throws PendingException;
}
