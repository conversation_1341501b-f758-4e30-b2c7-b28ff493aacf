package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.service.AppVersionBetaUserService;
import com.lifekh.data.warehouse.service.BetaTagService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class BetaTagStrategy extends AbstractGenerateTagStrategy {

    @Autowired
    private BetaTagService betaTagService;
    @Autowired
    private AppVersionBetaUserService appVersionBetaUserService;

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.BETA_TAG;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {
        betaTagService.generateTag(scheduledStationLetterReqDTO);
    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        List<String> matchNos = new ArrayList<>();
        RuleDTO ruleDTO = rule.getSpecialRules().get(0);
        if (RuleTypeEnum.BETA_TAG.getCode().equals(ruleDTO.getRuleFiled())) {
            matchNos = appVersionBetaUserService.findAllBetaOperatorNo();
        }
        return matchNos;
    }
}
