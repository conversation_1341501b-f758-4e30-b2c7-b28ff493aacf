package com.lifekh.data.warehouse.service;

import com.chaos.usercenter.api.dto.req.UserRegisterReqDTO;
import com.lifekh.data.warehouse.api.enums.DeviceUseStatusEnum;
import com.lifekh.data.warehouse.api.req.DeviceIdReqDTO;
import com.lifekh.data.warehouse.api.resp.DeviceRespDTO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.outstanding.framework.core.PendingException;

import java.util.Date;

/**
 * @Date: 2022/4/8
 * @Description: 设备池服务层
 */
public interface DevicePoolService {

    /**
     * 保存设备
     *
     * @param deviceId
     * @param deviceType
     * @param useStatus
     * @param recordTime
     * @param lastActiveTime
     * @param language
     * @param province
     * @param district
     * @param coordinates
     * @return
     */
    boolean saveDevice(String deviceId, String deviceType, DeviceUseStatusEnum useStatus, Date recordTime, Date lastActiveTime,
                       String language, String province, String district, Double[] coordinates);

    /**
     * 从集合SUP_MOBILE_TOKEN同步设备数据
     *
     * @param startTime
     * @param endTime
     */
    void syncDeviceFromMobileToken(Date startTime, Date endTime);

    /**
     * 保存用户注册消息到临时表
     *
     * @param reqDTO
     */
    void saveUserRegisterMsg(UserRegisterReqDTO reqDTO);

    /**
     * 处理用户注册消息
     */
    void handleUserRegisterDeviceMsg();

    /**
     * 统计设备数据
     */
    void statisticsDeviceData();

    /**
     * 设备相关事件另存
     *
     * @param collectSpm
     */
    void saveDeviceEvent(CollectSpmBO collectSpm);

    /**
     * 处理设备埋点事件临时表
     */
    void handleDeviceEvent();

    /**
     * 获取设备信息
     *
     * @throws PendingException
     */
    DeviceRespDTO getDeviceInfo(DeviceIdReqDTO reqDTO) throws PendingException;
}
