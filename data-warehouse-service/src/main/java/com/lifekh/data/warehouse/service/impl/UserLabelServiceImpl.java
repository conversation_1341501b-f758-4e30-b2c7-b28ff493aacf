package com.lifekh.data.warehouse.service.impl;

import com.chaos.common.enums.AppIdEnum;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import com.lifekh.data.warehouse.dao.UserLabelDAO;
import com.lifekh.data.warehouse.service.UserLabelService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserLabelServiceImpl implements UserLabelService {
    @Autowired
    private UserLabelDAO userLabelDAO;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public void add(UserLabelBO bo) throws PendingException {
        userLabelDAO.save(bo);
    }

    @Override
    public List<UserLabelBO> findYesterdayData(ScheduledTagUsereqDTO scheduledTagUsereqDTO,Pageable pageable) throws PendingException {
        Criteria criteria = new Criteria();
        criteria.and("operatorNo").ne(null);
        criteria.and("appId").is(AppIdEnum.SUPER_APP.getCode());
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("createTime").gt(DateUtil.dateCalculation(new Date(), Calendar.DATE, -1)));
        criteriaList.add(Criteria.where("updateTime").gt(DateUtil.dateCalculation(new Date(), Calendar.DATE, -1)));
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.DESC, "createTime"),
                Aggregation.group("operatorNo")
                        .first("operatorNo").as("OPERATOR_NO")
                        .first("appId").as("APP_ID")
                        .first("language").as("LANGUAGE"),
                Aggregation.skip((pageable.getPageNumber()) * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        );
        AggregationResults<UserLabelBO> outputType = mongoTemplate.aggregate(agg, UserLabelBO.class, UserLabelBO.class);
        return outputType.getMappedResults();
    }

    @Override
    public List<UserLabelBO> findHistoryData(ScheduledTagUsereqDTO scheduledTagUsereqDTO,Pageable pageable) throws PendingException {
        AggregationOptions options = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("OPERATOR_NO").ne(null)
                        .and("appId").is(AppIdEnum.SUPER_APP.getCode())),
                Aggregation.sort(Sort.Direction.DESC, "createTime"),
                Aggregation.group("operatorNo")
                        .first("operatorNo").as("OPERATOR_NO")
                        .first("appId").as("APP_ID")
                        .first("language").as("LANGUAGE"),
                Aggregation.skip((pageable.getPageNumber()) * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        ).withOptions(options);;
        try {
            AggregationResults<UserLabelBO> outputType = mongoTemplate.aggregate(agg, UserLabelBO.class, UserLabelBO.class);
            return outputType.getMappedResults();
        }catch (Exception e){
            log.info("查询标签表异常",e.getMessage());
            return null;
        }
    }

    @Override
    public List<String> findByOperatorNoAndLanguage(String language, List<String> list) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("operatorNo").in(list).and("language").is(language).and("appId").is(AppIdEnum.SUPER_APP.getCode()));
        List<UserLabelBO> bos = mongoTemplate.find(query,UserLabelBO.class);
        if (CollectionUtils.isEmpty(bos)) {
            return null;
        }
        return bos.stream().map(UserLabelBO::getOperatorNo).collect(Collectors.toList());
    }

    @Override
    public List<String> findOperatorNoByPage(int page, int size, String language) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("language").is(language).and("appId").is(AppIdEnum.SUPER_APP.getCode()));
        query.with(PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "createTime")));
        List<UserLabelBO> bos = mongoTemplate.find(query, UserLabelBO.class);
        return bos.stream().map(UserLabelBO::getOperatorNo).collect(Collectors.toList());
    }
}
