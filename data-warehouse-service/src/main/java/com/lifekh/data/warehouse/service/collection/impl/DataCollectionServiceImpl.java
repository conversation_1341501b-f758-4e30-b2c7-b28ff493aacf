package com.lifekh.data.warehouse.service.collection.impl;

import com.lifekh.data.warehouse.api.collect.req.CollectBuriedPointReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.enums.collect.CollectionEventStatusEnum;
import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.collection.CollectHomePageViewBO;
import com.lifekh.data.warehouse.bo.collection.EventTypeBO;
import com.lifekh.data.warehouse.constant.FunctionSwitchConstant;
import com.lifekh.data.warehouse.dao.collect.EventTypeDAO;
import com.lifekh.data.warehouse.manage.FunctionSwitchManager;
import com.lifekh.data.warehouse.service.collection.DataCollectionService;
import com.lifekh.data.warehouse.service.collection.OtherDataCollectionService;
import com.lifekh.data.warehouse.strategy.collection.EventGroupStrategyContext;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.lifekh.data.warehouse.constant.ClickReportConstant.WOWNOW_HOME_PAGE_NAME_LIST;

/**
 * <AUTHOR>
 * @Date 2021/9/28 11:30
 * @Version 1.0
 **/
@Service
@Slf4j
public class DataCollectionServiceImpl implements DataCollectionService {

    @Autowired
    EventTypeDAO eventTypeDAO;

    @Autowired
    EventGroupStrategyContext eventGroupStrategyContext;

    @Autowired
    OtherDataCollectionService otherDataCollectionService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private FunctionSwitchManager functionSwitchManager;

    @Override
    public void saveStandardCollectionData(StandardDataCollectionReqDTO standardDataCollectionReqDTO) throws PendingException {
        EventTypeBO eventTypeBO = eventTypeDAO.queryByEventNo(standardDataCollectionReqDTO.getEvent());
        if (Objects.isNull(eventTypeBO) || CollectionEventStatusEnum.CLOSE.getCode().equals(eventTypeBO.getStatus())) {
            //事件不存在或事件采集已关闭
            return;
        }
        standardDataCollectionReqDTO.setEventGroup(eventTypeBO.getEventGroup());
        standardDataCollectionReqDTO.setEventName(eventTypeBO.getEventName());
        standardDataCollectionReqDTO.setTable(eventTypeBO.getTable());
        eventGroupStrategyContext.excuteSaveUserEventData(standardDataCollectionReqDTO, EventGroupEnum.getByCode(standardDataCollectionReqDTO.getEventGroup()));
    }

    @Override
    public void saveOtherCollectionData(StandardDataCollectionReqDTO otherDataCollectionReqDTO) throws PendingException {
        saveStandardCollectionData(otherDataCollectionReqDTO);
    }

    @Override
    public void collectBuriedPoint(CollectBuriedPointReqDTO reqDTO) throws PendingException {
        if (CollectionUtils.isEmpty(reqDTO.getEvenInfos())) {
            return;
        }

        List<StandardDataCollectionReqDTO> collectDtoList = new ArrayList<>();
        reqDTO.getEvenInfos().forEach(dto -> {
            EventTypeBO eventType = eventTypeDAO.queryByEventNo(dto.getEvent());
            if (Objects.isNull(eventType) || CollectionEventStatusEnum.CLOSE.getCode().equals(eventType.getStatus())) {
                //事件不存在或事件采集已关闭
                return;
            }
            StandardDataCollectionReqDTO data = new StandardDataCollectionReqDTO()
                    .setEventGroup(eventType.getEventGroup())
                    .setEvent(eventType.getEventNo())
                    .setEventName(eventType.getEventName())
                    .setTable(eventType.getTable())
                    .setOperatorNo(reqDTO.getOperatorNo())
                    .setLoginName(reqDTO.getLoginName())
                    .setLanguage(reqDTO.getLanguage())
                    .setIp(reqDTO.getIp())
                    .setAppId(reqDTO.getAppId())
                    .setAppNo(reqDTO.getAppNo())
                    .setChannel(reqDTO.getChannel())
                    .setAppVersion(reqDTO.getAppVersion())
                    .setDeviceId(reqDTO.getDeviceId())
                    .setDeviceType(reqDTO.getDeviceType())
                    .setSessionId(dto.getSessionId())
                    .setBusinessName(dto.getBusinessName())
                    .setBusinessLine(dto.getBusinessLine())
                    .setLongitude(dto.getLongitude())
                    .setLatitude(dto.getLatitude())
                    .setRecordTime(dto.getRecordTime())
                    .setSpm(dto.getSpm())
                    .setExt(dto.getExt());
            collectDtoList.add(data);
            eventGroupStrategyContext.excuteSaveUserEventData(data, EventGroupEnum.getByCode(data.getEventGroup()));
        });


        //首页流量埋点临时表
        collectHomePageData(collectDtoList);
    }

    @Override
    public void collectHomePageData(List<StandardDataCollectionReqDTO> standardCollectionDatas) throws PendingException {
        if(functionSwitchManager.getSwitchBool(FunctionSwitchConstant.COLLECT_HOME_PAGE_SWITCH)) {
            String homePage = "", homePageOperatorNo = "", homePageDeviceId = "";
            String takeawayPage = "", takeawayPageOperatorNo = "", takeawayPageDeviceId = "";
            for(StandardDataCollectionReqDTO reqDTO : standardCollectionDatas) {
                //判断是否是WOWNOW首页埋点
                if(reqDTO != null
                        && "@viewPage".equals(reqDTO.getEvent())
                        && reqDTO.getSpm() != null
                        && WOWNOW_HOME_PAGE_NAME_LIST.contains(reqDTO.getSpm().getCurrentPage())) {
                    homePage = "WOWNOW_HOME_PAGE";
                    homePageOperatorNo = StringUtils.isBlank(reqDTO.getOperatorNo()) ? homePageOperatorNo : reqDTO.getOperatorNo();
                    homePageDeviceId = StringUtils.isBlank(reqDTO.getDeviceId()) ? homePageDeviceId : reqDTO.getDeviceId();
                }

                //判断是否是外卖首页埋点
                if(reqDTO != null
                        && "takeawayPageEnter".equals(reqDTO.getEvent())) {
                    takeawayPage = "TAKEAWAY_HOME_PAGE";
                    takeawayPageOperatorNo = StringUtils.isBlank(reqDTO.getOperatorNo()) ? takeawayPageOperatorNo : reqDTO.getOperatorNo();
                    takeawayPageDeviceId = StringUtils.isBlank(reqDTO.getDeviceId()) ? takeawayPageDeviceId : reqDTO.getDeviceId();
                }
            }

            try {
                //获取当前小时所在的时间
                Date currentHour = DateUtil.getCurrentHour();

                //WOWNOW首页埋点入库
                if(StringUtils.isNotBlank(homePage) && StringUtils.isNotBlank(homePageDeviceId)) {
                    CollectHomePageViewBO bo = mongoTemplate.findOne(new Query(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).gte(currentHour)
                            .and(CollectHomePageViewBO.DEVICE_ID).is(homePageDeviceId)), CollectHomePageViewBO.class, MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
                    //防止未统计到当前小时登录的用户
                    if(bo != null && StringUtils.isBlank(bo.getOperatorNo()) && StringUtils.isNotBlank(homePageOperatorNo)) {
                        bo = mongoTemplate.findOne(new Query(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).gte(currentHour)
                                .and(CollectHomePageViewBO.OPERATOR_NO).is(homePageOperatorNo)), CollectHomePageViewBO.class, MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
                    }

                    if(bo == null) {
                        mongoTemplate.insert(new CollectHomePageViewBO(null, new Date(),
                                homePageOperatorNo, homePageDeviceId), MongoDbCollectonName.COLLECT_BURIED_POINT_HOME_PAGE_TEMP);
                    }
                }

                //外卖首页埋点入库
                if(StringUtils.isNotBlank(takeawayPage) && StringUtils.isNotBlank(takeawayPageDeviceId)) {
                    CollectHomePageViewBO bo = mongoTemplate.findOne(new Query(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).gte(currentHour)
                            .and(CollectHomePageViewBO.DEVICE_ID).is(takeawayPageDeviceId)), CollectHomePageViewBO.class, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP);
                    //防止未统计到当前小时登录的用户
                    if(bo != null && StringUtils.isBlank(bo.getOperatorNo()) && StringUtils.isNotBlank(takeawayPageOperatorNo)) {
                        bo = mongoTemplate.findOne(new Query(new Criteria().and(CollectHomePageViewBO.CREATE_TIME).gte(currentHour)
                                .and(CollectHomePageViewBO.OPERATOR_NO).is(takeawayPageOperatorNo)), CollectHomePageViewBO.class, MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP);
                    }

                    if(bo == null) {
                        mongoTemplate.insert(new CollectHomePageViewBO(null, new Date(),
                                takeawayPageOperatorNo, takeawayPageDeviceId), MongoDbCollectonName.COLLECT_BURIED_POINT_TAKEAWAY_HOME_PAGE_TEMP);
                    }
                }
            } catch (Exception e) {
                log.error("首页流量埋点入库异常", e);
            }
        }
    }
}