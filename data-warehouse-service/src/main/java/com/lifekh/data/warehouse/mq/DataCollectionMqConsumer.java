package com.lifekh.data.warehouse.mq;

import com.lifekh.data.warehouse.api.collect.req.CollectBuriedPointReqDTO;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.service.collection.DataCollectionService;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.plugin.mq.rocketmq.annotation.RocketMQMessageListener;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户数据采集
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = Topic.DATA_WAREHOUSE_COLLECTION_MQ_TOPIC, consumerGroup = "${spring.application.name}", consumeThreadMax = 1)
public class DataCollectionMqConsumer implements RocketMQListener<String> {

    @Autowired
    private DataCollectionService dataCollectionService;

    @Override
    public void onMessage(String message) {
        log.info("开始用户数据采集:{}", message);
        try {
            CollectBuriedPointReqDTO reqDTO = FastJsonUtil.jsonToObject(message, CollectBuriedPointReqDTO.class);
            dataCollectionService.collectBuriedPoint(reqDTO);
        } catch (Exception e) {
            log.error("用户数据采集消费异常", e);
        }
        log.info("结束用户数据采集:{}", message);
    }
}
