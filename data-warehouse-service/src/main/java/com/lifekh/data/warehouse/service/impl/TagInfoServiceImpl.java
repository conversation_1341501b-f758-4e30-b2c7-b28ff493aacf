package com.lifekh.data.warehouse.service.impl;


import com.chaos.common.enums.LanguageEnum;
import com.chaos.usercenter.api.constants.RoleConstant;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.ClassificationTagReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoFirstClassificationRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagInfoSeconfClassificationRespDTO;
import com.lifekh.data.warehouse.api.enums.*;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.TagDetailQueryRespDTO;
import com.lifekh.data.warehouse.api.resp.TagInfoHasUserRespDTO;
import com.lifekh.data.warehouse.bo.*;
import com.lifekh.data.warehouse.dao.TagInfoDAO;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagRuleService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.outstanding.framework.base.sequence.SequenceNo;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("tagInfoService")
public class TagInfoServiceImpl implements TagInfoService {

    @Autowired
    private TagInfoDAO tagInfoDAO;
    @Autowired
    private SequenceNo sequenceNo;
    @Autowired
    private TagRuleService tagRuleService;
    @Autowired
    private TagUserService tagUserService;

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String tagFirstClassification = "用户属性";

    private final String tagSecondaryClassification = "其他";

    @Override
    public void saveTag(TagInfoBO tagInfoBO) {
        tagInfoBO.setCreateTime(new Date());
        tagInfoBO.setTagNo(sequenceNo.nextSeq());
        tagInfoBO.setFirstClassification(tagFirstClassification);
        tagInfoBO.setSecondaryClassification(tagSecondaryClassification);
        tagInfoDAO.save(tagInfoBO);
    }

    @Override
    public PageInfoDTO<TagInfoDTO> findList(TagListQueryReqDTO reqDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getTagType())) {
            criteria.and("tagType").is(reqDTO.getTagType());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagName())) {
            List<Criteria> criteriaList = new ArrayList<>();
            for (LanguageEnum languageEnum : LanguageEnum.values()) {
                criteriaList.add(Criteria.where("tagName."+languageEnum.getCode()).regex(reqDTO.getTagName()));
            }
            criteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagClassify())) {
            criteria.and("tagClassify").is(reqDTO.getTagClassify());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagNo())) {
            criteria.and("tagNo").is(reqDTO.getTagNo());
        }
        if (StringUtils.isNotEmpty(reqDTO.getTagStatus())) {
            criteria.and("tagStatus").is(reqDTO.getTagStatus());
        }
        Query query = new Query(criteria);

        long total = mongoTemplate.count(query, TagInfoBO.class);

        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());

        List<TagInfoDTO> tagInfoDTOS = mongoTemplate.find(query,TagInfoDTO.class,"tag_info");

        PageInfoDTO<TagInfoDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(tagInfoDTOS.size());
        target.setTotal(total);
        target.setList(tagInfoDTOS);
        return target;
    }

    @Override
    public TagDetailQueryRespDTO detail(TagDetailQueryReqDTO reqDTO) {
        return tagInfoDAO.findByTagNo(reqDTO.getTagNo());
    }

    @Override
    public void editTag(TagEditReqDTO reqDTO) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tagNo").is(reqDTO.getTagNo()));
        Update update = new Update();
        //TagRuleDTO tagRuleDTO = tagRuleService.selectByRuleValue(RuleTypeEnum.CUSTOMIZE.getCode(),RuleTypeEnum.CUSTOMIZE.getCode());
        //TagRuleBO tagRuleBO = new TagRuleBO();
       // BeanCopierHelper.copyProperties(tagRuleDTO,tagRuleBO);
        TagUserListReqDTO tagUserListReqDTO = new TagUserListReqDTO();
        tagUserListReqDTO.setTagNo(reqDTO.getTagNo());
        List<TagUserDTO> tagUserDTOS = tagUserService.list(tagUserListReqDTO).getList();
/*        if (CollectionUtils.isEmpty(tagUserDTOS)) {
            update.set("rule", Arrays.asList(tagRuleBO));
        }*/
        update.set("tagName", reqDTO.getTagName());
        update.set("tagClassify",reqDTO.getTagClassify());
        update.set("tagDescription",reqDTO.getTagDescription());
        mongoTemplate.updateMulti(query,update,TagInfoBO.class);
    }

    @Override
    public void deleteTag(TagDeleteReqDTO reqDTO) {
//        tagInfoDAO.deleteById(reqDTO.getId());
    }

    @Override
    public TagInfoBO selectByRuleNo(String ruleNo) throws PendingException {
        return null;//tagInfoDAO.findByRuleNosContains(ruleNo);
    }

    @Override
    public List<TagInfoBO> findByRuleType(String ruleType) {
        return mongoTemplate.find(Query.query(Criteria.where("rule.ruleType").is(ruleType)), TagInfoBO.class);
    }

    @Override
    public TagInfoBO findFirstByCondition(String ruleType, String ruleValue, Integer size) {
        if (StringUtils.isEmpty(ruleValue)) {
            return null;
        }
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("rule.ruleType").is(ruleType)
                        .and("rule.ruleValue").is(ruleValue)
                        .and("rule").size(size))
        );
        AggregationResults<TagInfoBO> outputType = mongoTemplate.aggregate(agg, TagInfoBO.class, TagInfoBO.class);
        List<TagInfoBO> list=outputType.getMappedResults();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public PageInfoDTO<TagInfoHasUserRespDTO> listForHasUser(TagInfoHasUserReqDTO reqDTO) throws PendingException {
        Criteria criteria = new Criteria();
        if (StringUtils.isEmpty(reqDTO.getTagType())) {
            criteria.and("tagType").is(TagTypeEnum.BASIC_TAG.getCode());
        }
        Query query = new Query(criteria);

        long total = mongoTemplate.count(query, TagInfoBO.class);

        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());

        List<TagInfoHasUserRespDTO> list = mongoTemplate.find(query,TagInfoHasUserRespDTO.class,"tag_info");

        PageInfoDTO<TagInfoHasUserRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(list.size());
        target.setList(list);
        target.setTotal(total);
        return target;
    }

    @Override
    public void updateStatus(TagInfoUpdateStatusReqDTO reqDTO) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("tagNo").is(reqDTO.getTagNo()));
        Update update = new Update();
        update.set("tagStatus", reqDTO.getTagStatus());
        update.set("updateTime", new Date());
        mongoTemplate.updateMulti(query,update,TagInfoBO.class);
    }


    @Override
    public List<TagInfoRespDTO> queryTagByTagNos(List<String> tagNos) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("tagNo").in(tagNos));
        return mongoTemplate.find(query, TagInfoRespDTO.class, TagInfoBO.TABLE_NAME);
    }

    @Override
    public void add(TagAddReqDTO reqDTO) throws PendingException {
        TagInfoBO bo = new TagInfoBO();
        BeanCopierHelper.copyProperties(reqDTO,bo);
        TagRuleDTO tagRuleDTO = tagRuleService.selectByRuleValue(RuleTypeEnum.CUSTOMIZE.getCode(),RuleTypeEnum.CUSTOMIZE.getCode());
        TagRuleBO tagRuleBO = new TagRuleBO();
        BeanCopierHelper.copyProperties(tagRuleDTO,tagRuleBO);
        bo.setRule(Arrays.asList(tagRuleBO));
        saveTag(bo);
    }

    @Override
    public List<TagInfoFirstClassificationRespDTO> queryClassificationTagByTagName(ClassificationTagReqDTO reqDTO) {
        List<TagInfoFirstClassificationRespDTO> resps = new ArrayList<>();

        //获取语言参数,进行匹配
        String language = StringUtils.isBlank(reqDTO.getLanguage()) ? LanguageEnum.EN_US.getCode() : reqDTO.getLanguage();
        Criteria criteria = Criteria.where("tagStatus").is(TagStatusEnum.OPEN.getCode());
        //数据权限
        if (!RoleConstant.ADMIN_ROLE.equals(reqDTO.getRoleNo())) {
            List<Criteria> criterias = new ArrayList<>();
            Criteria ownCri = Criteria.where("tagScope").is(TagScopeEnum.OWN.getCode()).and("createBy").is(reqDTO.getLoginName());
            criterias.add(ownCri);

            List<String> businessLines = new ArrayList<>();
            businessLines.add(TagClassifyEnum.PUBLIC.getCode());
            if (StringUtils.isNotBlank(reqDTO.getBusinessLine())) {
                businessLines.addAll(Arrays.asList(reqDTO.getBusinessLine().split(",")));
            }
            Criteria everyCri = Criteria.where("tagScope").is(TagScopeEnum.EVERYONE.getCode()).and("tagClassify").in(businessLines);
            criterias.add(everyCri);
            criteria.orOperator(criterias);
        }

        //查询标签
        List<TagInfoV2BO> tags = mongoTemplate.find(Query.query(criteria), TagInfoV2BO.class);
        if (!tags.isEmpty()) {
            Map<String, List<TagInfoV2BO>> tagMap = tags.stream().filter(t -> StringUtils.isNotBlank(t.getSecondaryClassificationNo())).collect(Collectors.groupingBy(TagInfoV2BO::getSecondaryClassificationNo));

            //查询分类
            Set<String> secondClassNos = tags.stream().map(TagInfoV2BO::getSecondaryClassificationNo).collect(Collectors.toSet());
            List<TagClassificationBO> classifications = mongoTemplate.find(Query.query(Criteria.where("secondTagClassificationNo").in(secondClassNos)), TagClassificationBO.class);
            Map<String, List<TagClassificationBO>> classificationMap = classifications.stream()
                    .filter(c -> StringUtils.isNotBlank(c.getSecondTagClassificationNo()))
                    .collect(Collectors.groupingBy(TagClassificationBO::getFirstTagClassificationNo));

            //遍历分类
            classificationMap.forEach((k, v) -> {
                //一级分类
                TagInfoFirstClassificationRespDTO firstClassification = new TagInfoFirstClassificationRespDTO();
                firstClassification.setFirstTagClassificationNo(k);
                firstClassification.setSize(v.size());

                //二级分类
                List<TagInfoSeconfClassificationRespDTO> secondClassifications = new ArrayList<>();
                v.forEach(c -> {
                    firstClassification.setFirstTagClassificationName(c.getFirstClassificationName());

                    TagInfoSeconfClassificationRespDTO seconfClassification = new TagInfoSeconfClassificationRespDTO();
                    seconfClassification.setSecondaryTagClassificationNo(c.getSecondTagClassificationNo());
                    seconfClassification.setSecondaryTagClassificationName(c.getSecondaryClassificationName());
                    if (LanguageEnum.EN_US.getCode().equals(language)) {
                        seconfClassification.setSecondaryTagClassificationName(c.getSecondaryClassificationNameEn());
                        firstClassification.setFirstTagClassificationName(c.getFirstClassificationNameEn());
                    } else if(LanguageEnum.KM_KH.getCode().equals(language)) {
                        seconfClassification.setSecondaryTagClassificationName(c.getSecondaryClassificationNameKm());
                        firstClassification.setFirstTagClassificationName(c.getFirstClassificationNameKm());
                    }

                    //二级分类下的标签
                    List<TagInfoClassificationRespDTO> tagResps = new ArrayList<>();
                    List<TagInfoV2BO> secTags = tagMap.get(c.getSecondTagClassificationNo());
                    if (secTags != null) {
                        secTags.forEach(t -> {
                            TagInfoClassificationRespDTO tagResp = new TagInfoClassificationRespDTO();
                            tagResp.setTagNo(t.getTagNo());
                            tagResp.setTagName(t.getTagName().get(language));
                            tagResp.setTagClassify(t.getTagClassify());
                            tagResp.setTagType(t.getTagType());
                            tagResp.setTagCatalogue(t.getTagCatalogue());
                            tagResps.add(tagResp);
                        });
                    }
                    seconfClassification.setTagInfos(tagResps);
                    secondClassifications.add(seconfClassification);
                });
                firstClassification.setTagInfoSeconfClassifications(secondClassifications);
                resps.add(firstClassification);
            });
        }
        return resps;
    }
}
