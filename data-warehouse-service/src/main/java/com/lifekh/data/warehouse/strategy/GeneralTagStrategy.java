package com.lifekh.data.warehouse.strategy;

import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.service.GeneralTagService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class GeneralTagStrategy extends AbstractGenerateTagStrategy {
    @Autowired
    private GeneralTagService generalTagService;

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.GENERAL_USER;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {
        generalTagService.updateUserTag();
    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        return new ArrayList<>();
    }
}
