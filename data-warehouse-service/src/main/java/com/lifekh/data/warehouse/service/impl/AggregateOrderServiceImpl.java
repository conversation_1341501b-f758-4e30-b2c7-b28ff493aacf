package com.lifekh.data.warehouse.service.impl;

import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.bo.ogg.AggregateOrderBO;
import com.lifekh.data.warehouse.dao.AggregateOrderDAO;
import com.lifekh.data.warehouse.service.AggregateOrderService;
import com.outstanding.framework.core.PendingException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class AggregateOrderServiceImpl implements AggregateOrderService {
    @Autowired
    private AggregateOrderDAO aggregateOrderDAO;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public Page<AggregateOrderBO> findList(Pageable pageable) {
        return aggregateOrderDAO.findAll(pageable);
    }

    @Override
    public List<AggregateOrderBO> findByUpdateTimeAndAggregateOrderFinalState(Date date, Integer aggregateOrderFinalState,String businessLine,Pageable pageable) throws PendingException {
        Criteria criteria = new Criteria();
        criteria.and("orderTime").gte(date);
        criteria.and("aggregateOrderFinalState").is(aggregateOrderFinalState);
        criteria.and("businessLine").is(businessLine);
        AggregationOptions options = AggregationOptions.builder().allowDiskUse(true).build();
        GroupOperation groupOperation = Aggregation.group("userId").count().as("orderCount").sum("actualPayAmount").as("ACTUAL_PAY_AMOUNT").first("userId").as("USER_ID");
        Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),groupOperation,
                Aggregation.skip((pageable.getPageNumber()) * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())).withOptions(options);
        AggregationResults<AggregateOrderBO> results = mongoTemplate.aggregate(agg,AggregateOrderBO.class,AggregateOrderBO.class);
        return results.getMappedResults();
    }

    @Override
    public List<AggregateOrderBO> findByOperatorNosAndUpdateTime(List<String> operatorNos, Date date, Integer aggregateOrderFinalState, String businessLine) {
        Criteria criteria = new Criteria();
        criteria.and("orderTime").gte(date);
        criteria.and("aggregateOrderFinalState").is(aggregateOrderFinalState);
        criteria.and("businessLine").is(businessLine);
        criteria.and("userId").in(operatorNos);
        AggregationOptions options = AggregationOptions.builder().allowDiskUse(true).build();
        GroupOperation groupOperation = Aggregation.group("userId").count().as("orderCount").sum("actualPayAmount").as("ACTUAL_PAY_AMOUNT").first("userId").as("USER_ID");
        Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),groupOperation).withOptions(options);
        AggregationResults<AggregateOrderBO> results = mongoTemplate.aggregate(agg,AggregateOrderBO.class,AggregateOrderBO.class);
        return results.getMappedResults();
    }

    @Override
    public List<AggregateOrderBO> findOrderByPage(int page, int size, Date startTime, Date endTime, List<Integer> aggregateOrderFinalState, String businessLine, String payType) {
        Criteria criteria = new Criteria();
        criteria.and("orderTime").gte(startTime).lt(endTime);
        criteria.and("businessLine").is(businessLine);
        if (CollectionUtils.isNotEmpty(aggregateOrderFinalState)) {
            criteria.and("aggregateOrderFinalState").in(aggregateOrderFinalState);
        }
        AggregationOptions options = AggregationOptions.builder().allowDiskUse(true).build();
        GroupOperation groupOperation = Aggregation.group("userId")
                .first("userId").as("USER_ID")
                .count().as("orderCount")
                .sum("actualPayAmount").as("ACTUAL_PAY_AMOUNT")
                .min("updateTime").as("UPDATE_TIME");
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                groupOperation,
                Aggregation.sort(Sort.Direction.ASC, "UPDATE_TIME"),
                Aggregation.skip(page * size),
                Aggregation.limit(size)).withOptions(options);
        AggregationResults<AggregateOrderBO> results = mongoTemplate.aggregate(agg, AggregateOrderBO.class, AggregateOrderBO.class);
        return results.getMappedResults();
    }

    @Override
    public AggregateOrderBO findFirstOrderByUserId(String userId) {
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("USER_ID").is(userId)),
                Aggregation.project(Fields.from(
                        Fields.field("USER_ID", "USER_ID"),
                        Fields.field("BUSINESS_LINE", "BUSINESS_LINE"),
                        Fields.field("AGGREGATE_ORDER_NO", "AGGREGATE_ORDER_NO"),
                        Fields.field("CREATE_TIME", "CREATE_TIME"))),
                Aggregation.sort(Sort.Direction.ASC, "CREATE_TIME"),
                Aggregation.limit(1))
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<AggregateOrderBO> results = mongoTemplate.aggregate(agg, MongoDbCollectonName.AGGREGATE_ORDER, AggregateOrderBO.class);
        return results.getMappedResults().isEmpty() ? null : results.getMappedResults().get(0);
    }
}
