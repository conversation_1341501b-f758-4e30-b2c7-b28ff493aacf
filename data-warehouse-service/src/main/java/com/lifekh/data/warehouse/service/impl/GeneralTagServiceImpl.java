package com.lifekh.data.warehouse.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.chaos.common.enums.AppIdEnum;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.TagInfoDTO;
import com.lifekh.data.warehouse.api.dto.TagRuleDTO;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.bo.TagInfoBO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagDTO;
import com.lifekh.data.warehouse.api.dto.req.UpdateUserTagMQReqDTO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorInfoBO;
import com.lifekh.data.warehouse.dao.UserOperatorInfoDAO;
import com.lifekh.data.warehouse.service.GeneralTagService;
import com.lifekh.data.warehouse.service.TagInfoService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.utils.DateUtil;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class GeneralTagServiceImpl implements GeneralTagService {
    @Autowired
    private UserOperatorInfoDAO userOperatorInfoDAO;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private TagUserService tagUserService;
    @Override
    public void updateUserTag() throws PendingException {
        int size = 500;
        for(int page=0; ; page++) {
            Pageable pageable = PageRequest.of(page, size);
            List<UserOperatorInfoBO> list = userOperatorInfoDAO.findByCreateTimeAfter(DateUtil.dateCalculation(new Date(), Calendar.DATE, -1),pageable);
            if(CollectionUtils.isEmpty(list)) {
                break;
            }
            updateUserTagList(list);
        }
    }
    private void updateUserTagList(List<UserOperatorInfoBO> list) {
        //批量更新标签列表
        List<UpdateUserTagDTO> updateList = new ArrayList<>();
        //对用户标签数据进行遍历贴标签
        list.forEach(bo -> {
            updateUserTag(bo,updateList);
        });
        if (org.apache.commons.collections.CollectionUtils.isEmpty(updateList)) {
            return;
        }
        log.info("批量发送更新普通用户标签数据：{},{}",updateList.size(),updateList.get(0).toString());
        //批量发送
        rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateList));
    }
    public void updateUserTag(UserOperatorInfoBO bo,List<UpdateUserTagDTO> updateList) {
        List<UpdateUserTagDTO> newUpdateList = new ArrayList<>();
        if (AppIdEnum.SUPER_APP.getCode().equals(bo.getAppId())) {
            //根据普通用户标签规则
            TagInfoBO tagInfo = tagInfoService.findFirstByCondition(RuleTypeEnum.GENERAL_USER.getCode(), RuleTypeEnum.GENERAL_USER.getCode(), 1);
            if (tagInfo == null) {
                return;
            }
            //根据标签规则编号查询标签表
            TagUserDTO tagUserDTO = tagUserService.selectUserLeftJoinTagInfo(bo.getOperatorNo());
            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
            updateUserTagDTO.setOperatorNo(bo.getOperatorNo());
            updateUserTagDTO.setTagNo(tagInfo.getTagNo());
            updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
            boolean exist = true;
            if (tagUserDTO != null ) {
                for(TagInfoDTO tagInfoDTO : tagUserDTO.getTagInfo()) {
                    if(Objects.nonNull(tagInfoDTO)) {
                        for(TagRuleDTO ruleDTO : tagInfoDTO.getRule()) {
                            //用户已添加过地区标签
                            if(Objects.nonNull(ruleDTO) && RuleTypeEnum.GENERAL_USER.getCode().equals(ruleDTO.getRuleType())) {
                                //如果新标签编号和旧标签编号相同则不更新
                                if(tagInfoDTO.getTagNo().equals(tagInfo.getTagNo())) {
                                    exist = false;
                                    return;
                                }
                                //删除旧标签
                                newUpdateList.add(new UpdateUserTagDTO(bo.getOperatorNo(), tagInfoDTO.getTagNo(),
                                        OptTypeEnum.DEL.getCode(), null, null));
                                //添加新标签
                                newUpdateList.add(updateUserTagDTO);
                                updateList.addAll(newUpdateList);
                                return;
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(newUpdateList) && exist) {
                newUpdateList.add(updateUserTagDTO);
            }
        }
        updateList.addAll(newUpdateList);
    }
}
