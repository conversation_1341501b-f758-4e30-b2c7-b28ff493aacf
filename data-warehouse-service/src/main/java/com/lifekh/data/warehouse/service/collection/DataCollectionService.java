package com.lifekh.data.warehouse.service.collection;

import com.lifekh.data.warehouse.api.collect.req.CollectBuriedPointReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface DataCollectionService {
    void saveStandardCollectionData(StandardDataCollectionReqDTO standardDataCollectionReqDTO) throws PendingException;
    void saveOtherCollectionData(StandardDataCollectionReqDTO otherDataCollectionReqDTO) throws PendingException;

    /**
     * 埋点采集
     *
     * @param reqDTO
     * @throws PendingException
     */
    void collectBuriedPoint(CollectBuriedPointReqDTO reqDTO) throws PendingException;

    void collectHomePageData(List<StandardDataCollectionReqDTO> standardCollectionDatas) throws PendingException;
}
