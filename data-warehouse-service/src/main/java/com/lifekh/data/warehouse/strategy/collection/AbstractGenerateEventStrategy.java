package com.lifekh.data.warehouse.strategy.collection;

import com.chaos.common.enums.BusinessLineEnum;
import com.lifekh.data.warehouse.api.collect.req.CollectEntranceReqDTO;
import com.lifekh.data.warehouse.api.collect.req.CollectSpmReqDTO;
import com.lifekh.data.warehouse.api.collect.req.StandardDataCollectionReqDTO;
import com.lifekh.data.warehouse.bo.ZoneBO;
import com.lifekh.data.warehouse.bo.behavior.*;
import com.lifekh.data.warehouse.bo.collection.CollectBaseBO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationDTO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.Decimal128;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2021/9/28 17:58
 * @Version 1.0
 **/
@Slf4j
public abstract class AbstractGenerateEventStrategy implements DataCollectionStrategy {

    protected MongoTemplate mongoTemplate;
    protected ZoneDAO zoneDAO;
    protected LocationManage locationManage;

    protected AbstractGenerateEventStrategy(MongoTemplate mongoTemplate, ZoneDAO zoneDAO, LocationManage locationManage) {
        this.mongoTemplate = mongoTemplate;
        this.zoneDAO = zoneDAO;
        this.locationManage = locationManage;
    }

    /**
     * 保存事件（模板方法）
     *
     * @param reqDTO
     * @throws PendingException
     */
    @Override
    public final void saveUserEventData(StandardDataCollectionReqDTO reqDTO) throws PendingException {
        CollectSpmBO collectSpmBO = new CollectSpmBO();

        convertBaseBo(collectSpmBO, reqDTO);

        convertSpm(collectSpmBO, reqDTO);

        convertEntrance(collectSpmBO, reqDTO);

        //业务信息
        collectSpmBO.setCreateTime(new Date());
        collectSpmBO.setBusinessName(reqDTO.getBusinessName());
        collectSpmBO.setRecordTime(reqDTO.getRecordTime());
        collectSpmBO.setSessionId(reqDTO.getSessionId());

        //下单业务处理
        if("order_submit".equals(reqDTO.getEvent())) {
            setOrderEvenBusinessLines(reqDTO.getBusinessName(), collectSpmBO);
        }

        if (StringUtils.isNotBlank(reqDTO.getBusinessLine())) {
            collectSpmBO.setBusinessLine(reqDTO.getBusinessLine());
        }

        String table = reqDTO.getTable();
        if (StringUtils.isNotBlank(table)) {
            mongoTemplate.insert(collectSpmBO, table);
        } else {
            log.warn("获取存储表为空,请检查配置, eventNo: {}", reqDTO.getEvent());
        }

        //调用后置处理方法
        doAfter(collectSpmBO);
    }

    public void setOrderEvenBusinessLines(String businessName, CollectSpmBO collectSpmBO) {
        if(StringUtils.isBlank(businessName)) {
            return;
        }
        if(businessName.startsWith("外卖")) {
            collectSpmBO.setBusinessLine(BusinessLineEnum.YUMNOW.getCode());
        }
        if(businessName.startsWith("电商")) {
            collectSpmBO.setBusinessLine(BusinessLineEnum.TINHNOW.getCode());
        }
        if(businessName.startsWith("团购")) {
            collectSpmBO.setBusinessLine(BusinessLineEnum.GROUP_BUY.getCode());
        }
    }

    /**
     * 填充页面位置信息
     *
     * @param collectSpmBO
     * @param reqDTO
     */
    @Override
    public void convertSpm(CollectSpmBO collectSpmBO, StandardDataCollectionReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getSpm())) {
            CollectSpmReqDTO spmReqDTO = reqDTO.getSpm();
            collectSpmBO.setAppNo(reqDTO.getAppNo());
            collectSpmBO.setParentPage(spmReqDTO.getParentPage());
            collectSpmBO.setCurrentPage(spmReqDTO.getCurrentPage());
            collectSpmBO.setCurrentArea(spmReqDTO.getCurrentArea());
            collectSpmBO.setNode(spmReqDTO.getNode());
            collectSpmBO.setChildPage(spmReqDTO.getChildPage());
            collectSpmBO.setStayTime(spmReqDTO.getStayTime());
        }
    }

    /**
     * 后置处理方法
     *
     * @param collectSpmBO
     * @throws PendingException
     */
    protected abstract void doAfter(CollectSpmBO collectSpmBO) throws PendingException;

    /**
     * 填充公共信息
     *
     * @param collectBase
     * @param reqDTO
     */
    private void convertBaseBo(CollectBaseBO collectBase, StandardDataCollectionReqDTO reqDTO) {
        //用户信息
        UserInfoBO userInfoBo = new UserInfoBO();
        userInfoBo.setLanguage(reqDTO.getLanguage());
        userInfoBo.setAppId(reqDTO.getAppId());
        userInfoBo.setOperatorNo(reqDTO.getOperatorNo());
        userInfoBo.setLoginName(reqDTO.getLoginName());
        collectBase.setUserInfoBo(userInfoBo);

        //应用信息
        ApplicationInfoBO applicationInfoBo = new ApplicationInfoBO();
        applicationInfoBo.setAppNo(reqDTO.getAppNo());
        applicationInfoBo.setChannel(reqDTO.getChannel());
        applicationInfoBo.setAppVersion(reqDTO.getAppVersion());
        collectBase.setApplicationInfoBo(applicationInfoBo);

        //设备信息
        DeviceInfoBO deviceInfoBo = new DeviceInfoBO();
        deviceInfoBo.setDeviceId(reqDTO.getDeviceId());
        deviceInfoBo.setDeviceType(reqDTO.getDeviceType());
        deviceInfoBo.setAppVersion(reqDTO.getAppVersion());
        collectBase.setDeviceInfoBo(deviceInfoBo);

        //事件信息
        EventBO eventBo = new EventBO();
        eventBo.setEvent(reqDTO.getEvent());
        eventBo.setEventName(reqDTO.getEventName());
        collectBase.setEventBo(eventBo);

        //网络信息
        NetworkBO networkBo = new NetworkBO();
        networkBo.setIp(reqDTO.getIp());
        collectBase.setNetworkBo(networkBo);

        //位置信息
        LocationBO locationBo = searchLocationInfo(reqDTO.getLongitude(), reqDTO.getLatitude());
        collectBase.setLocationBo(locationBo);

        //业务信息
        collectBase.setExt(reqDTO.getExt());


    }

    /**
     * 填充入口信息
     *
     * @param collectBase
     * @param reqDTO
     */
    private void convertEntrance(CollectBaseBO collectBase, StandardDataCollectionReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getEntrance())) {
            CollectEntranceReqDTO entrance = reqDTO.getEntrance();
            EntranceBO entranceBO = new EntranceBO();
            entranceBO.setEntranceId(entrance.getEntranceId());
            entranceBO.setEntranceName(entrance.getEntranceName());
            entranceBO.setEntranceType(entrance.getEntranceType());
            collectBase.setEntrance(entranceBO);
        }

    }

    /**
     * 查询位置信息
     *
     * @param longitude
     * @param latitude
     * @return
     * @throws PendingException
     */
    private LocationBO searchLocationInfo(String longitude, String latitude) throws PendingException {
        LocationBO locationBo = new LocationBO();
        if (StringUtils.isNotEmpty(longitude) && StringUtils.isNotEmpty(latitude)) {
            Decimal128 longitudeD = Decimal128.parse(longitude);
            Decimal128 latitudeD = Decimal128.parse(latitude);
            //根据经纬度查询区域
            if (Objects.nonNull(longitudeD) && Objects.nonNull(latitudeD)) {
                locationBo.setCoordinates(new Double[]{Double.parseDouble(longitude), Double.parseDouble(latitude)});
                //根据经纬度找出对应的省份名称
                LocationDTO locationDTO = locationManage.getNameByPosition(longitudeD.toString(), latitudeD.toString());
                if (Objects.nonNull(locationDTO)) {
                    //找出省份对应的编码
                    locationBo.setProvinceName(locationDTO.getProvinceName());
                    List<ZoneBO> zoneList = zoneDAO.findByMsgEn(locationDTO.getProvinceName());
                    locationBo.setProvinceNo(CollectionUtils.isNotEmpty(zoneList) ? zoneList.get(0).getCode() : null);
                    locationBo.setProvinceNameKm(CollectionUtils.isNotEmpty(zoneList) ? zoneList.get(0).getMsgCb() : null);
                    locationBo.setProvinceNameZh(CollectionUtils.isNotEmpty(zoneList) ? zoneList.get(0).getMsgZh() : null);
                    locationBo.setAreaName(locationDTO.getDistinctName());
                }
            }
            locationBo.setLongitude(longitudeD.bigDecimalValue().floatValue());
            locationBo.setLatitude(latitudeD.bigDecimalValue().floatValue());
        }
        return locationBo;
    }
}
