package com.lifekh.data.warehouse.service;


import com.lifekh.data.warehouse.api.dto.req.TagClassificationEditReqDTO;
import com.lifekh.data.warehouse.api.dto.req.TagClassificationQueryReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagClassificationRespDTO;
import com.outstanding.framework.core.PendingException;

import java.util.List;

public interface TagClassificationService {
    /**
     *
     *获取标签分类选项
     * @return
     * @throws PendingException
     */
    List<TagClassificationRespDTO> queryTagClassification(TagClassificationQueryReqDTO tagClassificationQueryReqDTO) throws PendingException;

    /**
     * 编辑标签类别
     * @param reqDTO
     * @throws PendingException
     */
    void editOrAddTagClassification(TagClassificationEditReqDTO reqDTO) throws PendingException;


    String addTagClassification(String tagClassify, String firstTagClassificationNo, String secondaryClassification, String secondaryClassificationEn, String secondaryClassificationKm) throws PendingException;
}
