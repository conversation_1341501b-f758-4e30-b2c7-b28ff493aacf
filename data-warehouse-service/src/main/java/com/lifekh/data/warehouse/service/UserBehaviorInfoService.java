package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.outstanding.framework.core.PendingException;

public interface UserBehaviorInfoService {

    void add(UserBehaviorInfoBO bo) throws PendingException;

    /**
     * 保存用户行为信息
     *
     * @throws PendingException
     */
    void collectBehaviorInfoFromGatewayBuriedPoint(String message) throws PendingException;

    /**
     * 发送用户登录任务（活动任务完成需要）
     *
     * @param collectSpmBO
     */
    void sendUserLoginTaskMq(CollectSpmBO collectSpmBO);
}
