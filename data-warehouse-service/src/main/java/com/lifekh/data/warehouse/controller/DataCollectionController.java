package com.lifekh.data.warehouse.controller;


import com.chaos.common.enums.LanguageEnum;
import com.lifekh.data.warehouse.api.collect.req.*;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.service.collection.DataCollectionService;
import com.lifekh.data.warehouse.utils.FastJsonUtil;
import com.outstanding.framework.core.Context;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * 埋点-数据采集
 * @module data-warehouse-collection
 */
@RequestMapping("/warehouse/collection")
@RestController
@Slf4j
public class DataCollectionController {

    @Autowired
    DataCollectionService dataCollectionService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    private ThreadPoolExecutor poolExecutor ;

    @Value("${data.collection.thread.pool.size:1}")
    private int dataCollectionThreadPoolSize;

    @Value("${data.collection.thread.pool.queue.size:50}")
    private int dataCollectionThreadPoolQueueSize;

    @PostConstruct
    public void init() {
        poolExecutor =  new ThreadPoolExecutor(dataCollectionThreadPoolSize, dataCollectionThreadPoolSize,
                60L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(dataCollectionThreadPoolQueueSize),new ThreadPoolExecutor.AbortPolicy());
    }



    /**
     * 采集标准埋点数据
     * @throws
     */
    @RequestMapping(value = "/saveStandardCollectionData.do", method = RequestMethod.POST)
    @ResponseBody
    public void saveStandardCollectionData(@RequestBody StandardDataCollectionListReqDTO collectionDatas,
                                           @RequestHeader(value = "clientIp",required = false) String ip,
                                           @RequestHeader(value = "deviceid",required = false) String deviceid,
                                           @RequestHeader(value = "channel",required = false) String channel,
                                           @RequestHeader( value = "accept-language",required = false) String language) throws PendingException {
        for (StandardDataCollectionReqDTO standardDataCollectionReqDTO : collectionDatas.getStandardCollectionDatas()) {
            if (standardDataCollectionReqDTO == null) {
                continue;
            }
            standardDataCollectionReqDTO.setIp(ip);
            if(StringUtils.isBlank(standardDataCollectionReqDTO.getLanguage())) {
                standardDataCollectionReqDTO.setLanguage(Optional.ofNullable(language).orElse(LanguageEnum.EN_US.getCode()));
            }
            if (Objects.nonNull(standardDataCollectionReqDTO.getSpm())&&StringUtils.isNotEmpty(standardDataCollectionReqDTO.getAppNo())){
                standardDataCollectionReqDTO.getSpm().setAppNo(standardDataCollectionReqDTO.getAppNo());
            }
            if (StringUtils.isBlank(standardDataCollectionReqDTO.getLoginName())) {
                standardDataCollectionReqDTO.setLoginName(collectionDatas.getLoginName());
            }
            if (StringUtils.isEmpty(standardDataCollectionReqDTO.getDeviceId())){
                standardDataCollectionReqDTO.setDeviceId(deviceid);
            }
        }

        try {
            poolExecutor.execute(()-> saveStandardData(collectionDatas));
        } catch (Exception e) {
            log.warn("线程池异常，无法处理标准事件埋点数据，需要MQ补偿,loginName:{}", collectionDatas.getLoginName());
            try {
                rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_COLLECTION_STANDARD_MQ_TOPIC, collectionDatas);
            } catch (Exception exception) {
                log.error("MQ发送失败,loginName:{}", collectionDatas.getLoginName());
            }
        }

    }

    private void saveStandardData(StandardDataCollectionListReqDTO collectionDatas) {
        for (StandardDataCollectionReqDTO standardDataCollectionReqDTO : collectionDatas.getStandardCollectionDatas()) {
            if (standardDataCollectionReqDTO == null) {
                continue;
            }
            dataCollectionService.saveStandardCollectionData(standardDataCollectionReqDTO);
        }

        //首页流量埋点临时表
        dataCollectionService.collectHomePageData(collectionDatas.getStandardCollectionDatas());
    }

    /**
     * 采集其他埋点数据
     * @throws
     */
    @RequestMapping(value = "/saveOtherCollectionData.do", method = RequestMethod.POST)
    @ResponseBody
    public void saveOtherCollectionData(@RequestBody OtherDataCollectionListReqDTO collectionDatas,
                                        @RequestHeader(value = "clientIp",required = false) String ip,
                                        @RequestHeader(value = "deviceid",required = false) String deviceid,
                                        @RequestHeader( value = "accept-language",required = false) String language) throws PendingException {
        for (StandardDataCollectionReqDTO otherDataCollectionReqDTO :collectionDatas.getOtherCollectionDatas()) {
            if (otherDataCollectionReqDTO == null) {
                continue;
            }
            otherDataCollectionReqDTO.setIp(ip);
            if(StringUtils.isBlank(otherDataCollectionReqDTO.getLanguage())) {
                otherDataCollectionReqDTO.setLanguage(Optional.ofNullable(language).orElse(LanguageEnum.EN_US.getCode()));
            }
            if (Objects.nonNull(otherDataCollectionReqDTO.getSpm())&&StringUtils.isNotEmpty(otherDataCollectionReqDTO.getAppNo())){
                otherDataCollectionReqDTO.getSpm().setAppNo(otherDataCollectionReqDTO.getAppNo());
            }
            if (StringUtils.isBlank(otherDataCollectionReqDTO.getLoginName())) {
                otherDataCollectionReqDTO.setLoginName(collectionDatas.getLoginName());
            }
            if (StringUtils.isEmpty(otherDataCollectionReqDTO.getDeviceId())){
                otherDataCollectionReqDTO.setDeviceId(deviceid);
            }
        }

        try {
            poolExecutor.execute(()-> saveOtherData(collectionDatas));
        } catch (Exception e) {
            log.warn("线程池异常，无法处理其它事件埋点数据，需要MQ补偿,loginName:{}", collectionDatas.getLoginName());
            try {
                rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_COLLECTION_OTHER_MQ_TOPIC, collectionDatas);
            } catch (Exception exception) {
                log.error("MQ发送失败,loginName:{}", collectionDatas.getLoginName());
            }
        }
    }

    private void saveOtherData(OtherDataCollectionListReqDTO collectionDatas) {
        for (StandardDataCollectionReqDTO otherDataCollectionReqDTO : collectionDatas.getOtherCollectionDatas()) {
            if (otherDataCollectionReqDTO == null) {
                continue;
            }
            dataCollectionService.saveOtherCollectionData(otherDataCollectionReqDTO);
        }

        //首页流量埋点临时表
        dataCollectionService.collectHomePageData(collectionDatas.getOtherCollectionDatas());
    }

    /**
     * 埋点采集
     *
     * @param reqDTO
     * @throws PendingException
     */
    @RequestMapping(value = "/buriedPoint.do", method = RequestMethod.POST)
    @ResponseBody
    public void collectBuriedPoint(@RequestBody CollectBuriedPointReqDTO reqDTO,
                                   @RequestHeader(value = "clientIp", required = false) String ip,
                                   @RequestHeader(value = "deviceid", required = false) String deviceid,
                                   @RequestHeader(value = "appId", required = false) String appId,
                                   @RequestHeader(value = "appNo", required = false) String appNo,
                                   @RequestHeader(value = "language", required = false) String language,
                                   @RequestHeader(value = "appVersion", required = false) String appVersion,
                                   @RequestHeader(value = "termTyp", required = false) String deviceType,
                                   @RequestHeader(value = "operatorNo", required = false) String operatorNo,
                                   @RequestHeader(value = "channel", required = false) String channel) throws PendingException {
        reqDTO.setIp(Optional.ofNullable(ip).orElse(reqDTO.getIp()));
        reqDTO.setDeviceId(Optional.ofNullable(deviceid).orElse(reqDTO.getDeviceId()));
        reqDTO.setAppId(Optional.ofNullable(appId).orElse(reqDTO.getAppId()));
        reqDTO.setAppNo(Optional.ofNullable(appNo).orElse(reqDTO.getAppNo()));
        reqDTO.setLanguage(Optional.ofNullable(language).orElse(reqDTO.getLanguage()));
        reqDTO.setAppVersion(Optional.ofNullable(appVersion).orElse(reqDTO.getAppVersion()));
        reqDTO.setDeviceType(Optional.ofNullable(deviceType).orElse(reqDTO.getDeviceType()));
        reqDTO.setOperatorNo(Optional.ofNullable(operatorNo).orElse(reqDTO.getOperatorNo()));
        reqDTO.setChannel(Optional.ofNullable(channel).orElse(reqDTO.getChannel()));

        try {
            poolExecutor.execute(()-> dataCollectionService.collectBuriedPoint(reqDTO));
        } catch (Exception e) {
            log.warn("线程池异常，无法处理事件埋点数据，需要MQ补偿,loginName:{}", reqDTO.getLoginName());
            try {
                rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_COLLECTION_MQ_TOPIC, FastJsonUtil.objectToJson(reqDTO));
            } catch (Exception exception) {
                log.error("MQ发送失败,loginName:{}", reqDTO.getLoginName());
            }
        }
    }
}