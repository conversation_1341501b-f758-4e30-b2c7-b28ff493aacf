package com.lifekh.data.warehouse.facade;

import cn.hutool.core.date.DateUtil;
import com.lifekh.data.warehouse.api.UserBehaviorFacade;
import com.lifekh.data.warehouse.api.constants.MongoDbCollectonName;
import com.lifekh.data.warehouse.api.dto.req.QueryInactiveUserReqDTO;
import com.lifekh.data.warehouse.api.dto.req.UserBehaviorSearchReqDTO;
import com.lifekh.data.warehouse.api.dto.resp.InactiveUserRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserBehaviorRespDTO;
import com.lifekh.data.warehouse.bo.DwdUserBehaviorInfoDO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class UserBehaviorFacadeImpl implements UserBehaviorFacade {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public PageInfoDTO<InactiveUserRespDTO> queryInactiveUser(QueryInactiveUserReqDTO reqDTO) throws PendingException {
        List<InactiveUserRespDTO> users = new ArrayList<>();
        long total = 0L;

        if (reqDTO.getLastOnlineTime() != null
                || (reqDTO.getRegisterStartTime() != null && reqDTO.getRegisterEndTime() != null)) {
            Criteria criteria = new Criteria();
            if (reqDTO.getLastOnlineTime() != null) {
                //取当天开始时间和结束时间
                Date startTime = DateUtil.beginOfDay(reqDTO.getLastOnlineTime());
                Date endTime = DateUtil.endOfDay(reqDTO.getLastOnlineTime());
                criteria.and(DwdUserBehaviorInfoDO.ONLINE_TIME).gte(startTime).lte(endTime);
            }
            if (reqDTO.getRegisterStartTime() != null && reqDTO.getRegisterEndTime() != null) {
                criteria.and(DwdUserBehaviorInfoDO.REGISTER_TIME).gte(reqDTO.getRegisterStartTime()).lte(reqDTO.getRegisterEndTime());
            }
            Query query = Query.query(criteria);
            query.fields().include(DwdUserBehaviorInfoDO.OPERATOR_NO);
            total = mongoTemplate.count(query, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
            if (total > 0) {
                query.with(Sort.by(Sort.Direction.ASC, DwdUserBehaviorInfoDO.ONLINE_TIME));
                query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
                query.limit(reqDTO.getPageSize());
                users = mongoTemplate.find(query, InactiveUserRespDTO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
            }
        }

        PageInfoDTO<InactiveUserRespDTO> page = new PageInfoDTO<>();
        page.setPageSize(reqDTO.getPageSize());
        page.setPageNum(reqDTO.getPageNum());
        page.setSize(users.size());
        page.setTotal(total);
        page.setPages(((int) total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        page.setHasNextPage(page.getPageNum() < page.getPages());
        page.setList(users);
        return page;
    }

    @Override
    public List<UserBehaviorRespDTO> queryUserBehavior(UserBehaviorSearchReqDTO reqDTO) throws PendingException {
        List<UserBehaviorRespDTO> respList = new ArrayList<>();

        if (reqDTO.getLastOnlineTime() != null
                || (reqDTO.getRegisterStartTime() != null && reqDTO.getRegisterEndTime() != null)) {
            Criteria criteria = new Criteria();
            if (reqDTO.getRegisterStartTime() != null && reqDTO.getRegisterEndTime() != null) {
                criteria.and(DwdUserBehaviorInfoDO.REGISTER_TIME).gte(reqDTO.getRegisterStartTime()).lte(reqDTO.getRegisterEndTime());
            }
            if (reqDTO.getLastOnlineTime() != null) {
                //取当天开始时间和结束时间
                Date startTime = DateUtil.beginOfDay(reqDTO.getLastOnlineTime());
                Date endTime = DateUtil.endOfDay(reqDTO.getLastOnlineTime());
                criteria.and(DwdUserBehaviorInfoDO.ONLINE_TIME).gte(startTime).lte(endTime);
            }
            Query query = Query.query(criteria);
            query.fields().include(DwdUserBehaviorInfoDO.OPERATOR_NO);
            query.with(Sort.by(Sort.Direction.ASC, DwdUserBehaviorInfoDO.ONLINE_TIME));
            query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
            query.limit(reqDTO.getPageSize());
            respList = mongoTemplate.find(query, UserBehaviorRespDTO.class, MongoDbCollectonName.DWD_USER_BEHAVIOR_INFO);
        }
        return respList;
    }
}
