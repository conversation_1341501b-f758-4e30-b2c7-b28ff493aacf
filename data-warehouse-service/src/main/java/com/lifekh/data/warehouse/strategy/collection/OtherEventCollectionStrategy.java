package com.lifekh.data.warehouse.strategy.collection;

import com.lifekh.data.warehouse.api.enums.collect.EventGroupEnum;
import com.lifekh.data.warehouse.bo.collection.CollectSpmBO;
import com.lifekh.data.warehouse.dao.ZoneDAO;
import com.lifekh.data.warehouse.manage.LocationManage;
import com.lifekh.data.warehouse.service.UserBehaviorInfoService;
import com.lifekh.data.warehouse.service.discovery.DiscoveryService;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/5/24
 **/
@Component
@Slf4j
public class OtherEventCollectionStrategy extends AbstractGenerateEventStrategy {

    public OtherEventCollectionStrategy(MongoTemplate mongoTemplate, ZoneDAO zoneDAO, LocationManage locationManage) {
        super(mongoTemplate, zoneDAO, locationManage);
    }

    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private UserBehaviorInfoService userBehaviorInfoService;

    @Override
    public EventGroupEnum getEventGroup() {
        return EventGroupEnum.OTHER;
    }

    @Override
    public void doAfter(CollectSpmBO collectSpmBO) throws PendingException {
        //内容分享数
        discoveryService.staticContentShareCount(collectSpmBO);

        //发送用户登录mq
        userBehaviorInfoService.sendUserLoginTaskMq(collectSpmBO);

        //发现频道任务mq
        discoveryService.sendDiscoveryTaskMq(collectSpmBO);
    }
}