package com.lifekh.data.warehouse.service;

import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.EventTypeRespDTO;
import com.outstanding.framework.core.PageInfoDTO;

public interface EventTypeService {

    PageInfoDTO<EventTypeRespDTO> queryList(EventTypeQueryReqDTO reqDTO);

    void add(EventTypeAddReqDTO reqDTO);

    void disable(EventTypeDisableReqDTO reqDTO);

    void enable(EventTypeEnableReqDTO reqDTO);

    void update(EventTypeUpdateReqDTO reqDTO);
}
