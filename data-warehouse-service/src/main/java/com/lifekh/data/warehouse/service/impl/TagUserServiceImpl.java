package com.lifekh.data.warehouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.chaos.common.enums.AppIdEnum;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.UserTagGroupFacade;
import com.chaos.usercenter.api.dto.req.TagGroupGetReqDTO;
import com.chaos.usercenter.api.dto.resp.OperatorBatchQueryDTO;
import com.chaos.usercenter.api.dto.resp.TagGroupRespDTO;
import com.chaos.usercenter.api.enums.TagLogicEnum;
import com.lifekh.data.warehouse.api.constants.Topic;
import com.lifekh.data.warehouse.api.dto.TagUserDTO;
import com.lifekh.data.warehouse.api.dto.req.*;
import com.lifekh.data.warehouse.api.dto.resp.SimpleTagInfoRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.TagUserRespDTO;
import com.lifekh.data.warehouse.api.dto.resp.UserTagInfoRespDTO;
import com.lifekh.data.warehouse.api.enums.OptTypeEnum;
import com.lifekh.data.warehouse.api.enums.SymbolEnum;
import com.lifekh.data.warehouse.api.enums.TagExecStatusEnum;
import com.lifekh.data.warehouse.api.req.*;
import com.lifekh.data.warehouse.api.resp.GroupContainUserRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserGetRespDTO;
import com.lifekh.data.warehouse.api.resp.TagUserInfoRespDTO;
import com.lifekh.data.warehouse.bo.TagInfoV2BO;
import com.lifekh.data.warehouse.bo.TagUserBO;
import com.lifekh.data.warehouse.bo.TagUserTempBO;
import com.lifekh.data.warehouse.bo.UserBehaviorInfoBO;
import com.lifekh.data.warehouse.bo.behavior.LocationBO;
import com.lifekh.data.warehouse.bo.behavior.UserInfoBO;
import com.lifekh.data.warehouse.bo.ogg.UserLabelBO;
import com.lifekh.data.warehouse.bo.ogg.UserOperatorLoginInfoBO;
import com.lifekh.data.warehouse.dao.*;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.lifekh.data.warehouse.service.AggregateOrderService;
import com.lifekh.data.warehouse.service.LanguageTagService;
import com.lifekh.data.warehouse.service.TagUserService;
import com.lifekh.data.warehouse.service.ZoneTagService;
import com.outstanding.framework.core.BeanCopierHelper;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TagUserServiceImpl implements TagUserService {

    @Autowired
    private TagUserDAO tagUserDAO;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private UserOperatorInfoDAO userOperatorInfoDAO;

    @Autowired
    private UserOperatorLoginInfoDAO userOperatorLoginInfoDAO;
    @Autowired
    private TagUserService tagUserService;
    @Autowired
    private LanguageTagService languageTagService;
    @Autowired
    private TagInfoDAO tagInfoDAO;
    @Autowired
    private TagUserTempDAO tagUserTempDAO;
    @Autowired
    private AggregateOrderService aggregateOrderService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private UserTagGroupFacade userTagGroupFacade;
    @Autowired
    private UserOperatorFacade userOperatorFacade;
    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;


    @Value("${user.tag.expire.hour:72}")
    private Integer userTagExpireHour;
    @Value("${user.tag.update.size:100}")
    private Integer updateTagSize;
    @Value("${user.tag.internelRuleNo:1429759338576302080}")
    private String internelRuleNo;
    @Value("${user.tag.generalRuleNo:1419569630149836806}")
    private String generalRuleNo;

    
    @Autowired
    private ZoneTagService zoneTagService;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    //@Autowired
    //private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void add(TagUserAddReqDTO reqDTO) throws PendingException {
        TagUserBO tagUserBO = new TagUserBO();
        tagUserBO.setOperatorNo(reqDTO.getOperatorNo());
        tagUserBO.setTagNo(reqDTO.getTagNo());
        tagUserBO.setCreateTime(new Date());
        tagUserBO.setUpdateTime(new Date());
        tagUserBO.setNickname(reqDTO.getNickname());
        tagUserBO.setMobile(reqDTO.getMobile());
        tagUserDAO.save(tagUserBO);
    }

    @Override
    public void edit(TagUserEditReqDTO reqDTO) throws PendingException {
        TagUserBO tagUserBO = new TagUserBO();
        BeanCopierHelper.copyProperties(reqDTO,tagUserBO);
        tagUserBO.setUpdateTime(new Date());
        tagUserDAO.save(tagUserBO);
    }

    @Override
    public TagUserDTO selectUserLeftJoinTagInfo(String operatorNo) throws PendingException {
        Criteria criteria = new Criteria();
        criteria.and("operatorNo").is(operatorNo);
        //联合查询总条数，分页用
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup("tag_info","tagNo","tagNo","tagInfo")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<TagUserDTO> aggregate = mongoTemplate.aggregate(
                aggregationCount ,"tag_user",TagUserDTO.class//A表，是查询的主表
        );
        List<TagUserDTO> tagRuleBOS =  aggregate.getMappedResults();
        if (CollectionUtils.isNotEmpty(tagRuleBOS)) {
            return tagRuleBOS.get(0);
        }
        return null;
    }

    @Override
    public void updateUserTag(List<UpdateUserTagDTO> list) throws PendingException {
        list.forEach(updateUserTagDTO -> {
            if (StringUtils.isEmpty(updateUserTagDTO.getOperatorNo())||StringUtils.isEmpty(updateUserTagDTO.getTagNo())) {
                return;
            }

            //查询用户手机号和昵称
            List<OperatorBatchQueryDTO> operators = userOperatorFacade.batchQuery(Collections.singletonList(updateUserTagDTO.getOperatorNo()));
            OperatorBatchQueryDTO operator = CollectionUtils.isNotEmpty(operators) ? operators.get(0) : null;
            if(operator == null || !AppIdEnum.SUPER_APP.getCode().equals(operator.getAppId())) {
                return;
            }

            try {
                if (OptTypeEnum.DEL.getCode().equals(updateUserTagDTO.getOptType())) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where(TagUserBO.OPERATOR_NO).is(updateUserTagDTO.getOperatorNo()));
                    Update update = new Update();
                    update.pull(TagUserBO.TAG_NO, updateUserTagDTO.getTagNo());
                    update.set(TagUserBO.MOBILE, operator.getLoginName());
                    update.set(TagUserBO.NICKNAME, operator.getNickname());
                    update.set(TagUserBO.UPDATE_TIME, new Date());
                    mongoTemplate.updateMulti(query, update, TagUserBO.class);
                } else {
                    TagUserBO tagUserBO = tagUserDAO.findByOperatorNo(updateUserTagDTO.getOperatorNo());
                    if (tagUserBO != null) {
                        Query query = new Query();
                        query.addCriteria(Criteria.where(TagUserBO.OPERATOR_NO).is(updateUserTagDTO.getOperatorNo()));
                        Update update = new Update();
                        update.addToSet(TagUserBO.TAG_NO, updateUserTagDTO.getTagNo());
                        update.set(TagUserBO.MOBILE, operator.getLoginName());
                        update.set(TagUserBO.NICKNAME, operator.getNickname());
                        update.set(TagUserBO.UPDATE_TIME, new Date());
                        mongoTemplate.updateMulti(query, update, TagUserBO.class);

                        //普通用户和WOWNOW员工标签只能存在一个
                        if (internelRuleNo.equals(updateUserTagDTO.getTagNo()) || generalRuleNo.equals(updateUserTagDTO.getTagNo())) {
                            Query delQuery = new Query();
                            delQuery.addCriteria(Criteria.where(TagUserBO.OPERATOR_NO).is(updateUserTagDTO.getOperatorNo()));
                            Update delUpdate = new Update();
                            delUpdate.pull(TagUserBO.TAG_NO, generalRuleNo.equals(updateUserTagDTO.getTagNo()) ? internelRuleNo : generalRuleNo);
                            mongoTemplate.updateMulti(delQuery, delUpdate, TagUserBO.class);
                        }
                    } else {
                        //新增用户标签
                        TagUserAddReqDTO reqDTO = new TagUserAddReqDTO();
                        reqDTO.setOperatorNo(updateUserTagDTO.getOperatorNo());
                        reqDTO.setTagNo(Arrays.asList(updateUserTagDTO.getTagNo()));
                        reqDTO.setMobile(operator.getLoginName());
                        reqDTO.setNickname(operator.getNickname());
                        tagUserService.add(reqDTO);
                    }
                }

                //更新标签执行状态
                this.updateTagExecStatus(updateUserTagDTO.getTagNo(), OptTypeEnum.ADD.getCode().equals(updateUserTagDTO.getOptType()));

//                redisTemplate.delete(UserTagRedisKeyUtils.getUserTagKey(updateUserTagDTO.getOperatorNo()));
            } catch (Exception e) {
                log.info("更新用户标签异常",e);
            }
        });
    }

    @Override
    public PageInfoDTO<TagUserDTO> list(TagUserListReqDTO reqDTO) throws PendingException {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(reqDTO.getTagNo())) {
            criteria.and("tagNo").is(reqDTO.getTagNo());
        }
        if (CollectionUtils.isNotEmpty(reqDTO.getTagNos())) {
            criteria.and("tagNo").all(reqDTO.getTagNos());
        }
        if (StringUtils.isNotEmpty(reqDTO.getOperatorNo())) {
            criteria.and("operatorNo").is(reqDTO.getOperatorNo());
        }
        if (StringUtils.isNotEmpty(reqDTO.getMobile())) {
            criteria.and("mobile").is(reqDTO.getMobile());
        }
        if (StringUtils.isNotEmpty(reqDTO.getNickname())) {
            criteria.and("nickname").is(reqDTO.getNickname());
        }
        Query query = new Query(criteria);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.DESC, "createTime"),
                Aggregation.skip((reqDTO.getPageNum()-1) * reqDTO.getPageSize()),
                Aggregation.limit(reqDTO.getPageSize()),
                Aggregation.lookup("tag_info","tagNo","tagNo","tagInfo")
        ).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        long total = mongoTemplate.count(query, TagUserBO.class);
        AggregationResults<TagUserDTO> aggregate = mongoTemplate.aggregate(
                aggregation ,TagUserBO.class,TagUserDTO.class
        );
        List<TagUserDTO> tagUserBOS =  aggregate.getMappedResults();
        PageInfoDTO<TagUserDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(tagUserBOS.size());
        target.setList(tagUserBOS);
        target.setTotal(total);
        return target;
    }

    @Override
    public void updateNickname(String operatorNo, String nickname, String language) throws PendingException {
        if (StringUtils.isNotEmpty(nickname)) {
            //更新昵称
            Query query = new Query();
            query.addCriteria(Criteria.where("operatorNo").is(operatorNo));
            Update update = new Update();
            update.set("nickname", nickname);
            mongoTemplate.updateMulti(query,update,TagUserBO.class);
        }
        if (StringUtils.isNotEmpty(language)) {
            //更新语言标签
            UserLabelBO userLabelBO = new UserLabelBO();
            userLabelBO.setOperatorNo(operatorNo);
            userLabelBO.setLanguage(language);
            if (StringUtils.isNotEmpty(userLabelBO.getOperatorNo()) && StringUtils.isNotEmpty(userLabelBO.getLanguage())) {
                languageTagService.updateUserTagOne(userLabelBO);
            }
        }
    }

    @Override
    public TagUserRespDTO queryByOperatorNo(TagUserQueryReqDTO reqDTO) throws PendingException {
//        //查询缓存是否存在
//        String userTagJson = "";
//        try {
//            userTagJson = redisTemplate.opsForValue().get(UserTagRedisKeyUtils.getUserTagKey(reqDTO.getOperatorNo()));
//        } catch (Exception e) {
//            log.info("查询缓存异常",e);
//        }
//        if(StringUtils.isNotBlank(userTagJson) && !"null".equalsIgnoreCase(userTagJson)) {
//            log.info("命中缓存,operatorNo:{}", reqDTO.getOperatorNo());
//            return FastJsonUtil.jsonToObject(userTagJson, TagUserRespDTO.class);
//        }

        //查询mongodb
        TagUserBO tagUserBO = mongoTemplate.findOne(new Query(Criteria.where("operatorNo").is(reqDTO.getOperatorNo())),TagUserBO.class, TagUserBO.TABLE_NAME);

        if(Objects.isNull(tagUserBO)) {
            TagUserRespDTO respDTO = new TagUserRespDTO();
            respDTO.setOperatorNo(reqDTO.getOperatorNo());
            return respDTO;
        }

//        //缓存2天
//        try {
//            redisTemplate.opsForValue().set(UserTagRedisKeyUtils.getUserTagKey(reqDTO.getOperatorNo()),
//                    FastJsonUtil.objectToJson(tagUserBO), userTagExpireHour, TimeUnit.HOURS);
//        } catch (Exception e) {
//            log.info("查询缓存异常",e);
//        }
        return tagUserBO.clone(TagUserRespDTO.class);
    }

    @Override
    public void tagUserImport(TagUserImportReqDTO reqDTO) throws PendingException {
        if (CollectionUtils.isEmpty(reqDTO.getOperatorNoList())) {
            return;
        }
        List<TagUserTempBO> tagUserTempBOS = new ArrayList<>();
        reqDTO.getOperatorNoList().forEach(operatorNo -> {
            TagUserTempBO tagUserTempBO = new TagUserTempBO();
            tagUserTempBO.setOperatorNo(operatorNo);
            tagUserTempBO.setTagNo(reqDTO.getTagNo());
            tagUserTempBO.setCreateTime(new Date());
            tagUserTempBOS.add(tagUserTempBO);
        });
        //批量插入数据
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, TagUserTempBO.class);
        operations.insert(tagUserTempBOS);
        operations.execute();
    }

    @Override
    public void tagUserDelete(TagUserDeleteReqDTO reqDTO) throws PendingException {
        if (StringUtils.isBlank(reqDTO.getTagNo()) ||
                (CollectionUtils.isEmpty(reqDTO.getOperatorNos()) && CollectionUtils.isEmpty(reqDTO.getMobiles()))) {
            return;
        }

        this.sendTagUserDeleteMsg(reqDTO);
    }

    /**
     * 发送删除标签消息
     *
     * @param reqDTO
     */
    private void sendTagUserDeleteMsg(TagUserDeleteReqDTO reqDTO) {
        List<UpdateUserTagDTO> userTags = new ArrayList<>();

        List<String> optNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reqDTO.getOperatorNos())) {
            optNos = reqDTO.getOperatorNos();
        } else if (CollectionUtils.isNotEmpty(reqDTO.getMobiles())) {
            for (String mobile : reqDTO.getMobiles()) {
                //查询操作员编号
                List<UserOperatorLoginInfoBO> loginBos = userOperatorLoginInfoDAO.findByLoginNameAndAppId(mobile, AppIdEnum.SUPER_APP.getCode());
                if (CollectionUtils.isNotEmpty(loginBos)) {
                    String operatorNo = loginBos.get(0).getOperatorNo();
                    if (StringUtils.isNotBlank(operatorNo)) {
                        optNos.add(operatorNo);
                    }
                }
            }
        }

        optNos.forEach(operatorNo -> {
            UpdateUserTagDTO userTag = new UpdateUserTagDTO();
            userTag.setOperatorNo(operatorNo);
            userTag.setTagNo(reqDTO.getTagNo());
            userTag.setOptType(OptTypeEnum.DEL.getCode());
            userTags.add(userTag);
        });

        //发送至MQ
        if (CollectionUtils.isNotEmpty(userTags)) {
            try {
                //拆分list，避免mq单条消息过大
                List<List<UpdateUserTagDTO>> splitReqs = ListUtil.split(userTags, 10);
                splitReqs.forEach(req ->
                        rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(new ArrayList<>(req)))
                );
            } catch (Exception e) {
                log.error("MQ删除标签消息发送异常", e);
            }
        }
    }

    @Override
    public void updateUserTagForImport() throws PendingException {
        //查询导入的用户标签临时数据
        int size = 500;
        for(int page=0; ; page++) {
            Pageable pageable = PageRequest.of(0, size);
            Page<TagUserTempBO> tagUserTempBOPage = tagUserTempDAO.findAll(pageable);
            List<TagUserTempBO> tagUserTempBOS = tagUserTempBOPage.getContent();
            if (CollectionUtils.isEmpty(tagUserTempBOS)) {
                break;
            }
            tagUserTempBOS.forEach(tagUserTempBO -> {
                updateUserTag(tagUserTempBO.getOperatorNo(),tagUserTempBO.getTagNo());
                //处理完删数据
                mongoTemplate.remove(tagUserTempBO);
            });
        }
    }

    private void updateUserTag(String loginName, String tagNo) {
        try {
            //查询操作员编号
            OracleOperatorBO operator = oracleUserOperatorLoginInfoDAO.queryByLoginName(loginName);
            if (operator != null) {
                UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
                updateUserTagDTO.setOperatorNo(operator.getOperatorNo());
                updateUserTagDTO.setTagNo(tagNo);
                updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
                this.updateUserTag(Collections.singletonList(updateUserTagDTO));
            }
        } catch (Exception e) {
            log.info("更新用户标签异常",e);
        }
    }

    @Override
    public void updateRuleTag() throws PendingException {
        //查询规则标签，判断该订单总金额和下单总次数是否有匹配的标签
//        List<TagInfoBO> tagInfoBOS = tagInfoDAO.findByTagTypeAndTagStatus(TagTypeEnum.RULE_TAG.getCode(), TagStatusEnum.OPEN.getCode());
//        for(TagInfoBO tagInfoBO : tagInfoBOS){
//            List<TagRuleBO> tagRuleBOS = tagInfoBO.getRule();
//            for(TagRuleBO tagRuleBO : tagRuleBOS) {
//                if (RuleTypeEnum.RULE_TAG.getCode().equals(tagRuleBO.getRuleType())) {
//                    int size = updateTagSize;
//                    int newSize = 1;
//                    for(int page=0; newSize>0; page++) {
//                        List<UpdateUserTagDTO> list = new ArrayList<>();
//                        String ruleValue = tagRuleBO.getRuleValue();
//                        Map<String, Object> map = FastJsonUtil.jsonToObject(ruleValue, Map.class);
//                        //过去天数
//                        Long days = Long.valueOf(String.valueOf(map.get("days")));
//                        //最大值
//                        Long max = map.get("max") == null ? null : Long.valueOf(String.valueOf(map.get("max")));
//                        //最小值
//                        Long min = map.get("min") == null ? null : Long.valueOf(String.valueOf(map.get("min")));
//                        //行为：下单成功/订单金额
//                        String behavior = (String) map.get("behavior");
//                        //符号：小于/等于/大于/区间
//                        String symbol = (String) map.get("symbol");
//                        //查询过去days天的用户下单成功次数和总金额
//                        List<AggregateOrderBO> aggregateOrderDTOS = aggregateOrderService.findByUpdateTimeAndAggregateOrderFinalState(DateUtil.dateCalculation(new Date(), Calendar.DATE, (int) (-days)), 11, tagRuleBO.getBusinessLine(),PageRequest.of(page,size));
//                        if (CollectionUtils.isEmpty(aggregateOrderDTOS)) {
//                            newSize = 0;
//                        }
//                        //对操作员进行分组
//                        aggregateOrderDTOS.forEach(aggregateOrderDTO -> {
//                            //对每一个操作员进行遍历
//                            boolean exist = false;
//                            Double totalAmount = Double.valueOf(String.valueOf(aggregateOrderDTO.getActualPayAmount()));
//                            Double orderCount = Double.valueOf(String.valueOf(aggregateOrderDTO.getOrderCount()));
//                            //匹配规则标签则对用户进行贴标签
//                            if (BehaviorEnum.ORDER_SUCCESS.getCode().equals(behavior)) {
//                                exist = ruleMatching(orderCount, symbol, min, max);
//                            } else if (BehaviorEnum.ORDER_AMOUNT.getCode().equals(behavior)) {
//                                exist = ruleMatching(totalAmount, symbol, min, max);
//                            }
//                            //匹配规则对用户贴标签
//                            UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
//                            updateUserTagDTO.setOperatorNo(aggregateOrderDTO.getUserId());
//                            updateUserTagDTO.setTagNo(tagInfoBO.getTagNo());
//                            if (exist) {
//                                updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
//                            } else {
//                                updateUserTagDTO.setOptType(OptTypeEnum.DEL.getCode());
//                            }
//                            list.add(updateUserTagDTO);
//                        });
//                        log.info("更新规则标签数量：{}", list.size());
//                        if (CollectionUtils.isNotEmpty(list)) {
//                            try {
//                                rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(list));
//                            } catch (Exception e) {
//                                log.info("更新标签mq异常",e);
//                            }
//                        }
//                    }
//                    log.info("标签更新完成：{}",tagInfoBO.getTagNo());
//                }
//            }
//        }

    }

    @Override
    public void updateRuleTagV2() throws PendingException {
//        List<TagInfoV2BO> tagInfoBOS = tagInfoV2DAO.findByTagTypeAndTagStatus(TagTypeEnum.RULE_TAG.getCode(), TagStatusEnum.OPEN.getCode());
//        for (TagInfoV2BO tagInfoV2BO : tagInfoBOS) {
//            List<String> operatorNos = new ArrayList<>();
//            List<String> noExistOperatorNos = new ArrayList<>();
//            List<RuleTagRuleReqV2DTO> tagRuleBOS = tagInfoV2BO.getRule();
//            //查询行为标签
//            for (RuleTagRuleReqV2DTO ruleTagRuleReqV2DTO : tagRuleBOS) {
//                //判断行为规则是否存在，下单成功次数、下单总金额
//                if (ClassificationEnum.BEHAVIOR.getCode().equals(ruleTagRuleReqV2DTO.getClassification())) {
//                    operatorNos = generateTagStrategyContent.calculateTag(ruleTagRuleReqV2DTO,operatorNos,noExistOperatorNos);
//                }
//            }
//            //查询属性标签
//            for (RuleTagRuleReqV2DTO ruleTagRuleReqV2DTO : tagRuleBOS) {
//                //判断属性规则是否存在，地区、语言、内测
//                if (ClassificationEnum.ATTRIBUTES.getCode().equals(ruleTagRuleReqV2DTO.getClassification())) {
//                    operatorNos = generateTagStrategyContent.calculateTag(ruleTagRuleReqV2DTO,operatorNos,noExistOperatorNos);
//                }
//            }
//            //1.最终获取到匹配规则的操作员编号，然后对这批用户进行贴规则标签
//            List<UpdateUserTagDTO> updateUserTagDTOS = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(operatorNos)) {
//                operatorNos.forEach(operatorNo->{
//                    UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
//                    updateUserTagDTO.setOptType(OptTypeEnum.ADD.getCode());
//                    updateUserTagDTO.setOperatorNo(operatorNo);
//                    updateUserTagDTO.setTagNo(tagInfoV2BO.getTagNo());
//                    updateUserTagDTOS.add(updateUserTagDTO);
//                });
//            }
//            //2.获取不匹配的操作员编号，然后对这批用户进行删除规则标签
//            if (CollectionUtils.isNotEmpty(noExistOperatorNos)) {
//                noExistOperatorNos.forEach(operatorNo->{
//                    UpdateUserTagDTO updateUserTagDTO = new UpdateUserTagDTO();
//                    updateUserTagDTO.setOptType(OptTypeEnum.DEL.getCode());
//                    updateUserTagDTO.setOperatorNo(operatorNo);
//                    updateUserTagDTO.setTagNo(tagInfoV2BO.getTagNo());
//                    updateUserTagDTOS.add(updateUserTagDTO);
//                });
//            }
//            rocketMQTemplate.syncSend(Topic.DATA_WAREHOUSE_UPDATE_USER_TAG_TOPIC, new UpdateUserTagMQReqDTO(updateUserTagDTOS));
//        }
    }

    @Override
    public List<String> findByOperatorNosAndTagNo(String tagNo, List<String> operatorNos) throws PendingException {
        Query query = new Query();
        query.addCriteria(Criteria.where("operatorNo").in(operatorNos).and("tagNo").is(tagNo));
        List<TagUserBO> bos = mongoTemplate.find(query, TagUserBO.class);
        if (CollectionUtils.isEmpty(bos)) {
            return null;
        }
        return bos.stream().map(TagUserBO::getOperatorNo).collect(Collectors.toList());
    }

    @Override
    public Long queryUserTotalByTag(List<String> tagNos) throws PendingException {
        if(CollectionUtils.isEmpty(tagNos)) {
            return 0L;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("tagNo").all(tagNos));
        return mongoTemplate.count(query, TagUserBO.class);
    }

    @Override
    public Long countTagUser(TagUserCountReqDTO reqDTO) throws PendingException {
        long count = 0L;
        if (reqDTO != null && CollectionUtils.isNotEmpty(reqDTO.getTagNos())) {
            Query query = new Query();
            if (TagLogicEnum.OR.getCode().equals(reqDTO.getTagLogic())) {
                //或
                query.addCriteria(Criteria.where(TagUserBO.TAG_NO).in(reqDTO.getTagNos()));
            } else {
                //且
                query.addCriteria(Criteria.where(TagUserBO.TAG_NO).all(reqDTO.getTagNos()));
            }
            count = mongoTemplate.count(query, TagUserBO.TABLE_NAME);
        }
        return count;
    }

    @Override
    public PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfo(TagUserQueryByTagsReqDTO reqDTO) throws PendingException {
        if(CollectionUtils.isEmpty(reqDTO.getTagNos())) {
            return new PageInfoDTO<>();
        }

        Query query = new Query();
        if (TagLogicEnum.OR.getCode().equals(reqDTO.getTagLogic())) {
            query.addCriteria(Criteria.where("tagNo").in(reqDTO.getTagNos()));
        } else {
            query.addCriteria(Criteria.where("tagNo").all(reqDTO.getTagNos()));
        }

        long total = mongoTemplate.count(query, TagUserBO.class);

        query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
        query.limit(reqDTO.getPageSize());
        List<TagUserInfoRespDTO> tagUserList = mongoTemplate.find(query, TagUserInfoRespDTO.class, TagUserBO.TABLE_NAME);

        PageInfoDTO<TagUserInfoRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(tagUserList.size());
        target.setList(tagUserList);
        target.setPages(((int)total + reqDTO.getPageSize() - 1)/reqDTO.getPageSize() );
        target.setTotal(total);
        return target;
    }

    @Override
    public PageInfoDTO<TagUserInfoRespDTO> queryTagUserInfoByTagGroup(UserTagGroupsReqDTO reqDTO) {
        long total = 0L;
        List<TagUserInfoRespDTO> tagUserList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(reqDTO.getGroupNos())) {
            //获取查询条件
            List<Criteria> tagCriterias = this.genTagGroupCriteria(reqDTO.getGroupNos());
            if (CollectionUtils.isNotEmpty(tagCriterias)) {
                Criteria criteria = new Criteria();
                criteria.orOperator(tagCriterias);
                Query query = new Query(criteria);
                total = mongoTemplate.count(query, TagUserBO.class);

                query.skip((reqDTO.getPageNum() - 1) * reqDTO.getPageSize());
                query.limit(reqDTO.getPageSize());
                tagUserList = mongoTemplate.find(query, TagUserInfoRespDTO.class, TagUserBO.TABLE_NAME);
            }
        }
        PageInfoDTO<TagUserInfoRespDTO> target = new PageInfoDTO<>();
        target.setPageSize(reqDTO.getPageSize());
        target.setPageNum(reqDTO.getPageNum());
        target.setSize(tagUserList.size());
        target.setList(tagUserList);
        target.setPages(((int) total + reqDTO.getPageSize() - 1) / reqDTO.getPageSize());
        target.setTotal(total);
        return target;
    }

    @Override
    public GroupContainUserRespDTO checkUserOfGroup(OperatorNoAndGroupNoReqDTO reqDTO) {
        GroupContainUserRespDTO respDTO = new GroupContainUserRespDTO();
        respDTO.setOperatorNo(reqDTO.getOperatorNo());
        respDTO.setCheckResult(false);

        //查询分群所含标签
        List<TagGroupRespDTO> groupList = userTagGroupFacade.getTagGroupByGroupNo(new TagGroupGetReqDTO().setGroupNos(Collections.singletonList(reqDTO.getGroupNo())));
        TagGroupRespDTO group = CollectionUtils.isNotEmpty(groupList) ? groupList.get(0) : null;

        //查询用户标签
        TagUserBO tagUserBO = mongoTemplate.findOne(new Query(Criteria.where(TagUserBO.OPERATOR_NO).is(reqDTO.getOperatorNo())),TagUserBO.class, TagUserBO.TABLE_NAME);
        if (tagUserBO != null) {
            respDTO.setUserTagNos(tagUserBO.getTagNo());

            //判断是否该用户群
            if (CollectionUtils.isNotEmpty(tagUserBO.getTagNo()) && group != null && CollectionUtils.isNotEmpty(group.getTagNos())) {
                if (TagLogicEnum.OR.getCode().equals(group.getTagLogic())) {
                    respDTO.setCheckResult(CollUtil.containsAny(tagUserBO.getTagNo(), group.getTagNos()));
                } else {
                    respDTO.setCheckResult(CollUtil.containsAll(tagUserBO.getTagNo(), group.getTagNos()));
                }
            }
        }

        return respDTO;
    }

    @Override
    public void addZoneTag(AddZoneTagReqDTO reqDTO) throws PendingException {
        //更新地区标签
        if (StringUtils.isNotEmpty(reqDTO.getLongitude()) && StringUtils.isNotEmpty(reqDTO.getLatitude()) && StringUtils.isNotEmpty(reqDTO.getOperatorNo())) {
            UserBehaviorInfoBO behaviorBo2 = new UserBehaviorInfoBO();
            LocationBO locationBo = new LocationBO();
            locationBo.setCoordinates(new Double[]{new BigDecimal(reqDTO.getLongitude()).doubleValue(),
                    new BigDecimal(reqDTO.getLatitude()).doubleValue()});
            behaviorBo2.setLocationBo(locationBo);
            UserInfoBO userInfoBo = new UserInfoBO();
            userInfoBo.setOperatorNo(reqDTO.getOperatorNo());
            userInfoBo.setLoginName(reqDTO.getMobile());
            behaviorBo2.setUserInfoBo(userInfoBo);
            zoneTagService.updateUserTagOne(behaviorBo2);
        }
    }

    private boolean ruleMatching(Double orderAgr, String symbol, Long min, Long max) {
        //下单总金额/下单总次数
        if (SymbolEnum.LESS_THAN.getCode().equals(symbol)) {
            //小于
            if (orderAgr<max) {
                return true;
            }
        }else if(SymbolEnum.EQUAL.getCode().equals(symbol)){
            //等于
            if (orderAgr.equals(min)) {
                return true;
            }
        } else if (SymbolEnum.MORE_THAN.getCode().equals(symbol)) {
            //大于
            if (orderAgr>min) {
                return true;
            }
        } else if (SymbolEnum.INTERVAL.getCode().equals(symbol)) {
            //区间
            if (orderAgr>=min&&orderAgr<=max) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<String> findOperatorNoByTagNo(String tagNo) {
        Query query = Query.query(Criteria.where("tagNo").is(tagNo));
        query.fields().include("operatorNo");
        List<TagUserBO> bos = mongoTemplate.find(query, TagUserBO.class);
        return bos.stream().map(TagUserBO::getOperatorNo).collect(Collectors.toList());
    }

    /**
     * 更新标签执行状态
     *
     * @param tagNo
     * @param isAdd
     */
    private void updateTagExecStatus(String tagNo, boolean isAdd) {
        Update update = Update
                .update(TagInfoV2BO.EXEC_STATUS, TagExecStatusEnum.EFFECTIVE.getCode())
                .set(TagInfoV2BO.EXEC_TIME, new Date())
                .inc(TagInfoV2BO.TOTAL_USER, isAdd ? 1 : -1);
        mongoTemplate.updateFirst(Query.query(Criteria.where(TagInfoV2BO.TAG_NO).is(tagNo)), update, TagInfoV2BO.TABLE_NAME);
    }

    @Override
    public List<TagUserGetRespDTO> queryUserByTag(TagUserGetReqDTO reqDTO) throws PendingException {
        List<TagUserGetRespDTO> respList = new ArrayList<>();
        Criteria criteria = new Criteria();
        //标签群编号和标签编号参数二选一
        if (CollectionUtils.isNotEmpty(reqDTO.getGroupNos())) {
            List<Criteria> tagCriterias = this.genTagGroupCriteria(reqDTO.getGroupNos());
            //增加条件, 无条件增加直接返回
            if (CollectionUtils.isNotEmpty(tagCriterias)) {
                criteria.orOperator(tagCriterias);
            } else {
                return respList;
            }
        } else if (CollectionUtils.isNotEmpty(reqDTO.getTagNos())) {
            criteria.and(TagUserBO.TAG_NO).all(reqDTO.getTagNos());
        } else {
            return respList;
        }

        if (StringUtils.isNotBlank(reqDTO.getPrevId())) {
            criteria.and("_id").gt(new ObjectId(reqDTO.getPrevId()));
        }

        //查询
        Query query = Query.query(criteria)
                .with(Sort.by(Sort.Direction.ASC, "_id"))
                .limit(reqDTO.getLimit());
        query.fields().include(TagUserBO.OPERATOR_NO);
        List<TagUserBO> tagUsers = mongoTemplate.find(query, TagUserBO.class, TagUserBO.TABLE_NAME);
        tagUsers.forEach(tagUser ->
                respList.add(new TagUserGetRespDTO()
                        .setId(tagUser.getId())
                        .setOperatorNo(tagUser.getOperatorNo())));
        return respList;
    }

    /**
     * 根据标签群编号生成查询条件
     *
     * @param groupNos
     * @return
     */
    private List<Criteria> genTagGroupCriteria(List<String> groupNos) {
        List<Criteria> criteriaList = new ArrayList<>();
        //查询标签群信息
        if (CollectionUtils.isNotEmpty(groupNos)) {
            List<TagGroupRespDTO> groupList = userTagGroupFacade.getTagGroupByGroupNo(new TagGroupGetReqDTO().setGroupNos(groupNos));
            if (CollectionUtils.isNotEmpty(groupList)) {
                for (TagGroupRespDTO group : groupList) {
                    if (CollectionUtils.isNotEmpty(group.getTagNos())) {
                        if (TagLogicEnum.OR.getCode().equals(group.getTagLogic())) {
                            //或逻辑
                            criteriaList.add(Criteria.where(TagUserBO.TAG_NO).in(group.getTagNos()));
                        } else {
                            //且逻辑
                            criteriaList.add(Criteria.where(TagUserBO.TAG_NO).all(group.getTagNos()));
                        }
                    }
                }
            }
        }
        return criteriaList;
    }

    @Override
    public UserTagInfoRespDTO getUserTagInfo(UserTagInfoGetReqDTO reqDTO) throws PendingException {
        Query query = Query.query(Criteria.where(TagUserBO.OPERATOR_NO).is(reqDTO.getOperatorNo()));
        query.fields().include(TagUserBO.OPERATOR_NO, TagUserBO.UPDATE_TIME, TagUserBO.TAG_NO);
        UserTagInfoRespDTO respDTO = mongoTemplate.findOne(query, UserTagInfoRespDTO.class, TagUserBO.TABLE_NAME);
        if (respDTO != null) {
            if (CollectionUtils.isNotEmpty(respDTO.getTagNo())) {
                Query tagQuery = Query.query(Criteria.where(TagInfoV2BO.TAG_NO).in(respDTO.getTagNo()));
                tagQuery.fields().include(TagInfoV2BO.TAG_NO, TagInfoV2BO.TAG_NAME, TagInfoV2BO.TAG_TYPE, TagInfoV2BO.TAG_CATALOGUE);
                List<SimpleTagInfoRespDTO> tagInfos = mongoTemplate.find(tagQuery, SimpleTagInfoRespDTO.class, TagInfoV2BO.TABLE_NAME);
                respDTO.setTagInfos(tagInfos);
            }
        } else {
            respDTO = new UserTagInfoRespDTO().setOperatorNo(reqDTO.getOperatorNo());
        }
        return respDTO;
    }
}
