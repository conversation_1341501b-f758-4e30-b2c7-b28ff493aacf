package com.lifekh.data.warehouse.strategy;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.lifekh.data.warehouse.api.dto.RuleDTO;
import com.lifekh.data.warehouse.api.enums.RuleTypeEnum;
import com.lifekh.data.warehouse.api.req.RuleTagRuleReqV2DTO;
import com.lifekh.data.warehouse.api.req.ScheduledTagUsereqDTO;
import com.lifekh.data.warehouse.oracle.bo.OracleOperatorBO;
import com.lifekh.data.warehouse.oracle.dao.OracleUserOperatorLoginInfoDAO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class UserOfflineTagStrategy extends AbstractGenerateTagStrategy{

    @Autowired
    private OracleUserOperatorLoginInfoDAO oracleUserOperatorLoginInfoDAO;

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.USER_OFFLINE;
    }

    @Override
    public void executeGenerateTag(ScheduledTagUsereqDTO scheduledStationLetterReqDTO) throws PendingException {

    }

    @Override
    public List<String> calculateTag(RuleTagRuleReqV2DTO rule) throws PendingException {
        log.info("开始查询没有打开过APP的用户, ruleNo: {}", rule.getRuleNo());
        List<String> operatorNos = new ArrayList<>();

        //参数解析
        int days = 0;
        List<RuleDTO> specialRules = rule.getSpecialRules();
        for (RuleDTO specialRule : specialRules) {
            if ("days".equals(specialRule.getRuleFiled())) {
                days = Integer.valueOf(specialRule.getMin());
            }
        }

        //查询
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -days));

        int size = 500;
        for (int page = 1; ; page++) {
            PageHelper.startPage(page, size);
            List<OracleOperatorBO> userList = oracleUserOperatorLoginInfoDAO.queryOfflineUser(startTime);
            if (CollectionUtils.isEmpty(userList)) {
                break;
            }

            userList.forEach(user -> operatorNos.add(user.getOperatorNo()));
        }

        log.info("结束查询没有打开过APP的用户, ruleNo: {}, 用户数量: {}", rule.getRuleNo(), operatorNos.size());
        return operatorNos;
    }

}
