//package com.lifekh.data.warehouse.config;
//
//import com.mongodb.*;
//import lombok.Getter;
//import lombok.Setter;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.mongodb.MongoDbFactory;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
//import org.springframework.data.mongodb.core.convert.*;
//import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
//import org.springframework.util.StringUtils;
//import org.springframework.validation.annotation.Validated;
//
//import javax.validation.constraints.Min;
//import javax.validation.constraints.NotEmpty;
//import javax.validation.constraints.NotNull;
//
//
//@Configuration
//@EnableConfigurationProperties(MongoConfig.MongoClientOptionProperties.class)
//public class MongoConfig {
//
//
//    @Bean
//    public MongoTemplate mongoTemplate(MongoDbFactory mongoDbFactory, MappingMongoConverter converter) {
//        return new MongoTemplate(mongoDbFactory, converter);
//    }
//
//    /**
//     * 自定义mongo连接池
//     *
//     * @param properties 属性配置类
//     * @return MongoDbFactory对象
//     */
//    @Bean
//    public MongoDbFactory mongoDbFactory(MongoClientOptionProperties properties) {
//
//        MongoClient mongoClient;
//
//        // 创建客户端参数
//        MongoClientOptions mongoClientOptions = mongoClientOptions(properties);
//
//
//        // 创建认证
//        MongoCredential mongoCredential = getCredential(properties);
//
//        // 创建客户端
//        if (null == mongoCredential) {
//            mongoClient = new MongoClient(new ServerAddress(properties.getHost(), properties.getPort()), mongoClientOptions);
//        } else {
//            mongoClient = new MongoClient(new ServerAddress(properties.getHost(), properties.getPort()), mongoCredential, mongoClientOptions);
//        }
//
//        return new SimpleMongoDbFactory(mongoClient, properties.getDatabase());
//    }
//
//    /**
//     * 创建认证
//     *
//     * @param properties 属性配置类
//     * @return 认证对象
//     */
//    private MongoCredential getCredential(MongoClientOptionProperties properties) {
//        if (!StringUtils.isEmpty(properties.getUsername()) && !StringUtils.isEmpty(properties.getPassword())) {
//            // 没有专用认证数据库则取当前数据库
//            String database = StringUtils.isEmpty(properties.getAuthenticationDatabase()) ?
//                    properties.getDatabase() : properties.getAuthenticationDatabase();
//            return MongoCredential.createCredential(properties.getUsername(), database,
//                    properties.getPassword().toCharArray());
//        }
//        return null;
//    }
//
//    /**
//     * mongo客户端参数配置
//     *
//     * @param properties 属性配置类
//     * @return mongo客户端参数配置对象
//     */
//    private MongoClientOptions mongoClientOptions(MongoClientOptionProperties properties) {
//        return MongoClientOptions.builder().applicationName(properties.getClientName()).
//                connectTimeout(properties.getConnectionTimeoutMs())
//                .maxConnectionIdleTime(properties.getMaxConnectionIdleTimeMs())
//                .maxConnectionLifeTime(properties.getMaxConnectionLifeTimeMs())
//                .socketTimeout(properties.getReadTimeoutMs())
//                .maxWaitTime(properties.getMaxWaitTimeMs())
//                .heartbeatFrequency(properties.getHeartbeatFrequencyMs())
//                .minHeartbeatFrequency(properties.getMinHeartbeatFrequencyMs())
//                .heartbeatConnectTimeout(properties.getHeartbeatConnectionTimeoutMs())
//                .heartbeatSocketTimeout(properties.getHeartbeatReadTimeoutMs())
//                .connectionsPerHost(properties.getConnectionsPerHost())
//                .minConnectionsPerHost(properties.getMinConnectionsPerHost())
//                .threadsAllowedToBlockForConnectionMultiplier(properties.getThreadsAllowedToBlockForConnectionMultiplier())
//                .build();
//    }
//
//    @Getter
//    @Setter
//    @Validated
//    @ConfigurationProperties(prefix = "mongodb")
//    public static class MongoClientOptionProperties {
//
//        /**
//         * 基础连接参数
//         */
//        @NotEmpty
//        private String database; // 要连接的数据库
//        private String username; // 用户名
//        private String password; // 密码
//        @NotEmpty
//        private String host; // IP
//        @NotNull
//        private Integer port; //端口
//        private String authenticationDatabase; // 设置认证数据库，如果有的话
//
//        /**
//         * 客户端连接池参数
//         */
//        @NotEmpty
//        private String clientName; // 客户端的标识，用于定位请求来源等，一般用程序名
//        @Min(value = 1)
//        private int connectionTimeoutMs; // TCP（socket）连接超时时间，毫秒
//        @Min(value = 1)
//        private int maxConnectionIdleTimeMs; // TCP（socket）连接闲置时间，毫秒
//        @Min(value = 1)
//        private int maxConnectionLifeTimeMs; // TCP（socket）连接最多可以使用多久，毫秒
//        @Min(value = 1)
//        private int readTimeoutMs; // TCP（socket）读取超时时间，毫秒
//        @Min(value = 1)
//        private int maxWaitTimeMs; // 当连接池无可用连接时客户端阻塞等待的最大时长，毫秒
//        @Min(value = 2000)
//        private int heartbeatFrequencyMs; // 心跳检测发送频率，毫秒
//        @Min(value = 300)
//        private int minHeartbeatFrequencyMs; // 最小的心跳检测发送频率，毫秒
//        @Min(value = 200)
//        private int heartbeatConnectionTimeoutMs; // 心跳检测连接超时时间，毫秒
//        @Min(value = 200)
//        private int heartbeatReadTimeoutMs; // 心跳检测读取超时时间，毫秒
//        @Min(value = 1)
//        private int connectionsPerHost; // 线程池允许的最大连接数
//        @Min(value = 1)
//        private int minConnectionsPerHost; // 线程池空闲时保持的最小连接数
//        @Min(value = 1)
//        // 计算允许多少个线程阻塞等待时的乘数，算法：threadsAllowedToBlockForConnectionMultiplier*maxConnectionsPerHost
//        private int threadsAllowedToBlockForConnectionMultiplier;
//    }
//}
//
