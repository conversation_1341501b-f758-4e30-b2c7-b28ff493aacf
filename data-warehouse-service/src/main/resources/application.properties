spring.application.name = user-datawarehouse-service
spring.profiles.active = local
#spring.data.mongodb.url = mongodb://127.0.0.1:27017/test
#spring.data.mongodb.host=************
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=REPORT_BEHAVIOR
#spring.data.mongodb.username=report_behavior
#spring.data.mongodb.password=report_behavior_2020
logging.level.com = DEBUG
logging.level.org.springframework.data = DEBUG

dubbo.application.name=user-datawarehouse-service
#dubbo.registry.address=zookeeper://svc-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
dubbo.registry.address=zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
dubbo.protocol.port=20880
dubbo.provider.group=chaos
dubbo.consumer.group=chaos

spring.redis.cluster.nodes = drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-0-1.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-0.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-1.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-0.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-1.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379
spring.redis.cluster.max-redirects = 3

spring.data.mongodb.uri=******************************************************************************************************************************************************************************************************************************************************************************************

rocketmq.producer.group = user-datawarehouse-service
rocketmq.name-server = svc-rocketmq-name-service.lifekh-tool-sit.svc.cluster.local:9876

#elastic.job.zk.serverLists = svc-zk-job.lifekh-tool-sit.svc.cluster.local:2181
elastic.job.zk.serverLists = zk-job-0.hs-zk-job.lifekh-tool-sit.svc.cluster.local:2181,zk-job-1.hs-zk-job.lifekh-tool-sit.svc.cluster.local:2181,zk-job-2.hs-zk-job.lifekh-tool-sit.svc.cluster.local:2181
elastic.job.zk.namespace = user-datawarehouse-service
elastic.job.zk.baseSleepTimeMilliseconds = 100
elastic.job.zk.maxSleepTimeMilliseconds = 1000
elastic.job.zk.maxRetries = 5

go.mvc.responseFilter=/actuator/prometheus


#要连接的数据库
#mongodb.database=REPORT_BEHAVIOR
##用户名
#mongodb.username=report_behavior
##密码
#mongodb.password=report_behavior_2020
##IP和端口（host:port），例如127.0.0.1:27017。集群模式用,分隔开，例如host1:port1,host2:port2
#mongodb.host=************
#mongodb.port=27017

# 客户端连接池参数
#客户端的标识，用于定位请求来源等，一般用程序名
#mongodb.clientName=${spring.application.name}
##TCP（socket）连接超时时间，毫秒
#mongodb.connectionTimeoutMs=5000
##TCP（socket）连接闲置时间，毫秒
#mongodb.maxConnectionIdleTimeMs=60000
##TCP（socket）连接最多可以使用多久，毫秒
#mongodb.maxConnectionLifeTimeMs=300000
##TCP（socket）读取超时时间，毫秒
#mongodb.readTimeoutMs=15000
##当连接池无可用连接时客户端阻塞等待的最大时长，毫秒
#mongodb.maxWaitTimeMs=5000
##心跳检测发送频率，毫秒
#mongodb.heartbeatFrequencyMs=20000
##最小的心跳检测发送频率，毫秒
#mongodb.minHeartbeatFrequencyMs=8000
##心跳检测连接超时时间，毫秒
#mongodb.heartbeatConnectionTimeoutMs=10000
##心跳检测读取超时时间，毫秒
#mongodb.heartbeatReadTimeoutMs=15000
##线程池允许的最大连接数
#mongodb.connectionsPerHost=1
##线程池空闲时保持的最小连接数
#mongodb.minConnectionsPerHost=1
##计算允许多少个线程阻塞等待时的乘数，算法：threadsAllowedToBlockForConnectionMultiplier*maxConnectionsPerHost
#mongodb.threadsAllowedToBlockForConnectionMultiplier=10

db.customer.jdbc.name=DataSource-customer
db.customer.jdbc.driverClassName=oracle.jdbc.driver.OracleDriver
db.customer.jdbc.type=com.alibaba.druid.pool.DruidDataSource
db.customer.jdbc.url= *******************************************************
db.customer.jdbc.username= lifekh_mp_customer_uat
db.customer.jdbc.password= lifekh_mp_customer_uat_2020
db.customer.jdbc.initialSize= 5
db.customer.jdbc.maxActive= 5
db.customer.jdbc.minIdle = 5
db.customer.jdbc.maxWait= 60000
db.customer.jdbc.dialect=  oracle
db.customer.jdbc.validationQuery=SELECT 1 FROM DUAL
db.customer.jdbc.testWhileIdle=true
db.customer.jdbc.testOnBorrow=false
db.customer.jdbc.testOnReturn=false

db.shop.jdbc.name=DataSource-shop
db.shop.jdbc.driverClassName=oracle.jdbc.driver.OracleDriver
db.shop.jdbc.type=com.alibaba.druid.pool.DruidDataSource
db.shop.jdbc.url= *******************************************************
db.shop.jdbc.username= lifekh_mp_shop
db.shop.jdbc.password= lifekh_mp_shop_2020
db.shop.jdbc.initialSize= 5
db.shop.jdbc.maxActive= 5
db.shop.jdbc.minIdle = 5
db.shop.jdbc.maxWait= 60000
db.shop.jdbc.dialect=  oracle
db.shop.jdbc.validationQuery=SELECT 1 FROM DUAL
db.shop.jdbc.testWhileIdle=true
db.shop.jdbc.testOnBorrow=false
db.shop.jdbc.testOnReturn=false

db.appconfig.jdbc.name=DataSource-appconfig
db.appconfig.jdbc.driverClassName=oracle.jdbc.driver.OracleDriver
db.appconfig.jdbc.type=com.alibaba.druid.pool.DruidDataSource
db.appconfig.jdbc.url= *******************************************************
db.appconfig.jdbc.username= lifekh_mp_appconfig
db.appconfig.jdbc.password= lifekh_mp_appconfig_2020
db.appconfig.jdbc.initialSize= 5
db.appconfig.jdbc.maxActive= 5
db.appconfig.jdbc.minIdle = 5
db.appconfig.jdbc.maxWait= 60000
db.appconfig.jdbc.dialect=  oracle
db.appconfig.jdbc.validationQuery=SELECT 1 FROM DUAL
db.appconfig.jdbc.testWhileIdle=true
db.appconfig.jdbc.testOnBorrow=false
db.appconfig.jdbc.testOnReturn=false

db.ords.jdbc.name=DataSource-ords
db.ords.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
db.ords.jdbc.type=com.alibaba.druid.pool.DruidDataSource
db.ords.jdbc.url= ******************************************************************************************************************************************************
db.ords.jdbc.username= lifekh_all
db.ords.jdbc.password= lifekh_all_2024
db.ords.jdbc.initialSize= 5
db.ords.jdbc.maxActive= 5
db.ords.jdbc.minIdle = 5
db.ords.jdbc.maxWait= 60000
db.ords.jdbc.dialect=  mysql
db.ords.jdbc.validationQuery=SELECT 1
db.ords.jdbc.testWhileIdle=true
db.ords.jdbc.testOnBorrow=false
db.ords.jdbc.testOnReturn=false

wownow.homePage=WOWNOW首页,WOWNOW首页O2O,WOWNOW首页3.0,WOWNOW首页4.0

shortlink.baseUrl=http://svc-lifekh-mp-nodejs-mobile-app-composition-sit.lifekh-mp-sit.svc.cluster.local:8080
shortlink.reportUrl=/node-composition/short-lint/report-short-for-channel/list