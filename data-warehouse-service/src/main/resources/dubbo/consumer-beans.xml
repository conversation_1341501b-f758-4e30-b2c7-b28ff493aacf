<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context-4.1.xsd
    http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <context:component-scan base-package="com.lifekh"/>

    <dubbo:reference interface="com.chaos.usercenter.api.UserTagGroupFacade"  id="userTagGroupFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.shop.api.AggregateFacadeService"  id="aggregateFacadeService" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.app.config.api.cmsFacade.CmsMerchantAdsPublishFacade" id="cmsMerchantAdsPublishFacade" version="1.0.0"/>
    <dubbo:reference interface="com.chaos.discovery.review.api.ContentInfoFacade" id="contentInfoFacade" version="1.0.0" />

    <dubbo:reference interface="com.chaos.usercenter.api.UserOperatorFacade"  id="userOperatorFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.message.api.MessageFacade" id="messageFacade" version="1.0.0"></dubbo:reference>


    <dubbo:reference interface="com.khsuper.product.api.ProductFacade" id="productFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.khsuper.takeaway.merchant.api.facade.MerchantStoreFacade" id="merchantStoreFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.khsuper.takeaway.merchant.api.facade.AdvertisingFacade" id="advertisingFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.khsuper.takeaway.merchant.api.facade.ThemeFacade" id="themeFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.marketing.api.UserCouponInfoFacade" id="userCouponInfoFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.marketing.api.promocode.PromoCodeServiceFacade" id="promoCodeServiceFacade" version="1.0.0"></dubbo:reference>
</beans>

