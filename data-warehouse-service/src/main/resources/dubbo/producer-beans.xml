<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:service interface="com.lifekh.data.warehouse.api.TagInfoFacade" class="com.lifekh.data.warehouse.facade.TagInfoFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.TagUserFacade" class="com.lifekh.data.warehouse.facade.TagUserFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.RuleTagInfoFacade" class="com.lifekh.data.warehouse.facade.RuleTagInfoFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.TagClassificationFacade" class="com.lifekh.data.warehouse.facade.TagClassificationFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.UserDataStaticFacade" class="com.lifekh.data.warehouse.facade.UserDataStaticFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.MerchantAdsPublishStaticFacade" class="com.lifekh.data.warehouse.facade.MerchantAdsPublishStaticFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.DiscoveryContentFacade" class="com.lifekh.data.warehouse.facade.DiscoveryContentFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.DevicePoolFacade" class="com.lifekh.data.warehouse.facade.DevicePoolFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.AdsLogFacade" class="com.lifekh.data.warehouse.facade.AdsLogFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.UserBehaviorFacade" class="com.lifekh.data.warehouse.facade.UserBehaviorFacadeImpl" version="1.0.0"/>
    <dubbo:service interface="com.lifekh.data.warehouse.api.ShortLinkReportFacade" class="com.lifekh.data.warehouse.facade.ShortLinkReportFacadeImpl" version="1.0.0"/>
</beans>
