<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lifekh</groupId>
    <artifactId>user-data-warehouse</artifactId>
    <packaging>pom</packaging>
    <version>2.0.0.0-SNAPSHOT</version>
    <modules>
        <module>data-warehouse-api</module>
        <module>data-warehouse-service</module>
        <module>data-warehouse-collection</module>
        <module>data-warehouse-dao</module>
        <module>data-warehouse-common</module>
    </modules>

    <parent>
        <groupId>com.outstanding</groupId>
        <artifactId>go-framework-parent</artifactId>
        <version>3.3.4-SNAPSHOT</version>
    </parent>


    <properties>
        <basic.common.api.version>2.5.1.0-SNAPSHOT</basic.common.api.version>
        <hutool-all.version>5.7.6</hutool-all.version>
        <chaos-usercenter-api.version>5.4.4.0-SNAPSHOT</chaos-usercenter-api.version>
        <fastjson.version>1.2.78</fastjson.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <shop-api.version>2.8.0.8-SNAPSHOT</shop-api.version>
        <message-api.version>3.2.6-SNAPSHOT</message-api.version>
        <app-config-api.version>4.5.4.0-SNAPSHOT</app-config-api.version>
        <discovery-review-api.version>3.0.2.0-SNAPSHOT</discovery-review-api.version>
        <marketing-api.version>2.9.9.0-SNAPSHOT</marketing-api.version>
        <kh-super.product.version>3.1.0.6-SNAPSHOT</kh-super.product.version>
        <kh-super.merchant.version>3.1.2.4-SNAPSHOT</kh-super.merchant.version>
        <mybatis.version>3.4.6</mybatis.version>
        <druid.version>1.1.10</druid.version>
        <mybatis-spring.version>1.3.2</mybatis-spring.version>
        <ojdbc.version>11.2.0</ojdbc.version>
        <mysql.version>8.0.33</mysql.version>
        <pagehelper.version>5.1.11</pagehelper.version>
        <pagehelper-spring-boot-starter.version>1.2.13</pagehelper-spring-boot-starter.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.lifekh</groupId>
                <artifactId>data-warehouse-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.outstanding</groupId>
                <artifactId>framework-plugin-junit</artifactId>
                <version>${parent.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-api-mockito</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-module-junit4</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.lifekh</groupId>
                <artifactId>data-warehouse-dao</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lifekh</groupId>
                <artifactId>data-warehouse-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lifekh</groupId>
                <artifactId>data-warehouse-collection</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>basic-common-api</artifactId>
                <version>${basic.common.api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>chaos-usercenter-api</artifactId>
                <version>${chaos-usercenter-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>shop-api</artifactId>
                <version>${shop-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>message-api</artifactId>
                <version>${message-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>app-config-api</artifactId>
                <version>${app-config-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>discovery-review-api</artifactId>
                <version>${discovery-review-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chaos</groupId>
                <artifactId>marketing-api</artifactId>
                <version>${marketing-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kh-super.takeaway</groupId>
                <artifactId>product-api</artifactId>
                <version>${kh-super.product.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kh-super.takeaway</groupId>
                <artifactId>merchant-api</artifactId>
                <version>${kh-super.merchant.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

  <!--          <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc</artifactId>
                <version>${ojdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Releases Repository</name>
            <url>${ReleaseRepository}</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>${SnapshotRepository}</url>
        </snapshotRepository>
    </distributionManagement>
</project>